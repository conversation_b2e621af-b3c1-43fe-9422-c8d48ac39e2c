"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/products/page",{

/***/ "(app-pages-browser)/./src/components/ui/SlidingPanel.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/SlidingPanel.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_CloseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CloseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CloseOutlined.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst SlidingPanel = (param)=>{\n    let { isOpen, onClose, title, children, width = \"400px\", footer } = param;\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRendered, setIsRendered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [windowWidth, setWindowWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)( true ? window.innerWidth : 0);\n    // Handle window resize\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SlidingPanel.useEffect\": ()=>{\n            const handleResize = {\n                \"SlidingPanel.useEffect.handleResize\": ()=>{\n                    setWindowWidth(window.innerWidth);\n                }\n            }[\"SlidingPanel.useEffect.handleResize\"];\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"SlidingPanel.useEffect\": ()=>{\n                    window.removeEventListener('resize', handleResize);\n                }\n            })[\"SlidingPanel.useEffect\"];\n        }\n    }[\"SlidingPanel.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SlidingPanel.useEffect\": ()=>{\n            console.log(\"SlidingPanel - isOpen changed:\", isOpen, \"title:\", title);\n            if (isOpen) {\n                setIsRendered(true);\n                console.log(\"SlidingPanel - Setting isRendered to true\");\n                // Small delay to ensure the panel is rendered before animating\n                setTimeout({\n                    \"SlidingPanel.useEffect\": ()=>{\n                        setIsVisible(true);\n                        console.log(\"SlidingPanel - Setting isVisible to true\");\n                    }\n                }[\"SlidingPanel.useEffect\"], 50);\n            } else {\n                setIsVisible(false);\n                console.log(\"SlidingPanel - Setting isVisible to false\");\n                // Wait for animation to complete before unmounting\n                const timer = setTimeout({\n                    \"SlidingPanel.useEffect.timer\": ()=>{\n                        setIsRendered(false);\n                        console.log(\"SlidingPanel - Setting isRendered to false\");\n                    }\n                }[\"SlidingPanel.useEffect.timer\"], 300);\n                return ({\n                    \"SlidingPanel.useEffect\": ()=>clearTimeout(timer)\n                })[\"SlidingPanel.useEffect\"];\n            }\n        }\n    }[\"SlidingPanel.useEffect\"], [\n        isOpen,\n        title\n    ]);\n    if (!isRendered) return null;\n    // Calculate responsive width based on screen size\n    const getResponsiveWidth = ()=>{\n        // For small screens (mobile), use full width\n        if (windowWidth < 640) {\n            return '100vw'; // 100% of viewport width\n        }\n        // For medium screens\n        if (windowWidth < 1024) {\n            return '450px';\n        }\n        // For larger screens, use the provided width or default\n        return width;\n    };\n    // Render the panel using a portal to ensure it's at the body level\n    const panelContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-[99999] overflow-hidden\",\n        style: {\n            zIndex: 99999\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black transition-opacity duration-300 \".concat(isVisible ? \"opacity-50\" : \"opacity-0\"),\n                onClick: onClose,\n                style: {\n                    zIndex: 99999\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform \".concat(isVisible ? \"translate-x-0\" : \"translate-x-full\"),\n                style: {\n                    width: getResponsiveWidth(),\n                    zIndex: 99999\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-medium text-gray-800 truncate\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                type: \"text\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    style: {\n                                        color: '#333'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 19\n                                }, void 0),\n                                onClick: onClose,\n                                \"aria-label\": \"Close panel\",\n                                style: {\n                                    color: '#333',\n                                    borderColor: 'transparent',\n                                    background: 'transparent'\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto p-4 pt-6 bg-white\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, undefined),\n                    footer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-200 bg-gray-50\",\n                        children: footer\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, undefined);\n    // Use portal to render at body level to avoid stacking context issues\n    return  true ? /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(panelContent, document.body) : 0;\n};\n_s(SlidingPanel, \"sb9mljCr9lGLLPaevMatKyoxyc4=\");\n_c = SlidingPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SlidingPanel);\nvar _c;\n$RefreshReg$(_c, \"SlidingPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/SlidingPanel.tsx\n"));

/***/ })

});