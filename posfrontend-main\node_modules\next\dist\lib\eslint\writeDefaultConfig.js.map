{"version": 3, "sources": ["../../../src/lib/eslint/writeDefaultConfig.ts"], "sourcesContent": ["import { promises as fs } from 'fs'\nimport { bold, green } from '../picocolors'\nimport os from 'os'\nimport path from 'path'\nimport * as CommentJson from 'next/dist/compiled/comment-json'\nimport type { ConfigAvailable } from './hasEslintConfiguration'\n\nimport * as Log from '../../build/output/log'\n\nexport async function writeDefaultConfig(\n  baseDir: string,\n  { exists, emptyEslintrc, emptyPkgJsonConfig }: ConfigAvailable,\n  selectedConfig: any,\n  eslintrcFile: string | null,\n  pkgJsonPath: string | null,\n  packageJsonConfig: { eslintConfig: any } | null\n) {\n  if (!exists && emptyEslintrc && eslintrcFile) {\n    const ext = path.extname(eslintrcFile)\n\n    let newFileContent\n    if (ext === '.yaml' || ext === '.yml') {\n      newFileContent = \"extends: 'next'\"\n    } else {\n      newFileContent = CommentJson.stringify(selectedConfig, null, 2)\n\n      if (ext === '.js') {\n        newFileContent = 'module.exports = ' + newFileContent\n      }\n    }\n\n    await fs.writeFile(eslintrcFile, newFileContent + os.EOL)\n\n    Log.info(\n      `We detected an empty ESLint configuration file (${bold(\n        path.basename(eslintrcFile)\n      )}) and updated it for you!`\n    )\n  } else if (!exists && emptyPkgJsonConfig && packageJsonConfig) {\n    packageJsonConfig.eslintConfig = selectedConfig\n\n    if (pkgJsonPath)\n      await fs.writeFile(\n        pkgJsonPath,\n        CommentJson.stringify(packageJsonConfig, null, 2) + os.EOL\n      )\n\n    Log.info(\n      `We detected an empty ${bold(\n        'eslintConfig'\n      )} field in package.json and updated it for you!`\n    )\n  } else if (!exists) {\n    await fs.writeFile(\n      path.join(baseDir, '.eslintrc.json'),\n      CommentJson.stringify(selectedConfig, null, 2) + os.EOL\n    )\n\n    console.log(\n      green(\n        `We created the ${bold(\n          '.eslintrc.json'\n        )} file for you and included your selected configuration.`\n      )\n    )\n  }\n}\n"], "names": ["writeDefaultConfig", "baseDir", "exists", "emptyEslintrc", "emptyPkgJsonConfig", "selectedConfig", "eslintrcFile", "pkgJsonPath", "packageJsonConfig", "ext", "path", "extname", "newFileContent", "CommentJson", "stringify", "fs", "writeFile", "os", "EOL", "Log", "info", "bold", "basename", "eslintConfig", "join", "console", "log", "green"], "mappings": ";;;;+BASsBA;;;eAAAA;;;oBATS;4BACH;2DACb;6DACE;qEACY;6DAGR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEd,eAAeA,mBACpBC,OAAe,EACf,EAAEC,MAAM,EAAEC,aAAa,EAAEC,kBAAkB,EAAmB,EAC9DC,cAAmB,EACnBC,YAA2B,EAC3BC,WAA0B,EAC1BC,iBAA+C;IAE/C,IAAI,CAACN,UAAUC,iBAAiBG,cAAc;QAC5C,MAAMG,MAAMC,aAAI,CAACC,OAAO,CAACL;QAEzB,IAAIM;QACJ,IAAIH,QAAQ,WAAWA,QAAQ,QAAQ;YACrCG,iBAAiB;QACnB,OAAO;YACLA,iBAAiBC,aAAYC,SAAS,CAACT,gBAAgB,MAAM;YAE7D,IAAII,QAAQ,OAAO;gBACjBG,iBAAiB,sBAAsBA;YACzC;QACF;QAEA,MAAMG,YAAE,CAACC,SAAS,CAACV,cAAcM,iBAAiBK,WAAE,CAACC,GAAG;QAExDC,KAAIC,IAAI,CACN,CAAC,gDAAgD,EAAEC,IAAAA,gBAAI,EACrDX,aAAI,CAACY,QAAQ,CAAChB,eACd,yBAAyB,CAAC;IAEhC,OAAO,IAAI,CAACJ,UAAUE,sBAAsBI,mBAAmB;QAC7DA,kBAAkBe,YAAY,GAAGlB;QAEjC,IAAIE,aACF,MAAMQ,YAAE,CAACC,SAAS,CAChBT,aACAM,aAAYC,SAAS,CAACN,mBAAmB,MAAM,KAAKS,WAAE,CAACC,GAAG;QAG9DC,KAAIC,IAAI,CACN,CAAC,qBAAqB,EAAEC,IAAAA,gBAAI,EAC1B,gBACA,8CAA8C,CAAC;IAErD,OAAO,IAAI,CAACnB,QAAQ;QAClB,MAAMa,YAAE,CAACC,SAAS,CAChBN,aAAI,CAACc,IAAI,CAACvB,SAAS,mBACnBY,aAAYC,SAAS,CAACT,gBAAgB,MAAM,KAAKY,WAAE,CAACC,GAAG;QAGzDO,QAAQC,GAAG,CACTC,IAAAA,iBAAK,EACH,CAAC,eAAe,EAAEN,IAAAA,gBAAI,EACpB,kBACA,uDAAuD,CAAC;IAGhE;AACF"}