/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/components/Sales/sales-panels.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/* Sale details panel styling */
.sale-detail-light .ant-descriptions-item-label {
  background-color: #f5f5f5 !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
}

.sale-detail-light .ant-descriptions-item-content {
  background-color: #ffffff !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
}

.sale-detail-light .ant-descriptions-bordered {
  border-color: #e8e8e8 !important;
}

/* Modern POS Sales Form Styling */
.sales-form .ant-form-item-label > label {
  color: #333 !important;
  font-weight: 500 !important;
}

/* Input and Select Styling */
.sales-form .ant-input-number-input::-moz-placeholder, .sales-form .ant-input::-moz-placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}
.sales-form .ant-select-selection-placeholder,
.sales-form .ant-input-number-input::placeholder,
.sales-form .ant-input::placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

.sales-form .ant-input,
.sales-form .ant-input-number-input,
.sales-form .ant-select-selection-item {
  color: #333 !important;
}

.sales-form .ant-select-selector,
.sales-form .ant-input-number,
.sales-form .ant-input {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

.sales-form .ant-select-selector:hover,
.sales-form .ant-input-number:hover,
.sales-form .ant-input:hover {
  border-color: #40a9ff !important;
}

.sales-form .ant-select-focused .ant-select-selector,
.sales-form .ant-input-number-focused,
.sales-form .ant-input:focus {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.sales-form .ant-select-arrow,
.sales-form .ant-input-number-handler-wrap {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Button Styling */
.sales-form .ant-btn-primary {
  border: none !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  transition: all 0.3s ease !important;
}

.sales-form .ant-btn-primary:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.sales-form .ant-btn-primary:active {
  transform: translateY(0) !important;
}

/* Table Styling */
.sales-form table {
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 8px;
  overflow: hidden;
  width: 100%;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.sales-form table th {
  font-weight: 500;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
}

.sales-form table th,
.sales-form table td {
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
}

.sales-form table tbody tr:last-child td {
  border-bottom: none;
}

.sales-form table tbody tr {
  transition: background-color 0.2s ease;
}

.sales-form table tbody tr:hover {
  background-color: rgba(245, 245, 245, 0.7) !important;
}

/* Empty State Styling */
.sales-form .ant-empty-description {
  color: #666 !important;
}

.sales-form .ant-empty-img-simple-ellipse {
  fill: #f5f5f5 !important;
}

.sales-form .ant-empty-img-simple-g {
  stroke: #d9d9d9 !important;
}

.sales-form .ant-empty-img-simple-path {
  fill: #e8e8e8 !important;
}

/* Form Validation Styling */
.sales-form .ant-form-item-has-error .ant-input,
.sales-form .ant-form-item-has-error .ant-input-number,
.sales-form .ant-form-item-has-error .ant-select-selector,
.sales-form .ant-form-item-has-error .ant-picker {
  border-color: #ef4444 !important;
}

.sales-form .ant-form-item-explain-error {
  color: #ef4444 !important;
  font-size: 0.8rem !important;
  margin-top: 4px !important;
}

/* Checkout Section Styling */
.sales-form .bg-\[#111827\] {
  border-radius: 8px;
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  background-color: #f5f5f5 !important;
}

/* Scrollbar Styling */
.sales-form .overflow-y-auto::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.sales-form .overflow-y-auto::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 4px;
}

.sales-form .overflow-y-auto::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 4px;
}

.sales-form .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* Sales action buttons */
.view-button {
  color: #10b981 !important; /* green-500 */
}

.view-button:hover {
  color: #34d399 !important; /* green-400 */
}

.edit-button {
  color: #3b82f6 !important; /* blue-500 */
}

.edit-button:hover {
  color: #60a5fa !important; /* blue-400 */
}

.delete-button {
  color: #ef4444 !important; /* red-500 */
}

.delete-button:hover {
  color: #f87171 !important; /* red-400 */
}

/* Sales table styling */
.sales-table {
  background-color: #ffffff !important;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.sales-table .ant-table {
  background-color: transparent !important;
}

.sales-table .ant-table-thead > tr > th {
  background-color: #f5f5f5 !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  font-size: 0.75rem !important;
  letter-spacing: 0.05em !important;
  padding: 12px 16px !important;
}

.sales-table .ant-table-tbody > tr > td {
  background-color: #ffffff !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
  padding: 12px 16px !important;
}

.sales-table .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5 !important;
}

.sales-table .ant-table-summary {
  background-color: #f5f5f5 !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
}

.sales-table .ant-table-summary-cell {
  background-color: #f5f5f5 !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
  font-weight: 600;
  padding: 12px 16px !important;
}

.sales-table .ant-empty-description {
  color: #666 !important;
}

/* Add borders to the table */
.sales-table .ant-table-container {
  border: 1px solid #e8e8e8 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

/* Style the table header */
.sales-table .ant-table-thead > tr > th:first-child {
  border-top-left-radius: 8px !important;
}

.sales-table .ant-table-thead > tr > th:last-child {
  border-top-right-radius: 8px !important;
}

/* Sliding Panel Styling */
.ant-drawer-content {
  background-color: #ffffff !important;
}

.ant-drawer-header {
  background-color: #f5f5f5 !important;
  border-bottom: 1px solid #e8e8e8 !important;
}

.ant-drawer-title {
  color: #333 !important;
  font-weight: 600 !important;
}

.ant-drawer-close {
  color: #333 !important;
}

/* Custom styling for the sliding panel */

/* Receipt Preview Modal Styling */
.receipt-preview-modal .ant-modal-content {
  background-color: #ffffff !important;
}

.receipt-preview-modal .ant-modal-header {
  background-color: #f5f5f5 !important;
  border-bottom: 1px solid #e8e8e8 !important;
}

.receipt-preview-modal .ant-modal-body {
  background-color: #ffffff !important;
}

.receipt-preview-modal .ant-modal-footer {
  background-color: #f5f5f5 !important;
  border-top: 1px solid #e8e8e8 !important;
}

/* Sliding panels now use z-index 9999 and cover the full screen properly */

/* Mobile Responsiveness */
@media (max-width: 768px) {
  /* Make the sliding panel full width on mobile */
  .fixed.inset-0.z-\[1000\].overflow-hidden {
    left: 0 !important;
  }

  /* Make the panel content full width */
  .sales-form {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
  }

  /* Adjust grid layout for mobile */
  .sales-form .grid {
    grid-template-columns: 1fr !important;
  }

  /* Adjust table for mobile */
  .sales-form table {
    font-size: 0.8rem;
  }

  .sales-form table th,
  .sales-form table td {
    padding: 6px 8px;
  }

  /* Adjust buttons for mobile */
  .sales-form .ant-btn {
    font-size: 0.9rem !important;
    height: auto !important;
    padding: 8px 12px !important;
  }

  /* Adjust headings for mobile */
  .sales-form h3 {
    font-size: 1rem !important;
  }

  /* Ensure checkout section is visible */
  .sales-form .sticky {
    position: relative !important;
    top: 0 !important;
  }
}


/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/components/Sales/modern-sales.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/* Modern Sales Form Styling */

/* Modern Select Styling - EXACT height match */
.modern-select .ant-select-selector {
  border-radius: 12px !important;
  border: 2px solid #e2e8f0 !important;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
  transition: all 0.2s ease-in-out !important;
  height: 52px !important;
  min-height: 52px !important;
  max-height: 52px !important;
  padding-right: 48px !important;
  padding-left: 16px !important;
  display: flex !important;
  align-items: center !important;
  position: relative !important;
  box-sizing: border-box !important;
}

/* Remove any default list styling or icons */
.modern-select .ant-select-selector,
.modern-select .ant-select-selector * {
  list-style: none !important;
  background-image: none !important;
  text-indent: 0 !important;
}

.modern-select .ant-select-selector:hover {
  border-color: #3b82f6 !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.modern-select.ant-select-focused .ant-select-selector {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.modern-select .ant-select-selection-placeholder {
  color: #94a3b8 !important;
  font-weight: 500 !important;
}

/* Fix dropdown arrow positioning - center it properly */
.modern-select .ant-select-arrow {
  right: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  color: #64748b !important;
  font-size: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 20px !important;
  height: 20px !important;
  position: absolute !important;
  margin: 0 !important;
  padding: 0 !important;
}

.modern-select .ant-select-arrow .anticon {
  vertical-align: middle !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
  padding: 0 !important;
  line-height: 1 !important;
}

/* Fix search icon positioning - center it properly */
.modern-select .ant-select-suffix {
  right: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #64748b !important;
  font-size: 14px !important;
  height: 100% !important;
  position: absolute !important;
}

.modern-select .ant-select-suffix .anticon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  vertical-align: middle !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Modern Input Number Styling - EXACT match with select height */
.modern-input .ant-input-number {
  border-radius: 12px !important;
  border: 2px solid #e2e8f0 !important;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
  transition: all 0.2s ease-in-out !important;
  height: 52px !important;
  min-height: 52px !important;
  max-height: 52px !important;
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
  padding: 0 16px !important;
  box-sizing: border-box !important;
}

.modern-input .ant-input-number-input-wrap {
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
}

.modern-input .ant-input-number-input {
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  padding: 0 !important;
  border: none !important;
  outline: none !important;
  background: transparent !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
}

.modern-input .ant-input-number-handler-wrap {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
}

.modern-input .ant-input-number:hover {
  border-color: #3b82f6 !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.modern-input .ant-input-number-focused {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Dropdown Menu Styling */
.ant-select-dropdown {
  border-radius: 16px !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  -webkit-backdrop-filter: blur(8px) !important;
          backdrop-filter: blur(8px) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  max-height: 300px !important;
  overflow-y: auto !important;
}

.ant-select-item {
  border-radius: 8px !important;
  margin: 4px 8px !important;
  padding: 8px 12px !important;
  transition: all 0.2s ease-in-out !important;
  min-height: auto !important;
  line-height: 1.4 !important;
}

.ant-select-item-option {
  padding: 8px 12px !important;
  min-height: 48px !important;
  display: flex !important;
  align-items: center !important;
}

.ant-select-item-option-content {
  flex: 1 !important;
  overflow: hidden !important;
}

.ant-select-item-option-selected {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%) !important;
  color: #1e40af !important;
  font-weight: 600 !important;
}

.ant-select-item-option-active {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
}

/* Fix for payment method display */
.modern-select .ant-select-selection-item {
  display: flex !important;
  align-items: center !important;
  line-height: 1.4 !important;
  padding: 8px 12px !important;
  font-weight: 500 !important;
  color: #334155 !important;
  background: none !important;
  border: none !important;
}

/* Remove any default icons or backgrounds */
.modern-select .ant-select-selection-item::before,
.modern-select .ant-select-selection-item::after {
  display: none !important;
  content: none !important;
}

/* Ensure clean text display without extra elements */
.modern-select .ant-select-selection-item {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol' !important;
}

/* Remove any Ant Design default styling that might add icons */
.modern-select .ant-select-selector .ant-select-selection-item {
  background-image: none !important;
  list-style: none !important;
}

/* Fix icon positioning in selected item */
.modern-select .ant-select-selection-item span {
  display: inline-flex !important;
  align-items: center !important;
  vertical-align: middle !important;
}

/* Form Item Labels */
.ant-form-item-label > label {
  font-weight: 600 !important;
  color: #334155 !important;
}

/* Scrollbar Styling */
.max-h-\[400px\]::-webkit-scrollbar {
  width: 6px;
}

.max-h-\[400px\]::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.max-h-\[400px\]::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  border-radius: 3px;
}

.max-h-\[400px\]::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
}

/* Button Hover Effects */
.ant-btn:not(.ant-btn-primary):hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Loading Spinner */
.ant-spin-dot-item {
  background-color: #3b82f6 !important;
}

/* Modal Styling */
.ant-modal-content {
  border-radius: 20px !important;
  overflow: hidden !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

.ant-modal-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  border-bottom: 1px solid #e2e8f0 !important;
  padding: 20px 24px !important;
}

.ant-modal-title {
  font-weight: 700 !important;
  color: #1e293b !important;
}

/* Backdrop Blur Effect */
.backdrop-blur-sm {
  -webkit-backdrop-filter: blur(4px) !important;
          backdrop-filter: blur(4px) !important;
}

/* Gradient Text */
.bg-clip-text {
  -webkit-background-clip: text !important;
  background-clip: text !important;
}

/* Animation Classes */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

/* Glass Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.7) !important;
  -webkit-backdrop-filter: blur(10px) !important;
          backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Additional fixes for dropdown content */
.ant-select-item-option div {
  white-space: normal !important;
  word-wrap: break-word !important;
}

.ant-select-item-option .flex {
  width: 100% !important;
}

/* Ensure proper spacing in dropdown items */
.ant-select-dropdown .ant-select-item-option {
  height: auto !important;
  min-height: 48px !important;
  padding: 12px 16px !important;
  font-weight: 500 !important;
  color: #334155 !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

/* Fix icon alignment in dropdown options */
.ant-select-dropdown .ant-select-item-option span {
  display: inline-flex !important;
  align-items: center !important;
  vertical-align: middle !important;
}

/* Fix text overflow in selected item */
.modern-select .ant-select-selector .ant-select-selection-item {
  overflow: visible !important;
  text-overflow: clip !important;
  white-space: nowrap !important;
}

/* Specific emoji icon fixes */
.modern-select .ant-select-selection-item,
.ant-select-dropdown .ant-select-item-option {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol' !important;
}

/* Ensure emojis are properly sized and aligned */
.modern-select .ant-select-selection-item::first-letter,
.ant-select-dropdown .ant-select-item-option::first-letter {
  font-size: 16px !important;
  line-height: 1 !important;
  vertical-align: middle !important;
  margin-right: 6px !important;
}

/* Additional icon positioning fixes */
.modern-select .ant-select-selection-item {
  text-align: left !important;
  justify-content: flex-start !important;
}

.ant-select-dropdown .ant-select-item-option {
  text-align: left !important;
  justify-content: flex-start !important;
}

/* Fix for any icon misalignment */
.modern-select .ant-select-selection-item > *,
.ant-select-dropdown .ant-select-item-option > * {
  vertical-align: middle !important;
}

/* Force remove any unwanted icons or styling */
.modern-select .ant-select-selection-item {
  position: relative !important;
}

.modern-select .ant-select-selection-item::before,
.modern-select .ant-select-selection-item::after,
.modern-select .ant-select-selection-item > *::before,
.modern-select .ant-select-selection-item > *::after {
  display: none !important;
  content: none !important;
  background: none !important;
  border: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* Ensure only our text content shows */
.modern-select .ant-select-selection-item {
  overflow: visible !important;
  white-space: nowrap !important;
  text-overflow: clip !important;
}

/* Remove any browser default styling */
.modern-select select,
.modern-select option {
  background-image: none !important;
  list-style-image: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* Fix for product selection icons and search positioning */
.modern-select .ant-select-selection-search {
  left: 16px !important;
  right: 48px !important;
  top: 0 !important;
  bottom: 0 !important;
  display: flex !important;
  align-items: center !important;
  height: 100% !important;
  position: absolute !important;
}

.modern-select .ant-select-selection-search-input {
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  border: none !important;
  outline: none !important;
  background: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
  line-height: 1.4 !important;
}

/* Ensure proper spacing for search functionality */
.modern-select.ant-select-show-search .ant-select-selector {
  cursor: text !important;
}

.modern-select.ant-select-show-search .ant-select-selection-item {
  padding-right: 0 !important;
  margin-right: 0 !important;
}

/* Additional icon positioning fixes */
.modern-select .ant-select-clear,
.modern-select .ant-select-arrow,
.modern-select .ant-select-suffix {
  position: absolute !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 1 !important;
}

/* Ensure icons don't overlap with content */
.modern-select .ant-select-selection-overflow {
  padding-right: 32px !important;
}

/* Fix for multiple icons (clear + arrow) */
.modern-select .ant-select-clear {
  right: 40px !important;
}

.modern-select .ant-select-arrow {
  right: 16px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-select .ant-select-selector,
  .modern-input .ant-input-number {
    min-height: 44px !important;
  }

  .ant-select-dropdown {
    border-radius: 12px !important;
  }

  .ant-select-dropdown .ant-select-item-option {
    min-height: 48px !important;
    padding: 10px !important;
  }

  /* Mobile icon positioning */
  .modern-select .ant-select-arrow,
  .modern-select .ant-select-suffix {
    right: 12px !important;
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/search.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/* Custom styles for search inputs */

/* Light background for input and its wrapper */
.ant-input-affix-wrapper {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
}

/* Ensure hover and focus states maintain light background */
.ant-input-affix-wrapper:hover,
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
  background-color: #ffffff !important;
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* Style for the input inside the wrapper */
.ant-input-affix-wrapper .ant-input {
  background-color: transparent !important;
  color: rgba(0, 0, 0, 0.85) !important;
}

/* Style for the clear icon */
.ant-input-affix-wrapper .ant-input-clear-icon {
  color: rgba(0, 0, 0, 0.25) !important;
}

.ant-input-affix-wrapper .ant-input-clear-icon:hover {
  color: rgba(0, 0, 0, 0.45) !important;
}

/* Style for the placeholder */
.ant-input::-moz-placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}
.ant-input::placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Style for the search icon */
.ant-input-prefix .anticon {
  color: rgba(0, 0, 0, 0.25) !important;
}

