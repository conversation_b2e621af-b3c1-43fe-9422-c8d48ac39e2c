"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/products/page",{

/***/ "(app-pages-browser)/./src/components/Products/ProductTable.tsx":
/*!**************************************************!*\
  !*** ./src/components/Products/ProductTable.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/notification/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/checkbox/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteFilled.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarcodeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CalendarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ResponsiveTable */ \"(app-pages-browser)/./src/components/ui/ResponsiveTable.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ProductTable = (param)=>{\n    let { products, loading, onView, onEdit, onDelete, onBulkDelete, onAdjustStock, isMobile = false } = param;\n    _s();\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__.useSelector)({\n        \"ProductTable.useSelector[user]\": (state)=>state.auth.user\n    }[\"ProductTable.useSelector[user]\"]);\n    const userRole = user === null || user === void 0 ? void 0 : user.role;\n    // State for selected products\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectAll, setSelectAll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle select all checkbox change\n    const handleSelectAllChange = (e)=>{\n        const checked = e.target.checked;\n        setSelectAll(checked);\n        if (checked) {\n            // Select all products that the user can delete\n            const selectableProductIds = products.filter((product)=>canEditDelete(product)).map((product)=>product.id);\n            setSelectedProducts(selectableProductIds);\n        } else {\n            // Deselect all products\n            setSelectedProducts([]);\n        }\n    };\n    // Handle individual checkbox change\n    const handleCheckboxChange = (productId, checked)=>{\n        if (checked) {\n            setSelectedProducts((prev)=>[\n                    ...prev,\n                    productId\n                ]);\n        } else {\n            setSelectedProducts((prev)=>prev.filter((id)=>id !== productId));\n        }\n    };\n    // Handle bulk delete\n    const handleBulkDelete = ()=>{\n        if (selectedProducts.length > 0 && onBulkDelete) {\n            onBulkDelete(selectedProducts);\n            setSelectedProducts([]);\n            setSelectAll(false);\n        } else {\n            _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].warning({\n                message: 'No products selected',\n                description: 'Please select at least one product to delete.'\n            });\n        }\n    };\n    // Format date for display\n    const formatDate = (dateString)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_3___default()(dateString).format(\"MMM D, YYYY\");\n    };\n    // Format currency for display\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-GH', {\n            style: 'currency',\n            currency: 'GHS'\n        }).format(parseFloat(amount));\n    };\n    // Check if user can edit/delete (superadmin can edit all, others only their own)\n    const canEditDelete = (product)=>{\n        if (userRole === \"superadmin\") return true;\n        if (userRole === \"admin\" && (user === null || user === void 0 ? void 0 : user.id) === product.createdBy) return true;\n        return false;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-hidden bg-white\",\n        children: [\n            selectedProducts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 bg-gray-100 border-b flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: [\n                            selectedProducts.length,\n                            \" \",\n                            selectedProducts.length === 1 ? 'product' : 'products',\n                            \" selected\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        type: \"primary\",\n                        danger: true,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: handleBulkDelete,\n                        className: \"ml-2\",\n                        children: \"Delete Selected\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.ResponsiveTableGrid, {\n                columns: isMobile ? \"50px 200px 100px 80px 120px 150px\" : \"50px 200px 100px 80px 100px 120px 150px\",\n                minWidth: isMobile ? \"800px\" : \"1000px\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            checked: selectAll,\n                            onChange: handleSelectAllChange,\n                            disabled: products.filter((product)=>canEditDelete(product)).length === 0\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        sticky: isMobile ? undefined : \"left\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Name\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Price\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Stock\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, undefined),\n                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"SKU\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Created At\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        sticky: isMobile ? undefined : \"right\",\n                        className: \"text-right\",\n                        children: \"Actions\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined),\n                    products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableRow, {\n                            selected: selectedProducts.includes(product.id),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    className: \"text-center\",\n                                    children: canEditDelete(product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        checked: selectedProducts.includes(product.id),\n                                        onChange: (e)=>handleCheckboxChange(product.id, e.target.checked)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    sticky: isMobile ? undefined : \"left\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-[180px] overflow-hidden text-ellipsis font-medium\",\n                                        children: product.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-green-600\",\n                                        children: formatCurrency(product.price)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full \".concat(product.stockQuantity <= 0 ? 'bg-red-500 text-white' : product.stockQuantity <= (product.minStockLevel || 5) ? 'bg-yellow-500 text-white' : 'bg-green-500 text-white'),\n                                        children: product.stockQuantity\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, undefined),\n                                !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-xs\",\n                                        children: product.sku || 'N/A'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: formatDate(product.createdAt)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    sticky: isMobile ? undefined : \"right\",\n                                    className: \"text-right\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                title: \"View\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    onClick: ()=>onView(product.id),\n                                                    type: \"text\",\n                                                    className: \"view-button text-green-500 hover:text-green-400\",\n                                                    size: isMobile ? \"small\" : \"middle\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            userRole === \"admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                title: \"Adjust Stock\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    onClick: ()=>onAdjustStock(product),\n                                                    type: \"text\",\n                                                    className: \"adjust-stock-button text-purple-500 hover:text-purple-400\",\n                                                    size: isMobile ? \"small\" : \"middle\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            canEditDelete(product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        title: \"Edit\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            onClick: ()=>onEdit(product),\n                                                            type: \"text\",\n                                                            className: \"edit-button text-blue-500 hover:text-blue-400\",\n                                                            size: isMobile ? \"small\" : \"middle\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        title: \"Delete\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            onClick: ()=>onDelete(product.id),\n                                                            type: \"text\",\n                                                            className: \"delete-button text-red-500 hover:text-red-400\",\n                                                            size: isMobile ? \"small\" : \"middle\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, product.id, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductTable, \"QVOA1Pi+5rHhJmGQeX8BO2tvPjo=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_4__.useSelector\n    ];\n});\n_c = ProductTable;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductTable);\nvar _c;\n$RefreshReg$(_c, \"ProductTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Products/ProductTable.tsx\n"));

/***/ })

});