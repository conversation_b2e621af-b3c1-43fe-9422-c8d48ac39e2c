{"version": 3, "sources": ["../../../../src/server/web/spec-extension/request.ts"], "sourcesContent": ["import type { I18NConfig } from '../../config-shared'\nimport { NextURL } from '../next-url'\nimport { toNodeOutgoingHttpHeaders, validateURL } from '../utils'\nimport { RemovedUAError, RemovedPageError } from '../error'\nimport { RequestCookies } from './cookies'\n\nexport const INTERNALS = Symbol('internal request')\n\n/**\n * This class extends the [Web `Request` API](https://developer.mozilla.org/docs/Web/API/Request) with additional convenience methods.\n *\n * Read more: [Next.js Docs: `NextRequest`](https://nextjs.org/docs/app/api-reference/functions/next-request)\n */\nexport class NextRequest extends Request {\n  [INTERNALS]: {\n    cookies: RequestCookies\n    url: string\n    nextUrl: NextURL\n  }\n\n  constructor(input: URL | RequestInfo, init: RequestInit = {}) {\n    const url =\n      typeof input !== 'string' && 'url' in input ? input.url : String(input)\n    validateURL(url)\n    if (input instanceof Request) super(input, init)\n    else super(url, init)\n    const nextUrl = new NextURL(url, {\n      headers: toNodeOutgoingHttpHeaders(this.headers),\n      nextConfig: init.nextConfig,\n    })\n    this[INTERNALS] = {\n      cookies: new RequestCookies(this.headers),\n      nextUrl,\n      url: process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE\n        ? url\n        : nextUrl.toString(),\n    }\n  }\n\n  [Symbol.for('edge-runtime.inspect.custom')]() {\n    return {\n      cookies: this.cookies,\n      nextUrl: this.nextUrl,\n      url: this.url,\n      // rest of props come from Request\n      bodyUsed: this.bodyUsed,\n      cache: this.cache,\n      credentials: this.credentials,\n      destination: this.destination,\n      headers: Object.fromEntries(this.headers),\n      integrity: this.integrity,\n      keepalive: this.keepalive,\n      method: this.method,\n      mode: this.mode,\n      redirect: this.redirect,\n      referrer: this.referrer,\n      referrerPolicy: this.referrerPolicy,\n      signal: this.signal,\n    }\n  }\n\n  public get cookies() {\n    return this[INTERNALS].cookies\n  }\n\n  public get nextUrl() {\n    return this[INTERNALS].nextUrl\n  }\n\n  /**\n   * @deprecated\n   * `page` has been deprecated in favour of `URLPattern`.\n   * Read more: https://nextjs.org/docs/messages/middleware-request-page\n   */\n  public get page() {\n    throw new RemovedPageError()\n  }\n\n  /**\n   * @deprecated\n   * `ua` has been removed in favour of \\`userAgent\\` function.\n   * Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent\n   */\n  public get ua() {\n    throw new RemovedUAError()\n  }\n\n  public get url() {\n    return this[INTERNALS].url\n  }\n}\n\nexport interface RequestInit extends globalThis.RequestInit {\n  nextConfig?: {\n    basePath?: string\n    i18n?: I18NConfig | null\n    trailingSlash?: boolean\n  }\n  signal?: AbortSignal\n}\n"], "names": ["NextURL", "toNodeOutgoingHttpHeaders", "validateURL", "RemovedUAError", "RemovedPageError", "RequestCookies", "INTERNALS", "Symbol", "NextRequest", "Request", "constructor", "input", "init", "url", "String", "nextUrl", "headers", "nextConfig", "cookies", "process", "env", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "toString", "for", "bodyUsed", "cache", "credentials", "destination", "Object", "fromEntries", "integrity", "keepalive", "method", "mode", "redirect", "referrer", "referrerPolicy", "signal", "page", "ua"], "mappings": "AACA,SAASA,OAAO,QAAQ,cAAa;AACrC,SAASC,yBAAyB,EAAEC,WAAW,QAAQ,WAAU;AACjE,SAASC,cAAc,EAAEC,gBAAgB,QAAQ,WAAU;AAC3D,SAASC,cAAc,QAAQ,YAAW;AAE1C,OAAO,MAAMC,YAAYC,OAAO,oBAAmB;AAEnD;;;;CAIC,GACD,OAAO,MAAMC,oBAAoBC;IAO/BC,YAAYC,KAAwB,EAAEC,OAAoB,CAAC,CAAC,CAAE;QAC5D,MAAMC,MACJ,OAAOF,UAAU,YAAY,SAASA,QAAQA,MAAME,GAAG,GAAGC,OAAOH;QACnET,YAAYW;QACZ,IAAIF,iBAAiBF,SAAS,KAAK,CAACE,OAAOC;aACtC,KAAK,CAACC,KAAKD;QAChB,MAAMG,UAAU,IAAIf,QAAQa,KAAK;YAC/BG,SAASf,0BAA0B,IAAI,CAACe,OAAO;YAC/CC,YAAYL,KAAKK,UAAU;QAC7B;QACA,IAAI,CAACX,UAAU,GAAG;YAChBY,SAAS,IAAIb,eAAe,IAAI,CAACW,OAAO;YACxCD;YACAF,KAAKM,QAAQC,GAAG,CAACC,kCAAkC,GAC/CR,MACAE,QAAQO,QAAQ;QACtB;IACF;IAEA,CAACf,OAAOgB,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO;YACLL,SAAS,IAAI,CAACA,OAAO;YACrBH,SAAS,IAAI,CAACA,OAAO;YACrBF,KAAK,IAAI,CAACA,GAAG;YACb,kCAAkC;YAClCW,UAAU,IAAI,CAACA,QAAQ;YACvBC,OAAO,IAAI,CAACA,KAAK;YACjBC,aAAa,IAAI,CAACA,WAAW;YAC7BC,aAAa,IAAI,CAACA,WAAW;YAC7BX,SAASY,OAAOC,WAAW,CAAC,IAAI,CAACb,OAAO;YACxCc,WAAW,IAAI,CAACA,SAAS;YACzBC,WAAW,IAAI,CAACA,SAAS;YACzBC,QAAQ,IAAI,CAACA,MAAM;YACnBC,MAAM,IAAI,CAACA,IAAI;YACfC,UAAU,IAAI,CAACA,QAAQ;YACvBC,UAAU,IAAI,CAACA,QAAQ;YACvBC,gBAAgB,IAAI,CAACA,cAAc;YACnCC,QAAQ,IAAI,CAACA,MAAM;QACrB;IACF;IAEA,IAAWnB,UAAU;QACnB,OAAO,IAAI,CAACZ,UAAU,CAACY,OAAO;IAChC;IAEA,IAAWH,UAAU;QACnB,OAAO,IAAI,CAACT,UAAU,CAACS,OAAO;IAChC;IAEA;;;;GAIC,GACD,IAAWuB,OAAO;QAChB,MAAM,IAAIlC;IACZ;IAEA;;;;GAIC,GACD,IAAWmC,KAAK;QACd,MAAM,IAAIpC;IACZ;IAEA,IAAWU,MAAM;QACf,OAAO,IAAI,CAACP,UAAU,CAACO,GAAG;IAC5B;AACF"}