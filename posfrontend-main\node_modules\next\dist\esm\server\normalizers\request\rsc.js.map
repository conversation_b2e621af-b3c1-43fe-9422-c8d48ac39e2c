{"version": 3, "sources": ["../../../../src/server/normalizers/request/rsc.ts"], "sourcesContent": ["import type { PathnameNormalizer } from './pathname-normalizer'\n\nimport { RSC_SUFFIX } from '../../../lib/constants'\nimport { SuffixPathnameNormalizer } from './suffix'\n\nexport class RSCPathnameNormalizer\n  extends SuffixPathnameNormalizer\n  implements PathnameNormalizer\n{\n  constructor() {\n    super(RSC_SUFFIX)\n  }\n}\n"], "names": ["RSC_SUFFIX", "SuffixPathnameNormalizer", "RSCPathnameNormalizer", "constructor"], "mappings": "AAEA,SAASA,UAAU,QAAQ,yBAAwB;AACnD,SAASC,wBAAwB,QAAQ,WAAU;AAEnD,OAAO,MAAMC,8BACHD;IAGRE,aAAc;QACZ,KAAK,CAACH;IACR;AACF"}