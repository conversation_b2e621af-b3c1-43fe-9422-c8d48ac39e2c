{"version": 3, "sources": ["../../src/lib/constants.ts"], "sourcesContent": ["import type { ServerRuntime } from '../types'\n\nexport const NEXT_QUERY_PARAM_PREFIX = 'nxtP'\nexport const NEXT_INTERCEPTION_MARKER_PREFIX = 'nxtI'\n\nexport const MATCHED_PATH_HEADER = 'x-matched-path'\nexport const PRERENDER_REVALIDATE_HEADER = 'x-prerender-revalidate'\nexport const PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER =\n  'x-prerender-revalidate-if-generated'\n\nexport const RSC_PREFETCH_SUFFIX = '.prefetch.rsc'\nexport const RSC_SEGMENTS_DIR_SUFFIX = '.segments'\nexport const RSC_SEGMENT_SUFFIX = '.segment.rsc'\nexport const RSC_SUFFIX = '.rsc'\nexport const ACTION_SUFFIX = '.action'\nexport const NEXT_DATA_SUFFIX = '.json'\nexport const NEXT_META_SUFFIX = '.meta'\nexport const NEXT_BODY_SUFFIX = '.body'\n\nexport const NEXT_CACHE_TAGS_HEADER = 'x-next-cache-tags'\nexport const NEXT_CACHE_SOFT_TAGS_HEADER = 'x-next-cache-soft-tags'\nexport const NEXT_CACHE_REVALIDATED_TAGS_HEADER = 'x-next-revalidated-tags'\nexport const NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER =\n  'x-next-revalidate-tag-token'\n\nexport const NEXT_RESUME_HEADER = 'next-resume'\n\n// if these change make sure we update the related\n// documentation as well\nexport const NEXT_CACHE_TAG_MAX_ITEMS = 128\nexport const NEXT_CACHE_TAG_MAX_LENGTH = 256\nexport const NEXT_CACHE_SOFT_TAG_MAX_LENGTH = 1024\nexport const NEXT_CACHE_IMPLICIT_TAG_ID = '_N_T_'\n\n// in seconds\nexport const CACHE_ONE_YEAR = 31536000\n\n// in seconds, represents revalidate=false. I.e. never revaliate.\n// We use this value since it can be represented as a V8 SMI for optimal performance.\n// It can also be serialized as JSON if it ever leaks accidentally as an actual value.\nexport const INFINITE_CACHE = 0xfffffffe\n\n// Patterns to detect middleware files\nexport const MIDDLEWARE_FILENAME = 'middleware'\nexport const MIDDLEWARE_LOCATION_REGEXP = `(?:src/)?${MIDDLEWARE_FILENAME}`\n\n// Pattern to detect instrumentation hooks file\nexport const INSTRUMENTATION_HOOK_FILENAME = 'instrumentation'\n\n// Because on Windows absolute paths in the generated code can break because of numbers, eg 1 in the path,\n// we have to use a private alias\nexport const PAGES_DIR_ALIAS = 'private-next-pages'\nexport const DOT_NEXT_ALIAS = 'private-dot-next'\nexport const ROOT_DIR_ALIAS = 'private-next-root-dir'\nexport const APP_DIR_ALIAS = 'private-next-app-dir'\nexport const RSC_MOD_REF_PROXY_ALIAS = 'private-next-rsc-mod-ref-proxy'\nexport const RSC_ACTION_VALIDATE_ALIAS = 'private-next-rsc-action-validate'\nexport const RSC_ACTION_PROXY_ALIAS = 'private-next-rsc-server-reference'\nexport const RSC_CACHE_WRAPPER_ALIAS = 'private-next-rsc-cache-wrapper'\nexport const RSC_ACTION_ENCRYPTION_ALIAS = 'private-next-rsc-action-encryption'\nexport const RSC_ACTION_CLIENT_WRAPPER_ALIAS =\n  'private-next-rsc-action-client-wrapper'\n\nexport const PUBLIC_DIR_MIDDLEWARE_CONFLICT = `You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict`\n\nexport const SSG_GET_INITIAL_PROPS_CONFLICT = `You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps`\n\nexport const SERVER_PROPS_GET_INIT_PROPS_CONFLICT = `You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.`\n\nexport const SERVER_PROPS_SSG_CONFLICT = `You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps`\n\nexport const STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR = `can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props`\n\nexport const SERVER_PROPS_EXPORT_ERROR = `pages with \\`getServerSideProps\\` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export`\n\nexport const GSP_NO_RETURNED_VALUE =\n  'Your `getStaticProps` function did not return an object. Did you forget to add a `return`?'\nexport const GSSP_NO_RETURNED_VALUE =\n  'Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?'\n\nexport const UNSTABLE_REVALIDATE_RENAME_ERROR =\n  'The `unstable_revalidate` property is available for general use.\\n' +\n  'Please use `revalidate` instead.'\n\nexport const GSSP_COMPONENT_MEMBER_ERROR = `can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member`\n\nexport const NON_STANDARD_NODE_ENV = `You are using a non-standard \"NODE_ENV\" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env`\n\nexport const SSG_FALLBACK_EXPORT_ERROR = `Pages with \\`fallback\\` enabled in \\`getStaticPaths\\` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export`\n\nexport const ESLINT_DEFAULT_DIRS = ['app', 'pages', 'components', 'lib', 'src']\n\nexport const SERVER_RUNTIME: Record<string, ServerRuntime> = {\n  edge: 'edge',\n  experimentalEdge: 'experimental-edge',\n  nodejs: 'nodejs',\n}\n\n/**\n * The names of the webpack layers. These layers are the primitives for the\n * webpack chunks.\n */\nconst WEBPACK_LAYERS_NAMES = {\n  /**\n   * The layer for the shared code between the client and server bundles.\n   */\n  shared: 'shared',\n  /**\n   * The layer for server-only runtime and picking up `react-server` export conditions.\n   * Including app router RSC pages and app router custom routes and metadata routes.\n   */\n  reactServerComponents: 'rsc',\n  /**\n   * Server Side Rendering layer for app (ssr).\n   */\n  serverSideRendering: 'ssr',\n  /**\n   * The browser client bundle layer for actions.\n   */\n  actionBrowser: 'action-browser',\n  /**\n   * The layer for the API routes.\n   */\n  api: 'api',\n  /**\n   * The layer for the middleware code.\n   */\n  middleware: 'middleware',\n  /**\n   * The layer for the instrumentation hooks.\n   */\n  instrument: 'instrument',\n  /**\n   * The layer for assets on the edge.\n   */\n  edgeAsset: 'edge-asset',\n  /**\n   * The browser client bundle layer for App directory.\n   */\n  appPagesBrowser: 'app-pages-browser',\n} as const\n\nexport type WebpackLayerName =\n  (typeof WEBPACK_LAYERS_NAMES)[keyof typeof WEBPACK_LAYERS_NAMES]\n\nconst WEBPACK_LAYERS = {\n  ...WEBPACK_LAYERS_NAMES,\n  GROUP: {\n    builtinReact: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n    serverOnly: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    neutralTarget: [\n      // pages api\n      WEBPACK_LAYERS_NAMES.api,\n    ],\n    clientOnly: [\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n    ],\n    bundled: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.shared,\n      WEBPACK_LAYERS_NAMES.instrument,\n    ],\n    appPages: [\n      // app router pages and layouts\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n  },\n}\n\nconst WEBPACK_RESOURCE_QUERIES = {\n  edgeSSREntry: '__next_edge_ssr_entry__',\n  metadata: '__next_metadata__',\n  metadataRoute: '__next_metadata_route__',\n  metadataImageMeta: '__next_metadata_image_meta__',\n}\n\nexport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES }\n"], "names": ["ACTION_SUFFIX", "APP_DIR_ALIAS", "CACHE_ONE_YEAR", "DOT_NEXT_ALIAS", "ESLINT_DEFAULT_DIRS", "GSP_NO_RETURNED_VALUE", "GSSP_COMPONENT_MEMBER_ERROR", "GSSP_NO_RETURNED_VALUE", "INFINITE_CACHE", "INSTRUMENTATION_HOOK_FILENAME", "MATCHED_PATH_HEADER", "MIDDLEWARE_FILENAME", "MIDDLEWARE_LOCATION_REGEXP", "NEXT_BODY_SUFFIX", "NEXT_CACHE_IMPLICIT_TAG_ID", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "NEXT_CACHE_SOFT_TAGS_HEADER", "NEXT_CACHE_SOFT_TAG_MAX_LENGTH", "NEXT_CACHE_TAGS_HEADER", "NEXT_CACHE_TAG_MAX_ITEMS", "NEXT_CACHE_TAG_MAX_LENGTH", "NEXT_DATA_SUFFIX", "NEXT_INTERCEPTION_MARKER_PREFIX", "NEXT_META_SUFFIX", "NEXT_QUERY_PARAM_PREFIX", "NEXT_RESUME_HEADER", "NON_STANDARD_NODE_ENV", "PAGES_DIR_ALIAS", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "ROOT_DIR_ALIAS", "RSC_ACTION_CLIENT_WRAPPER_ALIAS", "RSC_ACTION_ENCRYPTION_ALIAS", "RSC_ACTION_PROXY_ALIAS", "RSC_ACTION_VALIDATE_ALIAS", "RSC_CACHE_WRAPPER_ALIAS", "RSC_MOD_REF_PROXY_ALIAS", "RSC_PREFETCH_SUFFIX", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SEGMENT_SUFFIX", "RSC_SUFFIX", "SERVER_PROPS_EXPORT_ERROR", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "SERVER_RUNTIME", "SSG_FALLBACK_EXPORT_ERROR", "SSG_GET_INITIAL_PROPS_CONFLICT", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "UNSTABLE_REVALIDATE_RENAME_ERROR", "WEBPACK_LAYERS", "WEBPACK_RESOURCE_QUERIES", "edge", "experimentalEdge", "nodejs", "WEBPACK_LAYERS_NAMES", "shared", "reactServerComponents", "serverSideRendering", "<PERSON><PERSON><PERSON><PERSON>", "api", "middleware", "instrument", "edgeAsset", "appPagesBrowser", "GROUP", "builtinReact", "serverOnly", "neutralTarget", "clientOnly", "bundled", "appPages", "edgeSSREntry", "metadata", "metadataRoute", "metadataImageMeta"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAcaA,aAAa;eAAbA;;IAwCAC,aAAa;eAAbA;;IAnBAC,cAAc;eAAdA;;IAiBAC,cAAc;eAAdA;;IAsCAC,mBAAmB;eAAnBA;;IAfAC,qBAAqB;eAArBA;;IASAC,2BAA2B;eAA3BA;;IAPAC,sBAAsB;eAAtBA;;IArCAC,cAAc;eAAdA;;IAOAC,6BAA6B;eAA7BA;;IA1CAC,mBAAmB;eAAnBA;;IAsCAC,mBAAmB;eAAnBA;;IACAC,0BAA0B;eAA1BA;;IA3BAC,gBAAgB;eAAhBA;;IAeAC,0BAA0B;eAA1BA;;IAXAC,kCAAkC;eAAlCA;;IACAC,sCAAsC;eAAtCA;;IAFAC,2BAA2B;eAA3BA;;IAWAC,8BAA8B;eAA9BA;;IAZAC,sBAAsB;eAAtBA;;IAUAC,wBAAwB;eAAxBA;;IACAC,yBAAyB;eAAzBA;;IAfAC,gBAAgB;eAAhBA;;IAZAC,+BAA+B;eAA/BA;;IAaAC,gBAAgB;eAAhBA;;IAdAC,uBAAuB;eAAvBA;;IAuBAC,kBAAkB;eAAlBA;;IA6DAC,qBAAqB;eAArBA;;IAnCAC,eAAe;eAAfA;;IA7CAC,2BAA2B;eAA3BA;;IACAC,0CAA0C;eAA1CA;;IAwDAC,8BAA8B;eAA9BA;;IAVAC,cAAc;eAAdA;;IAOAC,+BAA+B;eAA/BA;;IADAC,2BAA2B;eAA3BA;;IAFAC,sBAAsB;eAAtBA;;IADAC,yBAAyB;eAAzBA;;IAEAC,uBAAuB;eAAvBA;;IAHAC,uBAAuB;eAAvBA;;IA7CAC,mBAAmB;eAAnBA;;IACAC,uBAAuB;eAAvBA;;IACAC,kBAAkB;eAAlBA;;IACAC,UAAU;eAAVA;;IA4DAC,yBAAyB;eAAzBA;;IANAC,oCAAoC;eAApCA;;IAEAC,yBAAyB;eAAzBA;;IAuBAC,cAAc;eAAdA;;IAJAC,yBAAyB;eAAzBA;;IAvBAC,8BAA8B;eAA9BA;;IAMAC,0CAA0C;eAA1CA;;IASAC,gCAAgC;eAAhCA;;IA+GJC,cAAc;eAAdA;;IAAgBC,wBAAwB;eAAxBA;;;AA7LlB,MAAM3B,0BAA0B;AAChC,MAAMF,kCAAkC;AAExC,MAAMb,sBAAsB;AAC5B,MAAMmB,8BAA8B;AACpC,MAAMC,6CACX;AAEK,MAAMS,sBAAsB;AAC5B,MAAMC,0BAA0B;AAChC,MAAMC,qBAAqB;AAC3B,MAAMC,aAAa;AACnB,MAAM1C,gBAAgB;AACtB,MAAMsB,mBAAmB;AACzB,MAAME,mBAAmB;AACzB,MAAMX,mBAAmB;AAEzB,MAAMM,yBAAyB;AAC/B,MAAMF,8BAA8B;AACpC,MAAMF,qCAAqC;AAC3C,MAAMC,yCACX;AAEK,MAAMU,qBAAqB;AAI3B,MAAMN,2BAA2B;AACjC,MAAMC,4BAA4B;AAClC,MAAMH,iCAAiC;AACvC,MAAMJ,6BAA6B;AAGnC,MAAMZ,iBAAiB;AAKvB,MAAMM,iBAAiB;AAGvB,MAAMG,sBAAsB;AAC5B,MAAMC,6BAA6B,CAAC,SAAS,EAAED,qBAAqB;AAGpE,MAAMF,gCAAgC;AAItC,MAAMmB,kBAAkB;AACxB,MAAMzB,iBAAiB;AACvB,MAAM6B,iBAAiB;AACvB,MAAM/B,gBAAgB;AACtB,MAAMqC,0BAA0B;AAChC,MAAMF,4BAA4B;AAClC,MAAMD,yBAAyB;AAC/B,MAAME,0BAA0B;AAChC,MAAMH,8BAA8B;AACpC,MAAMD,kCACX;AAEK,MAAMF,iCAAiC,CAAC,6KAA6K,CAAC;AAEtN,MAAMiB,iCAAiC,CAAC,mGAAmG,CAAC;AAE5I,MAAMJ,uCAAuC,CAAC,uFAAuF,CAAC;AAEtI,MAAMC,4BAA4B,CAAC,sHAAsH,CAAC;AAE1J,MAAMI,6CAA6C,CAAC,uGAAuG,CAAC;AAE5J,MAAMN,4BAA4B,CAAC,uHAAuH,CAAC;AAE3J,MAAMtC,wBACX;AACK,MAAME,yBACX;AAEK,MAAM2C,mCACX,uEACA;AAEK,MAAM5C,8BAA8B,CAAC,wJAAwJ,CAAC;AAE9L,MAAMqB,wBAAwB,CAAC,iNAAiN,CAAC;AAEjP,MAAMoB,4BAA4B,CAAC,wJAAwJ,CAAC;AAE5L,MAAM3C,sBAAsB;IAAC;IAAO;IAAS;IAAc;IAAO;CAAM;AAExE,MAAM0C,iBAAgD;IAC3DO,MAAM;IACNC,kBAAkB;IAClBC,QAAQ;AACV;AAEA;;;CAGC,GACD,MAAMC,uBAAuB;IAC3B;;GAEC,GACDC,QAAQ;IACR;;;GAGC,GACDC,uBAAuB;IACvB;;GAEC,GACDC,qBAAqB;IACrB;;GAEC,GACDC,eAAe;IACf;;GAEC,GACDC,KAAK;IACL;;GAEC,GACDC,YAAY;IACZ;;GAEC,GACDC,YAAY;IACZ;;GAEC,GACDC,WAAW;IACX;;GAEC,GACDC,iBAAiB;AACnB;AAKA,MAAMd,iBAAiB;IACrB,GAAGK,oBAAoB;IACvBU,OAAO;QACLC,cAAc;YACZX,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;SACnC;QACDQ,YAAY;YACVZ,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;YAClCJ,qBAAqBO,UAAU;YAC/BP,qBAAqBM,UAAU;SAChC;QACDO,eAAe;YACb,YAAY;YACZb,qBAAqBK,GAAG;SACzB;QACDS,YAAY;YACVd,qBAAqBG,mBAAmB;YACxCH,qBAAqBS,eAAe;SACrC;QACDM,SAAS;YACPf,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;YAClCJ,qBAAqBG,mBAAmB;YACxCH,qBAAqBS,eAAe;YACpCT,qBAAqBC,MAAM;YAC3BD,qBAAqBO,UAAU;SAChC;QACDS,UAAU;YACR,+BAA+B;YAC/BhB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBG,mBAAmB;YACxCH,qBAAqBS,eAAe;YACpCT,qBAAqBI,aAAa;SACnC;IACH;AACF;AAEA,MAAMR,2BAA2B;IAC/BqB,cAAc;IACdC,UAAU;IACVC,eAAe;IACfC,mBAAmB;AACrB"}