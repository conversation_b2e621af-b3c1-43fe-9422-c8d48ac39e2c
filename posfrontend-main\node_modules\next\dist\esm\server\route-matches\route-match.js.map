{"version": 3, "sources": ["../../../src/server/route-matches/route-match.ts"], "sourcesContent": ["import type { Params } from '../request/params'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\n\n/**\n * RouteMatch is the resolved match for a given request. This will contain all\n * the dynamic parameters used for this route.\n */\nexport interface RouteMatch<D extends RouteDefinition = RouteDefinition> {\n  readonly definition: D\n\n  /**\n   * params when provided are the dynamic route parameters that were parsed from\n   * the incoming request pathname. If a route match is returned without any\n   * params, it should be considered a static route.\n   */\n  readonly params: Params | undefined\n}\n"], "names": [], "mappings": "AAGA;;;CAGC,GACD,WASC"}