/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/components/Suppliers/supplier-panels.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/* Supplier Form Styles */
.supplier-form .ant-form-item-label > label {
  color: #333 !important;
}

.supplier-form .ant-form-item-explain-error {
  color: #ff4d4f;
  margin-top: 2px;
}

.supplier-form .ant-input,
.supplier-form .ant-input-number,
.supplier-form .ant-select-selector,
.supplier-form .ant-picker {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
  color: #333 !important;
}

.supplier-form .ant-input:hover,
.supplier-form .ant-input-number:hover,
.supplier-form .ant-select-selector:hover,
.supplier-form .ant-picker:hover {
  border-color: #40a9ff !important;
}

/* Required field indicator styles */
.supplier-form .text-red-500 {
  color: #ff4d4f !important;
  font-weight: bold;
}

/* Optional field styles */
.supplier-form input::-moz-placeholder, .supplier-form textarea::-moz-placeholder {
  color: #bfbfbf;
  font-style: italic;
}
.supplier-form input::placeholder,
.supplier-form textarea::placeholder {
  color: #bfbfbf;
  font-style: italic;
}

.supplier-form .ant-input:focus,
.supplier-form .ant-input-number:focus,
.supplier-form .ant-select-selector:focus,
.supplier-form .ant-picker:focus,
.supplier-form .ant-input-focused,
.supplier-form .ant-input-number-focused,
.supplier-form .ant-select-focused .ant-select-selector,
.supplier-form .ant-picker-focused {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.supplier-form .ant-input-number-handler-wrap {
  background-color: #f5f5f5 !important;
}

.supplier-form .ant-input-number-handler:hover .ant-input-number-handler-up-inner,
.supplier-form .ant-input-number-handler:hover .ant-input-number-handler-down-inner {
  color: #40a9ff !important;
}

.supplier-form .ant-select-arrow,
.supplier-form .ant-picker-suffix {
  color: rgba(0, 0, 0, 0.25) !important;
}

.supplier-form .ant-form-item-required::before {
  color: #ff4d4f !important;
}

.supplier-form .ant-input::-moz-placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

.supplier-form .ant-input::placeholder,
.supplier-form .ant-select-selection-placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Button Styles */
.view-button {
  color: #10b981 !important; /* green-500 */
}

.view-button:hover {
  color: #34d399 !important; /* green-400 */
}

.edit-button {
  color: #3b82f6 !important; /* blue-500 */
}

.edit-button:hover {
  color: #60a5fa !important; /* blue-400 */
}

.delete-button {
  color: #ef4444 !important; /* red-500 */
}

.delete-button:hover {
  color: #f87171 !important; /* red-400 */
}

/* Detail Panel Styles */
.supplier-detail-light .ant-descriptions-item-label {
  background-color: #f5f5f5 !important;
  color: #333 !important;
  padding: 12px 16px !important;
  border-color: #e8e8e8 !important;
}

.supplier-detail-light .ant-descriptions-item-content {
  background-color: #ffffff !important;
  color: #333 !important;
  padding: 12px 16px !important;
  border-color: #e8e8e8 !important;
}

.supplier-detail-light .ant-descriptions-bordered {
  border-color: #e8e8e8 !important;
}

/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/search.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/* Custom styles for search inputs */

/* Light background for input and its wrapper */
.ant-input-affix-wrapper {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
}

/* Ensure hover and focus states maintain light background */
.ant-input-affix-wrapper:hover,
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
  background-color: #ffffff !important;
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* Style for the input inside the wrapper */
.ant-input-affix-wrapper .ant-input {
  background-color: transparent !important;
  color: rgba(0, 0, 0, 0.85) !important;
}

/* Style for the clear icon */
.ant-input-affix-wrapper .ant-input-clear-icon {
  color: rgba(0, 0, 0, 0.25) !important;
}

.ant-input-affix-wrapper .ant-input-clear-icon:hover {
  color: rgba(0, 0, 0, 0.45) !important;
}

/* Style for the placeholder */
.ant-input::-moz-placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}
.ant-input::placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Style for the search icon */
.ant-input-prefix .anticon {
  color: rgba(0, 0, 0, 0.25) !important;
}

