{"version": 3, "sources": ["../../src/lib/client-reference.ts"], "sourcesContent": ["export function isClientReference(mod: any): boolean {\n  const defaultExport = mod?.default || mod\n  return defaultExport?.$$typeof === Symbol.for('react.client.reference')\n}\n"], "names": ["isClientReference", "mod", "defaultExport", "default", "$$typeof", "Symbol", "for"], "mappings": ";;;;+BAAgBA;;;eAAAA;;;AAAT,SAASA,kBAAkBC,GAAQ;IACxC,MAAMC,gBAAgBD,CAAAA,uBAAAA,IAAKE,OAAO,KAAIF;IACtC,OAAOC,CAAAA,iCAAAA,cAAeE,QAAQ,MAAKC,OAAOC,GAAG,CAAC;AAChD"}