{"version": 3, "sources": ["../../../src/export/routes/pages.ts"], "sourcesContent": ["import type { ExportRouteResult, FileWriter } from '../types'\nimport type { RenderOpts } from '../../server/render'\nimport type { LoadComponentsReturnType } from '../../server/load-components'\nimport type { AmpValidation } from '../types'\nimport type { NextParsedUrlQuery } from '../../server/request-meta'\nimport type { Params } from '../../server/request/params'\n\nimport RenderResult from '../../server/render-result'\nimport { join } from 'path'\nimport type {\n  MockedRequest,\n  MockedResponse,\n} from '../../server/lib/mock-request'\nimport { isInAmpMode } from '../../shared/lib/amp-mode'\nimport {\n  NEXT_DATA_SUFFIX,\n  SERVER_PROPS_EXPORT_ERROR,\n} from '../../lib/constants'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport AmpHtmlValidator from 'next/dist/compiled/amphtml-validator'\nimport { FileType, fileExists } from '../../lib/file-exists'\nimport { lazyRenderPagesPage } from '../../server/route-modules/pages/module.render'\n\nexport const enum ExportedPagesFiles {\n  HTML = 'HTML',\n  DATA = 'DATA',\n  AMP_HTML = 'AMP_HTML',\n  AMP_DATA = 'AMP_PAGE_DATA',\n}\n\n/**\n * Renders & exports a page associated with the /pages directory\n */\nexport async function exportPagesPage(\n  req: MockedRequest,\n  res: MockedResponse,\n  path: string,\n  page: string,\n  query: NextParsedUrlQuery,\n  params: Params | undefined,\n  htmlFilepath: string,\n  htmlFilename: string,\n  ampPath: string,\n  subFolders: boolean,\n  outDir: string,\n  ampValidatorPath: string | undefined,\n  pagesDataDir: string,\n  buildExport: boolean,\n  isDynamic: boolean,\n  hasOrigQueryValues: boolean,\n  renderOpts: RenderOpts,\n  components: LoadComponentsReturnType,\n  fileWriter: FileWriter\n): Promise<ExportRouteResult | undefined> {\n  const ampState = {\n    ampFirst: components.pageConfig?.amp === true,\n    hasQuery: Boolean(query.amp),\n    hybrid: components.pageConfig?.amp === 'hybrid',\n  }\n\n  if (!ampValidatorPath) {\n    ampValidatorPath = require.resolve(\n      'next/dist/compiled/amphtml-validator/validator_wasm.js'\n    )\n  }\n\n  const inAmpMode = isInAmpMode(ampState)\n  const hybridAmp = ampState.hybrid\n\n  if (components.getServerSideProps) {\n    throw new Error(`Error for page ${page}: ${SERVER_PROPS_EXPORT_ERROR}`)\n  }\n\n  // for non-dynamic SSG pages we should have already\n  // prerendered the file\n  if (!buildExport && components.getStaticProps && !isDynamic) {\n    return\n  }\n\n  // Pages router merges page params (e.g. [lang]) with query params\n  // primarily to support them both being accessible on `useRouter().query`.\n  // If we extracted dynamic params from the path, we need to merge them\n  // back into the query object.\n  const searchAndDynamicParams = {\n    ...query,\n    ...params,\n  }\n\n  if (components.getStaticProps && !htmlFilepath.endsWith('.html')) {\n    // make sure it ends with .html if the name contains a dot\n    htmlFilepath += '.html'\n    htmlFilename += '.html'\n  }\n\n  let renderResult: RenderResult | undefined\n\n  if (typeof components.Component === 'string') {\n    renderResult = RenderResult.fromStatic(components.Component)\n\n    if (hasOrigQueryValues) {\n      throw new Error(\n        `\\nError: you provided query values for ${path} which is an auto-exported page. These can not be applied since the page can no longer be re-rendered on the server. To disable auto-export for this page add \\`getInitialProps\\`\\n`\n      )\n    }\n  } else {\n    /**\n     * This sets environment variable to be used at the time of SSR by head.tsx.\n     * Using this from process.env allows targeting SSR by calling\n     * `process.env.__NEXT_OPTIMIZE_CSS`.\n     */\n    if (renderOpts.optimizeCss) {\n      process.env.__NEXT_OPTIMIZE_CSS = JSON.stringify(true)\n    }\n    try {\n      renderResult = await lazyRenderPagesPage(\n        req,\n        res,\n        page,\n        searchAndDynamicParams,\n        renderOpts\n      )\n    } catch (err) {\n      if (!isBailoutToCSRError(err)) throw err\n    }\n  }\n\n  const ssgNotFound = renderResult?.metadata.isNotFound\n\n  const ampValidations: AmpValidation[] = []\n\n  const validateAmp = async (\n    rawAmpHtml: string,\n    ampPageName: string,\n    validatorPath: string | undefined\n  ) => {\n    const validator = await AmpHtmlValidator.getInstance(validatorPath)\n    const result = validator.validateString(rawAmpHtml)\n    const errors = result.errors.filter((e) => e.severity === 'ERROR')\n    const warnings = result.errors.filter((e) => e.severity !== 'ERROR')\n\n    if (warnings.length || errors.length) {\n      ampValidations.push({\n        page: ampPageName,\n        result: {\n          errors,\n          warnings,\n        },\n      })\n    }\n  }\n\n  const html =\n    renderResult && !renderResult.isNull ? renderResult.toUnchunkedString() : ''\n\n  let ampRenderResult: RenderResult | undefined\n\n  if (inAmpMode && !renderOpts.ampSkipValidation) {\n    if (!ssgNotFound) {\n      await validateAmp(html, path, ampValidatorPath)\n    }\n  } else if (hybridAmp) {\n    const ampHtmlFilename = subFolders\n      ? join(ampPath, 'index.html')\n      : `${ampPath}.html`\n\n    const ampHtmlFilepath = join(outDir, ampHtmlFilename)\n\n    const exists = await fileExists(ampHtmlFilepath, FileType.File)\n    if (!exists) {\n      try {\n        ampRenderResult = await lazyRenderPagesPage(\n          req,\n          res,\n          page,\n          { ...searchAndDynamicParams, amp: '1' },\n          renderOpts\n        )\n      } catch (err) {\n        if (!isBailoutToCSRError(err)) throw err\n      }\n\n      const ampHtml =\n        ampRenderResult && !ampRenderResult.isNull\n          ? ampRenderResult.toUnchunkedString()\n          : ''\n      if (!renderOpts.ampSkipValidation) {\n        await validateAmp(ampHtml, page + '?amp=1', ampValidatorPath)\n      }\n\n      await fileWriter(\n        ExportedPagesFiles.AMP_HTML,\n        ampHtmlFilepath,\n        ampHtml,\n        'utf8'\n      )\n    }\n  }\n\n  const metadata = renderResult?.metadata || ampRenderResult?.metadata || {}\n  if (metadata.pageData) {\n    const dataFile = join(\n      pagesDataDir,\n      htmlFilename.replace(/\\.html$/, NEXT_DATA_SUFFIX)\n    )\n\n    await fileWriter(\n      ExportedPagesFiles.DATA,\n      dataFile,\n      JSON.stringify(metadata.pageData),\n      'utf8'\n    )\n\n    if (hybridAmp) {\n      await fileWriter(\n        ExportedPagesFiles.AMP_DATA,\n        dataFile.replace(/\\.json$/, '.amp.json'),\n        JSON.stringify(metadata.pageData),\n        'utf8'\n      )\n    }\n  }\n\n  if (!ssgNotFound) {\n    // don't attempt writing to disk if getStaticProps returned not found\n    await fileWriter(ExportedPagesFiles.HTML, htmlFilepath, html, 'utf8')\n  }\n\n  return {\n    ampValidations,\n    revalidate: metadata.revalidate ?? false,\n    ssgNotFound,\n  }\n}\n"], "names": ["ExportedPagesFiles", "exportPagesPage", "req", "res", "path", "page", "query", "params", "htmlFilepath", "htmlFilename", "ampPath", "subFolders", "outDir", "ampValidator<PERSON>ath", "pagesDataDir", "buildExport", "isDynamic", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderOpts", "components", "fileWriter", "ampState", "ampFirs<PERSON>", "pageConfig", "amp", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "hybrid", "require", "resolve", "inAmpMode", "isInAmpMode", "hybridAmp", "getServerSideProps", "Error", "SERVER_PROPS_EXPORT_ERROR", "getStaticProps", "searchAndDynamicParams", "endsWith", "renderResult", "Component", "RenderResult", "fromStatic", "optimizeCss", "process", "env", "__NEXT_OPTIMIZE_CSS", "JSON", "stringify", "lazyRenderPagesPage", "err", "isBailoutToCSRError", "ssgNotFound", "metadata", "isNotFound", "ampValidations", "validateAmp", "rawAmpHtml", "ampPageName", "validatorPath", "validator", "AmpHtmlValidator", "getInstance", "result", "validateString", "errors", "filter", "e", "severity", "warnings", "length", "push", "html", "isNull", "toUnchunkedString", "ampRenderResult", "ampSkipValidation", "ampHtmlFilename", "join", "ampHtmlFilepath", "exists", "fileExists", "FileType", "File", "ampHtml", "pageData", "dataFile", "replace", "NEXT_DATA_SUFFIX", "revalidate"], "mappings": ";;;;;;;;;;;;;;;IAuBkBA,kBAAkB;eAAlBA;;IAUIC,eAAe;eAAfA;;;qEA1BG;sBACJ;yBAKO;2BAIrB;8BAC6B;yEACP;4BACQ;8BACD;;;;;;AAE7B,IAAA,AAAWD,4CAAAA;;;;;WAAAA;;AAUX,eAAeC,gBACpBC,GAAkB,EAClBC,GAAmB,EACnBC,IAAY,EACZC,IAAY,EACZC,KAAyB,EACzBC,MAA0B,EAC1BC,YAAoB,EACpBC,YAAoB,EACpBC,OAAe,EACfC,UAAmB,EACnBC,MAAc,EACdC,gBAAoC,EACpCC,YAAoB,EACpBC,WAAoB,EACpBC,SAAkB,EAClBC,kBAA2B,EAC3BC,UAAsB,EACtBC,UAAoC,EACpCC,UAAsB;QAGVD,wBAEFA;IAHV,MAAME,WAAW;QACfC,UAAUH,EAAAA,yBAAAA,WAAWI,UAAU,qBAArBJ,uBAAuBK,GAAG,MAAK;QACzCC,UAAUC,QAAQpB,MAAMkB,GAAG;QAC3BG,QAAQR,EAAAA,0BAAAA,WAAWI,UAAU,qBAArBJ,wBAAuBK,GAAG,MAAK;IACzC;IAEA,IAAI,CAACX,kBAAkB;QACrBA,mBAAmBe,QAAQC,OAAO,CAChC;IAEJ;IAEA,MAAMC,YAAYC,IAAAA,oBAAW,EAACV;IAC9B,MAAMW,YAAYX,SAASM,MAAM;IAEjC,IAAIR,WAAWc,kBAAkB,EAAE;QACjC,MAAM,IAAIC,MAAM,CAAC,eAAe,EAAE7B,KAAK,EAAE,EAAE8B,oCAAyB,EAAE;IACxE;IAEA,mDAAmD;IACnD,uBAAuB;IACvB,IAAI,CAACpB,eAAeI,WAAWiB,cAAc,IAAI,CAACpB,WAAW;QAC3D;IACF;IAEA,kEAAkE;IAClE,0EAA0E;IAC1E,sEAAsE;IACtE,8BAA8B;IAC9B,MAAMqB,yBAAyB;QAC7B,GAAG/B,KAAK;QACR,GAAGC,MAAM;IACX;IAEA,IAAIY,WAAWiB,cAAc,IAAI,CAAC5B,aAAa8B,QAAQ,CAAC,UAAU;QAChE,0DAA0D;QAC1D9B,gBAAgB;QAChBC,gBAAgB;IAClB;IAEA,IAAI8B;IAEJ,IAAI,OAAOpB,WAAWqB,SAAS,KAAK,UAAU;QAC5CD,eAAeE,qBAAY,CAACC,UAAU,CAACvB,WAAWqB,SAAS;QAE3D,IAAIvB,oBAAoB;YACtB,MAAM,IAAIiB,MACR,CAAC,uCAAuC,EAAE9B,KAAK,mLAAmL,CAAC;QAEvO;IACF,OAAO;QACL;;;;KAIC,GACD,IAAIc,WAAWyB,WAAW,EAAE;YAC1BC,QAAQC,GAAG,CAACC,mBAAmB,GAAGC,KAAKC,SAAS,CAAC;QACnD;QACA,IAAI;YACFT,eAAe,MAAMU,IAAAA,iCAAmB,EACtC/C,KACAC,KACAE,MACAgC,wBACAnB;QAEJ,EAAE,OAAOgC,KAAK;YACZ,IAAI,CAACC,IAAAA,iCAAmB,EAACD,MAAM,MAAMA;QACvC;IACF;IAEA,MAAME,cAAcb,gCAAAA,aAAcc,QAAQ,CAACC,UAAU;IAErD,MAAMC,iBAAkC,EAAE;IAE1C,MAAMC,cAAc,OAClBC,YACAC,aACAC;QAEA,MAAMC,YAAY,MAAMC,yBAAgB,CAACC,WAAW,CAACH;QACrD,MAAMI,SAASH,UAAUI,cAAc,CAACP;QACxC,MAAMQ,SAASF,OAAOE,MAAM,CAACC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,KAAK;QAC1D,MAAMC,WAAWN,OAAOE,MAAM,CAACC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,KAAK;QAE5D,IAAIC,SAASC,MAAM,IAAIL,OAAOK,MAAM,EAAE;YACpCf,eAAegB,IAAI,CAAC;gBAClBlE,MAAMqD;gBACNK,QAAQ;oBACNE;oBACAI;gBACF;YACF;QACF;IACF;IAEA,MAAMG,OACJjC,gBAAgB,CAACA,aAAakC,MAAM,GAAGlC,aAAamC,iBAAiB,KAAK;IAE5E,IAAIC;IAEJ,IAAI7C,aAAa,CAACZ,WAAW0D,iBAAiB,EAAE;QAC9C,IAAI,CAACxB,aAAa;YAChB,MAAMI,YAAYgB,MAAMpE,MAAMS;QAChC;IACF,OAAO,IAAImB,WAAW;QACpB,MAAM6C,kBAAkBlE,aACpBmE,IAAAA,UAAI,EAACpE,SAAS,gBACd,GAAGA,QAAQ,KAAK,CAAC;QAErB,MAAMqE,kBAAkBD,IAAAA,UAAI,EAAClE,QAAQiE;QAErC,MAAMG,SAAS,MAAMC,IAAAA,sBAAU,EAACF,iBAAiBG,oBAAQ,CAACC,IAAI;QAC9D,IAAI,CAACH,QAAQ;YACX,IAAI;gBACFL,kBAAkB,MAAM1B,IAAAA,iCAAmB,EACzC/C,KACAC,KACAE,MACA;oBAAE,GAAGgC,sBAAsB;oBAAEb,KAAK;gBAAI,GACtCN;YAEJ,EAAE,OAAOgC,KAAK;gBACZ,IAAI,CAACC,IAAAA,iCAAmB,EAACD,MAAM,MAAMA;YACvC;YAEA,MAAMkC,UACJT,mBAAmB,CAACA,gBAAgBF,MAAM,GACtCE,gBAAgBD,iBAAiB,KACjC;YACN,IAAI,CAACxD,WAAW0D,iBAAiB,EAAE;gBACjC,MAAMpB,YAAY4B,SAAS/E,OAAO,UAAUQ;YAC9C;YAEA,MAAMO,uBAEJ2D,iBACAK,SACA;QAEJ;IACF;IAEA,MAAM/B,WAAWd,CAAAA,gCAAAA,aAAcc,QAAQ,MAAIsB,mCAAAA,gBAAiBtB,QAAQ,KAAI,CAAC;IACzE,IAAIA,SAASgC,QAAQ,EAAE;QACrB,MAAMC,WAAWR,IAAAA,UAAI,EACnBhE,cACAL,aAAa8E,OAAO,CAAC,WAAWC,2BAAgB;QAGlD,MAAMpE,mBAEJkE,UACAvC,KAAKC,SAAS,CAACK,SAASgC,QAAQ,GAChC;QAGF,IAAIrD,WAAW;YACb,MAAMZ,4BAEJkE,SAASC,OAAO,CAAC,WAAW,cAC5BxC,KAAKC,SAAS,CAACK,SAASgC,QAAQ,GAChC;QAEJ;IACF;IAEA,IAAI,CAACjC,aAAa;QAChB,qEAAqE;QACrE,MAAMhC,mBAAoCZ,cAAcgE,MAAM;IAChE;IAEA,OAAO;QACLjB;QACAkC,YAAYpC,SAASoC,UAAU,IAAI;QACnCrC;IACF;AACF"}