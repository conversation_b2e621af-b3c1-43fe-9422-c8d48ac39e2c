"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/(home)/page",{

/***/ "(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx":
/*!*************************************************!*\
  !*** ./src/components/Sales/SalesFormPanel.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/image/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ScanOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PrinterOutlined.js\");\n/* harmony import */ var _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/reduxRTK/services/salesApi */ \"(app-pages-browser)/./src/reduxRTK/services/salesApi.ts\");\n/* harmony import */ var _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/reduxRTK/services/productApi */ \"(app-pages-browser)/./src/reduxRTK/services/productApi.ts\");\n/* harmony import */ var _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/reduxRTK/services/userStoreApi */ \"(app-pages-browser)/./src/reduxRTK/services/userStoreApi.ts\");\n/* harmony import */ var _components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/SlidingPanel */ \"(app-pages-browser)/./src/components/ui/SlidingPanel.tsx\");\n/* harmony import */ var _components_BarcodeScanner_BarcodeScanner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/BarcodeScanner/BarcodeScanner */ \"(app-pages-browser)/./src/components/BarcodeScanner/BarcodeScanner.tsx\");\n/* harmony import */ var _hooks_useBarcodeScanner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useBarcodeScanner */ \"(app-pages-browser)/./src/hooks/useBarcodeScanner.ts\");\n/* harmony import */ var _utils_showMessage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/showMessage */ \"(app-pages-browser)/./src/utils/showMessage.ts\");\n/* harmony import */ var _utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/cloudinaryUtils */ \"(app-pages-browser)/./src/utils/cloudinaryUtils.ts\");\n/* harmony import */ var _sales_panels_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./sales-panels.css */ \"(app-pages-browser)/./src/components/Sales/sales-panels.css\");\n/* harmony import */ var _modern_sales_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./modern-sales.css */ \"(app-pages-browser)/./src/components/Sales/modern-sales.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst SalesFormPanel = (param)=>{\n    let { isOpen, onClose, onSuccess } = param;\n    var _productsData_data_products, _productsData_data;\n    _s();\n    const [form] = _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].useForm();\n    const [productForm] = _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].useForm();\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalAmount, setTotalAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingReceipt, setIsGeneratingReceipt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptPreviewVisible, setReceiptPreviewVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptUrl, setReceiptUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPrinted, setHasPrinted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [barcodeInput, setBarcodeInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Barcode scanner functionality\n    const { isOpen: isScannerOpen, openScanner, closeScanner, handleBarcodeScanned, isLoading: isScannerLoading } = (0,_hooks_useBarcodeScanner__WEBPACK_IMPORTED_MODULE_7__.useBarcodeScanner)({\n        onProductFound: {\n            \"SalesFormPanel.useBarcodeScanner\": (product)=>{\n                setSelectedProduct(product);\n                setQuantity(1);\n                if (productForm) {\n                    productForm.setFieldsValue({\n                        productId: product.id,\n                        quantity: 1\n                    });\n                }\n                // Auto-add to cart after scanning\n                setTimeout({\n                    \"SalesFormPanel.useBarcodeScanner\": ()=>{\n                        handleAddItem();\n                    }\n                }[\"SalesFormPanel.useBarcodeScanner\"], 100);\n            }\n        }[\"SalesFormPanel.useBarcodeScanner\"],\n        onProductNotFound: {\n            \"SalesFormPanel.useBarcodeScanner\": (barcode)=>{\n                console.log('Product not found for barcode:', barcode);\n            }\n        }[\"SalesFormPanel.useBarcodeScanner\"]\n    });\n    // Handle hardware barcode scanner input\n    const handleBarcodeInputChange = async (value)=>{\n        setBarcodeInput(value);\n        // If input looks like a barcode (typically 8+ characters), try to find product\n        if (value && value.length >= 8) {\n            try {\n                var _productsData_data;\n                console.log('Searching for product with barcode:', value);\n                // Use the existing products data to search for barcode\n                const products = (productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products) || [];\n                // Look for exact barcode match first\n                let foundProduct = products.find((product)=>product.barcode === value.trim());\n                // If no exact barcode match, try SKU\n                if (!foundProduct) {\n                    foundProduct = products.find((product)=>product.sku === value.trim());\n                }\n                if (foundProduct) {\n                    console.log('Product found via hardware scanner:', foundProduct);\n                    setSelectedProduct(foundProduct);\n                    setQuantity(1);\n                    if (productForm) {\n                        productForm.setFieldsValue({\n                            productId: foundProduct.id,\n                            quantity: 1\n                        });\n                    }\n                    // Clear the barcode input\n                    setBarcodeInput('');\n                    // Auto-add to cart\n                    setTimeout(()=>{\n                        handleAddItem();\n                    }, 100);\n                    (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)('success', \"Product found: \".concat(foundProduct.name));\n                } else {\n                    console.log('No product found for barcode:', value);\n                    (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)('warning', \"No product found for barcode: \".concat(value));\n                }\n            } catch (error) {\n                console.error('Error searching for product:', error);\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)('error', 'Error searching for product');\n            }\n        }\n    };\n    // Debug state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            console.log(\"Items state changed:\", items);\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items\n    ]);\n    const { data: productsData, isLoading: isLoadingProducts, refetch: refetchProducts } = (0,_reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery)({\n        page: 1,\n        limit: 1000,\n        search: searchTerm\n    }, {\n        // Force refetch when component mounts and when panel opens\n        refetchOnMountOrArgChange: true,\n        refetchOnFocus: false,\n        refetchOnReconnect: true\n    });\n    // Debug products data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (productsData) {\n                var _productsData_data, _productsData_data_products, _productsData_data1;\n                console.log(\"🛒 Products loaded:\", {\n                    total: ((_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.total) || 0,\n                    productsCount: ((_productsData_data1 = productsData.data) === null || _productsData_data1 === void 0 ? void 0 : (_productsData_data_products = _productsData_data1.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.length) || 0,\n                    isLoading: isLoadingProducts\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        productsData,\n        isLoadingProducts\n    ]);\n    // Get current user ID from auth state\n    const getCurrentUserId = ()=>{\n        if (true) {\n            var _state_auth_user, _state_auth;\n            // @ts-ignore - Redux state is exposed for debugging\n            const state = window.__REDUX_STATE;\n            return (state === null || state === void 0 ? void 0 : (_state_auth = state.auth) === null || _state_auth === void 0 ? void 0 : (_state_auth_user = _state_auth.user) === null || _state_auth_user === void 0 ? void 0 : _state_auth_user.id) || 0;\n        }\n        return 0;\n    };\n    // Fetch user stores\n    const { data: userStoresData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery)(getCurrentUserId());\n    // Fetch default store\n    const { data: defaultStoreData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery)(getCurrentUserId());\n    // Set default store when data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (defaultStoreData === null || defaultStoreData === void 0 ? void 0 : defaultStoreData.data) {\n                setSelectedStore(defaultStoreData.data);\n                form.setFieldsValue({\n                    storeId: defaultStoreData.data.id\n                });\n            } else if ((userStoresData === null || userStoresData === void 0 ? void 0 : userStoresData.data) && userStoresData.data.length > 0) {\n                setSelectedStore(userStoresData.data[0]);\n                form.setFieldsValue({\n                    storeId: userStoresData.data[0].id\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        defaultStoreData,\n        userStoresData,\n        form\n    ]);\n    // Create sale mutation\n    const [createSale, { isLoading: isSubmitting }] = (0,_reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation)();\n    // Calculate total amount whenever items change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (items && items.length > 0) {\n                const total = items.reduce({\n                    \"SalesFormPanel.useEffect.total\": (sum, item)=>sum + item.price * item.quantity\n                }[\"SalesFormPanel.useEffect.total\"], 0);\n                setTotalAmount(total);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: total\n                    });\n                }\n                // Debug log to check items state\n                console.log(\"Current items in useEffect:\", items);\n            } else {\n                setTotalAmount(0);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: 0\n                    });\n                }\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items,\n        form\n    ]);\n    // Handle panel open/close\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (isOpen) {\n                // When panel opens, ensure we have fresh product data\n                console.log(\"🛒 Sales panel opened - fetching fresh product data\");\n                refetchProducts();\n            } else {\n                // Reset forms when panel is closed\n                if (form) {\n                    form.resetFields();\n                }\n                if (productForm) {\n                    productForm.resetFields();\n                }\n                setItems([]);\n                setSelectedProduct(null);\n                setQuantity(1);\n                setTotalAmount(0);\n                setReceiptUrl(null);\n                setReceiptPreviewVisible(false);\n                setHasPrinted(false);\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        isOpen,\n        form,\n        refetchProducts\n    ]);\n    // Handle adding an item to the sale\n    const handleAddItem = ()=>{\n        if (!selectedProduct) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"Please select a product\");\n            return;\n        }\n        if (quantity <= 0) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"Quantity must be greater than 0\");\n            return;\n        }\n        if (selectedProduct.stockQuantity < quantity) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"Only \".concat(selectedProduct.stockQuantity, \" units available in stock\"));\n            return;\n        }\n        console.log(\"Adding item with product:\", selectedProduct);\n        // Check if product already exists in items\n        const existingItemIndex = items.findIndex((item)=>item.productId === selectedProduct.id);\n        if (existingItemIndex >= 0) {\n            // Update existing item\n            const updatedItems = [\n                ...items\n            ];\n            const newQuantity = updatedItems[existingItemIndex].quantity + quantity;\n            if (newQuantity > selectedProduct.stockQuantity) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"Cannot add more than \".concat(selectedProduct.stockQuantity, \" units of this product\"));\n                return;\n            }\n            updatedItems[existingItemIndex].quantity = newQuantity;\n            // Make sure the product name is set\n            if (!updatedItems[existingItemIndex].productName) {\n                updatedItems[existingItemIndex].productName = selectedProduct.name;\n            }\n            // Update the state with the new array\n            console.log(\"Updating existing item. New items array:\", updatedItems);\n            setItems([\n                ...updatedItems\n            ]); // Create a new array reference to force re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"success\", \"Updated quantity of \".concat(selectedProduct.name));\n        } else {\n            // Add new item\n            const newItem = {\n                productId: selectedProduct.id,\n                productName: selectedProduct.name,\n                quantity,\n                price: typeof selectedProduct.price === \"string\" ? parseFloat(selectedProduct.price) : selectedProduct.price\n            };\n            // Create a new array with the new item\n            const newItems = [\n                ...items,\n                newItem\n            ];\n            // Update the state with the new array\n            console.log(\"Adding new item. New items array:\", newItems);\n            setItems(newItems); // This should trigger a re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"success\", \"Added \".concat(quantity, \" \").concat(selectedProduct.name, \" to sale\"));\n        }\n        // Reset selection\n        setSelectedProduct(null);\n        setQuantity(1);\n        if (productForm) {\n            productForm.setFieldsValue({\n                productId: undefined,\n                quantity: 1\n            });\n        }\n    };\n    // Handle removing an item from the sale\n    const handleRemoveItem = (index)=>{\n        const updatedItems = [\n            ...items\n        ];\n        updatedItems.splice(index, 1);\n        setItems(updatedItems);\n    };\n    // Handle printing receipt - directly trigger print dialog\n    const handlePrintReceipt = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n            if (!receiptUrl || hasPrinted) {\n                console.log(\"Skipping print: \", !receiptUrl ? \"No receipt URL\" : \"Already printed\");\n                return;\n            }\n            console.log(\"Printing receipt:\", receiptUrl);\n            // Mark as printed immediately to prevent multiple print dialogs\n            setHasPrinted(true);\n            // Create a hidden iframe to load the image\n            const iframe = document.createElement(\"iframe\");\n            iframe.style.display = \"none\";\n            document.body.appendChild(iframe);\n            // Set up the iframe content with the image and print CSS\n            iframe.onload = ({\n                \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                    if (iframe.contentWindow) {\n                        // Write the HTML content to the iframe\n                        iframe.contentWindow.document.write('\\n          <!DOCTYPE html>\\n          <html>\\n            <head>\\n              <title>Print Receipt</title>\\n              <style>\\n                body {\\n                  margin: 0;\\n                  padding: 0;\\n                  display: flex;\\n                  justify-content: center;\\n                  align-items: center;\\n                  height: 100vh;\\n                }\\n                img {\\n                  max-width: 100%;\\n                  max-height: 100vh;\\n                }\\n                @media print {\\n                  body {\\n                    margin: 0;\\n                    padding: 0;\\n                  }\\n                  img {\\n                    width: 100%;\\n                    height: auto;\\n                  }\\n                }\\n              </style>\\n            </head>\\n            <body>\\n              <img src=\"'.concat(receiptUrl, '\" alt=\"Receipt\" />\\n            </body>\\n          </html>\\n        '));\n                        // Close the document\n                        iframe.contentWindow.document.close();\n                        // Use a single print trigger with a delay to ensure the image is loaded\n                        setTimeout({\n                            \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                if (iframe.contentWindow) {\n                                    try {\n                                        // Print the iframe content\n                                        iframe.contentWindow.focus();\n                                        iframe.contentWindow.print();\n                                    } catch (e) {\n                                        console.error(\"Error printing receipt:\", e);\n                                    }\n                                    // Remove the iframe after printing\n                                    setTimeout({\n                                        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                            document.body.removeChild(iframe);\n                                        }\n                                    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 1000);\n                                }\n                            }\n                        }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 500);\n                    }\n                }\n            })[\"SalesFormPanel.useCallback[handlePrintReceipt]\"];\n            // Set the iframe source to trigger the onload event\n            iframe.src = \"about:blank\";\n        }\n    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], [\n        receiptUrl,\n        hasPrinted\n    ]);\n    // Effect to automatically print receipt when modal is shown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (receiptPreviewVisible && receiptUrl && !hasPrinted) {\n                // Add a small delay to ensure the receipt image is loaded\n                const timer = setTimeout({\n                    \"SalesFormPanel.useEffect.timer\": ()=>{\n                        handlePrintReceipt(); // This now handles the hasPrinted state internally\n                    }\n                }[\"SalesFormPanel.useEffect.timer\"], 800);\n                return ({\n                    \"SalesFormPanel.useEffect\": ()=>clearTimeout(timer)\n                })[\"SalesFormPanel.useEffect\"];\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        receiptPreviewVisible,\n        receiptUrl,\n        hasPrinted,\n        handlePrintReceipt\n    ]);\n    // Handle form submission\n    const handleSubmit = async ()=>{\n        try {\n            var _userStoresData_data;\n            if (items.length === 0) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"Please add at least one item to the sale\");\n                return;\n            }\n            // Validate form fields\n            const values = await form.validateFields();\n            // Check if store is selected\n            if (!selectedStore) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"No store information available. Please set up your store in your profile settings.\");\n                return;\n            }\n            // Set loading state for receipt generation\n            setIsGeneratingReceipt(true);\n            // Get store information for receipt\n            const storeInfo = selectedStore || (userStoresData === null || userStoresData === void 0 ? void 0 : (_userStoresData_data = userStoresData.data) === null || _userStoresData_data === void 0 ? void 0 : _userStoresData_data.find((store)=>store.id === values.storeId)) || {\n                name: \"POS System\"\n            };\n            // Generate receipt HTML\n            const receiptHTML = (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_9__.generateReceiptHTML)({\n                id: Date.now(),\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                transactionDate: new Date().toISOString(),\n                items: items.map((item)=>({\n                        productName: item.productName,\n                        quantity: item.quantity,\n                        price: item.price\n                    }))\n            }, storeInfo);\n            // Generate receipt image and get URL\n            let receiptUrl = \"https://receipt.example.com/placeholder\";\n            try {\n                receiptUrl = await (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_9__.generateReceiptImage)(receiptHTML);\n            } catch (error) {\n                console.error(\"Failed to generate receipt image:\", error);\n            // Continue with placeholder URL if image generation fails\n            }\n            const saleData = {\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                items: items.map((item)=>({\n                        productId: item.productId,\n                        quantity: item.quantity,\n                        price: item.price\n                    })),\n                receiptUrl,\n                storeId: selectedStore === null || selectedStore === void 0 ? void 0 : selectedStore.id\n            };\n            const response = await createSale(saleData).unwrap();\n            if (response.success) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"success\", \"Sale created successfully\");\n                // Store the receipt URL for preview\n                setReceiptUrl(receiptUrl);\n                // Show receipt preview modal and offer print option\n                setReceiptPreviewVisible(true);\n                // Refresh product data to get updated stock quantities\n                refetchProducts();\n                // Trigger the success callback to refresh the list WITHOUT closing the panel\n                setTimeout(()=>{\n                    if (onSuccess) {\n                        // Call refetch directly instead of closing the panel\n                        refetchProducts();\n                    }\n                }, 300);\n            // Keep the panel open until the user explicitly closes it\n            // This ensures the receipt modal stays visible\n            } else {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", response.message || \"Failed to create sale\");\n            }\n        } catch (error) {\n            var _error_data;\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", ((_error_data = error.data) === null || _error_data === void 0 ? void 0 : _error_data.message) || \"An error occurred while creating the sale\");\n        } finally{\n            setIsGeneratingReceipt(false);\n        }\n    };\n    // Debug log to check items state when rendering\n    console.log(\"Rendering with items:\", items);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        title: \"Point of Sale\",\n        isOpen: isOpen,\n        onClose: onClose,\n        width: \"95%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sales-form min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between border-b border-slate-200 bg-white/80 backdrop-blur-sm px-6 py-4 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"text-xl text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"m-0 text-xl font-bold text-slate-800\",\n                                                children: \"New Transaction\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-slate-500 m-0\",\n                                                children: \"Point of Sale System\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-slate-500 m-0\",\n                                        children: \"Total Amount\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent\",\n                                        children: [\n                                            \"GHS \",\n                                            totalAmount.toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                lineNumber: 592,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 580,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-6 lg:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 rounded-2xl border border-slate-200 bg-white/70 backdrop-blur-sm p-6 shadow-xl shadow-slate-200/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg mr-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"text-sm text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 608,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 607,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-bold text-slate-800 m-0\",\n                                                                            children: \"Product Selection\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 611,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-slate-500 m-0\",\n                                                                            children: \"Choose products for this transaction\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 614,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 610,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-sm text-slate-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-1 text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 618,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \" Required fields\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    form: productForm,\n                                                    layout: \"vertical\",\n                                                    initialValues: {\n                                                        quantity: 1\n                                                    },\n                                                    className: \"product-form\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 gap-6 md:grid-cols-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"md:col-span-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Item, {\n                                                                        name: \"productId\",\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-slate-700 font-medium\",\n                                                                            children: [\n                                                                                \"Product \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-500\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 636,\n                                                                                    columnNumber: 37\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 635,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        className: \"mb-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            showSearch: true,\n                                                                            placeholder: isLoadingProducts ? \"Loading products...\" : \"🔍 Search and select a product...\",\n                                                                            optionFilterProp: \"children\",\n                                                                            loading: isLoadingProducts,\n                                                                            disabled: isLoadingProducts,\n                                                                            onChange: (value)=>{\n                                                                                var _productsData_data;\n                                                                                const product = productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products.find((p)=>p.id === value);\n                                                                                console.log(\"Selected product:\", product);\n                                                                                if (product) {\n                                                                                    // Make a deep copy to avoid reference issues\n                                                                                    setSelectedProduct({\n                                                                                        ...product,\n                                                                                        // Ensure price is properly formatted\n                                                                                        price: typeof product.price === \"string\" ? product.price : String(product.price)\n                                                                                    });\n                                                                                } else {\n                                                                                    setSelectedProduct(null);\n                                                                                }\n                                                                            },\n                                                                            onSearch: setSearchTerm,\n                                                                            filterOption: false,\n                                                                            className: \"modern-select\",\n                                                                            size: \"large\",\n                                                                            suffixIcon: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                spin: true,\n                                                                                className: \"text-blue-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 676,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-center bg-gradient-to-r mr-3 mt-4\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"text-slate-400 \"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 679,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 678,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            notFoundContent: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-center py-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        size: \"small\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 686,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"ml-2 text-slate-500\",\n                                                                                        children: \"Loading products...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 687,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 685,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center py-4 text-slate-500\",\n                                                                                children: \"No products found\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 690,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            children: productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : (_productsData_data_products = _productsData_data.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"].Option, {\n                                                                                    value: product.id,\n                                                                                    disabled: product.stockQuantity <= 0,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"font-medium text-slate-800\",\n                                                                                                        children: product.name\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 704,\n                                                                                                        columnNumber: 35\n                                                                                                    }, undefined),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-sm text-slate-500\",\n                                                                                                        children: [\n                                                                                                            \"GHS \",\n                                                                                                            Number(product.price).toFixed(2)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 705,\n                                                                                                        columnNumber: 35\n                                                                                                    }, undefined)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                lineNumber: 703,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-sm px-2 py-1 rounded-full \".concat(product.stockQuantity <= 0 ? 'bg-red-100 text-red-600' : product.stockQuantity <= 5 ? 'bg-yellow-100 text-yellow-600' : 'bg-green-100 text-green-600'),\n                                                                                                children: product.stockQuantity <= 0 ? \"Out of Stock\" : \"Stock: \".concat(product.stockQuantity)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                lineNumber: 709,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 702,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, product.id, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 697,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 641,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 632,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 631,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Item, {\n                                                                        name: \"quantity\",\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-slate-700 font-medium\",\n                                                                            children: [\n                                                                                \"Quantity \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-500\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 732,\n                                                                                    columnNumber: 38\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 731,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        className: \"mb-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            min: 1,\n                                                                            value: quantity,\n                                                                            onChange: (value)=>setQuantity(value || 1),\n                                                                            style: {\n                                                                                width: \"100%\"\n                                                                            },\n                                                                            className: \"modern-input\",\n                                                                            size: \"large\",\n                                                                            placeholder: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 737,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 728,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 727,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4 space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    type: \"primary\",\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    onClick: handleAddItem,\n                                                                    className: \"h-14 w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 border-0 rounded-xl font-semibold text-base shadow-lg shadow-blue-200 transition-all duration-200 hover:shadow-xl hover:shadow-blue-300 hover:-translate-y-0.5\",\n                                                                    disabled: !selectedProduct,\n                                                                    size: \"large\",\n                                                                    children: \"Add to Cart\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 751,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 763,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    onClick: openScanner,\n                                                                    loading: isScannerLoading,\n                                                                    className: \"h-12 w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 border-0 rounded-xl font-semibold text-base shadow-lg shadow-purple-200 transition-all duration-200 hover:shadow-xl hover:shadow-purple-300 hover:-translate-y-0.5 text-white\",\n                                                                    size: \"large\",\n                                                                    children: isScannerLoading ? 'Searching...' : 'Scan Barcode'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 762,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 750,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 rounded-2xl border border-slate-200 bg-white/70 backdrop-blur-sm p-6 shadow-xl shadow-slate-200/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"text-sm text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 778,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-bold text-slate-800 m-0\",\n                                                                    children: \"Cart Items\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 782,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-500 m-0\",\n                                                                    children: \"Items added to this transaction\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 783,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 781,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-[400px] overflow-x-auto overflow-y-auto\",\n                                                    children: items.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-2xl border border-slate-200 bg-gradient-to-br from-slate-50 to-blue-50 p-12 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center w-16 h-16 bg-gradient-to-r from-slate-400 to-slate-500 rounded-full mx-auto mb-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"text-2xl text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-slate-700 mb-2\",\n                                                                children: \"Cart is Empty\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 792,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-slate-500\",\n                                                                children: \"Add products to start building your transaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 793,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-hidden rounded-2xl border border-slate-200 bg-white shadow-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-slate-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-12 gap-4 text-sm font-semibold text-slate-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-5\",\n                                                                            children: \"Product\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 799,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 text-center\",\n                                                                            children: \"Qty\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 800,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 text-right\",\n                                                                            children: \"Price\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 801,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 text-right\",\n                                                                            children: \"Subtotal\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 802,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-1 text-center\",\n                                                                            children: \"Action\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 803,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 798,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 797,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"divide-y divide-slate-100\",\n                                                                children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"px-6 py-4 hover:bg-slate-50 transition-colors duration-150\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-12 gap-4 items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-5\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-semibold text-slate-800\",\n                                                                                        children: item.productName || \"Unknown Product\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 814,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 813,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-2 text-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"inline-flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full text-sm font-medium\",\n                                                                                        children: item.quantity\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 819,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 818,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-2 text-right text-slate-600 font-medium\",\n                                                                                    children: [\n                                                                                        \"GHS \",\n                                                                                        Number(item.price).toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 823,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-2 text-right font-bold text-slate-800\",\n                                                                                    children: [\n                                                                                        \"GHS \",\n                                                                                        (Number(item.price) * item.quantity).toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 826,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-1 text-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 831,\n                                                                                            columnNumber: 41\n                                                                                        }, void 0),\n                                                                                        onClick: ()=>handleRemoveItem(index),\n                                                                                        type: \"text\",\n                                                                                        danger: true,\n                                                                                        className: \"text-red-500 hover:bg-red-50 hover:text-red-600 rounded-lg\",\n                                                                                        size: \"small\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 830,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 829,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 812,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, \"\".concat(item.productId, \"-\").concat(index), false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 808,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-emerald-50 to-green-50 px-6 py-4 border-t border-slate-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-12 gap-4 items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-9 text-right\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg font-bold text-slate-800\",\n                                                                                children: \"Total:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 846,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 845,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-3 text-right\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    totalAmount.toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 849,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 848,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 844,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 843,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 796,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 786,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sticky top-4 rounded-2xl border border-slate-200 bg-white/70 backdrop-blur-sm p-6 shadow-xl shadow-slate-200/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg mr-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"text-sm text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 866,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 865,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-bold text-slate-800 m-0\",\n                                                                children: \"Checkout\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 869,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-slate-500 m-0\",\n                                                                children: \"Complete your transaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 870,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 868,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 864,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                form: form,\n                                                layout: \"vertical\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 rounded-xl border border-slate-200 bg-gradient-to-br from-slate-50 to-blue-50 p-5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-slate-600\",\n                                                                        children: \"Items:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 877,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full text-sm font-bold\",\n                                                                        children: items.length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 878,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 876,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-slate-600\",\n                                                                        children: \"Total Quantity:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 883,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-800 rounded-full text-sm font-bold\",\n                                                                        children: items.reduce((sum, item)=>sum + item.quantity, 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 884,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-slate-600\",\n                                                                        children: \"Subtotal:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 889,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-semibold text-slate-800\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 890,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 888,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"my-4 border-t border-slate-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 894,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-bold text-slate-800\",\n                                                                        children: \"Total:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 896,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 897,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 895,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 875,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    selectedStore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"mb-3 block text-slate-700 font-medium\",\n                                                                children: \"Store\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 906,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center rounded-xl border border-slate-200 bg-gradient-to-r from-blue-50 to-indigo-50 p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg mr-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"text-sm text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 909,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 908,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-semibold text-slate-800\",\n                                                                        children: selectedStore.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 911,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"hidden\",\n                                                                        name: \"storeId\",\n                                                                        value: selectedStore.id\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 914,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 907,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 905,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 rounded-xl border border-orange-200 bg-gradient-to-r from-orange-50 to-yellow-50 p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-lg mr-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"text-sm text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 925,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 924,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-orange-800 m-0\",\n                                                                            children: \"Store Setup Required\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 928,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-orange-600 m-0\",\n                                                                            children: \"Please set up your store in profile settings.\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 929,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 927,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 923,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 922,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Item, {\n                                                        name: \"paymentMethod\",\n                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-700 font-medium\",\n                                                            children: [\n                                                                \"Payment Method \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 941,\n                                                                    columnNumber: 40\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 940,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        rules: [\n                                                            {\n                                                                required: true,\n                                                                message: \"Please select a payment method\"\n                                                            }\n                                                        ],\n                                                        initialValue: \"cash\",\n                                                        className: \"mb-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"modern-select\",\n                                                            size: \"large\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"].Option, {\n                                                                    value: \"cash\",\n                                                                    children: \"\\uD83D\\uDCB5 Cash\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 954,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"].Option, {\n                                                                    value: \"card\",\n                                                                    children: \"\\uD83D\\uDCB3 Card\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 957,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"].Option, {\n                                                                    value: \"mobile_money\",\n                                                                    children: \"\\uD83D\\uDCF1 Mobile Money\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 960,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 953,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 937,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-8 space-y-4\",\n                                                        children: [\n                                                            receiptPreviewVisible ? // Show \"Done\" button when receipt is visible\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 971,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                onClick: ()=>{\n                                                                    // Close the modal and reset the receipt state\n                                                                    setReceiptPreviewVisible(false);\n                                                                    setReceiptUrl(null);\n                                                                    setHasPrinted(false);\n                                                                    // Reset the forms to start a new sale\n                                                                    form.resetFields();\n                                                                    productForm.resetFields();\n                                                                    setItems([]);\n                                                                    setSelectedProduct(null);\n                                                                    setQuantity(1);\n                                                                    setTotalAmount(0);\n                                                                },\n                                                                className: \"h-14 w-full bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 border-0 rounded-xl font-semibold text-base shadow-lg shadow-emerald-200 transition-all duration-200 hover:shadow-xl hover:shadow-emerald-300 hover:-translate-y-0.5\",\n                                                                size: \"large\",\n                                                                children: \"Start New Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 969,\n                                                                columnNumber: 23\n                                                            }, undefined) : // Show \"Complete Sale\" button when creating a sale\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 995,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                onClick: handleSubmit,\n                                                                loading: isSubmitting || isGeneratingReceipt,\n                                                                disabled: items.length === 0,\n                                                                className: \"h-14 w-full bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 border-0 rounded-xl font-semibold text-base shadow-lg shadow-emerald-200 transition-all duration-200 hover:shadow-xl hover:shadow-emerald-300 hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:transform-none disabled:hover:shadow-lg\",\n                                                                size: \"large\",\n                                                                children: isGeneratingReceipt ? \"Generating Receipt\" : \"Complete Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 993,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                onClick: onClose,\n                                                                className: \"h-12 w-full border-slate-300 bg-slate-100 text-slate-700 hover:bg-slate-200 rounded-xl font-medium transition-all duration-200 hover:shadow-md\",\n                                                                size: \"large\",\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1008,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 966,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 874,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 863,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 862,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 601,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 600,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 578,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-gray-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            className: \"mr-2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1027,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Receipt Preview\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1028,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 1026,\n                    columnNumber: 11\n                }, void 0),\n                open: receiptPreviewVisible,\n                onCancel: ()=>{\n                    // Close the modal and reset the receipt state\n                    setReceiptPreviewVisible(false);\n                    setReceiptUrl(null);\n                    setHasPrinted(false);\n                    // Reset the forms to start a new sale\n                    form.resetFields();\n                    productForm.resetFields();\n                    setItems([]);\n                    setSelectedProduct(null);\n                    setQuantity(1);\n                    setTotalAmount(0);\n                },\n                width: 500,\n                centered: true,\n                className: \"receipt-preview-modal\",\n                footer: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        onClick: ()=>{\n                            // Close the modal and reset the receipt state\n                            setReceiptPreviewVisible(false);\n                            setReceiptUrl(null);\n                            setHasPrinted(false);\n                            // Reset the forms to start a new sale\n                            form.resetFields();\n                            productForm.resetFields();\n                            setItems([]);\n                            setSelectedProduct(null);\n                            setQuantity(1);\n                            setTotalAmount(0);\n                        },\n                        className: \"border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200\",\n                        children: \"Close & New Sale\"\n                    }, \"close\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1050,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        type: \"primary\",\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1073,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: ()=>{\n                            // If already printed once, reset the flag to allow printing again\n                            if (hasPrinted) {\n                                setHasPrinted(false);\n                            }\n                            handlePrintReceipt();\n                        },\n                        className: \"bg-blue-600 hover:bg-blue-700\",\n                        children: hasPrinted ? \"Print Again\" : \"Print Receipt\"\n                    }, \"print\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1070,\n                        columnNumber: 11\n                    }, void 0)\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: receiptUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"receipt-image-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                            src: receiptUrl,\n                            alt: \"Receipt\",\n                            className: \"receipt-image\",\n                            style: {\n                                maxWidth: \"100%\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1090,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1089,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-64 items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            size: \"large\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1099,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1098,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 1087,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 1024,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BarcodeScanner_BarcodeScanner__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isScannerOpen,\n                onClose: closeScanner,\n                onScan: handleBarcodeScanned,\n                title: \"Scan Product Barcode\"\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 1106,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n        lineNumber: 572,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SalesFormPanel, \"cSW9e09S5a5AlxqCiHM5E8H+s9k=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].useForm,\n        _hooks_useBarcodeScanner__WEBPACK_IMPORTED_MODULE_7__.useBarcodeScanner,\n        _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery,\n        _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation\n    ];\n});\n_c = SalesFormPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SalesFormPanel);\nvar _c;\n$RefreshReg$(_c, \"SalesFormPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1NhbGVzL1NhbGVzRm9ybVBhbmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWdFO0FBVWxEO0FBV2E7QUFLVztBQUlFO0FBSUU7QUFDYztBQUNnQjtBQUNWO0FBQ1o7QUFJakI7QUFFTDtBQUNBO0FBUTVCLE1BQU02QixpQkFBZ0Q7UUFBQyxFQUNyREMsTUFBTSxFQUNOQyxPQUFPLEVBQ1BDLFNBQVMsRUFDVjtRQTRuQjBCQyw2QkFBQUE7O0lBM25CekIsTUFBTSxDQUFDQyxLQUFLLEdBQUc5QixvSEFBSUEsQ0FBQytCLE9BQU87SUFDM0IsTUFBTSxDQUFDQyxZQUFZLEdBQUdoQyxvSEFBSUEsQ0FBQytCLE9BQU87SUFDbEMsTUFBTSxDQUFDRSxPQUFPQyxTQUFTLEdBQUdyQywrQ0FBUUEsQ0FFaEMsRUFBRTtJQUNKLE1BQU0sQ0FBQ3NDLGlCQUFpQkMsbUJBQW1CLEdBQUd2QywrQ0FBUUEsQ0FBaUI7SUFDdkUsTUFBTSxDQUFDd0MsVUFBVUMsWUFBWSxHQUFHekMsK0NBQVFBLENBQVM7SUFDakQsTUFBTSxDQUFDMEMsYUFBYUMsZUFBZSxHQUFHM0MsK0NBQVFBLENBQVM7SUFDdkQsTUFBTSxDQUFDNEMsWUFBWUMsY0FBYyxHQUFHN0MsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDOEMsZUFBZUMsaUJBQWlCLEdBQUcvQywrQ0FBUUEsQ0FBZTtJQUNqRSxNQUFNLENBQUNnRCxxQkFBcUJDLHVCQUF1QixHQUFHakQsK0NBQVFBLENBQUM7SUFDL0QsTUFBTSxDQUFDa0QsdUJBQXVCQyx5QkFBeUIsR0FBR25ELCtDQUFRQSxDQUFDO0lBQ25FLE1BQU0sQ0FBQ29ELFlBQVlDLGNBQWMsR0FBR3JELCtDQUFRQSxDQUFnQjtJQUM1RCxNQUFNLENBQUNzRCxZQUFZQyxjQUFjLEdBQUd2RCwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUN3RCxjQUFjQyxnQkFBZ0IsR0FBR3pELCtDQUFRQSxDQUFTO0lBRXpELGdDQUFnQztJQUNoQyxNQUFNLEVBQ0o2QixRQUFRNkIsYUFBYSxFQUNyQkMsV0FBVyxFQUNYQyxZQUFZLEVBQ1pDLG9CQUFvQixFQUNwQkMsV0FBV0MsZ0JBQWdCLEVBQzVCLEdBQUd2QywyRUFBaUJBLENBQUM7UUFDcEJ3QyxjQUFjO2dEQUFFLENBQUNDO2dCQUNmMUIsbUJBQW1CMEI7Z0JBQ25CeEIsWUFBWTtnQkFDWixJQUFJTixhQUFhO29CQUNmQSxZQUFZK0IsY0FBYyxDQUFDO3dCQUN6QkMsV0FBV0YsUUFBUUcsRUFBRTt3QkFDckI1QixVQUFVO29CQUNaO2dCQUNGO2dCQUNBLGtDQUFrQztnQkFDbEM2Qjt3REFBVzt3QkFDVEM7b0JBQ0Y7dURBQUc7WUFDTDs7UUFDQUMsaUJBQWlCO2dEQUFFLENBQUNDO2dCQUNsQkMsUUFBUUMsR0FBRyxDQUFDLGtDQUFrQ0Y7WUFDaEQ7O0lBQ0Y7SUFFQSx3Q0FBd0M7SUFDeEMsTUFBTUcsMkJBQTJCLE9BQU9DO1FBQ3RDbkIsZ0JBQWdCbUI7UUFFaEIsK0VBQStFO1FBQy9FLElBQUlBLFNBQVNBLE1BQU1DLE1BQU0sSUFBSSxHQUFHO1lBQzlCLElBQUk7b0JBSWU3QztnQkFIakJ5QyxRQUFRQyxHQUFHLENBQUMsdUNBQXVDRTtnQkFFbkQsdURBQXVEO2dCQUN2RCxNQUFNRSxXQUFXOUMsQ0FBQUEseUJBQUFBLG9DQUFBQSxxQkFBQUEsYUFBYytDLElBQUksY0FBbEIvQyx5Q0FBQUEsbUJBQW9COEMsUUFBUSxLQUFJLEVBQUU7Z0JBRW5ELHFDQUFxQztnQkFDckMsSUFBSUUsZUFBZUYsU0FBU0csSUFBSSxDQUFDaEIsQ0FBQUEsVUFDL0JBLFFBQVFPLE9BQU8sS0FBS0ksTUFBTU0sSUFBSTtnQkFHaEMscUNBQXFDO2dCQUNyQyxJQUFJLENBQUNGLGNBQWM7b0JBQ2pCQSxlQUFlRixTQUFTRyxJQUFJLENBQUNoQixDQUFBQSxVQUMzQkEsUUFBUWtCLEdBQUcsS0FBS1AsTUFBTU0sSUFBSTtnQkFFOUI7Z0JBRUEsSUFBSUYsY0FBYztvQkFDaEJQLFFBQVFDLEdBQUcsQ0FBQyx1Q0FBdUNNO29CQUNuRHpDLG1CQUFtQnlDO29CQUNuQnZDLFlBQVk7b0JBQ1osSUFBSU4sYUFBYTt3QkFDZkEsWUFBWStCLGNBQWMsQ0FBQzs0QkFDekJDLFdBQVdhLGFBQWFaLEVBQUU7NEJBQzFCNUIsVUFBVTt3QkFDWjtvQkFDRjtvQkFDQSwwQkFBMEI7b0JBQzFCaUIsZ0JBQWdCO29CQUNoQixtQkFBbUI7b0JBQ25CWSxXQUFXO3dCQUNUQztvQkFDRixHQUFHO29CQUNIN0MsK0RBQVdBLENBQUMsV0FBVyxrQkFBb0MsT0FBbEJ1RCxhQUFhSSxJQUFJO2dCQUM1RCxPQUFPO29CQUNMWCxRQUFRQyxHQUFHLENBQUMsaUNBQWlDRTtvQkFDN0NuRCwrREFBV0EsQ0FBQyxXQUFXLGlDQUF1QyxPQUFObUQ7Z0JBQzFEO1lBQ0YsRUFBRSxPQUFPUyxPQUFPO2dCQUNkWixRQUFRWSxLQUFLLENBQUMsZ0NBQWdDQTtnQkFDOUM1RCwrREFBV0EsQ0FBQyxTQUFTO1lBQ3ZCO1FBQ0Y7SUFDRjtJQUVBLHNCQUFzQjtJQUN0QnhCLGdEQUFTQTtvQ0FBQztZQUNSd0UsUUFBUUMsR0FBRyxDQUFDLHdCQUF3QnRDO1FBQ3RDO21DQUFHO1FBQUNBO0tBQU07SUFHVixNQUFNLEVBQ0oyQyxNQUFNL0MsWUFBWSxFQUNsQjhCLFdBQVd3QixpQkFBaUIsRUFDNUJDLFNBQVNDLGVBQWUsRUFDekIsR0FBR3JFLHFGQUFzQkEsQ0FDeEI7UUFDRXNFLE1BQU07UUFDTkMsT0FBTztRQUNQQyxRQUFRL0M7SUFDVixHQUNBO1FBQ0UsMkRBQTJEO1FBQzNEZ0QsMkJBQTJCO1FBQzNCQyxnQkFBZ0I7UUFDaEJDLG9CQUFvQjtJQUN0QjtJQUdGLHNCQUFzQjtJQUN0QjdGLGdEQUFTQTtvQ0FBQztZQUNSLElBQUkrQixjQUFjO29CQUVQQSxvQkFDUUEsNkJBQUFBO2dCQUZqQnlDLFFBQVFDLEdBQUcsQ0FBQyx1QkFBdUI7b0JBQ2pDcUIsT0FBTy9ELEVBQUFBLHFCQUFBQSxhQUFhK0MsSUFBSSxjQUFqQi9DLHlDQUFBQSxtQkFBbUIrRCxLQUFLLEtBQUk7b0JBQ25DQyxlQUFlaEUsRUFBQUEsc0JBQUFBLGFBQWErQyxJQUFJLGNBQWpCL0MsMkNBQUFBLDhCQUFBQSxvQkFBbUI4QyxRQUFRLGNBQTNCOUMsa0RBQUFBLDRCQUE2QjZDLE1BQU0sS0FBSTtvQkFDdERmLFdBQVd3QjtnQkFDYjtZQUNGO1FBQ0Y7bUNBQUc7UUFBQ3REO1FBQWNzRDtLQUFrQjtJQUVwQyxzQ0FBc0M7SUFDdEMsTUFBTVcsbUJBQW1CO1FBQ3ZCLElBQUksSUFBNkIsRUFBRTtnQkFHMUJDLGtCQUFBQTtZQUZQLG9EQUFvRDtZQUNwRCxNQUFNQSxRQUFRQyxPQUFPQyxhQUFhO1lBQ2xDLE9BQU9GLENBQUFBLGtCQUFBQSw2QkFBQUEsY0FBQUEsTUFBT0csSUFBSSxjQUFYSCxtQ0FBQUEsbUJBQUFBLFlBQWFJLElBQUksY0FBakJKLHVDQUFBQSxpQkFBbUI5QixFQUFFLEtBQUk7UUFDbEM7UUFDQSxPQUFPO0lBQ1Q7SUFFQSxvQkFBb0I7SUFDcEIsTUFBTSxFQUFFVyxNQUFNd0IsY0FBYyxFQUFFLEdBQUduRixzRkFBcUJBLENBQUM2RTtJQUV2RCxzQkFBc0I7SUFDdEIsTUFBTSxFQUFFbEIsTUFBTXlCLGdCQUFnQixFQUFFLEdBQzlCbkYsNEZBQTJCQSxDQUFDNEU7SUFFOUIsd0NBQXdDO0lBQ3hDaEcsZ0RBQVNBO29DQUFDO1lBQ1IsSUFBSXVHLDZCQUFBQSx1Q0FBQUEsaUJBQWtCekIsSUFBSSxFQUFFO2dCQUMxQmhDLGlCQUFpQnlELGlCQUFpQnpCLElBQUk7Z0JBQ3RDOUMsS0FBS2lDLGNBQWMsQ0FBQztvQkFBRXVDLFNBQVNELGlCQUFpQnpCLElBQUksQ0FBQ1gsRUFBRTtnQkFBQztZQUMxRCxPQUFPLElBQUltQyxDQUFBQSwyQkFBQUEscUNBQUFBLGVBQWdCeEIsSUFBSSxLQUFJd0IsZUFBZXhCLElBQUksQ0FBQ0YsTUFBTSxHQUFHLEdBQUc7Z0JBQ2pFOUIsaUJBQWlCd0QsZUFBZXhCLElBQUksQ0FBQyxFQUFFO2dCQUN2QzlDLEtBQUtpQyxjQUFjLENBQUM7b0JBQUV1QyxTQUFTRixlQUFleEIsSUFBSSxDQUFDLEVBQUUsQ0FBQ1gsRUFBRTtnQkFBQztZQUMzRDtRQUNGO21DQUFHO1FBQUNvQztRQUFrQkQ7UUFBZ0J0RTtLQUFLO0lBRTNDLHVCQUF1QjtJQUN2QixNQUFNLENBQUN5RSxZQUFZLEVBQUU1QyxXQUFXNkMsWUFBWSxFQUFFLENBQUMsR0FBR3pGLGtGQUFxQkE7SUFFdkUsK0NBQStDO0lBQy9DakIsZ0RBQVNBO29DQUFDO1lBQ1IsSUFBSW1DLFNBQVNBLE1BQU15QyxNQUFNLEdBQUcsR0FBRztnQkFDN0IsTUFBTWtCLFFBQVEzRCxNQUFNd0UsTUFBTTtzREFDeEIsQ0FBQ0MsS0FBS0MsT0FBU0QsTUFBTUMsS0FBS0MsS0FBSyxHQUFHRCxLQUFLdEUsUUFBUTtxREFDL0M7Z0JBRUZHLGVBQWVvRDtnQkFDZixJQUFJOUQsTUFBTTtvQkFDUkEsS0FBS2lDLGNBQWMsQ0FBQzt3QkFBRXhCLGFBQWFxRDtvQkFBTTtnQkFDM0M7Z0JBRUEsaUNBQWlDO2dCQUNqQ3RCLFFBQVFDLEdBQUcsQ0FBQywrQkFBK0J0QztZQUM3QyxPQUFPO2dCQUNMTyxlQUFlO2dCQUNmLElBQUlWLE1BQU07b0JBQ1JBLEtBQUtpQyxjQUFjLENBQUM7d0JBQUV4QixhQUFhO29CQUFFO2dCQUN2QztZQUNGO1FBQ0Y7bUNBQUc7UUFBQ047UUFBT0g7S0FBSztJQUVoQiwwQkFBMEI7SUFDMUJoQyxnREFBU0E7b0NBQUM7WUFDUixJQUFJNEIsUUFBUTtnQkFDVixzREFBc0Q7Z0JBQ3RENEMsUUFBUUMsR0FBRyxDQUFDO2dCQUNaYztZQUNGLE9BQU87Z0JBQ0wsbUNBQW1DO2dCQUNuQyxJQUFJdkQsTUFBTTtvQkFDUkEsS0FBSytFLFdBQVc7Z0JBQ2xCO2dCQUNBLElBQUk3RSxhQUFhO29CQUNmQSxZQUFZNkUsV0FBVztnQkFDekI7Z0JBQ0EzRSxTQUFTLEVBQUU7Z0JBQ1hFLG1CQUFtQjtnQkFDbkJFLFlBQVk7Z0JBQ1pFLGVBQWU7Z0JBQ2ZVLGNBQWM7Z0JBQ2RGLHlCQUF5QjtnQkFDekJJLGNBQWM7WUFDaEI7UUFDRjttQ0FBRztRQUFDMUI7UUFBUUk7UUFBTXVEO0tBQWdCO0lBRWxDLG9DQUFvQztJQUNwQyxNQUFNbEIsZ0JBQWdCO1FBQ3BCLElBQUksQ0FBQ2hDLGlCQUFpQjtZQUNwQmIsK0RBQVdBLENBQUMsU0FBUztZQUNyQjtRQUNGO1FBRUEsSUFBSWUsWUFBWSxHQUFHO1lBQ2pCZiwrREFBV0EsQ0FBQyxTQUFTO1lBQ3JCO1FBQ0Y7UUFFQSxJQUFJYSxnQkFBZ0IyRSxhQUFhLEdBQUd6RSxVQUFVO1lBQzVDZiwrREFBV0EsQ0FDVCxTQUNBLFFBQXNDLE9BQTlCYSxnQkFBZ0IyRSxhQUFhLEVBQUM7WUFFeEM7UUFDRjtRQUVBeEMsUUFBUUMsR0FBRyxDQUFDLDZCQUE2QnBDO1FBRXpDLDJDQUEyQztRQUMzQyxNQUFNNEUsb0JBQW9COUUsTUFBTStFLFNBQVMsQ0FDdkMsQ0FBQ0wsT0FBU0EsS0FBSzNDLFNBQVMsS0FBSzdCLGdCQUFnQjhCLEVBQUU7UUFHakQsSUFBSThDLHFCQUFxQixHQUFHO1lBQzFCLHVCQUF1QjtZQUN2QixNQUFNRSxlQUFlO21CQUFJaEY7YUFBTTtZQUMvQixNQUFNaUYsY0FBY0QsWUFBWSxDQUFDRixrQkFBa0IsQ0FBQzFFLFFBQVEsR0FBR0E7WUFFL0QsSUFBSTZFLGNBQWMvRSxnQkFBZ0IyRSxhQUFhLEVBQUU7Z0JBQy9DeEYsK0RBQVdBLENBQ1QsU0FDQSx3QkFBc0QsT0FBOUJhLGdCQUFnQjJFLGFBQWEsRUFBQztnQkFFeEQ7WUFDRjtZQUVBRyxZQUFZLENBQUNGLGtCQUFrQixDQUFDMUUsUUFBUSxHQUFHNkU7WUFFM0Msb0NBQW9DO1lBQ3BDLElBQUksQ0FBQ0QsWUFBWSxDQUFDRixrQkFBa0IsQ0FBQ0ksV0FBVyxFQUFFO2dCQUNoREYsWUFBWSxDQUFDRixrQkFBa0IsQ0FBQ0ksV0FBVyxHQUFHaEYsZ0JBQWdCOEMsSUFBSTtZQUNwRTtZQUVBLHNDQUFzQztZQUN0Q1gsUUFBUUMsR0FBRyxDQUFDLDRDQUE0QzBDO1lBQ3hEL0UsU0FBUzttQkFBSStFO2FBQWEsR0FBRyxrREFBa0Q7WUFFL0UsdUJBQXVCO1lBQ3ZCM0YsK0RBQVdBLENBQUMsV0FBVyx1QkFBNEMsT0FBckJhLGdCQUFnQjhDLElBQUk7UUFDcEUsT0FBTztZQUNMLGVBQWU7WUFDZixNQUFNbUMsVUFBVTtnQkFDZHBELFdBQVc3QixnQkFBZ0I4QixFQUFFO2dCQUM3QmtELGFBQWFoRixnQkFBZ0I4QyxJQUFJO2dCQUNqQzVDO2dCQUNBdUUsT0FDRSxPQUFPekUsZ0JBQWdCeUUsS0FBSyxLQUFLLFdBQzdCUyxXQUFXbEYsZ0JBQWdCeUUsS0FBSyxJQUNoQ3pFLGdCQUFnQnlFLEtBQUs7WUFDN0I7WUFFQSx1Q0FBdUM7WUFDdkMsTUFBTVUsV0FBVzttQkFBSXJGO2dCQUFPbUY7YUFBUTtZQUVwQyxzQ0FBc0M7WUFDdEM5QyxRQUFRQyxHQUFHLENBQUMscUNBQXFDK0M7WUFDakRwRixTQUFTb0YsV0FBVyxrQ0FBa0M7WUFFdEQsdUJBQXVCO1lBQ3ZCaEcsK0RBQVdBLENBQ1QsV0FDQSxTQUFxQmEsT0FBWkUsVUFBUyxLQUF3QixPQUFyQkYsZ0JBQWdCOEMsSUFBSSxFQUFDO1FBRTlDO1FBRUEsa0JBQWtCO1FBQ2xCN0MsbUJBQW1CO1FBQ25CRSxZQUFZO1FBQ1osSUFBSU4sYUFBYTtZQUNmQSxZQUFZK0IsY0FBYyxDQUFDO2dCQUFFQyxXQUFXdUQ7Z0JBQVdsRixVQUFVO1lBQUU7UUFDakU7SUFDRjtJQUVBLHdDQUF3QztJQUN4QyxNQUFNbUYsbUJBQW1CLENBQUNDO1FBQ3hCLE1BQU1SLGVBQWU7ZUFBSWhGO1NBQU07UUFDL0JnRixhQUFhUyxNQUFNLENBQUNELE9BQU87UUFDM0J2RixTQUFTK0U7SUFDWDtJQUVBLDBEQUEwRDtJQUMxRCxNQUFNVSxxQkFBcUI1SCxrREFBV0E7MERBQUM7WUFFckMsSUFBSSxDQUFDa0QsY0FBY0UsWUFBWTtnQkFDN0JtQixRQUFRQyxHQUFHLENBQ1Qsb0JBQ0EsQ0FBQ3RCLGFBQWEsbUJBQW1CO2dCQUVuQztZQUNGO1lBRUFxQixRQUFRQyxHQUFHLENBQUMscUJBQXFCdEI7WUFFakMsZ0VBQWdFO1lBQ2hFRyxjQUFjO1lBRWQsMkNBQTJDO1lBQzNDLE1BQU13RSxTQUFTQyxTQUFTQyxhQUFhLENBQUM7WUFDdENGLE9BQU9HLEtBQUssQ0FBQ0MsT0FBTyxHQUFHO1lBQ3ZCSCxTQUFTSSxJQUFJLENBQUNDLFdBQVcsQ0FBQ047WUFFMUIseURBQXlEO1lBQ3pEQSxPQUFPTyxNQUFNO2tFQUFHO29CQUNkLElBQUlQLE9BQU9RLGFBQWEsRUFBRTt3QkFDeEIsdUNBQXVDO3dCQUN2Q1IsT0FBT1EsYUFBYSxDQUFDUCxRQUFRLENBQUNRLEtBQUssQ0FBQyw0MkJBK0JQLE9BQVhwRixZQUFXO3dCQUs3QixxQkFBcUI7d0JBQ3JCMkUsT0FBT1EsYUFBYSxDQUFDUCxRQUFRLENBQUNTLEtBQUs7d0JBRW5DLHdFQUF3RTt3QkFDeEVwRTs4RUFBVztnQ0FDVCxJQUFJMEQsT0FBT1EsYUFBYSxFQUFFO29DQUN4QixJQUFJO3dDQUNGLDJCQUEyQjt3Q0FDM0JSLE9BQU9RLGFBQWEsQ0FBQ0csS0FBSzt3Q0FDMUJYLE9BQU9RLGFBQWEsQ0FBQ0ksS0FBSztvQ0FDNUIsRUFBRSxPQUFPQyxHQUFHO3dDQUNWbkUsUUFBUVksS0FBSyxDQUFDLDJCQUEyQnVEO29DQUMzQztvQ0FFQSxtQ0FBbUM7b0NBQ25DdkU7MEZBQVc7NENBQ1QyRCxTQUFTSSxJQUFJLENBQUNTLFdBQVcsQ0FBQ2Q7d0NBQzVCO3lGQUFHO2dDQUNMOzRCQUNGOzZFQUFHO29CQUNMO2dCQUNGOztZQUVBLG9EQUFvRDtZQUNwREEsT0FBT2UsR0FBRyxHQUFHO1FBQ2Y7eURBQUc7UUFBQzFGO1FBQVlFO0tBQVc7SUFFM0IsNERBQTREO0lBQzVEckQsZ0RBQVNBO29DQUFDO1lBQ1IsSUFBSWlELHlCQUF5QkUsY0FBYyxDQUFDRSxZQUFZO2dCQUN0RCwwREFBMEQ7Z0JBQzFELE1BQU15RixRQUFRMUU7c0RBQVc7d0JBQ3ZCeUQsc0JBQXNCLG1EQUFtRDtvQkFDM0U7cURBQUc7Z0JBRUg7Z0RBQU8sSUFBTWtCLGFBQWFEOztZQUM1QjtRQUNGO21DQUFHO1FBQUM3RjtRQUF1QkU7UUFBWUU7UUFBWXdFO0tBQW1CO0lBRXRFLHlCQUF5QjtJQUN6QixNQUFNbUIsZUFBZTtRQUNuQixJQUFJO2dCQXVCQTFDO1lBdEJGLElBQUluRSxNQUFNeUMsTUFBTSxLQUFLLEdBQUc7Z0JBQ3RCcEQsK0RBQVdBLENBQUMsU0FBUztnQkFDckI7WUFDRjtZQUVBLHVCQUF1QjtZQUN2QixNQUFNeUgsU0FBUyxNQUFNakgsS0FBS2tILGNBQWM7WUFFeEMsNkJBQTZCO1lBQzdCLElBQUksQ0FBQ3JHLGVBQWU7Z0JBQ2xCckIsK0RBQVdBLENBQ1QsU0FDQTtnQkFFRjtZQUNGO1lBRUEsMkNBQTJDO1lBQzNDd0IsdUJBQXVCO1lBRXZCLG9DQUFvQztZQUNwQyxNQUFNbUcsWUFBWXRHLGtCQUNoQnlELDJCQUFBQSxzQ0FBQUEsdUJBQUFBLGVBQWdCeEIsSUFBSSxjQUFwQndCLDJDQUFBQSxxQkFBc0J0QixJQUFJLENBQUMsQ0FBQ29FLFFBQVVBLE1BQU1qRixFQUFFLEtBQUs4RSxPQUFPekMsT0FBTyxNQUFLO2dCQUNwRXJCLE1BQU07WUFDUjtZQUVGLHdCQUF3QjtZQUN4QixNQUFNa0UsY0FBYzVILDJFQUFtQkEsQ0FDckM7Z0JBQ0UwQyxJQUFJbUYsS0FBS0MsR0FBRztnQkFDWjlHO2dCQUNBK0csZUFBZVAsT0FBT08sYUFBYTtnQkFDbkNDLGlCQUFpQixJQUFJSCxPQUFPSSxXQUFXO2dCQUN2Q3ZILE9BQU9BLE1BQU13SCxHQUFHLENBQUMsQ0FBQzlDLE9BQVU7d0JBQzFCUSxhQUFhUixLQUFLUSxXQUFXO3dCQUM3QjlFLFVBQVVzRSxLQUFLdEUsUUFBUTt3QkFDdkJ1RSxPQUFPRCxLQUFLQyxLQUFLO29CQUNuQjtZQUNGLEdBQ0FxQztZQUdGLHFDQUFxQztZQUNyQyxJQUFJaEcsYUFBYTtZQUNqQixJQUFJO2dCQUNGQSxhQUFhLE1BQU16Qiw0RUFBb0JBLENBQUMySDtZQUMxQyxFQUFFLE9BQU9qRSxPQUFPO2dCQUNkWixRQUFRWSxLQUFLLENBQUMscUNBQXFDQTtZQUNuRCwwREFBMEQ7WUFDNUQ7WUFFQSxNQUFNd0UsV0FBMEI7Z0JBQzlCbkg7Z0JBQ0ErRyxlQUFlUCxPQUFPTyxhQUFhO2dCQUNuQ3JILE9BQU9BLE1BQU13SCxHQUFHLENBQUMsQ0FBQzlDLE9BQVU7d0JBQzFCM0MsV0FBVzJDLEtBQUszQyxTQUFTO3dCQUN6QjNCLFVBQVVzRSxLQUFLdEUsUUFBUTt3QkFDdkJ1RSxPQUFPRCxLQUFLQyxLQUFLO29CQUNuQjtnQkFDQTNEO2dCQUNBcUQsT0FBTyxFQUFFM0QsMEJBQUFBLG9DQUFBQSxjQUFlc0IsRUFBRTtZQUM1QjtZQUVBLE1BQU0wRixXQUFXLE1BQU1wRCxXQUFXbUQsVUFBVUUsTUFBTTtZQUVsRCxJQUFJRCxTQUFTRSxPQUFPLEVBQUU7Z0JBQ3BCdkksK0RBQVdBLENBQUMsV0FBVztnQkFFdkIsb0NBQW9DO2dCQUNwQzRCLGNBQWNEO2dCQUVkLG9EQUFvRDtnQkFDcERELHlCQUF5QjtnQkFJekIsdURBQXVEO2dCQUN2RHFDO2dCQUVBLDZFQUE2RTtnQkFFN0VuQixXQUFXO29CQUNULElBQUl0QyxXQUFXO3dCQUNiLHFEQUFxRDt3QkFDckR5RDtvQkFDRjtnQkFDRixHQUFHO1lBRUgsMERBQTBEO1lBQzFELCtDQUErQztZQUNqRCxPQUFPO2dCQUNML0QsK0RBQVdBLENBQUMsU0FBU3FJLFNBQVNHLE9BQU8sSUFBSTtZQUMzQztRQUNGLEVBQUUsT0FBTzVFLE9BQVk7Z0JBR2pCQTtZQUZGNUQsK0RBQVdBLENBQ1QsU0FDQTRELEVBQUFBLGNBQUFBLE1BQU1OLElBQUksY0FBVk0sa0NBQUFBLFlBQVk0RSxPQUFPLEtBQUk7UUFFM0IsU0FBVTtZQUNSaEgsdUJBQXVCO1FBQ3pCO0lBQ0Y7SUFFQSxnREFBZ0Q7SUFDaER3QixRQUFRQyxHQUFHLENBQUMseUJBQXlCdEM7SUFFckMscUJBQ0UsOERBQUNkLG1FQUFZQTtRQUNYNEksT0FBTTtRQUNOckksUUFBUUE7UUFDUkMsU0FBU0E7UUFDVHFJLE9BQU07OzBCQUVOLDhEQUFDQztnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ3pKLDBNQUFvQkE7NENBQUN5SixXQUFVOzs7Ozs7Ozs7OztrREFFbEMsOERBQUNEOzswREFDQyw4REFBQ0U7Z0RBQUdELFdBQVU7MERBQXVDOzs7Ozs7MERBR3JELDhEQUFDRTtnREFBRUYsV0FBVTswREFBNkI7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FHOUMsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0U7d0NBQUVGLFdBQVU7a0RBQTZCOzs7Ozs7a0RBQzFDLDhEQUFDRDt3Q0FBSUMsV0FBVTs7NENBQWtHOzRDQUMxRzNILFlBQVk4SCxPQUFPLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSy9CLDhEQUFDSjt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOzhFQUNiLDRFQUFDeEosME1BQWNBO3dFQUFDd0osV0FBVTs7Ozs7Ozs7Ozs7OEVBRTVCLDhEQUFDRDs7c0ZBQ0MsOERBQUNLOzRFQUFHSixXQUFVO3NGQUF1Qzs7Ozs7O3NGQUdyRCw4REFBQ0U7NEVBQUVGLFdBQVU7c0ZBQTZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBRzlDLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNLO29FQUFLTCxXQUFVOzhFQUFvQjs7Ozs7O2dFQUFROzs7Ozs7Ozs7Ozs7OzhEQUloRCw4REFBQ2xLLG9IQUFJQTtvREFDSDhCLE1BQU1FO29EQUNOd0ksUUFBTztvREFDUEMsZUFBZTt3REFDYnBJLFVBQVU7b0RBQ1o7b0RBQ0E2SCxXQUFVOztzRUFFViw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs4RUFDYiw0RUFBQ2xLLG9IQUFJQSxDQUFDMEssSUFBSTt3RUFDUnpGLE1BQUs7d0VBQ0wwRixxQkFDRSw4REFBQ0o7NEVBQUtMLFdBQVU7O2dGQUE2Qjs4RkFDbkMsOERBQUNLO29GQUFLTCxXQUFVOzhGQUFlOzs7Ozs7Ozs7Ozs7d0VBRzNDQSxXQUFVO2tGQUVWLDRFQUFDaEssb0hBQU1BOzRFQUNMMEssVUFBVTs0RUFDVkMsYUFDRTFGLG9CQUNJLHdCQUNBOzRFQUVOMkYsa0JBQWlCOzRFQUNqQkMsU0FBUzVGOzRFQUNUNkYsVUFBVTdGOzRFQUNWOEYsVUFBVSxDQUFDeEc7b0ZBQ081QztnRkFBaEIsTUFBTWlDLFVBQVVqQyx5QkFBQUEsb0NBQUFBLHFCQUFBQSxhQUFjK0MsSUFBSSxjQUFsQi9DLHlDQUFBQSxtQkFBb0I4QyxRQUFRLENBQUNHLElBQUksQ0FDL0MsQ0FBQ3NGLElBQU1BLEVBQUVuRyxFQUFFLEtBQUtRO2dGQUVsQkgsUUFBUUMsR0FBRyxDQUFDLHFCQUFxQlQ7Z0ZBQ2pDLElBQUlBLFNBQVM7b0ZBQ1gsNkNBQTZDO29GQUM3QzFCLG1CQUFtQjt3RkFDakIsR0FBRzBCLE9BQU87d0ZBQ1YscUNBQXFDO3dGQUNyQzhDLE9BQ0UsT0FBTzlDLFFBQVE4QyxLQUFLLEtBQUssV0FDckI5QyxRQUFROEMsS0FBSyxHQUNic0UsT0FBT3BILFFBQVE4QyxLQUFLO29GQUM1QjtnRkFDRixPQUFPO29GQUNMeEUsbUJBQW1CO2dGQUNyQjs0RUFDRjs0RUFDQStJLFVBQVV6STs0RUFDVjBJLGNBQWM7NEVBQ2RsQixXQUFVOzRFQUNWbUIsTUFBSzs0RUFDTEMsWUFDRW5HLGtDQUNFLDhEQUFDeEUsME1BQWVBO2dGQUFDNEssSUFBSTtnRkFBQ3JCLFdBQVU7Ozs7O3VHQUVoQyw4REFBQ0Q7Z0ZBQUlDLFdBQVU7MEZBQ2IsNEVBQUN4SiwwTUFBY0E7b0ZBQUN3SixXQUFVOzs7Ozs7Ozs7Ozs0RUFJaENzQixpQkFDRXJHLGtDQUNFLDhEQUFDOEU7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDOUosb0hBQUlBO3dGQUFDaUwsTUFBSzs7Ozs7O2tHQUNYLDhEQUFDZDt3RkFBS0wsV0FBVTtrR0FBc0I7Ozs7Ozs7Ozs7O3VHQUd4Qyw4REFBQ0Q7Z0ZBQUlDLFdBQVU7MEZBQWtDOzs7Ozs7c0ZBTXBEckkseUJBQUFBLG9DQUFBQSxxQkFBQUEsYUFBYytDLElBQUksY0FBbEIvQywwQ0FBQUEsOEJBQUFBLG1CQUFvQjhDLFFBQVEsY0FBNUI5QyxrREFBQUEsNEJBQThCNEgsR0FBRyxDQUFDLENBQUMzRix3QkFDbEMsOERBQUM1RCxvSEFBTUEsQ0FBQ3VMLE1BQU07b0ZBRVpoSCxPQUFPWCxRQUFRRyxFQUFFO29GQUNqQitHLFVBQVVsSCxRQUFRZ0QsYUFBYSxJQUFJOzhGQUVuQyw0RUFBQ21EO3dGQUFJQyxXQUFVOzswR0FDYiw4REFBQ0Q7O2tIQUNDLDhEQUFDQTt3R0FBSUMsV0FBVTtrSEFBOEJwRyxRQUFRbUIsSUFBSTs7Ozs7O2tIQUN6RCw4REFBQ2dGO3dHQUFJQyxXQUFVOzs0R0FBeUI7NEdBQ2pDd0IsT0FBTzVILFFBQVE4QyxLQUFLLEVBQUV5RCxPQUFPLENBQUM7Ozs7Ozs7Ozs7Ozs7MEdBR3ZDLDhEQUFDSjtnR0FBSUMsV0FBVyxrQ0FNZixPQUxDcEcsUUFBUWdELGFBQWEsSUFBSSxJQUNyQiw0QkFDQWhELFFBQVFnRCxhQUFhLElBQUksSUFDekIsa0NBQ0E7MEdBRUhoRCxRQUFRZ0QsYUFBYSxJQUFJLElBQ3RCLGlCQUNBLFVBQWdDLE9BQXRCaEQsUUFBUWdELGFBQWE7Ozs7Ozs7Ozs7OzttRkFwQmxDaEQsUUFBUUcsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBNkJ6Qiw4REFBQ2dHOzhFQUNDLDRFQUFDakssb0hBQUlBLENBQUMwSyxJQUFJO3dFQUNSekYsTUFBSzt3RUFDTDBGLHFCQUNFLDhEQUFDSjs0RUFBS0wsV0FBVTs7Z0ZBQTZCOzhGQUNsQyw4REFBQ0s7b0ZBQUtMLFdBQVU7OEZBQWU7Ozs7Ozs7Ozs7Ozt3RUFHNUNBLFdBQVU7a0ZBRVYsNEVBQUMvSixvSEFBV0E7NEVBQ1Z3TCxLQUFLOzRFQUNMbEgsT0FBT3BDOzRFQUNQNEksVUFBVSxDQUFDeEcsUUFBVW5DLFlBQVltQyxTQUFTOzRFQUMxQ3NELE9BQU87Z0ZBQUVpQyxPQUFPOzRFQUFPOzRFQUN2QkUsV0FBVTs0RUFDVm1CLE1BQUs7NEVBQ0xSLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBTXBCLDhEQUFDWjs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNqSyxvSEFBTUE7b0VBQ0wyTCxNQUFLO29FQUNMQyxvQkFBTSw4REFBQ3JMLDBNQUFZQTs7Ozs7b0VBQ25Cc0wsU0FBUzNIO29FQUNUK0YsV0FBVTtvRUFDVmMsVUFBVSxDQUFDN0k7b0VBQ1hrSixNQUFLOzhFQUNOOzs7Ozs7OEVBSUQsOERBQUNwTCxvSEFBTUE7b0VBQ0w0TCxvQkFBTSw4REFBQy9LLDBNQUFZQTs7Ozs7b0VBQ25CZ0wsU0FBU3RJO29FQUNUdUgsU0FBU25IO29FQUNUc0csV0FBVTtvRUFDVm1CLE1BQUs7OEVBRUp6SCxtQkFBbUIsaUJBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBTzdDLDhEQUFDcUc7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVO3NFQUNiLDRFQUFDekosME1BQW9CQTtnRUFBQ3lKLFdBQVU7Ozs7Ozs7Ozs7O3NFQUVsQyw4REFBQ0Q7OzhFQUNDLDhEQUFDSztvRUFBR0osV0FBVTs4RUFBdUM7Ozs7Ozs4RUFDckQsOERBQUNFO29FQUFFRixXQUFVOzhFQUE2Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUc5Qyw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ1pqSSxNQUFNeUMsTUFBTSxLQUFLLGtCQUNoQiw4REFBQ3VGO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQ2IsNEVBQUN6SiwwTUFBb0JBO29FQUFDeUosV0FBVTs7Ozs7Ozs7Ozs7MEVBRWxDLDhEQUFDNkI7Z0VBQUc3QixXQUFVOzBFQUE0Qzs7Ozs7OzBFQUMxRCw4REFBQ0U7Z0VBQUVGLFdBQVU7MEVBQWlCOzs7Ozs7Ozs7OztrRkFHaEMsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQ2IsNEVBQUNEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ0Q7NEVBQUlDLFdBQVU7c0ZBQWE7Ozs7OztzRkFDNUIsOERBQUNEOzRFQUFJQyxXQUFVO3NGQUF5Qjs7Ozs7O3NGQUN4Qyw4REFBQ0Q7NEVBQUlDLFdBQVU7c0ZBQXdCOzs7Ozs7c0ZBQ3ZDLDhEQUFDRDs0RUFBSUMsV0FBVTtzRkFBd0I7Ozs7OztzRkFDdkMsOERBQUNEOzRFQUFJQyxXQUFVO3NGQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBRzVDLDhEQUFDRDtnRUFBSUMsV0FBVTswRUFDWmpJLE1BQU13SCxHQUFHLENBQUMsQ0FBQzlDLE1BQU1jLHNCQUNoQiw4REFBQ3dDO3dFQUVDQyxXQUFVO2tGQUVWLDRFQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNEO29GQUFJQyxXQUFVOzhGQUNiLDRFQUFDRDt3RkFBSUMsV0FBVTtrR0FDWnZELEtBQUtRLFdBQVcsSUFBSTs7Ozs7Ozs7Ozs7OEZBR3pCLDhEQUFDOEM7b0ZBQUlDLFdBQVU7OEZBQ2IsNEVBQUNLO3dGQUFLTCxXQUFVO2tHQUNidkQsS0FBS3RFLFFBQVE7Ozs7Ozs7Ozs7OzhGQUdsQiw4REFBQzRIO29GQUFJQyxXQUFVOzt3RkFBbUQ7d0ZBQzNEd0IsT0FBTy9FLEtBQUtDLEtBQUssRUFBRXlELE9BQU8sQ0FBQzs7Ozs7Ozs4RkFFbEMsOERBQUNKO29GQUFJQyxXQUFVOzt3RkFBaUQ7d0ZBQ3hEd0IsQ0FBQUEsT0FBTy9FLEtBQUtDLEtBQUssSUFBSUQsS0FBS3RFLFFBQVEsRUFBRWdJLE9BQU8sQ0FBQzs7Ozs7Ozs4RkFFcEQsOERBQUNKO29GQUFJQyxXQUFVOzhGQUNiLDRFQUFDakssb0hBQU1BO3dGQUNMNEwsb0JBQU0sOERBQUN0TCwwTUFBY0E7Ozs7O3dGQUNyQnVMLFNBQVMsSUFBTXRFLGlCQUFpQkM7d0ZBQ2hDbUUsTUFBSzt3RkFDTEksTUFBTTt3RkFDTjlCLFdBQVU7d0ZBQ1ZtQixNQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozt1RUEzQk4sR0FBcUI1RCxPQUFsQmQsS0FBSzNDLFNBQVMsRUFBQyxLQUFTLE9BQU55RDs7Ozs7Ozs7OzswRUFrQ2hDLDhEQUFDd0M7Z0VBQUlDLFdBQVU7MEVBQ2IsNEVBQUNEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ0Q7NEVBQUlDLFdBQVU7c0ZBQ2IsNEVBQUNLO2dGQUFLTCxXQUFVOzBGQUFtQzs7Ozs7Ozs7Ozs7c0ZBRXJELDhEQUFDRDs0RUFBSUMsV0FBVTtzRkFDYiw0RUFBQ0s7Z0ZBQUtMLFdBQVU7O29GQUFpRztvRkFDMUczSCxZQUFZOEgsT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQVkzQyw4REFBQ0o7OENBQ0MsNEVBQUNBO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDYiw0RUFBQ3pKLDBNQUFvQkE7NERBQUN5SixXQUFVOzs7Ozs7Ozs7OztrRUFFbEMsOERBQUNEOzswRUFDQyw4REFBQ0s7Z0VBQUdKLFdBQVU7MEVBQXVDOzs7Ozs7MEVBQ3JELDhEQUFDRTtnRUFBRUYsV0FBVTswRUFBNkI7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFJOUMsOERBQUNsSyxvSEFBSUE7Z0RBQUM4QixNQUFNQTtnREFBTTBJLFFBQU87O2tFQUN2Qiw4REFBQ1A7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNLO3dFQUFLTCxXQUFVO2tGQUFxQzs7Ozs7O2tGQUNyRCw4REFBQ0s7d0VBQUtMLFdBQVU7a0ZBQ2JqSSxNQUFNeUMsTUFBTTs7Ozs7Ozs7Ozs7OzBFQUdqQiw4REFBQ3VGO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0s7d0VBQUtMLFdBQVU7a0ZBQXFDOzs7Ozs7a0ZBQ3JELDhEQUFDSzt3RUFBS0wsV0FBVTtrRkFDYmpJLE1BQU13RSxNQUFNLENBQUMsQ0FBQ0MsS0FBS0MsT0FBU0QsTUFBTUMsS0FBS3RFLFFBQVEsRUFBRTs7Ozs7Ozs7Ozs7OzBFQUd0RCw4REFBQzRIO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0s7d0VBQUtMLFdBQVU7a0ZBQXFDOzs7Ozs7a0ZBQ3JELDhEQUFDSzt3RUFBS0wsV0FBVTs7NEVBQXVDOzRFQUNoRDNILFlBQVk4SCxPQUFPLENBQUM7Ozs7Ozs7Ozs7Ozs7MEVBRzdCLDhEQUFDSjtnRUFBSUMsV0FBVTs7Ozs7OzBFQUNmLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNLO3dFQUFLTCxXQUFVO2tGQUFtQzs7Ozs7O2tGQUNuRCw4REFBQ0s7d0VBQUtMLFdBQVU7OzRFQUFpRzs0RUFDMUczSCxZQUFZOEgsT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O29EQU05QjFILDhCQUNDLDhEQUFDc0g7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDUztnRUFBTVQsV0FBVTswRUFBd0M7Ozs7OzswRUFDekQsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0Q7d0VBQUlDLFdBQVU7a0ZBQ2IsNEVBQUN0SiwwTUFBWUE7NEVBQUNzSixXQUFVOzs7Ozs7Ozs7OztrRkFFMUIsOERBQUNLO3dFQUFLTCxXQUFVO2tGQUNidkgsY0FBY3NDLElBQUk7Ozs7OztrRkFFckIsOERBQUNnSDt3RUFDQ0wsTUFBSzt3RUFDTDNHLE1BQUs7d0VBQ0xSLE9BQU85QixjQUFjc0IsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0ZBSzdCLDhEQUFDZ0c7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7OEVBQ2IsNEVBQUN0SiwwTUFBWUE7d0VBQUNzSixXQUFVOzs7Ozs7Ozs7Ozs4RUFFMUIsOERBQUNEOztzRkFDQyw4REFBQ0c7NEVBQUVGLFdBQVU7c0ZBQWtDOzs7Ozs7c0ZBQy9DLDhEQUFDRTs0RUFBRUYsV0FBVTtzRkFBOEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQVFuRCw4REFBQ2xLLG9IQUFJQSxDQUFDMEssSUFBSTt3REFDUnpGLE1BQUs7d0RBQ0wwRixxQkFDRSw4REFBQ0o7NERBQUtMLFdBQVU7O2dFQUE2Qjs4RUFDNUIsOERBQUNLO29FQUFLTCxXQUFVOzhFQUFlOzs7Ozs7Ozs7Ozs7d0RBR2xEZ0MsT0FBTzs0REFDTDtnRUFDRUMsVUFBVTtnRUFDVnJDLFNBQVM7NERBQ1g7eURBQ0Q7d0RBQ0RzQyxjQUFhO3dEQUNibEMsV0FBVTtrRUFFViw0RUFBQ2hLLG9IQUFNQTs0REFBQ2dLLFdBQVU7NERBQWdCbUIsTUFBSzs7OEVBQ3JDLDhEQUFDbkwsb0hBQU1BLENBQUN1TCxNQUFNO29FQUFDaEgsT0FBTTs4RUFBTzs7Ozs7OzhFQUc1Qiw4REFBQ3ZFLG9IQUFNQSxDQUFDdUwsTUFBTTtvRUFBQ2hILE9BQU07OEVBQU87Ozs7Ozs4RUFHNUIsOERBQUN2RSxvSEFBTUEsQ0FBQ3VMLE1BQU07b0VBQUNoSCxPQUFNOzhFQUFlOzs7Ozs7Ozs7Ozs7Ozs7OztrRUFNeEMsOERBQUN3Rjt3REFBSUMsV0FBVTs7NERBQ1puSCx3QkFDQyw2Q0FBNkM7MEVBQzdDLDhEQUFDOUMsb0hBQU1BO2dFQUNMMkwsTUFBSztnRUFDTEMsb0JBQU0sOERBQUNwTCwwTUFBb0JBOzs7OztnRUFDM0JxTCxTQUFTO29FQUNQLDhDQUE4QztvRUFDOUM5SSx5QkFBeUI7b0VBQ3pCRSxjQUFjO29FQUNkRSxjQUFjO29FQUVkLHNDQUFzQztvRUFDdEN0QixLQUFLK0UsV0FBVztvRUFDaEI3RSxZQUFZNkUsV0FBVztvRUFDdkIzRSxTQUFTLEVBQUU7b0VBQ1hFLG1CQUFtQjtvRUFDbkJFLFlBQVk7b0VBQ1pFLGVBQWU7Z0VBQ2pCO2dFQUNBMEgsV0FBVTtnRUFDVm1CLE1BQUs7MEVBQ047Ozs7OzRFQUlELG1EQUFtRDswRUFDbkQsOERBQUNwTCxvSEFBTUE7Z0VBQ0wyTCxNQUFLO2dFQUNMQyxvQkFBTSw4REFBQ3BMLDBNQUFvQkE7Ozs7O2dFQUMzQnFMLFNBQVNoRDtnRUFDVGlDLFNBQVN2RSxnQkFBZ0IzRDtnRUFDekJtSSxVQUFVL0ksTUFBTXlDLE1BQU0sS0FBSztnRUFDM0J3RixXQUFVO2dFQUNWbUIsTUFBSzswRUFFSnhJLHNCQUNHLHVCQUNBOzs7Ozs7MEVBSVIsOERBQUM1QyxvSEFBTUE7Z0VBQ0w2TCxTQUFTbks7Z0VBQ1R1SSxXQUFVO2dFQUNWbUIsTUFBSzswRUFDTjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFZZiw4REFBQ2hMLG9IQUFLQTtnQkFDSjBKLHFCQUNFLDhEQUFDRTtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNySiwwTUFBZUE7NEJBQUNxSixXQUFVOzs7Ozs7c0NBQzNCLDhEQUFDSztzQ0FBSzs7Ozs7Ozs7Ozs7O2dCQUdWOEIsTUFBTXRKO2dCQUNOdUosVUFBVTtvQkFDUiw4Q0FBOEM7b0JBQzlDdEoseUJBQXlCO29CQUN6QkUsY0FBYztvQkFDZEUsY0FBYztvQkFFZCxzQ0FBc0M7b0JBQ3RDdEIsS0FBSytFLFdBQVc7b0JBQ2hCN0UsWUFBWTZFLFdBQVc7b0JBQ3ZCM0UsU0FBUyxFQUFFO29CQUNYRSxtQkFBbUI7b0JBQ25CRSxZQUFZO29CQUNaRSxlQUFlO2dCQUNqQjtnQkFDQXdILE9BQU87Z0JBQ1B1QyxRQUFRO2dCQUNSckMsV0FBVTtnQkFDVnNDLFFBQVE7a0NBQ04sOERBQUN2TSxvSEFBTUE7d0JBRUw2TCxTQUFTOzRCQUNQLDhDQUE4Qzs0QkFDOUM5SSx5QkFBeUI7NEJBQ3pCRSxjQUFjOzRCQUNkRSxjQUFjOzRCQUVkLHNDQUFzQzs0QkFDdEN0QixLQUFLK0UsV0FBVzs0QkFDaEI3RSxZQUFZNkUsV0FBVzs0QkFDdkIzRSxTQUFTLEVBQUU7NEJBQ1hFLG1CQUFtQjs0QkFDbkJFLFlBQVk7NEJBQ1pFLGVBQWU7d0JBQ2pCO3dCQUNBMEgsV0FBVTtrQ0FDWDt1QkFoQks7Ozs7O2tDQW1CTiw4REFBQ2pLLG9IQUFNQTt3QkFFTDJMLE1BQUs7d0JBQ0xDLG9CQUFNLDhEQUFDaEwsME1BQWVBOzs7Ozt3QkFDdEJpTCxTQUFTOzRCQUNQLGtFQUFrRTs0QkFDbEUsSUFBSTNJLFlBQVk7Z0NBQ2RDLGNBQWM7NEJBQ2hCOzRCQUNBdUU7d0JBQ0Y7d0JBQ0F1QyxXQUFVO2tDQUVUL0csYUFBYSxnQkFBZ0I7dUJBWjFCOzs7OztpQkFjUDswQkFFRCw0RUFBQzhHO29CQUFJQyxXQUFVOzhCQUNaakgsMkJBQ0MsOERBQUNnSDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQzVKLG9IQUFLQTs0QkFDSnFJLEtBQUsxRjs0QkFDTHdKLEtBQUk7NEJBQ0p2QyxXQUFVOzRCQUNWbkMsT0FBTztnQ0FBRTJFLFVBQVU7NEJBQU87Ozs7Ozs7Ozs7a0RBSTlCLDhEQUFDekM7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUM5SixvSEFBSUE7NEJBQUNpTCxNQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT25CLDhEQUFDakssaUZBQWNBO2dCQUNiTSxRQUFRNkI7Z0JBQ1I1QixTQUFTOEI7Z0JBQ1RrSixRQUFRako7Z0JBQ1JxRyxPQUFNOzs7Ozs7Ozs7Ozs7QUFJZDtHQWxpQ010STs7UUFLV3pCLG9IQUFJQSxDQUFDK0I7UUFDRS9CLG9IQUFJQSxDQUFDK0I7UUFzQnZCVix1RUFBaUJBO1FBa0ZqQkwsaUZBQXNCQTtRQW9DT0Msa0ZBQXFCQTtRQUlwREMsd0ZBQTJCQTtRQWNxQkgsOEVBQXFCQTs7O0tBcEtuRVU7QUFvaUNOLGlFQUFlQSxjQUFjQSxFQUFDIiwic291cmNlcyI6WyJFOlxcUFJPSkVDVFNcXHBvc1xccG9zZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcU2FsZXNcXFNhbGVzRm9ybVBhbmVsLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrIH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQge1xuICBGb3JtLFxuICBCdXR0b24sXG4gIFNlbGVjdCxcbiAgSW5wdXROdW1iZXIsXG4gIEVtcHR5LFxuICBTcGluLFxuICBNb2RhbCxcbiAgSW1hZ2UsXG59IGZyb20gXCJhbnRkXCI7XG5pbXBvcnQge1xuICBEZWxldGVPdXRsaW5lZCxcbiAgUGx1c091dGxpbmVkLFxuICBTaG9wcGluZ0NhcnRPdXRsaW5lZCxcbiAgU2VhcmNoT3V0bGluZWQsXG4gIExvYWRpbmdPdXRsaW5lZCxcbiAgU2hvcE91dGxpbmVkLFxuICBQcmludGVyT3V0bGluZWQsXG4gIEV5ZU91dGxpbmVkLFxuICBTY2FuT3V0bGluZWQsXG59IGZyb20gXCJAYW50LWRlc2lnbi9pY29uc1wiO1xuaW1wb3J0IHtcbiAgdXNlQ3JlYXRlU2FsZU11dGF0aW9uLFxuICBDcmVhdGVTYWxlRHRvLFxuICBDcmVhdGVTYWxlSXRlbUR0byxcbn0gZnJvbSBcIkAvcmVkdXhSVEsvc2VydmljZXMvc2FsZXNBcGlcIjtcbmltcG9ydCB7XG4gIHVzZUdldEFsbFByb2R1Y3RzUXVlcnksXG4gIFByb2R1Y3QsXG59IGZyb20gXCJAL3JlZHV4UlRLL3NlcnZpY2VzL3Byb2R1Y3RBcGlcIjtcbmltcG9ydCB7XG4gIHVzZUdldFVzZXJTdG9yZXNRdWVyeSxcbiAgdXNlR2V0VXNlckRlZmF1bHRTdG9yZVF1ZXJ5LFxufSBmcm9tIFwiQC9yZWR1eFJUSy9zZXJ2aWNlcy91c2VyU3RvcmVBcGlcIjtcbmltcG9ydCBTbGlkaW5nUGFuZWwgZnJvbSBcIkAvY29tcG9uZW50cy91aS9TbGlkaW5nUGFuZWxcIjtcbmltcG9ydCBCYXJjb2RlU2Nhbm5lciBmcm9tIFwiQC9jb21wb25lbnRzL0JhcmNvZGVTY2FubmVyL0JhcmNvZGVTY2FubmVyXCI7XG5pbXBvcnQgeyB1c2VCYXJjb2RlU2Nhbm5lciB9IGZyb20gXCJAL2hvb2tzL3VzZUJhcmNvZGVTY2FubmVyXCI7XG5pbXBvcnQgeyBzaG93TWVzc2FnZSB9IGZyb20gXCJAL3V0aWxzL3Nob3dNZXNzYWdlXCI7XG5pbXBvcnQge1xuICBnZW5lcmF0ZVJlY2VpcHRIVE1MLFxuICBnZW5lcmF0ZVJlY2VpcHRJbWFnZSxcbn0gZnJvbSBcIkAvdXRpbHMvY2xvdWRpbmFyeVV0aWxzXCI7XG5pbXBvcnQgeyBTdG9yZSB9IGZyb20gXCJAL3R5cGVzL3N0b3JlXCI7XG5pbXBvcnQgXCIuL3NhbGVzLXBhbmVscy5jc3NcIjtcbmltcG9ydCBcIi4vbW9kZXJuLXNhbGVzLmNzc1wiO1xuXG5pbnRlcmZhY2UgU2FsZXNGb3JtUGFuZWxQcm9wcyB7XG4gIGlzT3BlbjogYm9vbGVhbjtcbiAgb25DbG9zZTogKCkgPT4gdm9pZDtcbiAgb25TdWNjZXNzPzogKCkgPT4gdm9pZDtcbn1cblxuY29uc3QgU2FsZXNGb3JtUGFuZWw6IFJlYWN0LkZDPFNhbGVzRm9ybVBhbmVsUHJvcHM+ID0gKHtcbiAgaXNPcGVuLFxuICBvbkNsb3NlLFxuICBvblN1Y2Nlc3MsXG59KSA9PiB7XG4gIGNvbnN0IFtmb3JtXSA9IEZvcm0udXNlRm9ybSgpO1xuICBjb25zdCBbcHJvZHVjdEZvcm1dID0gRm9ybS51c2VGb3JtKCk7XG4gIGNvbnN0IFtpdGVtcywgc2V0SXRlbXNdID0gdXNlU3RhdGU8XG4gICAgKENyZWF0ZVNhbGVJdGVtRHRvICYgeyBwcm9kdWN0TmFtZTogc3RyaW5nIH0pW11cbiAgPihbXSk7XG4gIGNvbnN0IFtzZWxlY3RlZFByb2R1Y3QsIHNldFNlbGVjdGVkUHJvZHVjdF0gPSB1c2VTdGF0ZTxQcm9kdWN0IHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtxdWFudGl0eSwgc2V0UXVhbnRpdHldID0gdXNlU3RhdGU8bnVtYmVyPigxKTtcbiAgY29uc3QgW3RvdGFsQW1vdW50LCBzZXRUb3RhbEFtb3VudF0gPSB1c2VTdGF0ZTxudW1iZXI+KDApO1xuICBjb25zdCBbc2VhcmNoVGVybSwgc2V0U2VhcmNoVGVybV0gPSB1c2VTdGF0ZShcIlwiKTtcbiAgY29uc3QgW3NlbGVjdGVkU3RvcmUsIHNldFNlbGVjdGVkU3RvcmVdID0gdXNlU3RhdGU8U3RvcmUgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2lzR2VuZXJhdGluZ1JlY2VpcHQsIHNldElzR2VuZXJhdGluZ1JlY2VpcHRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcmVjZWlwdFByZXZpZXdWaXNpYmxlLCBzZXRSZWNlaXB0UHJldmlld1Zpc2libGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcmVjZWlwdFVybCwgc2V0UmVjZWlwdFVybF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2hhc1ByaW50ZWQsIHNldEhhc1ByaW50ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbYmFyY29kZUlucHV0LCBzZXRCYXJjb2RlSW5wdXRdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XG5cbiAgLy8gQmFyY29kZSBzY2FubmVyIGZ1bmN0aW9uYWxpdHlcbiAgY29uc3Qge1xuICAgIGlzT3BlbjogaXNTY2FubmVyT3BlbixcbiAgICBvcGVuU2Nhbm5lcixcbiAgICBjbG9zZVNjYW5uZXIsXG4gICAgaGFuZGxlQmFyY29kZVNjYW5uZWQsXG4gICAgaXNMb2FkaW5nOiBpc1NjYW5uZXJMb2FkaW5nXG4gIH0gPSB1c2VCYXJjb2RlU2Nhbm5lcih7XG4gICAgb25Qcm9kdWN0Rm91bmQ6IChwcm9kdWN0OiBQcm9kdWN0KSA9PiB7XG4gICAgICBzZXRTZWxlY3RlZFByb2R1Y3QocHJvZHVjdCk7XG4gICAgICBzZXRRdWFudGl0eSgxKTtcbiAgICAgIGlmIChwcm9kdWN0Rm9ybSkge1xuICAgICAgICBwcm9kdWN0Rm9ybS5zZXRGaWVsZHNWYWx1ZSh7XG4gICAgICAgICAgcHJvZHVjdElkOiBwcm9kdWN0LmlkLFxuICAgICAgICAgIHF1YW50aXR5OiAxXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgICAgLy8gQXV0by1hZGQgdG8gY2FydCBhZnRlciBzY2FubmluZ1xuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGhhbmRsZUFkZEl0ZW0oKTtcbiAgICAgIH0sIDEwMCk7XG4gICAgfSxcbiAgICBvblByb2R1Y3ROb3RGb3VuZDogKGJhcmNvZGU6IHN0cmluZykgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ1Byb2R1Y3Qgbm90IGZvdW5kIGZvciBiYXJjb2RlOicsIGJhcmNvZGUpO1xuICAgIH1cbiAgfSk7XG5cbiAgLy8gSGFuZGxlIGhhcmR3YXJlIGJhcmNvZGUgc2Nhbm5lciBpbnB1dFxuICBjb25zdCBoYW5kbGVCYXJjb2RlSW5wdXRDaGFuZ2UgPSBhc3luYyAodmFsdWU6IHN0cmluZykgPT4ge1xuICAgIHNldEJhcmNvZGVJbnB1dCh2YWx1ZSk7XG5cbiAgICAvLyBJZiBpbnB1dCBsb29rcyBsaWtlIGEgYmFyY29kZSAodHlwaWNhbGx5IDgrIGNoYXJhY3RlcnMpLCB0cnkgdG8gZmluZCBwcm9kdWN0XG4gICAgaWYgKHZhbHVlICYmIHZhbHVlLmxlbmd0aCA+PSA4KSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zb2xlLmxvZygnU2VhcmNoaW5nIGZvciBwcm9kdWN0IHdpdGggYmFyY29kZTonLCB2YWx1ZSk7XG5cbiAgICAgICAgLy8gVXNlIHRoZSBleGlzdGluZyBwcm9kdWN0cyBkYXRhIHRvIHNlYXJjaCBmb3IgYmFyY29kZVxuICAgICAgICBjb25zdCBwcm9kdWN0cyA9IHByb2R1Y3RzRGF0YT8uZGF0YT8ucHJvZHVjdHMgfHwgW107XG5cbiAgICAgICAgLy8gTG9vayBmb3IgZXhhY3QgYmFyY29kZSBtYXRjaCBmaXJzdFxuICAgICAgICBsZXQgZm91bmRQcm9kdWN0ID0gcHJvZHVjdHMuZmluZChwcm9kdWN0ID0+XG4gICAgICAgICAgcHJvZHVjdC5iYXJjb2RlID09PSB2YWx1ZS50cmltKClcbiAgICAgICAgKTtcblxuICAgICAgICAvLyBJZiBubyBleGFjdCBiYXJjb2RlIG1hdGNoLCB0cnkgU0tVXG4gICAgICAgIGlmICghZm91bmRQcm9kdWN0KSB7XG4gICAgICAgICAgZm91bmRQcm9kdWN0ID0gcHJvZHVjdHMuZmluZChwcm9kdWN0ID0+XG4gICAgICAgICAgICBwcm9kdWN0LnNrdSA9PT0gdmFsdWUudHJpbSgpXG4gICAgICAgICAgKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChmb3VuZFByb2R1Y3QpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygnUHJvZHVjdCBmb3VuZCB2aWEgaGFyZHdhcmUgc2Nhbm5lcjonLCBmb3VuZFByb2R1Y3QpO1xuICAgICAgICAgIHNldFNlbGVjdGVkUHJvZHVjdChmb3VuZFByb2R1Y3QpO1xuICAgICAgICAgIHNldFF1YW50aXR5KDEpO1xuICAgICAgICAgIGlmIChwcm9kdWN0Rm9ybSkge1xuICAgICAgICAgICAgcHJvZHVjdEZvcm0uc2V0RmllbGRzVmFsdWUoe1xuICAgICAgICAgICAgICBwcm9kdWN0SWQ6IGZvdW5kUHJvZHVjdC5pZCxcbiAgICAgICAgICAgICAgcXVhbnRpdHk6IDFcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH1cbiAgICAgICAgICAvLyBDbGVhciB0aGUgYmFyY29kZSBpbnB1dFxuICAgICAgICAgIHNldEJhcmNvZGVJbnB1dCgnJyk7XG4gICAgICAgICAgLy8gQXV0by1hZGQgdG8gY2FydFxuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgaGFuZGxlQWRkSXRlbSgpO1xuICAgICAgICAgIH0sIDEwMCk7XG4gICAgICAgICAgc2hvd01lc3NhZ2UoJ3N1Y2Nlc3MnLCBgUHJvZHVjdCBmb3VuZDogJHtmb3VuZFByb2R1Y3QubmFtZX1gKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygnTm8gcHJvZHVjdCBmb3VuZCBmb3IgYmFyY29kZTonLCB2YWx1ZSk7XG4gICAgICAgICAgc2hvd01lc3NhZ2UoJ3dhcm5pbmcnLCBgTm8gcHJvZHVjdCBmb3VuZCBmb3IgYmFyY29kZTogJHt2YWx1ZX1gKTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2VhcmNoaW5nIGZvciBwcm9kdWN0OicsIGVycm9yKTtcbiAgICAgICAgc2hvd01lc3NhZ2UoJ2Vycm9yJywgJ0Vycm9yIHNlYXJjaGluZyBmb3IgcHJvZHVjdCcpO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICAvLyBEZWJ1ZyBzdGF0ZSBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc29sZS5sb2coXCJJdGVtcyBzdGF0ZSBjaGFuZ2VkOlwiLCBpdGVtcyk7XG4gIH0sIFtpdGVtc10pO1xuXG5cbiAgY29uc3Qge1xuICAgIGRhdGE6IHByb2R1Y3RzRGF0YSxcbiAgICBpc0xvYWRpbmc6IGlzTG9hZGluZ1Byb2R1Y3RzLFxuICAgIHJlZmV0Y2g6IHJlZmV0Y2hQcm9kdWN0cyxcbiAgfSA9IHVzZUdldEFsbFByb2R1Y3RzUXVlcnkoXG4gICAge1xuICAgICAgcGFnZTogMSxcbiAgICAgIGxpbWl0OiAxMDAwLCAvLyBJbmNyZWFzZWQgbGltaXQgdG8gZ2V0IG1vcmUgcHJvZHVjdHMgZm9yIHNhbGVzXG4gICAgICBzZWFyY2g6IHNlYXJjaFRlcm0sXG4gICAgfSxcbiAgICB7XG4gICAgICAvLyBGb3JjZSByZWZldGNoIHdoZW4gY29tcG9uZW50IG1vdW50cyBhbmQgd2hlbiBwYW5lbCBvcGVuc1xuICAgICAgcmVmZXRjaE9uTW91bnRPckFyZ0NoYW5nZTogdHJ1ZSxcbiAgICAgIHJlZmV0Y2hPbkZvY3VzOiBmYWxzZSxcbiAgICAgIHJlZmV0Y2hPblJlY29ubmVjdDogdHJ1ZSwgLy8gUmVmZXRjaCB3aGVuIHJlY29ubmVjdGluZ1xuICAgIH0sXG4gICk7XG5cbiAgLy8gRGVidWcgcHJvZHVjdHMgZGF0YVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChwcm9kdWN0c0RhdGEpIHtcbiAgICAgIGNvbnNvbGUubG9nKFwi8J+bkiBQcm9kdWN0cyBsb2FkZWQ6XCIsIHtcbiAgICAgICAgdG90YWw6IHByb2R1Y3RzRGF0YS5kYXRhPy50b3RhbCB8fCAwLFxuICAgICAgICBwcm9kdWN0c0NvdW50OiBwcm9kdWN0c0RhdGEuZGF0YT8ucHJvZHVjdHM/Lmxlbmd0aCB8fCAwLFxuICAgICAgICBpc0xvYWRpbmc6IGlzTG9hZGluZ1Byb2R1Y3RzLFxuICAgICAgfSk7XG4gICAgfVxuICB9LCBbcHJvZHVjdHNEYXRhLCBpc0xvYWRpbmdQcm9kdWN0c10pO1xuXG4gIC8vIEdldCBjdXJyZW50IHVzZXIgSUQgZnJvbSBhdXRoIHN0YXRlXG4gIGNvbnN0IGdldEN1cnJlbnRVc2VySWQgPSAoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgIC8vIEB0cy1pZ25vcmUgLSBSZWR1eCBzdGF0ZSBpcyBleHBvc2VkIGZvciBkZWJ1Z2dpbmdcbiAgICAgIGNvbnN0IHN0YXRlID0gd2luZG93Ll9fUkVEVVhfU1RBVEU7XG4gICAgICByZXR1cm4gc3RhdGU/LmF1dGg/LnVzZXI/LmlkIHx8IDA7XG4gICAgfVxuICAgIHJldHVybiAwO1xuICB9O1xuXG4gIC8vIEZldGNoIHVzZXIgc3RvcmVzXG4gIGNvbnN0IHsgZGF0YTogdXNlclN0b3Jlc0RhdGEgfSA9IHVzZUdldFVzZXJTdG9yZXNRdWVyeShnZXRDdXJyZW50VXNlcklkKCkpO1xuXG4gIC8vIEZldGNoIGRlZmF1bHQgc3RvcmVcbiAgY29uc3QgeyBkYXRhOiBkZWZhdWx0U3RvcmVEYXRhIH0gPVxuICAgIHVzZUdldFVzZXJEZWZhdWx0U3RvcmVRdWVyeShnZXRDdXJyZW50VXNlcklkKCkpO1xuXG4gIC8vIFNldCBkZWZhdWx0IHN0b3JlIHdoZW4gZGF0YSBpcyBsb2FkZWRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoZGVmYXVsdFN0b3JlRGF0YT8uZGF0YSkge1xuICAgICAgc2V0U2VsZWN0ZWRTdG9yZShkZWZhdWx0U3RvcmVEYXRhLmRhdGEpO1xuICAgICAgZm9ybS5zZXRGaWVsZHNWYWx1ZSh7IHN0b3JlSWQ6IGRlZmF1bHRTdG9yZURhdGEuZGF0YS5pZCB9KTtcbiAgICB9IGVsc2UgaWYgKHVzZXJTdG9yZXNEYXRhPy5kYXRhICYmIHVzZXJTdG9yZXNEYXRhLmRhdGEubGVuZ3RoID4gMCkge1xuICAgICAgc2V0U2VsZWN0ZWRTdG9yZSh1c2VyU3RvcmVzRGF0YS5kYXRhWzBdKTtcbiAgICAgIGZvcm0uc2V0RmllbGRzVmFsdWUoeyBzdG9yZUlkOiB1c2VyU3RvcmVzRGF0YS5kYXRhWzBdLmlkIH0pO1xuICAgIH1cbiAgfSwgW2RlZmF1bHRTdG9yZURhdGEsIHVzZXJTdG9yZXNEYXRhLCBmb3JtXSk7XG5cbiAgLy8gQ3JlYXRlIHNhbGUgbXV0YXRpb25cbiAgY29uc3QgW2NyZWF0ZVNhbGUsIHsgaXNMb2FkaW5nOiBpc1N1Ym1pdHRpbmcgfV0gPSB1c2VDcmVhdGVTYWxlTXV0YXRpb24oKTtcblxuICAvLyBDYWxjdWxhdGUgdG90YWwgYW1vdW50IHdoZW5ldmVyIGl0ZW1zIGNoYW5nZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpdGVtcyAmJiBpdGVtcy5sZW5ndGggPiAwKSB7XG4gICAgICBjb25zdCB0b3RhbCA9IGl0ZW1zLnJlZHVjZShcbiAgICAgICAgKHN1bSwgaXRlbSkgPT4gc3VtICsgaXRlbS5wcmljZSAqIGl0ZW0ucXVhbnRpdHksXG4gICAgICAgIDAsXG4gICAgICApO1xuICAgICAgc2V0VG90YWxBbW91bnQodG90YWwpO1xuICAgICAgaWYgKGZvcm0pIHtcbiAgICAgICAgZm9ybS5zZXRGaWVsZHNWYWx1ZSh7IHRvdGFsQW1vdW50OiB0b3RhbCB9KTtcbiAgICAgIH1cblxuICAgICAgLy8gRGVidWcgbG9nIHRvIGNoZWNrIGl0ZW1zIHN0YXRlXG4gICAgICBjb25zb2xlLmxvZyhcIkN1cnJlbnQgaXRlbXMgaW4gdXNlRWZmZWN0OlwiLCBpdGVtcyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHNldFRvdGFsQW1vdW50KDApO1xuICAgICAgaWYgKGZvcm0pIHtcbiAgICAgICAgZm9ybS5zZXRGaWVsZHNWYWx1ZSh7IHRvdGFsQW1vdW50OiAwIH0pO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW2l0ZW1zLCBmb3JtXSk7XG5cbiAgLy8gSGFuZGxlIHBhbmVsIG9wZW4vY2xvc2VcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaXNPcGVuKSB7XG4gICAgICAvLyBXaGVuIHBhbmVsIG9wZW5zLCBlbnN1cmUgd2UgaGF2ZSBmcmVzaCBwcm9kdWN0IGRhdGFcbiAgICAgIGNvbnNvbGUubG9nKFwi8J+bkiBTYWxlcyBwYW5lbCBvcGVuZWQgLSBmZXRjaGluZyBmcmVzaCBwcm9kdWN0IGRhdGFcIik7XG4gICAgICByZWZldGNoUHJvZHVjdHMoKTtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gUmVzZXQgZm9ybXMgd2hlbiBwYW5lbCBpcyBjbG9zZWRcbiAgICAgIGlmIChmb3JtKSB7XG4gICAgICAgIGZvcm0ucmVzZXRGaWVsZHMoKTtcbiAgICAgIH1cbiAgICAgIGlmIChwcm9kdWN0Rm9ybSkge1xuICAgICAgICBwcm9kdWN0Rm9ybS5yZXNldEZpZWxkcygpO1xuICAgICAgfVxuICAgICAgc2V0SXRlbXMoW10pO1xuICAgICAgc2V0U2VsZWN0ZWRQcm9kdWN0KG51bGwpO1xuICAgICAgc2V0UXVhbnRpdHkoMSk7XG4gICAgICBzZXRUb3RhbEFtb3VudCgwKTtcbiAgICAgIHNldFJlY2VpcHRVcmwobnVsbCk7XG4gICAgICBzZXRSZWNlaXB0UHJldmlld1Zpc2libGUoZmFsc2UpO1xuICAgICAgc2V0SGFzUHJpbnRlZChmYWxzZSk7XG4gICAgfVxuICB9LCBbaXNPcGVuLCBmb3JtLCByZWZldGNoUHJvZHVjdHNdKTtcblxuICAvLyBIYW5kbGUgYWRkaW5nIGFuIGl0ZW0gdG8gdGhlIHNhbGVcbiAgY29uc3QgaGFuZGxlQWRkSXRlbSA9ICgpID0+IHtcbiAgICBpZiAoIXNlbGVjdGVkUHJvZHVjdCkge1xuICAgICAgc2hvd01lc3NhZ2UoXCJlcnJvclwiLCBcIlBsZWFzZSBzZWxlY3QgYSBwcm9kdWN0XCIpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGlmIChxdWFudGl0eSA8PSAwKSB7XG4gICAgICBzaG93TWVzc2FnZShcImVycm9yXCIsIFwiUXVhbnRpdHkgbXVzdCBiZSBncmVhdGVyIHRoYW4gMFwiKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBpZiAoc2VsZWN0ZWRQcm9kdWN0LnN0b2NrUXVhbnRpdHkgPCBxdWFudGl0eSkge1xuICAgICAgc2hvd01lc3NhZ2UoXG4gICAgICAgIFwiZXJyb3JcIixcbiAgICAgICAgYE9ubHkgJHtzZWxlY3RlZFByb2R1Y3Quc3RvY2tRdWFudGl0eX0gdW5pdHMgYXZhaWxhYmxlIGluIHN0b2NrYCxcbiAgICAgICk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coXCJBZGRpbmcgaXRlbSB3aXRoIHByb2R1Y3Q6XCIsIHNlbGVjdGVkUHJvZHVjdCk7XG5cbiAgICAvLyBDaGVjayBpZiBwcm9kdWN0IGFscmVhZHkgZXhpc3RzIGluIGl0ZW1zXG4gICAgY29uc3QgZXhpc3RpbmdJdGVtSW5kZXggPSBpdGVtcy5maW5kSW5kZXgoXG4gICAgICAoaXRlbSkgPT4gaXRlbS5wcm9kdWN0SWQgPT09IHNlbGVjdGVkUHJvZHVjdC5pZCxcbiAgICApO1xuXG4gICAgaWYgKGV4aXN0aW5nSXRlbUluZGV4ID49IDApIHtcbiAgICAgIC8vIFVwZGF0ZSBleGlzdGluZyBpdGVtXG4gICAgICBjb25zdCB1cGRhdGVkSXRlbXMgPSBbLi4uaXRlbXNdO1xuICAgICAgY29uc3QgbmV3UXVhbnRpdHkgPSB1cGRhdGVkSXRlbXNbZXhpc3RpbmdJdGVtSW5kZXhdLnF1YW50aXR5ICsgcXVhbnRpdHk7XG5cbiAgICAgIGlmIChuZXdRdWFudGl0eSA+IHNlbGVjdGVkUHJvZHVjdC5zdG9ja1F1YW50aXR5KSB7XG4gICAgICAgIHNob3dNZXNzYWdlKFxuICAgICAgICAgIFwiZXJyb3JcIixcbiAgICAgICAgICBgQ2Fubm90IGFkZCBtb3JlIHRoYW4gJHtzZWxlY3RlZFByb2R1Y3Quc3RvY2tRdWFudGl0eX0gdW5pdHMgb2YgdGhpcyBwcm9kdWN0YCxcbiAgICAgICAgKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICB1cGRhdGVkSXRlbXNbZXhpc3RpbmdJdGVtSW5kZXhdLnF1YW50aXR5ID0gbmV3UXVhbnRpdHk7XG5cbiAgICAgIC8vIE1ha2Ugc3VyZSB0aGUgcHJvZHVjdCBuYW1lIGlzIHNldFxuICAgICAgaWYgKCF1cGRhdGVkSXRlbXNbZXhpc3RpbmdJdGVtSW5kZXhdLnByb2R1Y3ROYW1lKSB7XG4gICAgICAgIHVwZGF0ZWRJdGVtc1tleGlzdGluZ0l0ZW1JbmRleF0ucHJvZHVjdE5hbWUgPSBzZWxlY3RlZFByb2R1Y3QubmFtZTtcbiAgICAgIH1cblxuICAgICAgLy8gVXBkYXRlIHRoZSBzdGF0ZSB3aXRoIHRoZSBuZXcgYXJyYXlcbiAgICAgIGNvbnNvbGUubG9nKFwiVXBkYXRpbmcgZXhpc3RpbmcgaXRlbS4gTmV3IGl0ZW1zIGFycmF5OlwiLCB1cGRhdGVkSXRlbXMpO1xuICAgICAgc2V0SXRlbXMoWy4uLnVwZGF0ZWRJdGVtc10pOyAvLyBDcmVhdGUgYSBuZXcgYXJyYXkgcmVmZXJlbmNlIHRvIGZvcmNlIHJlLXJlbmRlclxuXG4gICAgICAvLyBTaG93IHN1Y2Nlc3MgbWVzc2FnZVxuICAgICAgc2hvd01lc3NhZ2UoXCJzdWNjZXNzXCIsIGBVcGRhdGVkIHF1YW50aXR5IG9mICR7c2VsZWN0ZWRQcm9kdWN0Lm5hbWV9YCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIEFkZCBuZXcgaXRlbVxuICAgICAgY29uc3QgbmV3SXRlbSA9IHtcbiAgICAgICAgcHJvZHVjdElkOiBzZWxlY3RlZFByb2R1Y3QuaWQsXG4gICAgICAgIHByb2R1Y3ROYW1lOiBzZWxlY3RlZFByb2R1Y3QubmFtZSwgLy8gTWFrZSBzdXJlIHRoaXMgaXMgY29ycmVjdGx5IHNldFxuICAgICAgICBxdWFudGl0eSxcbiAgICAgICAgcHJpY2U6XG4gICAgICAgICAgdHlwZW9mIHNlbGVjdGVkUHJvZHVjdC5wcmljZSA9PT0gXCJzdHJpbmdcIlxuICAgICAgICAgICAgPyBwYXJzZUZsb2F0KHNlbGVjdGVkUHJvZHVjdC5wcmljZSlcbiAgICAgICAgICAgIDogc2VsZWN0ZWRQcm9kdWN0LnByaWNlLFxuICAgICAgfTtcblxuICAgICAgLy8gQ3JlYXRlIGEgbmV3IGFycmF5IHdpdGggdGhlIG5ldyBpdGVtXG4gICAgICBjb25zdCBuZXdJdGVtcyA9IFsuLi5pdGVtcywgbmV3SXRlbV07XG5cbiAgICAgIC8vIFVwZGF0ZSB0aGUgc3RhdGUgd2l0aCB0aGUgbmV3IGFycmF5XG4gICAgICBjb25zb2xlLmxvZyhcIkFkZGluZyBuZXcgaXRlbS4gTmV3IGl0ZW1zIGFycmF5OlwiLCBuZXdJdGVtcyk7XG4gICAgICBzZXRJdGVtcyhuZXdJdGVtcyk7IC8vIFRoaXMgc2hvdWxkIHRyaWdnZXIgYSByZS1yZW5kZXJcblxuICAgICAgLy8gU2hvdyBzdWNjZXNzIG1lc3NhZ2VcbiAgICAgIHNob3dNZXNzYWdlKFxuICAgICAgICBcInN1Y2Nlc3NcIixcbiAgICAgICAgYEFkZGVkICR7cXVhbnRpdHl9ICR7c2VsZWN0ZWRQcm9kdWN0Lm5hbWV9IHRvIHNhbGVgLFxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBSZXNldCBzZWxlY3Rpb25cbiAgICBzZXRTZWxlY3RlZFByb2R1Y3QobnVsbCk7XG4gICAgc2V0UXVhbnRpdHkoMSk7XG4gICAgaWYgKHByb2R1Y3RGb3JtKSB7XG4gICAgICBwcm9kdWN0Rm9ybS5zZXRGaWVsZHNWYWx1ZSh7IHByb2R1Y3RJZDogdW5kZWZpbmVkLCBxdWFudGl0eTogMSB9KTtcbiAgICB9XG4gIH07XG5cbiAgLy8gSGFuZGxlIHJlbW92aW5nIGFuIGl0ZW0gZnJvbSB0aGUgc2FsZVxuICBjb25zdCBoYW5kbGVSZW1vdmVJdGVtID0gKGluZGV4OiBudW1iZXIpID0+IHtcbiAgICBjb25zdCB1cGRhdGVkSXRlbXMgPSBbLi4uaXRlbXNdO1xuICAgIHVwZGF0ZWRJdGVtcy5zcGxpY2UoaW5kZXgsIDEpO1xuICAgIHNldEl0ZW1zKHVwZGF0ZWRJdGVtcyk7XG4gIH07XG5cbiAgLy8gSGFuZGxlIHByaW50aW5nIHJlY2VpcHQgLSBkaXJlY3RseSB0cmlnZ2VyIHByaW50IGRpYWxvZ1xuICBjb25zdCBoYW5kbGVQcmludFJlY2VpcHQgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG5cbiAgICBpZiAoIXJlY2VpcHRVcmwgfHwgaGFzUHJpbnRlZCkge1xuICAgICAgY29uc29sZS5sb2coXG4gICAgICAgIFwiU2tpcHBpbmcgcHJpbnQ6IFwiLFxuICAgICAgICAhcmVjZWlwdFVybCA/IFwiTm8gcmVjZWlwdCBVUkxcIiA6IFwiQWxyZWFkeSBwcmludGVkXCIsXG4gICAgICApO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKFwiUHJpbnRpbmcgcmVjZWlwdDpcIiwgcmVjZWlwdFVybCk7XG5cbiAgICAvLyBNYXJrIGFzIHByaW50ZWQgaW1tZWRpYXRlbHkgdG8gcHJldmVudCBtdWx0aXBsZSBwcmludCBkaWFsb2dzXG4gICAgc2V0SGFzUHJpbnRlZCh0cnVlKTtcblxuICAgIC8vIENyZWF0ZSBhIGhpZGRlbiBpZnJhbWUgdG8gbG9hZCB0aGUgaW1hZ2VcbiAgICBjb25zdCBpZnJhbWUgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KFwiaWZyYW1lXCIpO1xuICAgIGlmcmFtZS5zdHlsZS5kaXNwbGF5ID0gXCJub25lXCI7XG4gICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChpZnJhbWUpO1xuXG4gICAgLy8gU2V0IHVwIHRoZSBpZnJhbWUgY29udGVudCB3aXRoIHRoZSBpbWFnZSBhbmQgcHJpbnQgQ1NTXG4gICAgaWZyYW1lLm9ubG9hZCA9ICgpID0+IHtcbiAgICAgIGlmIChpZnJhbWUuY29udGVudFdpbmRvdykge1xuICAgICAgICAvLyBXcml0ZSB0aGUgSFRNTCBjb250ZW50IHRvIHRoZSBpZnJhbWVcbiAgICAgICAgaWZyYW1lLmNvbnRlbnRXaW5kb3cuZG9jdW1lbnQud3JpdGUoYFxuICAgICAgICAgIDwhRE9DVFlQRSBodG1sPlxuICAgICAgICAgIDxodG1sPlxuICAgICAgICAgICAgPGhlYWQ+XG4gICAgICAgICAgICAgIDx0aXRsZT5QcmludCBSZWNlaXB0PC90aXRsZT5cbiAgICAgICAgICAgICAgPHN0eWxlPlxuICAgICAgICAgICAgICAgIGJvZHkge1xuICAgICAgICAgICAgICAgICAgbWFyZ2luOiAwO1xuICAgICAgICAgICAgICAgICAgcGFkZGluZzogMDtcbiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICAgICAgICBoZWlnaHQ6IDEwMHZoO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpbWcge1xuICAgICAgICAgICAgICAgICAgbWF4LXdpZHRoOiAxMDAlO1xuICAgICAgICAgICAgICAgICAgbWF4LWhlaWdodDogMTAwdmg7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIEBtZWRpYSBwcmludCB7XG4gICAgICAgICAgICAgICAgICBib2R5IHtcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luOiAwO1xuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAwO1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgaW1nIHtcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgICAgICAgICAgICAgIGhlaWdodDogYXV0bztcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIDwvc3R5bGU+XG4gICAgICAgICAgICA8L2hlYWQ+XG4gICAgICAgICAgICA8Ym9keT5cbiAgICAgICAgICAgICAgPGltZyBzcmM9XCIke3JlY2VpcHRVcmx9XCIgYWx0PVwiUmVjZWlwdFwiIC8+XG4gICAgICAgICAgICA8L2JvZHk+XG4gICAgICAgICAgPC9odG1sPlxuICAgICAgICBgKTtcblxuICAgICAgICAvLyBDbG9zZSB0aGUgZG9jdW1lbnRcbiAgICAgICAgaWZyYW1lLmNvbnRlbnRXaW5kb3cuZG9jdW1lbnQuY2xvc2UoKTtcblxuICAgICAgICAvLyBVc2UgYSBzaW5nbGUgcHJpbnQgdHJpZ2dlciB3aXRoIGEgZGVsYXkgdG8gZW5zdXJlIHRoZSBpbWFnZSBpcyBsb2FkZWRcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgaWYgKGlmcmFtZS5jb250ZW50V2luZG93KSB7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAvLyBQcmludCB0aGUgaWZyYW1lIGNvbnRlbnRcbiAgICAgICAgICAgICAgaWZyYW1lLmNvbnRlbnRXaW5kb3cuZm9jdXMoKTtcbiAgICAgICAgICAgICAgaWZyYW1lLmNvbnRlbnRXaW5kb3cucHJpbnQoKTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHByaW50aW5nIHJlY2VpcHQ6XCIsIGUpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBSZW1vdmUgdGhlIGlmcmFtZSBhZnRlciBwcmludGluZ1xuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQoaWZyYW1lKTtcbiAgICAgICAgICAgIH0sIDEwMDApO1xuICAgICAgICAgIH1cbiAgICAgICAgfSwgNTAwKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgLy8gU2V0IHRoZSBpZnJhbWUgc291cmNlIHRvIHRyaWdnZXIgdGhlIG9ubG9hZCBldmVudFxuICAgIGlmcmFtZS5zcmMgPSBcImFib3V0OmJsYW5rXCI7XG4gIH0sIFtyZWNlaXB0VXJsLCBoYXNQcmludGVkXSk7XG5cbiAgLy8gRWZmZWN0IHRvIGF1dG9tYXRpY2FsbHkgcHJpbnQgcmVjZWlwdCB3aGVuIG1vZGFsIGlzIHNob3duXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHJlY2VpcHRQcmV2aWV3VmlzaWJsZSAmJiByZWNlaXB0VXJsICYmICFoYXNQcmludGVkKSB7XG4gICAgICAvLyBBZGQgYSBzbWFsbCBkZWxheSB0byBlbnN1cmUgdGhlIHJlY2VpcHQgaW1hZ2UgaXMgbG9hZGVkXG4gICAgICBjb25zdCB0aW1lciA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBoYW5kbGVQcmludFJlY2VpcHQoKTsgLy8gVGhpcyBub3cgaGFuZGxlcyB0aGUgaGFzUHJpbnRlZCBzdGF0ZSBpbnRlcm5hbGx5XG4gICAgICB9LCA4MDApO1xuXG4gICAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVyKTtcbiAgICB9XG4gIH0sIFtyZWNlaXB0UHJldmlld1Zpc2libGUsIHJlY2VpcHRVcmwsIGhhc1ByaW50ZWQsIGhhbmRsZVByaW50UmVjZWlwdF0pO1xuXG4gIC8vIEhhbmRsZSBmb3JtIHN1Ym1pc3Npb25cbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBpZiAoaXRlbXMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIHNob3dNZXNzYWdlKFwiZXJyb3JcIiwgXCJQbGVhc2UgYWRkIGF0IGxlYXN0IG9uZSBpdGVtIHRvIHRoZSBzYWxlXCIpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIFZhbGlkYXRlIGZvcm0gZmllbGRzXG4gICAgICBjb25zdCB2YWx1ZXMgPSBhd2FpdCBmb3JtLnZhbGlkYXRlRmllbGRzKCk7XG5cbiAgICAgIC8vIENoZWNrIGlmIHN0b3JlIGlzIHNlbGVjdGVkXG4gICAgICBpZiAoIXNlbGVjdGVkU3RvcmUpIHtcbiAgICAgICAgc2hvd01lc3NhZ2UoXG4gICAgICAgICAgXCJlcnJvclwiLFxuICAgICAgICAgIFwiTm8gc3RvcmUgaW5mb3JtYXRpb24gYXZhaWxhYmxlLiBQbGVhc2Ugc2V0IHVwIHlvdXIgc3RvcmUgaW4geW91ciBwcm9maWxlIHNldHRpbmdzLlwiLFxuICAgICAgICApO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIFNldCBsb2FkaW5nIHN0YXRlIGZvciByZWNlaXB0IGdlbmVyYXRpb25cbiAgICAgIHNldElzR2VuZXJhdGluZ1JlY2VpcHQodHJ1ZSk7XG5cbiAgICAgIC8vIEdldCBzdG9yZSBpbmZvcm1hdGlvbiBmb3IgcmVjZWlwdFxuICAgICAgY29uc3Qgc3RvcmVJbmZvID0gc2VsZWN0ZWRTdG9yZSB8fFxuICAgICAgICB1c2VyU3RvcmVzRGF0YT8uZGF0YT8uZmluZCgoc3RvcmUpID0+IHN0b3JlLmlkID09PSB2YWx1ZXMuc3RvcmVJZCkgfHwge1xuICAgICAgICAgIG5hbWU6IFwiUE9TIFN5c3RlbVwiLFxuICAgICAgICB9O1xuXG4gICAgICAvLyBHZW5lcmF0ZSByZWNlaXB0IEhUTUxcbiAgICAgIGNvbnN0IHJlY2VpcHRIVE1MID0gZ2VuZXJhdGVSZWNlaXB0SFRNTChcbiAgICAgICAge1xuICAgICAgICAgIGlkOiBEYXRlLm5vdygpLCAvLyBUZW1wb3JhcnkgSUQgdW50aWwgd2UgZ2V0IHRoZSByZWFsIG9uZVxuICAgICAgICAgIHRvdGFsQW1vdW50LFxuICAgICAgICAgIHBheW1lbnRNZXRob2Q6IHZhbHVlcy5wYXltZW50TWV0aG9kLFxuICAgICAgICAgIHRyYW5zYWN0aW9uRGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIGl0ZW1zOiBpdGVtcy5tYXAoKGl0ZW0pID0+ICh7XG4gICAgICAgICAgICBwcm9kdWN0TmFtZTogaXRlbS5wcm9kdWN0TmFtZSxcbiAgICAgICAgICAgIHF1YW50aXR5OiBpdGVtLnF1YW50aXR5LFxuICAgICAgICAgICAgcHJpY2U6IGl0ZW0ucHJpY2UsXG4gICAgICAgICAgfSkpLFxuICAgICAgICB9LFxuICAgICAgICBzdG9yZUluZm8sXG4gICAgICApO1xuXG4gICAgICAvLyBHZW5lcmF0ZSByZWNlaXB0IGltYWdlIGFuZCBnZXQgVVJMXG4gICAgICBsZXQgcmVjZWlwdFVybCA9IFwiaHR0cHM6Ly9yZWNlaXB0LmV4YW1wbGUuY29tL3BsYWNlaG9sZGVyXCI7XG4gICAgICB0cnkge1xuICAgICAgICByZWNlaXB0VXJsID0gYXdhaXQgZ2VuZXJhdGVSZWNlaXB0SW1hZ2UocmVjZWlwdEhUTUwpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkZhaWxlZCB0byBnZW5lcmF0ZSByZWNlaXB0IGltYWdlOlwiLCBlcnJvcik7XG4gICAgICAgIC8vIENvbnRpbnVlIHdpdGggcGxhY2Vob2xkZXIgVVJMIGlmIGltYWdlIGdlbmVyYXRpb24gZmFpbHNcbiAgICAgIH1cblxuICAgICAgY29uc3Qgc2FsZURhdGE6IENyZWF0ZVNhbGVEdG8gPSB7XG4gICAgICAgIHRvdGFsQW1vdW50LFxuICAgICAgICBwYXltZW50TWV0aG9kOiB2YWx1ZXMucGF5bWVudE1ldGhvZCxcbiAgICAgICAgaXRlbXM6IGl0ZW1zLm1hcCgoaXRlbSkgPT4gKHtcbiAgICAgICAgICBwcm9kdWN0SWQ6IGl0ZW0ucHJvZHVjdElkLFxuICAgICAgICAgIHF1YW50aXR5OiBpdGVtLnF1YW50aXR5LFxuICAgICAgICAgIHByaWNlOiBpdGVtLnByaWNlLFxuICAgICAgICB9KSksXG4gICAgICAgIHJlY2VpcHRVcmwsXG4gICAgICAgIHN0b3JlSWQ6IHNlbGVjdGVkU3RvcmU/LmlkLFxuICAgICAgfTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjcmVhdGVTYWxlKHNhbGVEYXRhKS51bndyYXAoKTtcblxuICAgICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2hvd01lc3NhZ2UoXCJzdWNjZXNzXCIsIFwiU2FsZSBjcmVhdGVkIHN1Y2Nlc3NmdWxseVwiKTtcblxuICAgICAgICAvLyBTdG9yZSB0aGUgcmVjZWlwdCBVUkwgZm9yIHByZXZpZXdcbiAgICAgICAgc2V0UmVjZWlwdFVybChyZWNlaXB0VXJsKTtcblxuICAgICAgICAvLyBTaG93IHJlY2VpcHQgcHJldmlldyBtb2RhbCBhbmQgb2ZmZXIgcHJpbnQgb3B0aW9uXG4gICAgICAgIHNldFJlY2VpcHRQcmV2aWV3VmlzaWJsZSh0cnVlKTtcblxuXG5cbiAgICAgICAgLy8gUmVmcmVzaCBwcm9kdWN0IGRhdGEgdG8gZ2V0IHVwZGF0ZWQgc3RvY2sgcXVhbnRpdGllc1xuICAgICAgICByZWZldGNoUHJvZHVjdHMoKTtcblxuICAgICAgICAvLyBUcmlnZ2VyIHRoZSBzdWNjZXNzIGNhbGxiYWNrIHRvIHJlZnJlc2ggdGhlIGxpc3QgV0lUSE9VVCBjbG9zaW5nIHRoZSBwYW5lbFxuXG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgIGlmIChvblN1Y2Nlc3MpIHtcbiAgICAgICAgICAgIC8vIENhbGwgcmVmZXRjaCBkaXJlY3RseSBpbnN0ZWFkIG9mIGNsb3NpbmcgdGhlIHBhbmVsXG4gICAgICAgICAgICByZWZldGNoUHJvZHVjdHMoKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0sIDMwMCk7XG5cbiAgICAgICAgLy8gS2VlcCB0aGUgcGFuZWwgb3BlbiB1bnRpbCB0aGUgdXNlciBleHBsaWNpdGx5IGNsb3NlcyBpdFxuICAgICAgICAvLyBUaGlzIGVuc3VyZXMgdGhlIHJlY2VpcHQgbW9kYWwgc3RheXMgdmlzaWJsZVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2hvd01lc3NhZ2UoXCJlcnJvclwiLCByZXNwb25zZS5tZXNzYWdlIHx8IFwiRmFpbGVkIHRvIGNyZWF0ZSBzYWxlXCIpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIHNob3dNZXNzYWdlKFxuICAgICAgICBcImVycm9yXCIsXG4gICAgICAgIGVycm9yLmRhdGE/Lm1lc3NhZ2UgfHwgXCJBbiBlcnJvciBvY2N1cnJlZCB3aGlsZSBjcmVhdGluZyB0aGUgc2FsZVwiLFxuICAgICAgKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNHZW5lcmF0aW5nUmVjZWlwdChmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIERlYnVnIGxvZyB0byBjaGVjayBpdGVtcyBzdGF0ZSB3aGVuIHJlbmRlcmluZ1xuICBjb25zb2xlLmxvZyhcIlJlbmRlcmluZyB3aXRoIGl0ZW1zOlwiLCBpdGVtcyk7XG5cbiAgcmV0dXJuIChcbiAgICA8U2xpZGluZ1BhbmVsXG4gICAgICB0aXRsZT1cIlBvaW50IG9mIFNhbGVcIlxuICAgICAgaXNPcGVuPXtpc09wZW59XG4gICAgICBvbkNsb3NlPXtvbkNsb3NlfVxuICAgICAgd2lkdGg9XCI5NSVcIlxuICAgID5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic2FsZXMtZm9ybSBtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1zbGF0ZS01MCB0by1ibHVlLTUwXCI+XG4gICAgICAgIHsvKiBNb2Rlcm4gUE9TIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gYm9yZGVyLWIgYm9yZGVyLXNsYXRlLTIwMCBiZy13aGl0ZS84MCBiYWNrZHJvcC1ibHVyLXNtIHB4LTYgcHktNCBzaGFkb3ctc21cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHctMTAgaC0xMCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAgdG8taW5kaWdvLTYwMCByb3VuZGVkLXhsIG1yLTNcIj5cbiAgICAgICAgICAgICAgPFNob3BwaW5nQ2FydE91dGxpbmVkIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJtLTAgdGV4dC14bCBmb250LWJvbGQgdGV4dC1zbGF0ZS04MDBcIj5cbiAgICAgICAgICAgICAgICBOZXcgVHJhbnNhY3Rpb25cbiAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXNsYXRlLTUwMCBtLTBcIj5Qb2ludCBvZiBTYWxlIFN5c3RlbTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXNsYXRlLTUwMCBtLTBcIj5Ub3RhbCBBbW91bnQ8L3A+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCBiZy1ncmFkaWVudC10by1yIGZyb20tZW1lcmFsZC02MDAgdG8tZ3JlZW4tNjAwIGJnLWNsaXAtdGV4dCB0ZXh0LXRyYW5zcGFyZW50XCI+XG4gICAgICAgICAgICAgIEdIUyB7dG90YWxBbW91bnQudG9GaXhlZCgyKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBnYXAtNiBsZzpncmlkLWNvbHMtM1wiPlxuICAgICAgICAgICAgey8qIExlZnQgQ29sdW1uIC0gUHJvZHVjdCBTZWxlY3Rpb24gKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02IHJvdW5kZWQtMnhsIGJvcmRlciBib3JkZXItc2xhdGUtMjAwIGJnLXdoaXRlLzcwIGJhY2tkcm9wLWJsdXItc20gcC02IHNoYWRvdy14bCBzaGFkb3ctc2xhdGUtMjAwLzUwXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHctOCBoLTggYmctZ3JhZGllbnQtdG8tciBmcm9tLXB1cnBsZS01MDAgdG8tcGluay01MDAgcm91bmRlZC1sZyBtci0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFNlYXJjaE91dGxpbmVkIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LXNsYXRlLTgwMCBtLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIFByb2R1Y3QgU2VsZWN0aW9uXG4gICAgICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc2xhdGUtNTAwIG0tMFwiPkNob29zZSBwcm9kdWN0cyBmb3IgdGhpcyB0cmFuc2FjdGlvbjwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1zbSB0ZXh0LXNsYXRlLTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtci0xIHRleHQtcmVkLTUwMFwiPio8L3NwYW4+IFJlcXVpcmVkIGZpZWxkc1xuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8Rm9ybVxuICAgICAgICAgICAgICAgICAgZm9ybT17cHJvZHVjdEZvcm19XG4gICAgICAgICAgICAgICAgICBsYXlvdXQ9XCJ2ZXJ0aWNhbFwiXG4gICAgICAgICAgICAgICAgICBpbml0aWFsVmFsdWVzPXt7XG4gICAgICAgICAgICAgICAgICAgIHF1YW50aXR5OiAxLFxuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInByb2R1Y3QtZm9ybVwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGdhcC02IG1kOmdyaWQtY29scy00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWQ6Y29sLXNwYW4tM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJwcm9kdWN0SWRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9e1xuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTcwMCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFByb2R1Y3QgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwXCI+Kjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWItNFwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdFxuICAgICAgICAgICAgICAgICAgICAgICAgICBzaG93U2VhcmNoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0xvYWRpbmdQcm9kdWN0c1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcIkxvYWRpbmcgcHJvZHVjdHMuLi5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcIvCflI0gU2VhcmNoIGFuZCBzZWxlY3QgYSBwcm9kdWN0Li4uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25GaWx0ZXJQcm9wPVwiY2hpbGRyZW5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBsb2FkaW5nPXtpc0xvYWRpbmdQcm9kdWN0c31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZ1Byb2R1Y3RzfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcHJvZHVjdCA9IHByb2R1Y3RzRGF0YT8uZGF0YT8ucHJvZHVjdHMuZmluZChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChwKSA9PiBwLmlkID09PSB2YWx1ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiU2VsZWN0ZWQgcHJvZHVjdDpcIiwgcHJvZHVjdCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHByb2R1Y3QpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIE1ha2UgYSBkZWVwIGNvcHkgdG8gYXZvaWQgcmVmZXJlbmNlIGlzc3Vlc1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRQcm9kdWN0KHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJvZHVjdCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gRW5zdXJlIHByaWNlIGlzIHByb3Blcmx5IGZvcm1hdHRlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcmljZTpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlb2YgcHJvZHVjdC5wcmljZSA9PT0gXCJzdHJpbmdcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBwcm9kdWN0LnByaWNlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFN0cmluZyhwcm9kdWN0LnByaWNlKSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZFByb2R1Y3QobnVsbCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvblNlYXJjaD17c2V0U2VhcmNoVGVybX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsdGVyT3B0aW9uPXtmYWxzZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibW9kZXJuLXNlbGVjdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJsYXJnZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN1ZmZpeEljb249e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzTG9hZGluZ1Byb2R1Y3RzID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExvYWRpbmdPdXRsaW5lZCBzcGluIGNsYXNzTmFtZT1cInRleHQtYmx1ZS01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLWdyYWRpZW50LXRvLXIgbXItMyBtdC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWFyY2hPdXRsaW5lZCBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTQwMCBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG5vdEZvdW5kQ29udGVudD17XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNMb2FkaW5nUHJvZHVjdHMgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNwaW4gc2l6ZT1cInNtYWxsXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMiB0ZXh0LXNsYXRlLTUwMFwiPkxvYWRpbmcgcHJvZHVjdHMuLi48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS00IHRleHQtc2xhdGUtNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE5vIHByb2R1Y3RzIGZvdW5kXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb2R1Y3RzRGF0YT8uZGF0YT8ucHJvZHVjdHM/Lm1hcCgocHJvZHVjdCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3QuT3B0aW9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e3Byb2R1Y3QuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17cHJvZHVjdC5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtwcm9kdWN0LnN0b2NrUXVhbnRpdHkgPD0gMH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1zbGF0ZS04MDBcIj57cHJvZHVjdC5uYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXNsYXRlLTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgR0hTIHtOdW1iZXIocHJvZHVjdC5wcmljZSkudG9GaXhlZCgyKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdGV4dC1zbSBweC0yIHB5LTEgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvZHVjdC5zdG9ja1F1YW50aXR5IDw9IDBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtNjAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBwcm9kdWN0LnN0b2NrUXVhbnRpdHkgPD0gNVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy02MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi02MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdC5zdG9ja1F1YW50aXR5IDw9IDBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJPdXQgb2YgU3RvY2tcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBgU3RvY2s6ICR7cHJvZHVjdC5zdG9ja1F1YW50aXR5fWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3QuT3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgICAgICAgICAgICAgIDwvRm9ybS5JdGVtPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJxdWFudGl0eVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD17XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtNzAwIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgUXVhbnRpdHkgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwXCI+Kjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWItNFwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0TnVtYmVyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG1pbj17MX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3F1YW50aXR5fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiBzZXRRdWFudGl0eSh2YWx1ZSB8fCAxKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IFwiMTAwJVwiIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1vZGVybi1pbnB1dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJsYXJnZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiMVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvRm9ybS5JdGVtPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgc3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwicHJpbWFyeVwiXG4gICAgICAgICAgICAgICAgICAgICAgaWNvbj17PFBsdXNPdXRsaW5lZCAvPn1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVBZGRJdGVtfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtMTQgdy1mdWxsIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMCB0by1pbmRpZ28tNjAwIGhvdmVyOmZyb20tYmx1ZS02MDAgaG92ZXI6dG8taW5kaWdvLTcwMCBib3JkZXItMCByb3VuZGVkLXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1iYXNlIHNoYWRvdy1sZyBzaGFkb3ctYmx1ZS0yMDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGhvdmVyOnNoYWRvdy14bCBob3ZlcjpzaGFkb3ctYmx1ZS0zMDAgaG92ZXI6LXRyYW5zbGF0ZS15LTAuNVwiXG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFzZWxlY3RlZFByb2R1Y3R9XG4gICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cImxhcmdlXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIEFkZCB0byBDYXJ0XG4gICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBpY29uPXs8U2Nhbk91dGxpbmVkIC8+fVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29wZW5TY2FubmVyfVxuICAgICAgICAgICAgICAgICAgICAgIGxvYWRpbmc9e2lzU2Nhbm5lckxvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC0xMiB3LWZ1bGwgYmctZ3JhZGllbnQtdG8tciBmcm9tLXB1cnBsZS01MDAgdG8tcGluay02MDAgaG92ZXI6ZnJvbS1wdXJwbGUtNjAwIGhvdmVyOnRvLXBpbmstNzAwIGJvcmRlci0wIHJvdW5kZWQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWJhc2Ugc2hhZG93LWxnIHNoYWRvdy1wdXJwbGUtMjAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBob3ZlcjpzaGFkb3cteGwgaG92ZXI6c2hhZG93LXB1cnBsZS0zMDAgaG92ZXI6LXRyYW5zbGF0ZS15LTAuNSB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgICAgICBzaXplPVwibGFyZ2VcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge2lzU2Nhbm5lckxvYWRpbmcgPyAnU2VhcmNoaW5nLi4uJyA6ICdTY2FuIEJhcmNvZGUnfVxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvRm9ybT5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIENhcnQgSXRlbXMgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiByb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLXNsYXRlLTIwMCBiZy13aGl0ZS83MCBiYWNrZHJvcC1ibHVyLXNtIHAtNiBzaGFkb3cteGwgc2hhZG93LXNsYXRlLTIwMC81MFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB3LTggaC04IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1lbWVyYWxkLTUwMCB0by10ZWFsLTUwMCByb3VuZGVkLWxnIG1yLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgPFNob3BwaW5nQ2FydE91dGxpbmVkIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LXNsYXRlLTgwMCBtLTBcIj5DYXJ0IEl0ZW1zPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXNsYXRlLTUwMCBtLTBcIj5JdGVtcyBhZGRlZCB0byB0aGlzIHRyYW5zYWN0aW9uPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtaC1bNDAwcHhdIG92ZXJmbG93LXgtYXV0byBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgIHtpdGVtcy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicm91bmRlZC0yeGwgYm9yZGVyIGJvcmRlci1zbGF0ZS0yMDAgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1zbGF0ZS01MCB0by1ibHVlLTUwIHAtMTIgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHctMTYgaC0xNiBiZy1ncmFkaWVudC10by1yIGZyb20tc2xhdGUtNDAwIHRvLXNsYXRlLTUwMCByb3VuZGVkLWZ1bGwgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U2hvcHBpbmdDYXJ0T3V0bGluZWQgY2xhc3NOYW1lPVwidGV4dC0yeGwgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXNsYXRlLTcwMCBtYi0yXCI+Q2FydCBpcyBFbXB0eTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS01MDBcIj5BZGQgcHJvZHVjdHMgdG8gc3RhcnQgYnVpbGRpbmcgeW91ciB0cmFuc2FjdGlvbjwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LWhpZGRlbiByb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLXNsYXRlLTIwMCBiZy13aGl0ZSBzaGFkb3ctc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1zbGF0ZS01MCB0by1ibHVlLTUwIHB4LTYgcHktNCBib3JkZXItYiBib3JkZXItc2xhdGUtMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEyIGdhcC00IHRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LXNsYXRlLTcwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTVcIj5Qcm9kdWN0PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tMiB0ZXh0LWNlbnRlclwiPlF0eTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTIgdGV4dC1yaWdodFwiPlByaWNlPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tMiB0ZXh0LXJpZ2h0XCI+U3VidG90YWw8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xIHRleHQtY2VudGVyXCI+QWN0aW9uPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRpdmlkZS15IGRpdmlkZS1zbGF0ZS0xMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtcy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2Ake2l0ZW0ucHJvZHVjdElkfS0ke2luZGV4fWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNiBweS00IGhvdmVyOmJnLXNsYXRlLTUwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTE1MFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEyIGdhcC00IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi01XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXNsYXRlLTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLnByb2R1Y3ROYW1lIHx8IFwiVW5rbm93biBQcm9kdWN0XCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTIgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHctOCBoLTggYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCByb3VuZGVkLWZ1bGwgdGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLnF1YW50aXR5fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tMiB0ZXh0LXJpZ2h0IHRleHQtc2xhdGUtNjAwIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEdIUyB7TnVtYmVyKGl0ZW0ucHJpY2UpLnRvRml4ZWQoMil9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tMiB0ZXh0LXJpZ2h0IGZvbnQtYm9sZCB0ZXh0LXNsYXRlLTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBHSFMgeyhOdW1iZXIoaXRlbS5wcmljZSkgKiBpdGVtLnF1YW50aXR5KS50b0ZpeGVkKDIpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTEgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGljb249ezxEZWxldGVPdXRsaW5lZCAvPn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVSZW1vdmVJdGVtKGluZGV4KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGFuZ2VyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwIGhvdmVyOmJnLXJlZC01MCBob3Zlcjp0ZXh0LXJlZC02MDAgcm91bmRlZC1sZ1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtYWxsXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWVtZXJhbGQtNTAgdG8tZ3JlZW4tNTAgcHgtNiBweS00IGJvcmRlci10IGJvcmRlci1zbGF0ZS0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMTIgZ2FwLTQgaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tOSB0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC1zbGF0ZS04MDBcIj5Ub3RhbDo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTMgdGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1lbWVyYWxkLTYwMCB0by1ncmVlbi02MDAgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEdIUyB7dG90YWxBbW91bnQudG9GaXhlZCgyKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFJpZ2h0IENvbHVtbiAtIFBheW1lbnQgSW5mb3JtYXRpb24gKi99XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0aWNreSB0b3AtNCByb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLXNsYXRlLTIwMCBiZy13aGl0ZS83MCBiYWNrZHJvcC1ibHVyLXNtIHAtNiBzaGFkb3cteGwgc2hhZG93LXNsYXRlLTIwMC81MFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB3LTggaC04IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1vcmFuZ2UtNTAwIHRvLXJlZC01MDAgcm91bmRlZC1sZyBtci0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxTaG9wcGluZ0NhcnRPdXRsaW5lZCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC1zbGF0ZS04MDAgbS0wXCI+Q2hlY2tvdXQ8L2gzPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc2xhdGUtNTAwIG0tMFwiPkNvbXBsZXRlIHlvdXIgdHJhbnNhY3Rpb248L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxGb3JtIGZvcm09e2Zvcm19IGxheW91dD1cInZlcnRpY2FsXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgcm91bmRlZC14bCBib3JkZXIgYm9yZGVyLXNsYXRlLTIwMCBiZy1ncmFkaWVudC10by1iciBmcm9tLXNsYXRlLTUwIHRvLWJsdWUtNTAgcC01XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtc2xhdGUtNjAwXCI+SXRlbXM6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB3LTggaC04IGJnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAgcm91bmRlZC1mdWxsIHRleHQtc20gZm9udC1ib2xkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7aXRlbXMubGVuZ3RofVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtc2xhdGUtNjAwXCI+VG90YWwgUXVhbnRpdHk6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB3LTggaC04IGJnLXB1cnBsZS0xMDAgdGV4dC1wdXJwbGUtODAwIHJvdW5kZWQtZnVsbCB0ZXh0LXNtIGZvbnQtYm9sZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW1zLnJlZHVjZSgoc3VtLCBpdGVtKSA9PiBzdW0gKyBpdGVtLnF1YW50aXR5LCAwKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXNsYXRlLTYwMFwiPlN1YnRvdGFsOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1zbGF0ZS04MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEdIUyB7dG90YWxBbW91bnQudG9GaXhlZCgyKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm15LTQgYm9yZGVyLXQgYm9yZGVyLXNsYXRlLTIwMFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtc2xhdGUtODAwXCI+VG90YWw6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1lbWVyYWxkLTYwMCB0by1ncmVlbi02MDAgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEdIUyB7dG90YWxBbW91bnQudG9GaXhlZCgyKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBTdG9yZSBpbmZvcm1hdGlvbiBpcyBub3cgYXV0b21hdGljYWxseSBhc3NvY2lhdGVkIHdpdGggdGhlIGFkbWluIHVzZXIgKi99XG4gICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRTdG9yZSA/IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cIm1iLTMgYmxvY2sgdGV4dC1zbGF0ZS03MDAgZm9udC1tZWRpdW1cIj5TdG9yZTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciByb3VuZGVkLXhsIGJvcmRlciBib3JkZXItc2xhdGUtMjAwIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwIHRvLWluZGlnby01MCBwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdy04IGgtOCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAgdG8taW5kaWdvLTYwMCByb3VuZGVkLWxnIG1yLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFNob3BPdXRsaW5lZCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtc2xhdGUtODAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZFN0b3JlLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImhpZGRlblwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJzdG9yZUlkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkU3RvcmUuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiByb3VuZGVkLXhsIGJvcmRlciBib3JkZXItb3JhbmdlLTIwMCBiZy1ncmFkaWVudC10by1yIGZyb20tb3JhbmdlLTUwIHRvLXllbGxvdy01MCBwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHctOCBoLTggYmctZ3JhZGllbnQtdG8tciBmcm9tLW9yYW5nZS01MDAgdG8teWVsbG93LTUwMCByb3VuZGVkLWxnIG1yLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFNob3BPdXRsaW5lZCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LW9yYW5nZS04MDAgbS0wXCI+U3RvcmUgU2V0dXAgUmVxdWlyZWQ8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1vcmFuZ2UtNjAwIG0tMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFBsZWFzZSBzZXQgdXAgeW91ciBzdG9yZSBpbiBwcm9maWxlIHNldHRpbmdzLlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJwYXltZW50TWV0aG9kXCJcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9e1xuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtNzAwIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBQYXltZW50IE1ldGhvZCA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDBcIj4qPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBydWxlcz17W1xuICAgICAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogXCJQbGVhc2Ugc2VsZWN0IGEgcGF5bWVudCBtZXRob2RcIixcbiAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICBdfVxuICAgICAgICAgICAgICAgICAgICBpbml0aWFsVmFsdWU9XCJjYXNoXCJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWItNlwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3QgY2xhc3NOYW1lPVwibW9kZXJuLXNlbGVjdFwiIHNpemU9XCJsYXJnZVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3QuT3B0aW9uIHZhbHVlPVwiY2FzaFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAg8J+StSBDYXNoXG4gICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3QuT3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3QuT3B0aW9uIHZhbHVlPVwiY2FyZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAg8J+SsyBDYXJkXG4gICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3QuT3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3QuT3B0aW9uIHZhbHVlPVwibW9iaWxlX21vbmV5XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICDwn5OxIE1vYmlsZSBNb25leVxuICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0Lk9wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC04IHNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICB7cmVjZWlwdFByZXZpZXdWaXNpYmxlID8gKFxuICAgICAgICAgICAgICAgICAgICAgIC8vIFNob3cgXCJEb25lXCIgYnV0dG9uIHdoZW4gcmVjZWlwdCBpcyB2aXNpYmxlXG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInByaW1hcnlcIlxuICAgICAgICAgICAgICAgICAgICAgICAgaWNvbj17PFNob3BwaW5nQ2FydE91dGxpbmVkIC8+fVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBDbG9zZSB0aGUgbW9kYWwgYW5kIHJlc2V0IHRoZSByZWNlaXB0IHN0YXRlXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNldFJlY2VpcHRQcmV2aWV3VmlzaWJsZShmYWxzZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNldFJlY2VpcHRVcmwobnVsbCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNldEhhc1ByaW50ZWQoZmFsc2UpO1xuXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8vIFJlc2V0IHRoZSBmb3JtcyB0byBzdGFydCBhIG5ldyBzYWxlXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGZvcm0ucmVzZXRGaWVsZHMoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvZHVjdEZvcm0ucmVzZXRGaWVsZHMoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0SXRlbXMoW10pO1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZFByb2R1Y3QobnVsbCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNldFF1YW50aXR5KDEpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRUb3RhbEFtb3VudCgwKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTE0IHctZnVsbCBiZy1ncmFkaWVudC10by1yIGZyb20tZW1lcmFsZC01MDAgdG8tZ3JlZW4tNjAwIGhvdmVyOmZyb20tZW1lcmFsZC02MDAgaG92ZXI6dG8tZ3JlZW4tNzAwIGJvcmRlci0wIHJvdW5kZWQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWJhc2Ugc2hhZG93LWxnIHNoYWRvdy1lbWVyYWxkLTIwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2hhZG93LXhsIGhvdmVyOnNoYWRvdy1lbWVyYWxkLTMwMCBob3ZlcjotdHJhbnNsYXRlLXktMC41XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJsYXJnZVwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgU3RhcnQgTmV3IFNhbGVcbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAvLyBTaG93IFwiQ29tcGxldGUgU2FsZVwiIGJ1dHRvbiB3aGVuIGNyZWF0aW5nIGEgc2FsZVxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJwcmltYXJ5XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGljb249ezxTaG9wcGluZ0NhcnRPdXRsaW5lZCAvPn1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVN1Ym1pdH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGxvYWRpbmc9e2lzU3VibWl0dGluZyB8fCBpc0dlbmVyYXRpbmdSZWNlaXB0fVxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2l0ZW1zLmxlbmd0aCA9PT0gMH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtMTQgdy1mdWxsIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1lbWVyYWxkLTUwMCB0by1ncmVlbi02MDAgaG92ZXI6ZnJvbS1lbWVyYWxkLTYwMCBob3Zlcjp0by1ncmVlbi03MDAgYm9yZGVyLTAgcm91bmRlZC14bCBmb250LXNlbWlib2xkIHRleHQtYmFzZSBzaGFkb3ctbGcgc2hhZG93LWVtZXJhbGQtMjAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBob3ZlcjpzaGFkb3cteGwgaG92ZXI6c2hhZG93LWVtZXJhbGQtMzAwIGhvdmVyOi10cmFuc2xhdGUteS0wLjUgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6aG92ZXI6dHJhbnNmb3JtLW5vbmUgZGlzYWJsZWQ6aG92ZXI6c2hhZG93LWxnXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJsYXJnZVwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAge2lzR2VuZXJhdGluZ1JlY2VpcHRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcIkdlbmVyYXRpbmcgUmVjZWlwdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJDb21wbGV0ZSBTYWxlXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29uQ2xvc2V9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC0xMiB3LWZ1bGwgYm9yZGVyLXNsYXRlLTMwMCBiZy1zbGF0ZS0xMDAgdGV4dC1zbGF0ZS03MDAgaG92ZXI6Ymctc2xhdGUtMjAwIHJvdW5kZWQteGwgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGhvdmVyOnNoYWRvdy1tZFwiXG4gICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cImxhcmdlXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvRm9ybT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFJlY2VpcHQgUHJldmlldyBNb2RhbCAqL31cbiAgICAgIDxNb2RhbFxuICAgICAgICB0aXRsZT17XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LWdyYXktODAwXCI+XG4gICAgICAgICAgICA8UHJpbnRlck91dGxpbmVkIGNsYXNzTmFtZT1cIm1yLTJcIiAvPlxuICAgICAgICAgICAgPHNwYW4+UmVjZWlwdCBQcmV2aWV3PC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICB9XG4gICAgICAgIG9wZW49e3JlY2VpcHRQcmV2aWV3VmlzaWJsZX1cbiAgICAgICAgb25DYW5jZWw9eygpID0+IHtcbiAgICAgICAgICAvLyBDbG9zZSB0aGUgbW9kYWwgYW5kIHJlc2V0IHRoZSByZWNlaXB0IHN0YXRlXG4gICAgICAgICAgc2V0UmVjZWlwdFByZXZpZXdWaXNpYmxlKGZhbHNlKTtcbiAgICAgICAgICBzZXRSZWNlaXB0VXJsKG51bGwpO1xuICAgICAgICAgIHNldEhhc1ByaW50ZWQoZmFsc2UpO1xuXG4gICAgICAgICAgLy8gUmVzZXQgdGhlIGZvcm1zIHRvIHN0YXJ0IGEgbmV3IHNhbGVcbiAgICAgICAgICBmb3JtLnJlc2V0RmllbGRzKCk7XG4gICAgICAgICAgcHJvZHVjdEZvcm0ucmVzZXRGaWVsZHMoKTtcbiAgICAgICAgICBzZXRJdGVtcyhbXSk7XG4gICAgICAgICAgc2V0U2VsZWN0ZWRQcm9kdWN0KG51bGwpO1xuICAgICAgICAgIHNldFF1YW50aXR5KDEpO1xuICAgICAgICAgIHNldFRvdGFsQW1vdW50KDApO1xuICAgICAgICB9fVxuICAgICAgICB3aWR0aD17NTAwfVxuICAgICAgICBjZW50ZXJlZFxuICAgICAgICBjbGFzc05hbWU9XCJyZWNlaXB0LXByZXZpZXctbW9kYWxcIlxuICAgICAgICBmb290ZXI9e1tcbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICBrZXk9XCJjbG9zZVwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgIC8vIENsb3NlIHRoZSBtb2RhbCBhbmQgcmVzZXQgdGhlIHJlY2VpcHQgc3RhdGVcbiAgICAgICAgICAgICAgc2V0UmVjZWlwdFByZXZpZXdWaXNpYmxlKGZhbHNlKTtcbiAgICAgICAgICAgICAgc2V0UmVjZWlwdFVybChudWxsKTtcbiAgICAgICAgICAgICAgc2V0SGFzUHJpbnRlZChmYWxzZSk7XG5cbiAgICAgICAgICAgICAgLy8gUmVzZXQgdGhlIGZvcm1zIHRvIHN0YXJ0IGEgbmV3IHNhbGVcbiAgICAgICAgICAgICAgZm9ybS5yZXNldEZpZWxkcygpO1xuICAgICAgICAgICAgICBwcm9kdWN0Rm9ybS5yZXNldEZpZWxkcygpO1xuICAgICAgICAgICAgICBzZXRJdGVtcyhbXSk7XG4gICAgICAgICAgICAgIHNldFNlbGVjdGVkUHJvZHVjdChudWxsKTtcbiAgICAgICAgICAgICAgc2V0UXVhbnRpdHkoMSk7XG4gICAgICAgICAgICAgIHNldFRvdGFsQW1vdW50KDApO1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1ncmF5LTMwMCBiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMjAwXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICBDbG9zZSAmIE5ldyBTYWxlXG4gICAgICAgICAgPC9CdXR0b24+LFxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIGtleT1cInByaW50XCJcbiAgICAgICAgICAgIHR5cGU9XCJwcmltYXJ5XCJcbiAgICAgICAgICAgIGljb249ezxQcmludGVyT3V0bGluZWQgLz59XG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgIC8vIElmIGFscmVhZHkgcHJpbnRlZCBvbmNlLCByZXNldCB0aGUgZmxhZyB0byBhbGxvdyBwcmludGluZyBhZ2FpblxuICAgICAgICAgICAgICBpZiAoaGFzUHJpbnRlZCkge1xuICAgICAgICAgICAgICAgIHNldEhhc1ByaW50ZWQoZmFsc2UpO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIGhhbmRsZVByaW50UmVjZWlwdCgpO1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICB7aGFzUHJpbnRlZCA/IFwiUHJpbnQgQWdhaW5cIiA6IFwiUHJpbnQgUmVjZWlwdFwifVxuICAgICAgICAgIDwvQnV0dG9uPixcbiAgICAgICAgXX1cbiAgICAgID5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgIHtyZWNlaXB0VXJsID8gKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWNlaXB0LWltYWdlLWNvbnRhaW5lclwiPlxuICAgICAgICAgICAgICA8SW1hZ2VcbiAgICAgICAgICAgICAgICBzcmM9e3JlY2VpcHRVcmx9XG4gICAgICAgICAgICAgICAgYWx0PVwiUmVjZWlwdFwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVjZWlwdC1pbWFnZVwiXG4gICAgICAgICAgICAgICAgc3R5bGU9e3sgbWF4V2lkdGg6IFwiMTAwJVwiIH19XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtNjQgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxTcGluIHNpemU9XCJsYXJnZVwiIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvTW9kYWw+XG5cbiAgICAgIHsvKiBCYXJjb2RlIFNjYW5uZXIgKi99XG4gICAgICA8QmFyY29kZVNjYW5uZXJcbiAgICAgICAgaXNPcGVuPXtpc1NjYW5uZXJPcGVufVxuICAgICAgICBvbkNsb3NlPXtjbG9zZVNjYW5uZXJ9XG4gICAgICAgIG9uU2Nhbj17aGFuZGxlQmFyY29kZVNjYW5uZWR9XG4gICAgICAgIHRpdGxlPVwiU2NhbiBQcm9kdWN0IEJhcmNvZGVcIlxuICAgICAgLz5cbiAgICA8L1NsaWRpbmdQYW5lbD5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFNhbGVzRm9ybVBhbmVsO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsIkZvcm0iLCJCdXR0b24iLCJTZWxlY3QiLCJJbnB1dE51bWJlciIsIlNwaW4iLCJNb2RhbCIsIkltYWdlIiwiRGVsZXRlT3V0bGluZWQiLCJQbHVzT3V0bGluZWQiLCJTaG9wcGluZ0NhcnRPdXRsaW5lZCIsIlNlYXJjaE91dGxpbmVkIiwiTG9hZGluZ091dGxpbmVkIiwiU2hvcE91dGxpbmVkIiwiUHJpbnRlck91dGxpbmVkIiwiU2Nhbk91dGxpbmVkIiwidXNlQ3JlYXRlU2FsZU11dGF0aW9uIiwidXNlR2V0QWxsUHJvZHVjdHNRdWVyeSIsInVzZUdldFVzZXJTdG9yZXNRdWVyeSIsInVzZUdldFVzZXJEZWZhdWx0U3RvcmVRdWVyeSIsIlNsaWRpbmdQYW5lbCIsIkJhcmNvZGVTY2FubmVyIiwidXNlQmFyY29kZVNjYW5uZXIiLCJzaG93TWVzc2FnZSIsImdlbmVyYXRlUmVjZWlwdEhUTUwiLCJnZW5lcmF0ZVJlY2VpcHRJbWFnZSIsIlNhbGVzRm9ybVBhbmVsIiwiaXNPcGVuIiwib25DbG9zZSIsIm9uU3VjY2VzcyIsInByb2R1Y3RzRGF0YSIsImZvcm0iLCJ1c2VGb3JtIiwicHJvZHVjdEZvcm0iLCJpdGVtcyIsInNldEl0ZW1zIiwic2VsZWN0ZWRQcm9kdWN0Iiwic2V0U2VsZWN0ZWRQcm9kdWN0IiwicXVhbnRpdHkiLCJzZXRRdWFudGl0eSIsInRvdGFsQW1vdW50Iiwic2V0VG90YWxBbW91bnQiLCJzZWFyY2hUZXJtIiwic2V0U2VhcmNoVGVybSIsInNlbGVjdGVkU3RvcmUiLCJzZXRTZWxlY3RlZFN0b3JlIiwiaXNHZW5lcmF0aW5nUmVjZWlwdCIsInNldElzR2VuZXJhdGluZ1JlY2VpcHQiLCJyZWNlaXB0UHJldmlld1Zpc2libGUiLCJzZXRSZWNlaXB0UHJldmlld1Zpc2libGUiLCJyZWNlaXB0VXJsIiwic2V0UmVjZWlwdFVybCIsImhhc1ByaW50ZWQiLCJzZXRIYXNQcmludGVkIiwiYmFyY29kZUlucHV0Iiwic2V0QmFyY29kZUlucHV0IiwiaXNTY2FubmVyT3BlbiIsIm9wZW5TY2FubmVyIiwiY2xvc2VTY2FubmVyIiwiaGFuZGxlQmFyY29kZVNjYW5uZWQiLCJpc0xvYWRpbmciLCJpc1NjYW5uZXJMb2FkaW5nIiwib25Qcm9kdWN0Rm91bmQiLCJwcm9kdWN0Iiwic2V0RmllbGRzVmFsdWUiLCJwcm9kdWN0SWQiLCJpZCIsInNldFRpbWVvdXQiLCJoYW5kbGVBZGRJdGVtIiwib25Qcm9kdWN0Tm90Rm91bmQiLCJiYXJjb2RlIiwiY29uc29sZSIsImxvZyIsImhhbmRsZUJhcmNvZGVJbnB1dENoYW5nZSIsInZhbHVlIiwibGVuZ3RoIiwicHJvZHVjdHMiLCJkYXRhIiwiZm91bmRQcm9kdWN0IiwiZmluZCIsInRyaW0iLCJza3UiLCJuYW1lIiwiZXJyb3IiLCJpc0xvYWRpbmdQcm9kdWN0cyIsInJlZmV0Y2giLCJyZWZldGNoUHJvZHVjdHMiLCJwYWdlIiwibGltaXQiLCJzZWFyY2giLCJyZWZldGNoT25Nb3VudE9yQXJnQ2hhbmdlIiwicmVmZXRjaE9uRm9jdXMiLCJyZWZldGNoT25SZWNvbm5lY3QiLCJ0b3RhbCIsInByb2R1Y3RzQ291bnQiLCJnZXRDdXJyZW50VXNlcklkIiwic3RhdGUiLCJ3aW5kb3ciLCJfX1JFRFVYX1NUQVRFIiwiYXV0aCIsInVzZXIiLCJ1c2VyU3RvcmVzRGF0YSIsImRlZmF1bHRTdG9yZURhdGEiLCJzdG9yZUlkIiwiY3JlYXRlU2FsZSIsImlzU3VibWl0dGluZyIsInJlZHVjZSIsInN1bSIsIml0ZW0iLCJwcmljZSIsInJlc2V0RmllbGRzIiwic3RvY2tRdWFudGl0eSIsImV4aXN0aW5nSXRlbUluZGV4IiwiZmluZEluZGV4IiwidXBkYXRlZEl0ZW1zIiwibmV3UXVhbnRpdHkiLCJwcm9kdWN0TmFtZSIsIm5ld0l0ZW0iLCJwYXJzZUZsb2F0IiwibmV3SXRlbXMiLCJ1bmRlZmluZWQiLCJoYW5kbGVSZW1vdmVJdGVtIiwiaW5kZXgiLCJzcGxpY2UiLCJoYW5kbGVQcmludFJlY2VpcHQiLCJpZnJhbWUiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJzdHlsZSIsImRpc3BsYXkiLCJib2R5IiwiYXBwZW5kQ2hpbGQiLCJvbmxvYWQiLCJjb250ZW50V2luZG93Iiwid3JpdGUiLCJjbG9zZSIsImZvY3VzIiwicHJpbnQiLCJlIiwicmVtb3ZlQ2hpbGQiLCJzcmMiLCJ0aW1lciIsImNsZWFyVGltZW91dCIsImhhbmRsZVN1Ym1pdCIsInZhbHVlcyIsInZhbGlkYXRlRmllbGRzIiwic3RvcmVJbmZvIiwic3RvcmUiLCJyZWNlaXB0SFRNTCIsIkRhdGUiLCJub3ciLCJwYXltZW50TWV0aG9kIiwidHJhbnNhY3Rpb25EYXRlIiwidG9JU09TdHJpbmciLCJtYXAiLCJzYWxlRGF0YSIsInJlc3BvbnNlIiwidW53cmFwIiwic3VjY2VzcyIsIm1lc3NhZ2UiLCJ0aXRsZSIsIndpZHRoIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDIiLCJwIiwidG9GaXhlZCIsImgzIiwic3BhbiIsImxheW91dCIsImluaXRpYWxWYWx1ZXMiLCJJdGVtIiwibGFiZWwiLCJzaG93U2VhcmNoIiwicGxhY2Vob2xkZXIiLCJvcHRpb25GaWx0ZXJQcm9wIiwibG9hZGluZyIsImRpc2FibGVkIiwib25DaGFuZ2UiLCJTdHJpbmciLCJvblNlYXJjaCIsImZpbHRlck9wdGlvbiIsInNpemUiLCJzdWZmaXhJY29uIiwic3BpbiIsIm5vdEZvdW5kQ29udGVudCIsIk9wdGlvbiIsIk51bWJlciIsIm1pbiIsInR5cGUiLCJpY29uIiwib25DbGljayIsImg0IiwiZGFuZ2VyIiwiaW5wdXQiLCJydWxlcyIsInJlcXVpcmVkIiwiaW5pdGlhbFZhbHVlIiwib3BlbiIsIm9uQ2FuY2VsIiwiY2VudGVyZWQiLCJmb290ZXIiLCJhbHQiLCJtYXhXaWR0aCIsIm9uU2NhbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx\n"));

/***/ })

});