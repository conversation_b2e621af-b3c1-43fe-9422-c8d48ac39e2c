{"version": 3, "sources": ["../../../src/export/helpers/create-incremental-cache.ts"], "sourcesContent": ["import path from 'path'\nimport { IncrementalCache } from '../../server/lib/incremental-cache'\nimport { hasNextSupport } from '../../server/ci-info'\nimport { nodeFs } from '../../server/lib/node-fs-methods'\nimport { interopDefault } from '../../lib/interop-default'\nimport { formatDynamicImportPath } from '../../lib/format-dynamic-import-path'\n\nexport async function createIncrementalCache({\n  cacheHandler,\n  dynamicIO,\n  cacheMaxMemorySize,\n  fetchCacheKeyPrefix,\n  distDir,\n  dir,\n  flushToDisk,\n  cacheHandlers,\n}: {\n  dynamicIO: boolean\n  cacheHandler?: string\n  cacheMaxMemorySize?: number\n  fetchCacheKeyPrefix?: string\n  distDir: string\n  dir: string\n  flushToDisk?: boolean\n  cacheHandlers?: Record<string, string | undefined>\n}) {\n  // Custom cache handler overrides.\n  let CacheHandler: any\n  if (cacheHandler) {\n    CacheHandler = interopDefault(\n      await import(formatDynamicImportPath(dir, cacheHandler)).then(\n        (mod) => mod.default || mod\n      )\n    )\n  }\n\n  if (!(globalThis as any).__nextCacheHandlers && cacheHandlers) {\n    ;(globalThis as any).__nextCacheHandlers = {}\n\n    for (const key of Object.keys(cacheHandlers)) {\n      if (cacheHandlers[key]) {\n        ;(globalThis as any).__nextCacheHandlers[key] = interopDefault(\n          await import(formatDynamicImportPath(dir, cacheHandlers[key])).then(\n            (mod) => mod.default || mod\n          )\n        )\n      }\n    }\n  }\n\n  const incrementalCache = new IncrementalCache({\n    dev: false,\n    requestHeaders: {},\n    flushToDisk,\n    dynamicIO,\n    fetchCache: true,\n    maxMemoryCacheSize: cacheMaxMemorySize,\n    fetchCacheKeyPrefix,\n    getPrerenderManifest: () => ({\n      version: 4,\n      routes: {},\n      dynamicRoutes: {},\n      preview: {\n        previewModeEncryptionKey: '',\n        previewModeId: '',\n        previewModeSigningKey: '',\n      },\n      notFoundRoutes: [],\n    }),\n    fs: nodeFs,\n    serverDistDir: path.join(distDir, 'server'),\n    CurCacheHandler: CacheHandler,\n    minimalMode: hasNextSupport,\n  })\n\n  ;(globalThis as any).__incrementalCache = incrementalCache\n\n  return incrementalCache\n}\n"], "names": ["createIncrementalCache", "cache<PERSON><PERSON><PERSON>", "dynamicIO", "cacheMaxMemorySize", "fetchCacheKeyPrefix", "distDir", "dir", "flushToDisk", "cacheHandlers", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interopDefault", "formatDynamicImportPath", "then", "mod", "default", "globalThis", "__next<PERSON>ache<PERSON>and<PERSON>", "key", "Object", "keys", "incrementalCache", "IncrementalCache", "dev", "requestHeaders", "fetchCache", "maxMemoryCacheSize", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "preview", "previewModeEncryptionKey", "previewModeId", "previewModeSigningKey", "notFoundRoutes", "fs", "nodeFs", "serverDistDir", "path", "join", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minimalMode", "hasNextSupport", "__incrementalCache"], "mappings": ";;;;+BAOsBA;;;eAAAA;;;6DAPL;kCACgB;wBACF;+BACR;gCACQ;yCACS;;;;;;AAEjC,eAAeA,uBAAuB,EAC3CC,YAAY,EACZC,SAAS,EACTC,kBAAkB,EAClBC,mBAAmB,EACnBC,OAAO,EACPC,GAAG,EACHC,WAAW,EACXC,aAAa,EAUd;IACC,kCAAkC;IAClC,IAAIC;IACJ,IAAIR,cAAc;QAChBQ,eAAeC,IAAAA,8BAAc,EAC3B,MAAM,MAAM,CAACC,IAAAA,gDAAuB,EAACL,KAAKL,eAAeW,IAAI,CAC3D,CAACC,MAAQA,IAAIC,OAAO,IAAID;IAG9B;IAEA,IAAI,CAAC,AAACE,WAAmBC,mBAAmB,IAAIR,eAAe;;QAC3DO,WAAmBC,mBAAmB,GAAG,CAAC;QAE5C,KAAK,MAAMC,OAAOC,OAAOC,IAAI,CAACX,eAAgB;YAC5C,IAAIA,aAAa,CAACS,IAAI,EAAE;;gBACpBF,WAAmBC,mBAAmB,CAACC,IAAI,GAAGP,IAAAA,8BAAc,EAC5D,MAAM,MAAM,CAACC,IAAAA,gDAAuB,EAACL,KAAKE,aAAa,CAACS,IAAI,GAAGL,IAAI,CACjE,CAACC,MAAQA,IAAIC,OAAO,IAAID;YAG9B;QACF;IACF;IAEA,MAAMO,mBAAmB,IAAIC,kCAAgB,CAAC;QAC5CC,KAAK;QACLC,gBAAgB,CAAC;QACjBhB;QACAL;QACAsB,YAAY;QACZC,oBAAoBtB;QACpBC;QACAsB,sBAAsB,IAAO,CAAA;gBAC3BC,SAAS;gBACTC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,SAAS;oBACPC,0BAA0B;oBAC1BC,eAAe;oBACfC,uBAAuB;gBACzB;gBACAC,gBAAgB,EAAE;YACpB,CAAA;QACAC,IAAIC,qBAAM;QACVC,eAAeC,aAAI,CAACC,IAAI,CAAClC,SAAS;QAClCmC,iBAAiB/B;QACjBgC,aAAaC,sBAAc;IAC7B;IAEE3B,WAAmB4B,kBAAkB,GAAGvB;IAE1C,OAAOA;AACT"}