{"version": 3, "sources": ["../../src/lib/create-client-router-filter.ts"], "sourcesContent": ["import type { Token } from 'next/dist/compiled/path-to-regexp'\nimport { BloomFilter } from '../shared/lib/bloom-filter'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport type { Redirect } from './load-custom-routes'\nimport { tryToParsePath } from './try-to-parse-path'\nimport {\n  extractInterceptionRouteInformation,\n  isInterceptionRouteAppPath,\n} from '../server/lib/interception-routes'\n\nexport function createClientRouterFilter(\n  paths: string[],\n  redirects: Redirect[],\n  allowedErrorRate?: number\n): {\n  staticFilter: ReturnType<BloomFilter['export']>\n  dynamicFilter: ReturnType<BloomFilter['export']>\n} {\n  const staticPaths = new Set<string>()\n  const dynamicPaths = new Set<string>()\n\n  for (let path of paths) {\n    if (isDynamicRoute(path)) {\n      if (isInterceptionRouteAppPath(path)) {\n        path = extractInterceptionRouteInformation(path).interceptedRoute\n      }\n\n      let subPath = ''\n      const pathParts = path.split('/')\n\n      // start at 1 since we split on '/' and the path starts\n      // with this so the first entry is an empty string\n      for (let i = 1; i < pathParts.length + 1; i++) {\n        const curPart = pathParts[i]\n\n        if (curPart.startsWith('[')) {\n          break\n        }\n        subPath = `${subPath}/${curPart}`\n      }\n\n      if (subPath) {\n        dynamicPaths.add(subPath)\n      }\n    } else {\n      staticPaths.add(path)\n    }\n  }\n\n  for (const redirect of redirects) {\n    const { source } = redirect\n    const path = removeTrailingSlash(source)\n    let tokens: Token[] = []\n\n    try {\n      tokens = tryToParsePath(source).tokens || []\n    } catch {}\n\n    if (tokens.every((token) => typeof token === 'string')) {\n      // only include static redirects initially\n      staticPaths.add(path)\n    }\n  }\n\n  const staticFilter = BloomFilter.from([...staticPaths], allowedErrorRate)\n\n  const dynamicFilter = BloomFilter.from([...dynamicPaths], allowedErrorRate)\n  const data = {\n    staticFilter: staticFilter.export(),\n    dynamicFilter: dynamicFilter.export(),\n  }\n  return data\n}\n"], "names": ["createClientRouterFilter", "paths", "redirects", "allowedErrorRate", "staticPaths", "Set", "dynamicPaths", "path", "isDynamicRoute", "isInterceptionRouteAppPath", "extractInterceptionRouteInformation", "interceptedRoute", "subPath", "pathParts", "split", "i", "length", "cur<PERSON><PERSON>", "startsWith", "add", "redirect", "source", "removeTrailingSlash", "tokens", "tryToParsePath", "every", "token", "staticFilter", "<PERSON><PERSON><PERSON><PERSON>", "from", "dynamicFilter", "data", "export"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;6BAVY;uBACG;qCACK;gCAEL;oCAIxB;AAEA,SAASA,yBACdC,KAAe,EACfC,SAAqB,EACrBC,gBAAyB;IAKzB,MAAMC,cAAc,IAAIC;IACxB,MAAMC,eAAe,IAAID;IAEzB,KAAK,IAAIE,QAAQN,MAAO;QACtB,IAAIO,IAAAA,qBAAc,EAACD,OAAO;YACxB,IAAIE,IAAAA,8CAA0B,EAACF,OAAO;gBACpCA,OAAOG,IAAAA,uDAAmC,EAACH,MAAMI,gBAAgB;YACnE;YAEA,IAAIC,UAAU;YACd,MAAMC,YAAYN,KAAKO,KAAK,CAAC;YAE7B,uDAAuD;YACvD,kDAAkD;YAClD,IAAK,IAAIC,IAAI,GAAGA,IAAIF,UAAUG,MAAM,GAAG,GAAGD,IAAK;gBAC7C,MAAME,UAAUJ,SAAS,CAACE,EAAE;gBAE5B,IAAIE,QAAQC,UAAU,CAAC,MAAM;oBAC3B;gBACF;gBACAN,UAAU,GAAGA,QAAQ,CAAC,EAAEK,SAAS;YACnC;YAEA,IAAIL,SAAS;gBACXN,aAAaa,GAAG,CAACP;YACnB;QACF,OAAO;YACLR,YAAYe,GAAG,CAACZ;QAClB;IACF;IAEA,KAAK,MAAMa,YAAYlB,UAAW;QAChC,MAAM,EAAEmB,MAAM,EAAE,GAAGD;QACnB,MAAMb,OAAOe,IAAAA,wCAAmB,EAACD;QACjC,IAAIE,SAAkB,EAAE;QAExB,IAAI;YACFA,SAASC,IAAAA,8BAAc,EAACH,QAAQE,MAAM,IAAI,EAAE;QAC9C,EAAE,OAAM,CAAC;QAET,IAAIA,OAAOE,KAAK,CAAC,CAACC,QAAU,OAAOA,UAAU,WAAW;YACtD,0CAA0C;YAC1CtB,YAAYe,GAAG,CAACZ;QAClB;IACF;IAEA,MAAMoB,eAAeC,wBAAW,CAACC,IAAI,CAAC;WAAIzB;KAAY,EAAED;IAExD,MAAM2B,gBAAgBF,wBAAW,CAACC,IAAI,CAAC;WAAIvB;KAAa,EAAEH;IAC1D,MAAM4B,OAAO;QACXJ,cAAcA,aAAaK,MAAM;QACjCF,eAAeA,cAAcE,MAAM;IACrC;IACA,OAAOD;AACT"}