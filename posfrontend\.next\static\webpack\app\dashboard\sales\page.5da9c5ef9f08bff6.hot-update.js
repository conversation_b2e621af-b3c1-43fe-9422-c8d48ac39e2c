"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/sales/page",{

/***/ "(app-pages-browser)/./src/components/Sales/SalesTable.tsx":
/*!*********************************************!*\
  !*** ./src/components/Sales/SalesTable.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/notification/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/checkbox/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EyeOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteFilled.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EyeOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EyeOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EyeOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CalendarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EyeOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EyeOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _sales_panels_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./sales-panels.css */ \"(app-pages-browser)/./src/components/Sales/sales-panels.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst SalesTable = (param)=>{\n    let { sales, loading, onViewSale, onDelete, onBulkDelete, isMobile = false } = param;\n    _s();\n    const { user } = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__.useSelector)({\n        \"SalesTable.useSelector\": (state)=>state.auth\n    }[\"SalesTable.useSelector\"]);\n    // State for selected sales\n    const [selectedSales, setSelectedSales] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectAll, setSelectAll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check if user can delete sales\n    const canDeleteSale = (user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"superadmin\";\n    // The backend already filters sales based on user role and relationships\n    const filteredSales = sales;\n    // Handle select all checkbox change\n    const handleSelectAllChange = (e)=>{\n        const checked = e.target.checked;\n        setSelectAll(checked);\n        if (checked) {\n            // Select all sales that the user can delete\n            const selectableSaleIds = canDeleteSale ? filteredSales.map((sale)=>sale.id) : [];\n            setSelectedSales(selectableSaleIds);\n        } else {\n            // Deselect all sales\n            setSelectedSales([]);\n        }\n    };\n    // Handle individual checkbox change\n    const handleCheckboxChange = (saleId, checked)=>{\n        if (checked) {\n            setSelectedSales((prev)=>[\n                    ...prev,\n                    saleId\n                ]);\n        } else {\n            setSelectedSales((prev)=>prev.filter((id)=>id !== saleId));\n        }\n    };\n    // Handle bulk delete\n    const handleBulkDelete = ()=>{\n        if (selectedSales.length > 0 && onBulkDelete) {\n            onBulkDelete(selectedSales);\n            setSelectedSales([]);\n            setSelectAll(false);\n        } else {\n            _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].warning({\n                message: \"No sales selected\",\n                description: \"Please select at least one sale to delete.\"\n            });\n        }\n    };\n    // Format currency for display\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-GH\", {\n            style: \"currency\",\n            currency: \"GHS\"\n        }).format(parseFloat(amount));\n    };\n    // Format date for display\n    const formatDate = (dateString)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_2___default()(dateString).format(\"MMM D, YYYY HH:mm\");\n    };\n    // Handle delete sale\n    const handleDeleteSale = (saleId)=>{\n        onDelete(saleId);\n    };\n    // Check if we have any sales after filtering\n    const hasSales = filteredSales.length > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-hidden bg-white\",\n        children: [\n            selectedSales.length > 0 && canDeleteSale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between border-b bg-gray-100 p-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: [\n                            selectedSales.length,\n                            \" \",\n                            selectedSales.length === 1 ? \"sale\" : \"sales\",\n                            \" selected\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        type: \"primary\",\n                        danger: true,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: handleBulkDelete,\n                        className: \"ml-2\",\n                        children: \"Delete Selected\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, undefined),\n            !hasSales && sales.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-60 flex-col items-center justify-center bg-gray-50 text-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: 'You don\"t have access to view these sales.'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-sm text-gray-500\",\n                        children: \"You can only view sales created by you or your team.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                lineNumber: 134,\n                columnNumber: 9\n            }, undefined),\n            hasSales && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"min-w-full divide-y divide-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        className: \"bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: [\n                                canDeleteSale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    scope: \"col\",\n                                    className: \"w-10 px-3 py-3 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        checked: selectAll,\n                                        onChange: handleSelectAllChange,\n                                        disabled: filteredSales.length === 0\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    scope: \"col\",\n                                    className: \"sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"ID\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    scope: \"col\",\n                                    className: \"px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Total Amount\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    scope: \"col\",\n                                    className: \"px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Payment Method\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    scope: \"col\",\n                                    className: \"px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Date\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    scope: \"col\",\n                                    className: \"sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-700\",\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        className: \"divide-y divide-gray-200 bg-white\",\n                        children: filteredSales.map((sale)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: selectedSales.includes(sale.id) ? \"bg-blue-50\" : \"\",\n                                children: [\n                                    canDeleteSale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"whitespace-nowrap px-3 py-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            checked: selectedSales.includes(sale.id),\n                                            onChange: (e)=>handleCheckboxChange(sale.id, e.target.checked)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky left-0 z-10 whitespace-nowrap bg-white px-3 py-4 text-gray-800\",\n                                        children: sale.id\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"whitespace-nowrap px-3 py-4 text-gray-800\",\n                                        children: formatCurrency(sale.totalAmount)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"whitespace-nowrap px-3 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex rounded-full px-2 text-xs font-semibold leading-5 \".concat(sale.paymentMethod === \"cash\" ? \"bg-green-800 text-green-100\" : sale.paymentMethod === \"card\" ? \"bg-blue-800 text-blue-100\" : \"bg-purple-800 text-purple-100\"),\n                                            children: sale.paymentMethod.replace(\"_\", \" \").toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"whitespace-nowrap px-3 py-4 text-gray-800\",\n                                        children: formatDate(sale.transactionDate)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky right-0 z-10 whitespace-nowrap bg-white px-3 py-4 text-right text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-end space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    title: \"View Details\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        onClick: ()=>onViewSale(sale),\n                                                        type: \"text\",\n                                                        className: \"view-button\",\n                                                        size: isMobile ? \"small\" : \"middle\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                ((user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"superadmin\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    title: \"Delete\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 33\n                                                        }, void 0),\n                                                        onClick: ()=>handleDeleteSale(sale.id),\n                                                        type: \"text\",\n                                                        className: \"delete-button\",\n                                                        size: isMobile ? \"small\" : \"middle\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, sale.id, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SalesTable, \"t3PvHUdlxzSS6n51F8mQWWB2WQ0=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_4__.useSelector\n    ];\n});\n_c = SalesTable;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SalesTable);\nvar _c;\n$RefreshReg$(_c, \"SalesTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sales/SalesTable.tsx\n"));

/***/ })

});