{"version": 3, "sources": ["../../../src/server/stream-utils/node-web-streams-helper.ts"], "sourcesContent": ["import { getTracer } from '../lib/trace/tracer'\nimport { AppRenderSpan } from '../lib/trace/constants'\nimport { DetachedPromise } from '../../lib/detached-promise'\nimport { scheduleImmediate, atLeastOneTask } from '../../lib/scheduler'\nimport { ENCODED_TAGS } from './encodedTags'\nimport {\n  indexOfUint8Array,\n  isEquivalentUint8Arrays,\n  removeFromUint8Array,\n} from './uint8array-helpers'\n\nfunction voidCatch() {\n  // this catcher is designed to be used with pipeTo where we expect the underlying\n  // pipe implementation to forward errors but we don't want the pipeTo promise to reject\n  // and be unhandled\n}\n\nexport type ReactReadableStream = ReadableStream<Uint8Array> & {\n  allReady?: Promise<void> | undefined\n}\n\n// We can share the same encoder instance everywhere\n// Notably we cannot do the same for TextDecoder because it is stateful\n// when handling streaming data\nconst encoder = new TextEncoder()\n\nexport function chainStreams<T>(\n  ...streams: ReadableStream<T>[]\n): ReadableStream<T> {\n  // We could encode this invariant in the arguments but current uses of this function pass\n  // use spread so it would be missed by\n  if (streams.length === 0) {\n    throw new Error('Invariant: chainStreams requires at least one stream')\n  }\n\n  // If we only have 1 stream we fast path it by returning just this stream\n  if (streams.length === 1) {\n    return streams[0]\n  }\n\n  const { readable, writable } = new TransformStream()\n\n  // We always initiate pipeTo immediately. We know we have at least 2 streams\n  // so we need to avoid closing the writable when this one finishes.\n  let promise = streams[0].pipeTo(writable, { preventClose: true })\n\n  let i = 1\n  for (; i < streams.length - 1; i++) {\n    const nextStream = streams[i]\n    promise = promise.then(() =>\n      nextStream.pipeTo(writable, { preventClose: true })\n    )\n  }\n\n  // We can omit the length check because we halted before the last stream and there\n  // is at least two streams so the lastStream here will always be defined\n  const lastStream = streams[i]\n  promise = promise.then(() => lastStream.pipeTo(writable))\n\n  // Catch any errors from the streams and ignore them, they will be handled\n  // by whatever is consuming the readable stream.\n  promise.catch(voidCatch)\n\n  return readable\n}\n\nexport function streamFromString(str: string): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(encoder.encode(str))\n      controller.close()\n    },\n  })\n}\n\nexport function streamFromBuffer(chunk: Buffer): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(chunk)\n      controller.close()\n    },\n  })\n}\n\nexport async function streamToBuffer(\n  stream: ReadableStream<Uint8Array>\n): Promise<Buffer> {\n  const reader = stream.getReader()\n  const chunks: Uint8Array[] = []\n\n  while (true) {\n    const { done, value } = await reader.read()\n    if (done) {\n      break\n    }\n\n    chunks.push(value)\n  }\n\n  return Buffer.concat(chunks)\n}\n\nexport async function streamToString(\n  stream: ReadableStream<Uint8Array>\n): Promise<string> {\n  const decoder = new TextDecoder('utf-8', { fatal: true })\n  let string = ''\n\n  for await (const chunk of stream) {\n    string += decoder.decode(chunk, { stream: true })\n  }\n\n  string += decoder.decode()\n\n  return string\n}\n\nexport function createBufferedTransformStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let bufferedChunks: Array<Uint8Array> = []\n  let bufferByteLength: number = 0\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    // If we already have a pending flush, then return early.\n    if (pending) return\n\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        const chunk = new Uint8Array(bufferByteLength)\n        let copiedBytes = 0\n\n        for (let i = 0; i < bufferedChunks.length; i++) {\n          const bufferedChunk = bufferedChunks[i]\n          chunk.set(bufferedChunk, copiedBytes)\n          copiedBytes += bufferedChunk.byteLength\n        }\n        // We just wrote all the buffered chunks so we need to reset the bufferedChunks array\n        // and our bufferByteLength to prepare for the next round of buffered chunks\n        bufferedChunks.length = 0\n        bufferByteLength = 0\n        controller.enqueue(chunk)\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      // Combine the previous buffer with the new chunk.\n      bufferedChunks.push(chunk)\n      bufferByteLength += chunk.byteLength\n\n      // Flush the buffer to the controller.\n      flush(controller)\n    },\n    flush() {\n      if (!pending) return\n\n      return pending.promise\n    },\n  })\n}\n\nfunction createInsertedHTMLStream(\n  getServerInsertedHTML: () => Promise<string>\n): TransformStream<Uint8Array, Uint8Array> {\n  return new TransformStream({\n    transform: async (chunk, controller) => {\n      const html = await getServerInsertedHTML()\n      if (html) {\n        controller.enqueue(encoder.encode(html))\n      }\n\n      controller.enqueue(chunk)\n    },\n  })\n}\n\nexport function renderToInitialFizzStream({\n  ReactDOMServer,\n  element,\n  streamOptions,\n}: {\n  ReactDOMServer: typeof import('react-dom/server.edge')\n  element: React.ReactElement\n  streamOptions?: Parameters<typeof ReactDOMServer.renderToReadableStream>[1]\n}): Promise<ReactReadableStream> {\n  return getTracer().trace(AppRenderSpan.renderToReadableStream, async () =>\n    ReactDOMServer.renderToReadableStream(element, streamOptions)\n  )\n}\n\nfunction createHeadInsertionTransformStream(\n  insert: () => Promise<string>\n): TransformStream<Uint8Array, Uint8Array> {\n  let inserted = false\n  let freezing = false\n\n  // We need to track if this transform saw any bytes because if it didn't\n  // we won't want to insert any server HTML at all\n  let hasBytes = false\n\n  return new TransformStream({\n    async transform(chunk, controller) {\n      hasBytes = true\n      // While react is flushing chunks, we don't apply insertions\n      if (freezing) {\n        controller.enqueue(chunk)\n        return\n      }\n\n      const insertion = await insert()\n\n      if (inserted) {\n        if (insertion) {\n          const encodedInsertion = encoder.encode(insertion)\n          controller.enqueue(encodedInsertion)\n        }\n        controller.enqueue(chunk)\n        freezing = true\n      } else {\n        // TODO (@Ethan-Arrowood): Replace the generic `indexOfUint8Array` method with something finely tuned for the subset of things actually being checked for.\n        const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.HEAD)\n        if (index !== -1) {\n          if (insertion) {\n            const encodedInsertion = encoder.encode(insertion)\n            const insertedHeadContent = new Uint8Array(\n              chunk.length + encodedInsertion.length\n            )\n            insertedHeadContent.set(chunk.slice(0, index))\n            insertedHeadContent.set(encodedInsertion, index)\n            insertedHeadContent.set(\n              chunk.slice(index),\n              index + encodedInsertion.length\n            )\n            controller.enqueue(insertedHeadContent)\n          } else {\n            controller.enqueue(chunk)\n          }\n          freezing = true\n          inserted = true\n        }\n      }\n\n      if (!inserted) {\n        controller.enqueue(chunk)\n      } else {\n        scheduleImmediate(() => {\n          freezing = false\n        })\n      }\n    },\n    async flush(controller) {\n      // Check before closing if there's anything remaining to insert.\n      if (hasBytes) {\n        const insertion = await insert()\n        if (insertion) {\n          controller.enqueue(encoder.encode(insertion))\n        }\n      }\n    },\n  })\n}\n\n// Suffix after main body content - scripts before </body>,\n// but wait for the major chunks to be enqueued.\nfunction createDeferredSuffixStream(\n  suffix: string\n): TransformStream<Uint8Array, Uint8Array> {\n  let flushed = false\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        controller.enqueue(encoder.encode(suffix))\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // If we've already flushed, we're done.\n      if (flushed) return\n\n      // Schedule the flush to happen.\n      flushed = true\n      flush(controller)\n    },\n    flush(controller) {\n      if (pending) return pending.promise\n      if (flushed) return\n\n      // Flush now.\n      controller.enqueue(encoder.encode(suffix))\n    },\n  })\n}\n\n// Merge two streams into one. Ensure the final transform stream is closed\n// when both are finished.\nfunction createMergedTransformStream(\n  stream: ReadableStream<Uint8Array>\n): TransformStream<Uint8Array, Uint8Array> {\n  let pull: Promise<void> | null = null\n  let donePulling = false\n\n  async function startPulling(controller: TransformStreamDefaultController) {\n    if (pull) {\n      return\n    }\n\n    const reader = stream.getReader()\n\n    // NOTE: streaming flush\n    // We are buffering here for the inlined data stream because the\n    // \"shell\" stream might be chunkenized again by the underlying stream\n    // implementation, e.g. with a specific high-water mark. To ensure it's\n    // the safe timing to pipe the data stream, this extra tick is\n    // necessary.\n\n    // We don't start reading until we've left the current Task to ensure\n    // that it's inserted after flushing the shell. Note that this implementation\n    // might get stale if impl details of Fizz change in the future.\n    await atLeastOneTask()\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) {\n          donePulling = true\n          return\n        }\n\n        controller.enqueue(value)\n      }\n    } catch (err) {\n      controller.error(err)\n    }\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // Start the streaming if it hasn't already been started yet.\n      if (!pull) {\n        pull = startPulling(controller)\n      }\n    },\n    flush(controller) {\n      if (donePulling) {\n        return\n      }\n      return pull || startPulling(controller)\n    },\n  })\n}\n\nconst CLOSE_TAG = '</body></html>'\n\n/**\n * This transform stream moves the suffix to the end of the stream, so results\n * like `</body></html><script>...</script>` will be transformed to\n * `<script>...</script></body></html>`.\n */\nfunction createMoveSuffixStream(): TransformStream<Uint8Array, Uint8Array> {\n  let foundSuffix = false\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      if (foundSuffix) {\n        return controller.enqueue(chunk)\n      }\n\n      const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n      if (index > -1) {\n        foundSuffix = true\n\n        // If the whole chunk is the suffix, then don't write anything, it will\n        // be written in the flush.\n        if (chunk.length === ENCODED_TAGS.CLOSED.BODY_AND_HTML.length) {\n          return\n        }\n\n        // Write out the part before the suffix.\n        const before = chunk.slice(0, index)\n        controller.enqueue(before)\n\n        // In the case where the suffix is in the middle of the chunk, we need\n        // to split the chunk into two parts.\n        if (chunk.length > ENCODED_TAGS.CLOSED.BODY_AND_HTML.length + index) {\n          // Write out the part after the suffix.\n          const after = chunk.slice(\n            index + ENCODED_TAGS.CLOSED.BODY_AND_HTML.length\n          )\n          controller.enqueue(after)\n        }\n      } else {\n        controller.enqueue(chunk)\n      }\n    },\n    flush(controller) {\n      // Even if we didn't find the suffix, the HTML is not valid if we don't\n      // add it, so insert it at the end.\n      controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n    },\n  })\n}\n\nfunction createStripDocumentClosingTagsTransform(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  return new TransformStream({\n    transform(chunk, controller) {\n      // We rely on the assumption that chunks will never break across a code unit.\n      // This is reasonable because we currently concat all of React's output from a single\n      // flush into one chunk before streaming it forward which means the chunk will represent\n      // a single coherent utf-8 string. This is not safe to use if we change our streaming to no\n      // longer do this large buffered chunk\n      if (\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.HTML)\n      ) {\n        // the entire chunk is the closing tags; return without enqueueing anything.\n        return\n      }\n\n      // We assume these tags will go at together at the end of the document and that\n      // they won't appear anywhere else in the document. This is not really a safe assumption\n      // but until we revamp our streaming infra this is a performant way to string the tags\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY)\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.HTML)\n\n      controller.enqueue(chunk)\n    },\n  })\n}\n\n/*\n * Checks if the root layout is missing the html or body tags\n * and if so, it will inject a script tag to throw an error in the browser, showing the user\n * the error message in the error overlay.\n */\nexport function createRootLayoutValidatorStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let foundHtml = false\n  let foundBody = false\n  return new TransformStream({\n    async transform(chunk, controller) {\n      // Peek into the streamed chunk to see if the tags are present.\n      if (\n        !foundHtml &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.HTML) > -1\n      ) {\n        foundHtml = true\n      }\n\n      if (\n        !foundBody &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.BODY) > -1\n      ) {\n        foundBody = true\n      }\n\n      controller.enqueue(chunk)\n    },\n    flush(controller) {\n      const missingTags: typeof window.__next_root_layout_missing_tags = []\n      if (!foundHtml) missingTags.push('html')\n      if (!foundBody) missingTags.push('body')\n\n      if (!missingTags.length) return\n\n      controller.enqueue(\n        encoder.encode(\n          `<script>self.__next_root_layout_missing_tags=${JSON.stringify(\n            missingTags\n          )}</script>`\n        )\n      )\n    },\n  })\n}\n\nfunction chainTransformers<T>(\n  readable: ReadableStream<T>,\n  transformers: ReadonlyArray<TransformStream<T, T> | null>\n): ReadableStream<T> {\n  let stream = readable\n  for (const transformer of transformers) {\n    if (!transformer) continue\n\n    stream = stream.pipeThrough(transformer)\n  }\n  return stream\n}\n\nexport type ContinueStreamOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array> | undefined\n  isStaticGeneration: boolean\n  getServerInsertedHTML: (() => Promise<string>) | undefined\n  serverInsertedHTMLToHead: boolean\n  validateRootLayout?: boolean\n  /**\n   * Suffix to inject after the buffered data, but before the close tags.\n   */\n  suffix?: string | undefined\n}\n\nexport async function continueFizzStream(\n  renderStream: ReactReadableStream,\n  {\n    suffix,\n    inlinedDataStream,\n    isStaticGeneration,\n    getServerInsertedHTML,\n    serverInsertedHTMLToHead,\n    validateRootLayout,\n  }: ContinueStreamOptions\n): Promise<ReadableStream<Uint8Array>> {\n  // Suffix itself might contain close tags at the end, so we need to split it.\n  const suffixUnclosed = suffix ? suffix.split(CLOSE_TAG, 1)[0] : null\n\n  // If we're generating static HTML and there's an `allReady` promise on the\n  // stream, we need to wait for it to resolve before continuing.\n  if (isStaticGeneration && 'allReady' in renderStream) {\n    await renderStream.allReady\n  }\n\n  return chainTransformers(renderStream, [\n    // Buffer everything to avoid flushing too frequently\n    createBufferedTransformStream(),\n\n    // Insert generated tags to head\n    getServerInsertedHTML && !serverInsertedHTMLToHead\n      ? createInsertedHTMLStream(getServerInsertedHTML)\n      : null,\n\n    // Insert suffix content\n    suffixUnclosed != null && suffixUnclosed.length > 0\n      ? createDeferredSuffixStream(suffixUnclosed)\n      : null,\n\n    // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n    inlinedDataStream ? createMergedTransformStream(inlinedDataStream) : null,\n\n    // Validate the root layout for missing html or body tags\n    validateRootLayout ? createRootLayoutValidatorStream() : null,\n\n    // Close tags should always be deferred to the end\n    createMoveSuffixStream(),\n\n    // Special head insertions\n    // TODO-APP: Insert server side html to end of head in app layout rendering, to avoid\n    // hydration errors. Remove this once it's ready to be handled by react itself.\n    getServerInsertedHTML && serverInsertedHTMLToHead\n      ? createHeadInsertionTransformStream(getServerInsertedHTML)\n      : null,\n  ])\n}\n\ntype ContinueDynamicPrerenderOptions = {\n  getServerInsertedHTML: () => Promise<string>\n}\n\nexport async function continueDynamicPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  { getServerInsertedHTML }: ContinueDynamicPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      .pipeThrough(createStripDocumentClosingTagsTransform())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n  )\n}\n\ntype ContinueStaticPrerenderOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n}\n\nexport async function continueStaticPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  { inlinedDataStream, getServerInsertedHTML }: ContinueStaticPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\ntype ContinueResumeOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n}\n\nexport async function continueDynamicHTMLResume(\n  renderStream: ReadableStream<Uint8Array>,\n  { inlinedDataStream, getServerInsertedHTML }: ContinueResumeOptions\n) {\n  return (\n    renderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\nexport function createDocumentClosingStream(): ReadableStream<Uint8Array> {\n  return streamFromString(CLOSE_TAG)\n}\n"], "names": ["getTracer", "AppRenderSpan", "Detached<PERSON>romise", "scheduleImmediate", "atLeastOneTask", "ENCODED_TAGS", "indexOfUint8Array", "isEquivalentUint8Arrays", "removeFromUint8Array", "voidCatch", "encoder", "TextEncoder", "chainStreams", "streams", "length", "Error", "readable", "writable", "TransformStream", "promise", "pipeTo", "preventClose", "i", "nextStream", "then", "lastStream", "catch", "streamFromString", "str", "ReadableStream", "start", "controller", "enqueue", "encode", "close", "streamFromBuffer", "chunk", "streamToBuffer", "stream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "chunks", "done", "value", "read", "push", "<PERSON><PERSON><PERSON>", "concat", "streamToString", "decoder", "TextDecoder", "fatal", "string", "decode", "createBufferedTransformStream", "bufferedChunks", "bufferByteLength", "pending", "flush", "detached", "Uint8Array", "copiedBytes", "bufferedChunk", "set", "byteLength", "undefined", "resolve", "transform", "createInsertedHTMLStream", "getServerInsertedHTML", "html", "renderToInitialFizzStream", "ReactDOMServer", "element", "streamOptions", "trace", "renderToReadableStream", "createHeadInsertionTransformStream", "insert", "inserted", "freezing", "hasBytes", "insertion", "encodedInsertion", "index", "CLOSED", "HEAD", "insertedHeadContent", "slice", "createDeferredSuffixStream", "suffix", "flushed", "createMergedTransformStream", "pull", "donePulling", "startPulling", "err", "error", "CLOSE_TAG", "createMoveSuffixStream", "foundSuffix", "BODY_AND_HTML", "before", "after", "createStripDocumentClosingTagsTransform", "BODY", "HTML", "createRootLayoutValidatorStream", "foundHtml", "foundBody", "OPENING", "missingTags", "JSON", "stringify", "chainTransformers", "transformers", "transformer", "pipeThrough", "continueFizzStream", "renderStream", "inlinedDataStream", "isStaticGeneration", "serverInsertedHTMLToHead", "validateRootLayout", "suffixUnclosed", "split", "allReady", "continueDynamicPrerender", "prerenderStream", "continueStaticP<PERSON><PERSON>", "continueDynamicHTMLResume", "createDocumentClosingStream"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SAASC,eAAe,QAAQ,6BAA4B;AAC5D,SAASC,iBAAiB,EAAEC,cAAc,QAAQ,sBAAqB;AACvE,SAASC,YAAY,QAAQ,gBAAe;AAC5C,SACEC,iBAAiB,EACjBC,uBAAuB,EACvBC,oBAAoB,QACf,uBAAsB;AAE7B,SAASC;AACP,iFAAiF;AACjF,uFAAuF;AACvF,mBAAmB;AACrB;AAMA,oDAAoD;AACpD,uEAAuE;AACvE,+BAA+B;AAC/B,MAAMC,UAAU,IAAIC;AAEpB,OAAO,SAASC,aACd,GAAGC,OAA4B;IAE/B,yFAAyF;IACzF,sCAAsC;IACtC,IAAIA,QAAQC,MAAM,KAAK,GAAG;QACxB,MAAM,IAAIC,MAAM;IAClB;IAEA,yEAAyE;IACzE,IAAIF,QAAQC,MAAM,KAAK,GAAG;QACxB,OAAOD,OAAO,CAAC,EAAE;IACnB;IAEA,MAAM,EAAEG,QAAQ,EAAEC,QAAQ,EAAE,GAAG,IAAIC;IAEnC,4EAA4E;IAC5E,mEAAmE;IACnE,IAAIC,UAAUN,OAAO,CAAC,EAAE,CAACO,MAAM,CAACH,UAAU;QAAEI,cAAc;IAAK;IAE/D,IAAIC,IAAI;IACR,MAAOA,IAAIT,QAAQC,MAAM,GAAG,GAAGQ,IAAK;QAClC,MAAMC,aAAaV,OAAO,CAACS,EAAE;QAC7BH,UAAUA,QAAQK,IAAI,CAAC,IACrBD,WAAWH,MAAM,CAACH,UAAU;gBAAEI,cAAc;YAAK;IAErD;IAEA,kFAAkF;IAClF,wEAAwE;IACxE,MAAMI,aAAaZ,OAAO,CAACS,EAAE;IAC7BH,UAAUA,QAAQK,IAAI,CAAC,IAAMC,WAAWL,MAAM,CAACH;IAE/C,0EAA0E;IAC1E,gDAAgD;IAChDE,QAAQO,KAAK,CAACjB;IAEd,OAAOO;AACT;AAEA,OAAO,SAASW,iBAAiBC,GAAW;IAC1C,OAAO,IAAIC,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAACL;YAClCG,WAAWG,KAAK;QAClB;IACF;AACF;AAEA,OAAO,SAASC,iBAAiBC,KAAa;IAC5C,OAAO,IAAIP,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACI;YACnBL,WAAWG,KAAK;QAClB;IACF;AACF;AAEA,OAAO,eAAeG,eACpBC,MAAkC;IAElC,MAAMC,SAASD,OAAOE,SAAS;IAC/B,MAAMC,SAAuB,EAAE;IAE/B,MAAO,KAAM;QACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOK,IAAI;QACzC,IAAIF,MAAM;YACR;QACF;QAEAD,OAAOI,IAAI,CAACF;IACd;IAEA,OAAOG,OAAOC,MAAM,CAACN;AACvB;AAEA,OAAO,eAAeO,eACpBV,MAAkC;IAElC,MAAMW,UAAU,IAAIC,YAAY,SAAS;QAAEC,OAAO;IAAK;IACvD,IAAIC,SAAS;IAEb,WAAW,MAAMhB,SAASE,OAAQ;QAChCc,UAAUH,QAAQI,MAAM,CAACjB,OAAO;YAAEE,QAAQ;QAAK;IACjD;IAEAc,UAAUH,QAAQI,MAAM;IAExB,OAAOD;AACT;AAEA,OAAO,SAASE;IAId,IAAIC,iBAAoC,EAAE;IAC1C,IAAIC,mBAA2B;IAC/B,IAAIC;IAEJ,MAAMC,QAAQ,CAAC3B;QACb,yDAAyD;QACzD,IAAI0B,SAAS;QAEb,MAAME,WAAW,IAAIzD;QACrBuD,UAAUE;QAEVxD,kBAAkB;YAChB,IAAI;gBACF,MAAMiC,QAAQ,IAAIwB,WAAWJ;gBAC7B,IAAIK,cAAc;gBAElB,IAAK,IAAIvC,IAAI,GAAGA,IAAIiC,eAAezC,MAAM,EAAEQ,IAAK;oBAC9C,MAAMwC,gBAAgBP,cAAc,CAACjC,EAAE;oBACvCc,MAAM2B,GAAG,CAACD,eAAeD;oBACzBA,eAAeC,cAAcE,UAAU;gBACzC;gBACA,qFAAqF;gBACrF,4EAA4E;gBAC5ET,eAAezC,MAAM,GAAG;gBACxB0C,mBAAmB;gBACnBzB,WAAWC,OAAO,CAACI;YACrB,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRqB,UAAUQ;gBACVN,SAASO,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIhD,gBAAgB;QACzBiD,WAAU/B,KAAK,EAAEL,UAAU;YACzB,kDAAkD;YAClDwB,eAAeV,IAAI,CAACT;YACpBoB,oBAAoBpB,MAAM4B,UAAU;YAEpC,sCAAsC;YACtCN,MAAM3B;QACR;QACA2B;YACE,IAAI,CAACD,SAAS;YAEd,OAAOA,QAAQtC,OAAO;QACxB;IACF;AACF;AAEA,SAASiD,yBACPC,qBAA4C;IAE5C,OAAO,IAAInD,gBAAgB;QACzBiD,WAAW,OAAO/B,OAAOL;YACvB,MAAMuC,OAAO,MAAMD;YACnB,IAAIC,MAAM;gBACRvC,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAACqC;YACpC;YAEAvC,WAAWC,OAAO,CAACI;QACrB;IACF;AACF;AAEA,OAAO,SAASmC,0BAA0B,EACxCC,cAAc,EACdC,OAAO,EACPC,aAAa,EAKd;IACC,OAAO1E,YAAY2E,KAAK,CAAC1E,cAAc2E,sBAAsB,EAAE,UAC7DJ,eAAeI,sBAAsB,CAACH,SAASC;AAEnD;AAEA,SAASG,mCACPC,MAA6B;IAE7B,IAAIC,WAAW;IACf,IAAIC,WAAW;IAEf,wEAAwE;IACxE,iDAAiD;IACjD,IAAIC,WAAW;IAEf,OAAO,IAAI/D,gBAAgB;QACzB,MAAMiD,WAAU/B,KAAK,EAAEL,UAAU;YAC/BkD,WAAW;YACX,4DAA4D;YAC5D,IAAID,UAAU;gBACZjD,WAAWC,OAAO,CAACI;gBACnB;YACF;YAEA,MAAM8C,YAAY,MAAMJ;YAExB,IAAIC,UAAU;gBACZ,IAAIG,WAAW;oBACb,MAAMC,mBAAmBzE,QAAQuB,MAAM,CAACiD;oBACxCnD,WAAWC,OAAO,CAACmD;gBACrB;gBACApD,WAAWC,OAAO,CAACI;gBACnB4C,WAAW;YACb,OAAO;gBACL,0JAA0J;gBAC1J,MAAMI,QAAQ9E,kBAAkB8B,OAAO/B,aAAagF,MAAM,CAACC,IAAI;gBAC/D,IAAIF,UAAU,CAAC,GAAG;oBAChB,IAAIF,WAAW;wBACb,MAAMC,mBAAmBzE,QAAQuB,MAAM,CAACiD;wBACxC,MAAMK,sBAAsB,IAAI3B,WAC9BxB,MAAMtB,MAAM,GAAGqE,iBAAiBrE,MAAM;wBAExCyE,oBAAoBxB,GAAG,CAAC3B,MAAMoD,KAAK,CAAC,GAAGJ;wBACvCG,oBAAoBxB,GAAG,CAACoB,kBAAkBC;wBAC1CG,oBAAoBxB,GAAG,CACrB3B,MAAMoD,KAAK,CAACJ,QACZA,QAAQD,iBAAiBrE,MAAM;wBAEjCiB,WAAWC,OAAO,CAACuD;oBACrB,OAAO;wBACLxD,WAAWC,OAAO,CAACI;oBACrB;oBACA4C,WAAW;oBACXD,WAAW;gBACb;YACF;YAEA,IAAI,CAACA,UAAU;gBACbhD,WAAWC,OAAO,CAACI;YACrB,OAAO;gBACLjC,kBAAkB;oBAChB6E,WAAW;gBACb;YACF;QACF;QACA,MAAMtB,OAAM3B,UAAU;YACpB,gEAAgE;YAChE,IAAIkD,UAAU;gBACZ,MAAMC,YAAY,MAAMJ;gBACxB,IAAII,WAAW;oBACbnD,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAACiD;gBACpC;YACF;QACF;IACF;AACF;AAEA,2DAA2D;AAC3D,gDAAgD;AAChD,SAASO,2BACPC,MAAc;IAEd,IAAIC,UAAU;IACd,IAAIlC;IAEJ,MAAMC,QAAQ,CAAC3B;QACb,MAAM4B,WAAW,IAAIzD;QACrBuD,UAAUE;QAEVxD,kBAAkB;YAChB,IAAI;gBACF4B,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAACyD;YACpC,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRjC,UAAUQ;gBACVN,SAASO,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIhD,gBAAgB;QACzBiD,WAAU/B,KAAK,EAAEL,UAAU;YACzBA,WAAWC,OAAO,CAACI;YAEnB,wCAAwC;YACxC,IAAIuD,SAAS;YAEb,gCAAgC;YAChCA,UAAU;YACVjC,MAAM3B;QACR;QACA2B,OAAM3B,UAAU;YACd,IAAI0B,SAAS,OAAOA,QAAQtC,OAAO;YACnC,IAAIwE,SAAS;YAEb,aAAa;YACb5D,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAACyD;QACpC;IACF;AACF;AAEA,0EAA0E;AAC1E,0BAA0B;AAC1B,SAASE,4BACPtD,MAAkC;IAElC,IAAIuD,OAA6B;IACjC,IAAIC,cAAc;IAElB,eAAeC,aAAahE,UAA4C;QACtE,IAAI8D,MAAM;YACR;QACF;QAEA,MAAMtD,SAASD,OAAOE,SAAS;QAE/B,wBAAwB;QACxB,gEAAgE;QAChE,qEAAqE;QACrE,uEAAuE;QACvE,8DAA8D;QAC9D,aAAa;QAEb,qEAAqE;QACrE,6EAA6E;QAC7E,gEAAgE;QAChE,MAAMpC;QAEN,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAEsC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOK,IAAI;gBACzC,IAAIF,MAAM;oBACRoD,cAAc;oBACd;gBACF;gBAEA/D,WAAWC,OAAO,CAACW;YACrB;QACF,EAAE,OAAOqD,KAAK;YACZjE,WAAWkE,KAAK,CAACD;QACnB;IACF;IAEA,OAAO,IAAI9E,gBAAgB;QACzBiD,WAAU/B,KAAK,EAAEL,UAAU;YACzBA,WAAWC,OAAO,CAACI;YAEnB,6DAA6D;YAC7D,IAAI,CAACyD,MAAM;gBACTA,OAAOE,aAAahE;YACtB;QACF;QACA2B,OAAM3B,UAAU;YACd,IAAI+D,aAAa;gBACf;YACF;YACA,OAAOD,QAAQE,aAAahE;QAC9B;IACF;AACF;AAEA,MAAMmE,YAAY;AAElB;;;;CAIC,GACD,SAASC;IACP,IAAIC,cAAc;IAElB,OAAO,IAAIlF,gBAAgB;QACzBiD,WAAU/B,KAAK,EAAEL,UAAU;YACzB,IAAIqE,aAAa;gBACf,OAAOrE,WAAWC,OAAO,CAACI;YAC5B;YAEA,MAAMgD,QAAQ9E,kBAAkB8B,OAAO/B,aAAagF,MAAM,CAACgB,aAAa;YACxE,IAAIjB,QAAQ,CAAC,GAAG;gBACdgB,cAAc;gBAEd,uEAAuE;gBACvE,2BAA2B;gBAC3B,IAAIhE,MAAMtB,MAAM,KAAKT,aAAagF,MAAM,CAACgB,aAAa,CAACvF,MAAM,EAAE;oBAC7D;gBACF;gBAEA,wCAAwC;gBACxC,MAAMwF,SAASlE,MAAMoD,KAAK,CAAC,GAAGJ;gBAC9BrD,WAAWC,OAAO,CAACsE;gBAEnB,sEAAsE;gBACtE,qCAAqC;gBACrC,IAAIlE,MAAMtB,MAAM,GAAGT,aAAagF,MAAM,CAACgB,aAAa,CAACvF,MAAM,GAAGsE,OAAO;oBACnE,uCAAuC;oBACvC,MAAMmB,QAAQnE,MAAMoD,KAAK,CACvBJ,QAAQ/E,aAAagF,MAAM,CAACgB,aAAa,CAACvF,MAAM;oBAElDiB,WAAWC,OAAO,CAACuE;gBACrB;YACF,OAAO;gBACLxE,WAAWC,OAAO,CAACI;YACrB;QACF;QACAsB,OAAM3B,UAAU;YACd,uEAAuE;YACvE,mCAAmC;YACnCA,WAAWC,OAAO,CAAC3B,aAAagF,MAAM,CAACgB,aAAa;QACtD;IACF;AACF;AAEA,SAASG;IAIP,OAAO,IAAItF,gBAAgB;QACzBiD,WAAU/B,KAAK,EAAEL,UAAU;YACzB,6EAA6E;YAC7E,qFAAqF;YACrF,wFAAwF;YACxF,2FAA2F;YAC3F,sCAAsC;YACtC,IACExB,wBAAwB6B,OAAO/B,aAAagF,MAAM,CAACgB,aAAa,KAChE9F,wBAAwB6B,OAAO/B,aAAagF,MAAM,CAACoB,IAAI,KACvDlG,wBAAwB6B,OAAO/B,aAAagF,MAAM,CAACqB,IAAI,GACvD;gBACA,4EAA4E;gBAC5E;YACF;YAEA,+EAA+E;YAC/E,wFAAwF;YACxF,sFAAsF;YACtFtE,QAAQ5B,qBAAqB4B,OAAO/B,aAAagF,MAAM,CAACoB,IAAI;YAC5DrE,QAAQ5B,qBAAqB4B,OAAO/B,aAAagF,MAAM,CAACqB,IAAI;YAE5D3E,WAAWC,OAAO,CAACI;QACrB;IACF;AACF;AAEA;;;;CAIC,GACD,OAAO,SAASuE;IAId,IAAIC,YAAY;IAChB,IAAIC,YAAY;IAChB,OAAO,IAAI3F,gBAAgB;QACzB,MAAMiD,WAAU/B,KAAK,EAAEL,UAAU;YAC/B,+DAA+D;YAC/D,IACE,CAAC6E,aACDtG,kBAAkB8B,OAAO/B,aAAayG,OAAO,CAACJ,IAAI,IAAI,CAAC,GACvD;gBACAE,YAAY;YACd;YAEA,IACE,CAACC,aACDvG,kBAAkB8B,OAAO/B,aAAayG,OAAO,CAACL,IAAI,IAAI,CAAC,GACvD;gBACAI,YAAY;YACd;YAEA9E,WAAWC,OAAO,CAACI;QACrB;QACAsB,OAAM3B,UAAU;YACd,MAAMgF,cAA6D,EAAE;YACrE,IAAI,CAACH,WAAWG,YAAYlE,IAAI,CAAC;YACjC,IAAI,CAACgE,WAAWE,YAAYlE,IAAI,CAAC;YAEjC,IAAI,CAACkE,YAAYjG,MAAM,EAAE;YAEzBiB,WAAWC,OAAO,CAChBtB,QAAQuB,MAAM,CACZ,CAAC,6CAA6C,EAAE+E,KAAKC,SAAS,CAC5DF,aACA,SAAS,CAAC;QAGlB;IACF;AACF;AAEA,SAASG,kBACPlG,QAA2B,EAC3BmG,YAAyD;IAEzD,IAAI7E,SAAStB;IACb,KAAK,MAAMoG,eAAeD,aAAc;QACtC,IAAI,CAACC,aAAa;QAElB9E,SAASA,OAAO+E,WAAW,CAACD;IAC9B;IACA,OAAO9E;AACT;AAcA,OAAO,eAAegF,mBACpBC,YAAiC,EACjC,EACE7B,MAAM,EACN8B,iBAAiB,EACjBC,kBAAkB,EAClBpD,qBAAqB,EACrBqD,wBAAwB,EACxBC,kBAAkB,EACI;IAExB,6EAA6E;IAC7E,MAAMC,iBAAiBlC,SAASA,OAAOmC,KAAK,CAAC3B,WAAW,EAAE,CAAC,EAAE,GAAG;IAEhE,2EAA2E;IAC3E,+DAA+D;IAC/D,IAAIuB,sBAAsB,cAAcF,cAAc;QACpD,MAAMA,aAAaO,QAAQ;IAC7B;IAEA,OAAOZ,kBAAkBK,cAAc;QACrC,qDAAqD;QACrDjE;QAEA,gCAAgC;QAChCe,yBAAyB,CAACqD,2BACtBtD,yBAAyBC,yBACzB;QAEJ,wBAAwB;QACxBuD,kBAAkB,QAAQA,eAAe9G,MAAM,GAAG,IAC9C2E,2BAA2BmC,kBAC3B;QAEJ,+EAA+E;QAC/EJ,oBAAoB5B,4BAA4B4B,qBAAqB;QAErE,yDAAyD;QACzDG,qBAAqBhB,oCAAoC;QAEzD,kDAAkD;QAClDR;QAEA,0BAA0B;QAC1B,qFAAqF;QACrF,+EAA+E;QAC/E9B,yBAAyBqD,2BACrB7C,mCAAmCR,yBACnC;KACL;AACH;AAMA,OAAO,eAAe0D,yBACpBC,eAA2C,EAC3C,EAAE3D,qBAAqB,EAAmC;IAE1D,OACE2D,eACE,qDAAqD;KACpDX,WAAW,CAAC/D,iCACZ+D,WAAW,CAACb,0CACb,gCAAgC;KAC/Ba,WAAW,CAACxC,mCAAmCR;AAEtD;AAOA,OAAO,eAAe4D,wBACpBD,eAA2C,EAC3C,EAAER,iBAAiB,EAAEnD,qBAAqB,EAAkC;IAE5E,OACE2D,eACE,qDAAqD;KACpDX,WAAW,CAAC/D,gCACb,gCAAgC;KAC/B+D,WAAW,CAACxC,mCAAmCR,uBAChD,+EAA+E;KAC9EgD,WAAW,CAACzB,4BAA4B4B,mBACzC,kDAAkD;KACjDH,WAAW,CAAClB;AAEnB;AAOA,OAAO,eAAe+B,0BACpBX,YAAwC,EACxC,EAAEC,iBAAiB,EAAEnD,qBAAqB,EAAyB;IAEnE,OACEkD,YACE,qDAAqD;KACpDF,WAAW,CAAC/D,gCACb,gCAAgC;KAC/B+D,WAAW,CAACxC,mCAAmCR,uBAChD,+EAA+E;KAC9EgD,WAAW,CAACzB,4BAA4B4B,mBACzC,kDAAkD;KACjDH,WAAW,CAAClB;AAEnB;AAEA,OAAO,SAASgC;IACd,OAAOxG,iBAAiBuE;AAC1B"}