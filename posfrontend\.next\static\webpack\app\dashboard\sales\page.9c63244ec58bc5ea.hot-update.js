"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/sales/page",{

/***/ "(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx":
/*!*************************************************!*\
  !*** ./src/components/Sales/SalesFormPanel.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/image/index.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DatabaseOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PrinterOutlined.js\");\n/* harmony import */ var _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/reduxRTK/services/salesApi */ \"(app-pages-browser)/./src/reduxRTK/services/salesApi.ts\");\n/* harmony import */ var _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/reduxRTK/services/productApi */ \"(app-pages-browser)/./src/reduxRTK/services/productApi.ts\");\n/* harmony import */ var _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/reduxRTK/services/userStoreApi */ \"(app-pages-browser)/./src/reduxRTK/services/userStoreApi.ts\");\n/* harmony import */ var _components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/SlidingPanel */ \"(app-pages-browser)/./src/components/ui/SlidingPanel.tsx\");\n/* harmony import */ var _utils_showMessage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/showMessage */ \"(app-pages-browser)/./src/utils/showMessage.ts\");\n/* harmony import */ var _utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/cloudinaryUtils */ \"(app-pages-browser)/./src/utils/cloudinaryUtils.ts\");\n/* harmony import */ var _sales_panels_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./sales-panels.css */ \"(app-pages-browser)/./src/components/Sales/sales-panels.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst SalesFormPanel = (param)=>{\n    let { isOpen, onClose, onSuccess } = param;\n    var _productsData_data, _productsData_data_products, _productsData_data1;\n    _s();\n    const [form] = _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm();\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalAmount, setTotalAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingReceipt, setIsGeneratingReceipt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptPreviewVisible, setReceiptPreviewVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptUrl, setReceiptUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPrinted, setHasPrinted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            console.log(\"Items state changed:\", items);\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items\n    ]);\n    const { data: productsData, isLoading: isLoadingProducts, refetch: refetchProducts, error: productsError, isFetching: isFetchingProducts } = (0,_reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery)({\n        page: 1,\n        limit: 1000,\n        search: searchTerm\n    }, {\n        // Always fetch fresh data from database\n        refetchOnMountOrArgChange: true,\n        refetchOnFocus: true,\n        refetchOnReconnect: true,\n        // Skip caching entirely for sales form to ensure fresh stock data\n        skip: false\n    });\n    // Enhanced products data monitoring and error handling\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (productsData) {\n                var _productsData_data, _productsData_data_products, _productsData_data1, _productsData_data_products_slice, _productsData_data_products1, _productsData_data2;\n                console.log(\"🛒 Fresh products loaded from database:\", {\n                    total: ((_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.total) || 0,\n                    productsCount: ((_productsData_data1 = productsData.data) === null || _productsData_data1 === void 0 ? void 0 : (_productsData_data_products = _productsData_data1.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.length) || 0,\n                    isLoading: isLoadingProducts,\n                    isFetching: isFetchingProducts,\n                    timestamp: new Date().toISOString(),\n                    // Debug: Show first few products\n                    sampleProducts: ((_productsData_data2 = productsData.data) === null || _productsData_data2 === void 0 ? void 0 : (_productsData_data_products1 = _productsData_data2.products) === null || _productsData_data_products1 === void 0 ? void 0 : (_productsData_data_products_slice = _productsData_data_products1.slice(0, 3)) === null || _productsData_data_products_slice === void 0 ? void 0 : _productsData_data_products_slice.map({\n                        \"SalesFormPanel.useEffect\": (p)=>({\n                                id: p.id,\n                                name: p.name,\n                                price: p.price,\n                                stock: p.stockQuantity\n                            })\n                    }[\"SalesFormPanel.useEffect\"])) || [],\n                    // Debug: Full data structure\n                    fullData: productsData\n                });\n            }\n            if (productsError) {\n                console.error(\"❌ Error loading products:\", productsError);\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Failed to load products. Please try again.\");\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        productsData,\n        isLoadingProducts,\n        isFetchingProducts,\n        productsError\n    ]);\n    // Get current user ID from auth state\n    const getCurrentUserId = ()=>{\n        if (true) {\n            var _state_auth_user, _state_auth;\n            // @ts-ignore - Redux state is exposed for debugging\n            const state = window.__REDUX_STATE;\n            return (state === null || state === void 0 ? void 0 : (_state_auth = state.auth) === null || _state_auth === void 0 ? void 0 : (_state_auth_user = _state_auth.user) === null || _state_auth_user === void 0 ? void 0 : _state_auth_user.id) || 0;\n        }\n        return 0;\n    };\n    // Fetch user stores\n    const { data: userStoresData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery)(getCurrentUserId());\n    // Fetch default store\n    const { data: defaultStoreData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery)(getCurrentUserId());\n    // Set default store when data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (defaultStoreData === null || defaultStoreData === void 0 ? void 0 : defaultStoreData.data) {\n                setSelectedStore(defaultStoreData.data);\n                form.setFieldsValue({\n                    storeId: defaultStoreData.data.id\n                });\n            } else if ((userStoresData === null || userStoresData === void 0 ? void 0 : userStoresData.data) && userStoresData.data.length > 0) {\n                setSelectedStore(userStoresData.data[0]);\n                form.setFieldsValue({\n                    storeId: userStoresData.data[0].id\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        defaultStoreData,\n        userStoresData,\n        form\n    ]);\n    // Create sale mutation\n    const [createSale, { isLoading: isSubmitting }] = (0,_reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation)();\n    // Calculate total amount whenever items change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (items && items.length > 0) {\n                const total = items.reduce({\n                    \"SalesFormPanel.useEffect.total\": (sum, item)=>sum + item.price * item.quantity\n                }[\"SalesFormPanel.useEffect.total\"], 0);\n                setTotalAmount(total);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: total\n                    });\n                }\n                // Debug log to check items state\n                console.log(\"Current items in useEffect:\", items);\n            } else {\n                setTotalAmount(0);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: 0\n                    });\n                }\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items,\n        form\n    ]);\n    // Enhanced panel open/close handling with forced data refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (isOpen) {\n                // When panel opens, ALWAYS fetch fresh product data from database\n                console.log(\"🛒 Sales panel opened - forcing fresh product data fetch from database\");\n                // Force refetch to ensure we get the latest stock quantities\n                refetchProducts().then({\n                    \"SalesFormPanel.useEffect\": (result)=>{\n                        if (result.data) {\n                            var _result_data_data_products, _result_data_data;\n                            console.log(\"✅ Fresh product data successfully loaded:\", {\n                                productsCount: ((_result_data_data = result.data.data) === null || _result_data_data === void 0 ? void 0 : (_result_data_data_products = _result_data_data.products) === null || _result_data_data_products === void 0 ? void 0 : _result_data_data_products.length) || 0,\n                                timestamp: new Date().toISOString()\n                            });\n                        }\n                    }\n                }[\"SalesFormPanel.useEffect\"]).catch({\n                    \"SalesFormPanel.useEffect\": (error)=>{\n                        console.error(\"❌ Failed to fetch fresh product data:\", error);\n                        (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Failed to load current product data. Stock quantities may not be accurate.\");\n                    }\n                }[\"SalesFormPanel.useEffect\"]);\n            } else {\n                // Reset form when panel is closed\n                console.log(\"🛒 Sales panel closed - resetting form state\");\n                if (form) {\n                    form.resetFields();\n                }\n                setItems([]);\n                setSelectedProduct(null);\n                setQuantity(1);\n                setTotalAmount(0);\n                setReceiptUrl(null);\n                setReceiptPreviewVisible(false);\n                setHasPrinted(false);\n                setSearchTerm(\"\"); // Reset search term to ensure fresh data on next open\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        isOpen,\n        form,\n        refetchProducts\n    ]);\n    // Handle adding an item to the sale\n    const handleAddItem = ()=>{\n        if (!selectedProduct) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Please select a product\");\n            return;\n        }\n        if (quantity <= 0) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Quantity must be greater than 0\");\n            return;\n        }\n        if (selectedProduct.stockQuantity < quantity) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Only \".concat(selectedProduct.stockQuantity, \" units available in stock\"));\n            return;\n        }\n        console.log(\"Adding item with product:\", selectedProduct);\n        // Check if product already exists in items\n        const existingItemIndex = items.findIndex((item)=>item.productId === selectedProduct.id);\n        if (existingItemIndex >= 0) {\n            // Update existing item\n            const updatedItems = [\n                ...items\n            ];\n            const newQuantity = updatedItems[existingItemIndex].quantity + quantity;\n            if (newQuantity > selectedProduct.stockQuantity) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Cannot add more than \".concat(selectedProduct.stockQuantity, \" units of this product\"));\n                return;\n            }\n            updatedItems[existingItemIndex].quantity = newQuantity;\n            // Make sure the product name is set\n            if (!updatedItems[existingItemIndex].productName) {\n                updatedItems[existingItemIndex].productName = selectedProduct.name;\n            }\n            // Update the state with the new array\n            console.log(\"Updating existing item. New items array:\", updatedItems);\n            setItems([\n                ...updatedItems\n            ]); // Create a new array reference to force re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Updated quantity of \".concat(selectedProduct.name));\n        } else {\n            // Add new item\n            const newItem = {\n                productId: selectedProduct.id,\n                productName: selectedProduct.name,\n                quantity,\n                price: typeof selectedProduct.price === \"string\" ? parseFloat(selectedProduct.price) : selectedProduct.price\n            };\n            // Create a new array with the new item\n            const newItems = [\n                ...items,\n                newItem\n            ];\n            // Update the state with the new array\n            console.log(\"Adding new item. New items array:\", newItems);\n            setItems(newItems); // This should trigger a re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Added \".concat(quantity, \" \").concat(selectedProduct.name, \" to sale\"));\n        }\n        // Reset selection\n        setSelectedProduct(null);\n        setQuantity(1);\n        if (form) {\n            form.setFieldsValue({\n                productId: undefined,\n                quantity: 1\n            });\n        }\n    };\n    // Handle removing an item from the sale\n    const handleRemoveItem = (index)=>{\n        const updatedItems = [\n            ...items\n        ];\n        updatedItems.splice(index, 1);\n        setItems(updatedItems);\n    };\n    // Handle printing receipt - directly trigger print dialog\n    const handlePrintReceipt = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n            if (!receiptUrl || hasPrinted) {\n                console.log(\"Skipping print: \", !receiptUrl ? \"No receipt URL\" : \"Already printed\");\n                return;\n            }\n            console.log(\"Printing receipt:\", receiptUrl);\n            // Mark as printed immediately to prevent multiple print dialogs\n            setHasPrinted(true);\n            // Create a hidden iframe to load the image\n            const iframe = document.createElement(\"iframe\");\n            iframe.style.display = \"none\";\n            document.body.appendChild(iframe);\n            // Set up the iframe content with the image and print CSS\n            iframe.onload = ({\n                \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                    if (iframe.contentWindow) {\n                        // Write the HTML content to the iframe\n                        iframe.contentWindow.document.write('\\n          <!DOCTYPE html>\\n          <html>\\n            <head>\\n              <title>Print Receipt</title>\\n              <style>\\n                body {\\n                  margin: 0;\\n                  padding: 0;\\n                  display: flex;\\n                  justify-content: center;\\n                  align-items: center;\\n                  height: 100vh;\\n                }\\n                img {\\n                  max-width: 100%;\\n                  max-height: 100vh;\\n                }\\n                @media print {\\n                  body {\\n                    margin: 0;\\n                    padding: 0;\\n                  }\\n                  img {\\n                    width: 100%;\\n                    height: auto;\\n                  }\\n                }\\n              </style>\\n            </head>\\n            <body>\\n              <img src=\"'.concat(receiptUrl, '\" alt=\"Receipt\" />\\n            </body>\\n          </html>\\n        '));\n                        // Close the document\n                        iframe.contentWindow.document.close();\n                        // Use a single print trigger with a delay to ensure the image is loaded\n                        setTimeout({\n                            \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                if (iframe.contentWindow) {\n                                    try {\n                                        // Print the iframe content\n                                        iframe.contentWindow.focus();\n                                        iframe.contentWindow.print();\n                                    } catch (e) {\n                                        console.error(\"Error printing receipt:\", e);\n                                    }\n                                    // Remove the iframe after printing\n                                    setTimeout({\n                                        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                            document.body.removeChild(iframe);\n                                        }\n                                    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 1000);\n                                }\n                            }\n                        }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 500);\n                    }\n                }\n            })[\"SalesFormPanel.useCallback[handlePrintReceipt]\"];\n            // Set the iframe source to trigger the onload event\n            iframe.src = \"about:blank\";\n        }\n    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], [\n        receiptUrl,\n        hasPrinted\n    ]);\n    // Effect to automatically print receipt when modal is shown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (receiptPreviewVisible && receiptUrl && !hasPrinted) {\n                // Add a small delay to ensure the receipt image is loaded\n                const timer = setTimeout({\n                    \"SalesFormPanel.useEffect.timer\": ()=>{\n                        handlePrintReceipt(); // This now handles the hasPrinted state internally\n                    }\n                }[\"SalesFormPanel.useEffect.timer\"], 800);\n                return ({\n                    \"SalesFormPanel.useEffect\": ()=>clearTimeout(timer)\n                })[\"SalesFormPanel.useEffect\"];\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        receiptPreviewVisible,\n        receiptUrl,\n        hasPrinted,\n        handlePrintReceipt\n    ]);\n    // Handle form submission\n    const handleSubmit = async ()=>{\n        try {\n            var _userStoresData_data;\n            if (items.length === 0) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Please add at least one item to the sale\");\n                return;\n            }\n            // Validate form fields\n            const values = await form.validateFields();\n            // Check if store is selected\n            if (!selectedStore) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"No store information available. Please set up your store in your profile settings.\");\n                return;\n            }\n            // Set loading state for receipt generation\n            setIsGeneratingReceipt(true);\n            // Get store information for receipt\n            const storeInfo = selectedStore || (userStoresData === null || userStoresData === void 0 ? void 0 : (_userStoresData_data = userStoresData.data) === null || _userStoresData_data === void 0 ? void 0 : _userStoresData_data.find((store)=>store.id === values.storeId)) || {\n                name: \"POS System\"\n            };\n            // Generate receipt HTML\n            const receiptHTML = (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__.generateReceiptHTML)({\n                id: Date.now(),\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                transactionDate: new Date().toISOString(),\n                items: items.map((item)=>({\n                        productName: item.productName,\n                        quantity: item.quantity,\n                        price: item.price\n                    }))\n            }, storeInfo);\n            // Generate receipt image and get URL\n            let receiptUrl = \"https://receipt.example.com/placeholder\";\n            try {\n                receiptUrl = await (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__.generateReceiptImage)(receiptHTML);\n            } catch (error) {\n                console.error(\"Failed to generate receipt image:\", error);\n            // Continue with placeholder URL if image generation fails\n            }\n            const saleData = {\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                items: items.map((item)=>({\n                        productId: item.productId,\n                        quantity: item.quantity,\n                        price: item.price\n                    })),\n                receiptUrl,\n                storeId: selectedStore === null || selectedStore === void 0 ? void 0 : selectedStore.id\n            };\n            const response = await createSale(saleData).unwrap();\n            if (response.success) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Sale created successfully\");\n                // Store the receipt URL for preview\n                setReceiptUrl(receiptUrl);\n                // Show receipt preview modal and offer print option\n                setReceiptPreviewVisible(true);\n                // Force refresh product data to get updated stock quantities after sale\n                console.log(\"🔄 Sale completed - refreshing product data to update stock quantities\");\n                refetchProducts().then((result)=>{\n                    if (result.data) {\n                        console.log(\"✅ Product data refreshed after sale - stock quantities updated\");\n                    }\n                }).catch((error)=>{\n                    console.error(\"❌ Failed to refresh product data after sale:\", error);\n                });\n                // Trigger the success callback to refresh the sales list\n                setTimeout(()=>{\n                    if (onSuccess) {\n                        console.log(\"📊 Triggering sales list refresh\");\n                        onSuccess();\n                    }\n                }, 300);\n            // Keep the panel open until the user explicitly closes it\n            // This ensures the receipt modal stays visible\n            } else {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", response.message || \"Failed to create sale\");\n            }\n        } catch (error) {\n            var _error_data;\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", ((_error_data = error.data) === null || _error_data === void 0 ? void 0 : _error_data.message) || \"An error occurred while creating the sale\");\n        } finally{\n            setIsGeneratingReceipt(false);\n        }\n    };\n    // Debug log to check items state when rendering\n    console.log(\"Rendering with items:\", items);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        title: \"Point of Sale System\",\n        isOpen: isOpen,\n        onClose: onClose,\n        width: \"98%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sales-form min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-20 border-b border-gray-200 bg-white px-6 py-4 shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"text-xl text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-gray-800\",\n                                                    children: \"Point of Sale\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: (selectedStore === null || selectedStore === void 0 ? void 0 : selectedStore.name) || 'NEXAPO POS System'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Transaction Total\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-green-600\",\n                                                    children: [\n                                                        \"GHS \",\n                                                        totalAmount.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 554,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Items\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl font-semibold text-gray-700\",\n                                                    children: items.length\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-6 xl:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"xl:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-blue-500\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"text-sm text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 579,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 578,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-bold text-gray-800\",\n                                                                            children: \"Product Selection\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 581,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        (isLoadingProducts || isFetchingProducts) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 rounded-full bg-blue-100 px-3 py-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    className: \"text-blue-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 586,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-blue-600\",\n                                                                                    children: \"Loading fresh data...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 587,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 585,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 577,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 593,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            onClick: ()=>{\n                                                                                console.log(\"🔄 Manual refresh triggered\");\n                                                                                refetchProducts();\n                                                                                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"warning\", \"Refreshing product data...\");\n                                                                            },\n                                                                            loading: isLoadingProducts || isFetchingProducts,\n                                                                            size: \"small\",\n                                                                            className: \"border-blue-300 text-blue-600 hover:bg-blue-50\",\n                                                                            title: \"Refresh product data from database\",\n                                                                            children: \"Refresh\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 592,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"rounded-full bg-white px-3 py-1 text-xs text-gray-600 shadow-sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"mr-1 text-red-500\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 607,\n                                                                                    columnNumber: 25\n                                                                                }, undefined),\n                                                                                \" Required fields\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 606,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 576,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        (productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 flex items-center space-x-2 text-xs text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        productsData.data.products.length,\n                                                                        \" products loaded from database\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 614,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-600\",\n                                                                    children: [\n                                                                        \"• Last updated: \",\n                                                                        new Date().toLocaleTimeString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 617,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        form: form,\n                                                        layout: \"vertical\",\n                                                        initialValues: {\n                                                            paymentMethod: \"cash\",\n                                                            quantity: 1\n                                                        },\n                                                        className: \"pos-unified-form\",\n                                                        onFinish: handleSubmit,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 gap-6 lg:grid-cols-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"lg:col-span-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                                            name: \"productId\",\n                                                                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"flex items-center text-sm font-semibold text-gray-700\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"mr-2 text-blue-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 641,\n                                                                                        columnNumber: 31\n                                                                                    }, void 0),\n                                                                                    \"Select Product \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"ml-1 text-red-500\",\n                                                                                        children: \"*\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 642,\n                                                                                        columnNumber: 46\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 640,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            className: \"mb-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                showSearch: true,\n                                                                                placeholder: isLoadingProducts || isFetchingProducts ? \"🔄 Loading fresh product data from database...\" : productsError ? \"❌ Error loading products - click refresh\" : \"🔍 Search and select a product...\",\n                                                                                optionFilterProp: \"children\",\n                                                                                loading: isLoadingProducts || isFetchingProducts,\n                                                                                disabled: isLoadingProducts || isFetchingProducts,\n                                                                                onChange: (value)=>{\n                                                                                    var _productsData_data;\n                                                                                    const product = productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products.find((p)=>p.id === value);\n                                                                                    console.log(\"Selected product from fresh database data:\", product);\n                                                                                    if (product) {\n                                                                                        // Make a deep copy to avoid reference issues\n                                                                                        setSelectedProduct({\n                                                                                            ...product,\n                                                                                            // Ensure price is properly formatted\n                                                                                            price: typeof product.price === \"string\" ? product.price : String(product.price)\n                                                                                        });\n                                                                                        console.log(\"✅ Product selected with current stock:\", product.stockQuantity);\n                                                                                    } else {\n                                                                                        setSelectedProduct(null);\n                                                                                    }\n                                                                                },\n                                                                                onSearch: (value)=>{\n                                                                                    setSearchTerm(value);\n                                                                                    // Trigger a fresh search from database\n                                                                                    if (value.length > 2) {\n                                                                                        console.log(\"🔍 Searching products in database:\", value);\n                                                                                    }\n                                                                                },\n                                                                                filterOption: false,\n                                                                                className: \"rounded-lg border-gray-300 text-gray-800 shadow-sm\",\n                                                                                size: \"large\",\n                                                                                suffixIcon: isLoadingProducts || isFetchingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    spin: true,\n                                                                                    className: \"text-blue-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 691,\n                                                                                    columnNumber: 33\n                                                                                }, void 0) : productsError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-500\",\n                                                                                    children: \"❌\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 693,\n                                                                                    columnNumber: 33\n                                                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    className: \"text-gray-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 695,\n                                                                                    columnNumber: 33\n                                                                                }, void 0),\n                                                                                notFoundContent: isLoadingProducts || isFetchingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-center py-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                            size: \"small\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 701,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"ml-2 text-gray-500\",\n                                                                                            children: \"Loading fresh data from database...\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 702,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 700,\n                                                                                    columnNumber: 33\n                                                                                }, void 0) : productsError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"py-6 text-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-red-500\",\n                                                                                            children: \"❌ Failed to load products\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 706,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                            size: \"small\",\n                                                                                            onClick: ()=>refetchProducts(),\n                                                                                            className: \"mt-2\",\n                                                                                            children: \"Try Again\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 707,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 705,\n                                                                                    columnNumber: 33\n                                                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"py-4 text-center text-gray-500\",\n                                                                                    children: \"No products found in database\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 716,\n                                                                                    columnNumber: 33\n                                                                                }, void 0),\n                                                                                children: productsData === null || productsData === void 0 ? void 0 : (_productsData_data1 = productsData.data) === null || _productsData_data1 === void 0 ? void 0 : (_productsData_data_products = _productsData_data1.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Option, {\n                                                                                        value: product.id,\n                                                                                        disabled: product.stockQuantity <= 0,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center justify-between py-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex-1\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"font-medium text-gray-800\",\n                                                                                                            children: product.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 730,\n                                                                                                            columnNumber: 37\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"text-sm text-gray-500\",\n                                                                                                            children: [\n                                                                                                                \"GHS \",\n                                                                                                                Number(product.price).toFixed(2)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 733,\n                                                                                                            columnNumber: 37\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 729,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-right\",\n                                                                                                    children: product.stockQuantity <= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: \"rounded-full bg-red-100 px-2 py-1 text-xs font-medium text-red-600\",\n                                                                                                        children: \"Out of Stock\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 739,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: \"rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-600\",\n                                                                                                        children: [\n                                                                                                            \"Stock: \",\n                                                                                                            product.stockQuantity\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 743,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 737,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 728,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, product.id, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 723,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 647,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 637,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 636,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                                            name: \"quantity\",\n                                                                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"flex items-center text-sm font-semibold text-gray-700\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mr-2\",\n                                                                                        children: \"\\uD83D\\uDCE6\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 760,\n                                                                                        columnNumber: 31\n                                                                                    }, void 0),\n                                                                                    \"Quantity \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"ml-1 text-red-500\",\n                                                                                        children: \"*\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 761,\n                                                                                        columnNumber: 40\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 759,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            className: \"mb-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                min: 1,\n                                                                                max: (selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.stockQuantity) || 999,\n                                                                                value: quantity,\n                                                                                onChange: (value)=>setQuantity(value || 1),\n                                                                                style: {\n                                                                                    width: \"100%\"\n                                                                                },\n                                                                                className: \"rounded-lg border-gray-300 text-gray-800 shadow-sm\",\n                                                                                size: \"large\",\n                                                                                placeholder: \"Enter quantity\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 766,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 756,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 755,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            selectedProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 rounded-lg border border-blue-200 bg-blue-50 p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-semibold text-gray-800\",\n                                                                                    children: selectedProduct.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 785,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"Price: GHS \",\n                                                                                        Number(selectedProduct.price).toFixed(2),\n                                                                                        \" | Available: \",\n                                                                                        selectedProduct.stockQuantity,\n                                                                                        \" units\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 788,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 784,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-right\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"Subtotal\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 794,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-lg font-bold text-green-600\",\n                                                                                    children: [\n                                                                                        \"GHS \",\n                                                                                        (Number(selectedProduct.price) * quantity).toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 795,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 793,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 783,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                onClick: handleAddItem,\n                                                                className: \"mt-6 h-14 w-full rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 text-lg font-semibold shadow-lg hover:from-blue-600 hover:to-blue-700 hover:shadow-xl\",\n                                                                disabled: !selectedProduct,\n                                                                size: \"large\",\n                                                                children: \"\\uD83D\\uDED2 Add to Cart\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-8\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                                    name: \"paymentMethod\",\n                                                                    label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex items-center text-sm font-semibold text-gray-700\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mr-2\",\n                                                                                children: \"\\uD83D\\uDCB3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 820,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            \"Payment Method \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-1 text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 821,\n                                                                                columnNumber: 44\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 819,\n                                                                        columnNumber: 27\n                                                                    }, void 0),\n                                                                    rules: [\n                                                                        {\n                                                                            required: true,\n                                                                            message: \"Please select a payment method\"\n                                                                        }\n                                                                    ],\n                                                                    initialValue: \"cash\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"rounded-lg border-gray-300 text-gray-800 shadow-sm\",\n                                                                        size: \"large\",\n                                                                        placeholder: \"Select payment method\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Option, {\n                                                                                value: \"cash\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between py-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"mr-3 text-lg\",\n                                                                                                    children: \"\\uD83D\\uDCB5\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 840,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"font-medium\",\n                                                                                                            children: \"Cash Payment\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 842,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"text-xs text-gray-500\",\n                                                                                                            children: \"Physical cash transaction\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 843,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 841,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 839,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"rounded-full bg-green-100 px-2 py-1 text-xs text-green-600\",\n                                                                                            children: \"Instant\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 846,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 838,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 837,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Option, {\n                                                                                value: \"card\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between py-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"mr-3 text-lg\",\n                                                                                                    children: \"\\uD83D\\uDCB3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 854,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"font-medium\",\n                                                                                                            children: \"Card Payment\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 856,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"text-xs text-gray-500\",\n                                                                                                            children: \"Credit/Debit card\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 857,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 855,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 853,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-600\",\n                                                                                            children: \"Secure\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 860,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 852,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 851,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Option, {\n                                                                                value: \"mobile_money\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between py-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"mr-3 text-lg\",\n                                                                                                    children: \"\\uD83D\\uDCF1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 868,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"font-medium\",\n                                                                                                            children: \"Mobile Money\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 870,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"text-xs text-gray-500\",\n                                                                                                            children: \"MTN, Vodafone, AirtelTigo\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 871,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 869,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 867,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"rounded-full bg-purple-100 px-2 py-1 text-xs text-purple-600\",\n                                                                                            children: \"Popular\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 874,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 866,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 865,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 832,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 816,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 815,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-b border-gray-100 bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-green-500\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"text-sm text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 893,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 892,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-bold text-gray-800\",\n                                                                        children: \"Shopping Cart\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 895,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 891,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"rounded-full bg-white px-3 py-1 text-sm font-medium text-gray-600 shadow-sm\",\n                                                                        children: [\n                                                                            items.length,\n                                                                            \" \",\n                                                                            items.length === 1 ? 'item' : 'items'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 900,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"rounded-full bg-green-100 px-3 py-1 text-sm font-bold text-green-700\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 903,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 899,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 890,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 889,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-[400px] overflow-x-auto overflow-y-auto\",\n                                                    children: items.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center justify-center py-16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-gray-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"text-3xl text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 913,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 912,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"mb-2 text-lg font-semibold text-gray-600\",\n                                                                children: \"Your cart is empty\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 915,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Add products to start a new transaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 918,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 911,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"divide-y divide-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-12 gap-4 bg-gray-50 px-6 py-3 text-sm font-semibold text-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-5\",\n                                                                        children: \"Product\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 926,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-2 text-center\",\n                                                                        children: \"Qty\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 927,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-2 text-right\",\n                                                                        children: \"Price\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 928,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-2 text-right\",\n                                                                        children: \"Subtotal\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 929,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-1 text-center\",\n                                                                        children: \"Action\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 930,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 925,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-12 gap-4 px-6 py-4 transition-colors hover:bg-gray-50\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-5 flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"text-blue-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 940,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 939,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-semibold text-gray-800\",\n                                                                                            children: item.productName || \"Unknown Product\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 943,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-gray-500\",\n                                                                                            children: [\n                                                                                                \"Item #\",\n                                                                                                index + 1\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 946,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 942,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 938,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"rounded-full bg-blue-100 px-3 py-1 text-sm font-semibold text-blue-700\",\n                                                                                children: item.quantity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 952,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 951,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 flex items-center justify-end\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-800\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    Number(item.price).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 957,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 956,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 flex items-center justify-end\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-bold text-green-600\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    (Number(item.price) * item.quantity).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 962,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 961,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-1 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 968,\n                                                                                    columnNumber: 37\n                                                                                }, void 0),\n                                                                                onClick: ()=>handleRemoveItem(index),\n                                                                                type: \"text\",\n                                                                                danger: true,\n                                                                                className: \"rounded-full text-red-500 hover:bg-red-50 hover:text-red-600\",\n                                                                                size: \"small\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 967,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 966,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, \"\".concat(item.productId, \"-\").concat(index), true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 934,\n                                                                    columnNumber: 25\n                                                                }, undefined)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border-t border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-semibold text-gray-800\",\n                                                                            children: \"Cart Total\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 981,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-2xl font-bold text-green-600\",\n                                                                            children: [\n                                                                                \"GHS \",\n                                                                                totalAmount.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 984,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 980,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 979,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 923,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 909,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 888,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sticky top-24 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-b border-gray-100 bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-purple-500\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"text-sm text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1001,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 1000,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-bold text-gray-800\",\n                                                            children: \"Checkout\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 1003,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 999,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 998,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 overflow-hidden rounded-lg border border-gray-200 bg-gradient-to-br from-gray-50 to-gray-100 shadow-inner\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border-b border-gray-200 bg-white px-4 py-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-gray-800\",\n                                                                    children: \"Order Summary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1012,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1011,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"flex items-center text-gray-600\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mr-2\",\n                                                                                        children: \"\\uD83D\\uDCE6\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 1017,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    \" Items\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1016,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"rounded-full bg-blue-100 px-2 py-1 text-sm font-semibold text-blue-700\",\n                                                                                children: items.length\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1019,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1015,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"flex items-center text-gray-600\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mr-2\",\n                                                                                        children: \"\\uD83D\\uDD22\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 1025,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    \" Total Quantity\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1024,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-gray-800\",\n                                                                                children: items.reduce((sum, item)=>sum + item.quantity, 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1027,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1023,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between border-t border-gray-300 pt-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"flex items-center text-lg font-semibold text-gray-800\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mr-2\",\n                                                                                        children: \"\\uD83D\\uDCB0\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 1033,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    \" Total Amount\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1032,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-2xl font-bold text-green-600\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    totalAmount.toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1035,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1031,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1014,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 1010,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    selectedStore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"mb-3 block text-sm font-semibold text-gray-700\",\n                                                                children: \"Store Information\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1045,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border border-green-200 bg-green-50 p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-green-500\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"text-white\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 1051,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1050,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-semibold text-gray-800\",\n                                                                                        children: selectedStore.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 1054,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"Active Store\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 1057,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1053,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1049,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"hidden\",\n                                                                        name: \"storeId\",\n                                                                        value: selectedStore.id\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1062,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1048,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 1044,\n                                                        columnNumber: 23\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 rounded-lg border border-orange-200 bg-orange-50 p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-orange-500\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1073,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1072,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold text-orange-800\",\n                                                                            children: \"No Store Selected\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 1076,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-orange-600\",\n                                                                            children: \"Please set up your store in profile settings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 1079,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1075,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 1071,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 1070,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-8 space-y-4\",\n                                                        children: [\n                                                            receiptPreviewVisible ? // Enhanced \"New Sale\" button when receipt is visible\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1093,\n                                                                    columnNumber: 33\n                                                                }, void 0),\n                                                                onClick: ()=>{\n                                                                    // Close the modal and reset the receipt state\n                                                                    setReceiptPreviewVisible(false);\n                                                                    setReceiptUrl(null);\n                                                                    setHasPrinted(false);\n                                                                    // Reset the form to start a new sale\n                                                                    form.resetFields();\n                                                                    setItems([]);\n                                                                    setSelectedProduct(null);\n                                                                    setQuantity(1);\n                                                                    setTotalAmount(0);\n                                                                },\n                                                                className: \"h-16 w-full rounded-lg bg-gradient-to-r from-green-500 to-green-600 text-lg font-bold shadow-lg hover:from-green-600 hover:to-green-700 hover:shadow-xl\",\n                                                                size: \"large\",\n                                                                children: \"\\uD83D\\uDED2 Start New Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1091,\n                                                                columnNumber: 25\n                                                            }, undefined) : // Enhanced \"Complete Sale\" button\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1116,\n                                                                    columnNumber: 33\n                                                                }, void 0),\n                                                                onClick: handleSubmit,\n                                                                loading: isSubmitting || isGeneratingReceipt,\n                                                                disabled: items.length === 0,\n                                                                className: \"h-16 w-full rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 text-lg font-bold shadow-lg hover:from-blue-600 hover:to-blue-700 hover:shadow-xl disabled:from-gray-400 disabled:to-gray-500\",\n                                                                size: \"large\",\n                                                                children: isGeneratingReceipt ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 1125,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        \"Generating Receipt...\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1124,\n                                                                    columnNumber: 29\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        \"\\uD83D\\uDCB3 Complete Sale - GHS \",\n                                                                        totalAmount.toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1129,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1114,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                onClick: onClose,\n                                                                className: \"h-12 w-full rounded-lg border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400\",\n                                                                size: \"large\",\n                                                                children: \"❌ Cancel Transaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1136,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 1088,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 1008,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 997,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 996,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 536,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-gray-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            className: \"mr-2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1155,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Receipt Preview\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1156,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 1154,\n                    columnNumber: 11\n                }, void 0),\n                open: receiptPreviewVisible,\n                onCancel: ()=>{\n                    // Close the modal and reset the receipt state\n                    setReceiptPreviewVisible(false);\n                    setReceiptUrl(null);\n                    setHasPrinted(false);\n                    // Reset the form to start a new sale\n                    form.resetFields();\n                    setItems([]);\n                    setSelectedProduct(null);\n                    setQuantity(1);\n                    setTotalAmount(0);\n                },\n                width: 500,\n                centered: true,\n                className: \"receipt-preview-modal\",\n                footer: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        onClick: ()=>{\n                            // Close the modal and reset the receipt state\n                            setReceiptPreviewVisible(false);\n                            setReceiptUrl(null);\n                            setHasPrinted(false);\n                            // Reset the form to start a new sale\n                            form.resetFields();\n                            setItems([]);\n                            setSelectedProduct(null);\n                            setQuantity(1);\n                            setTotalAmount(0);\n                        },\n                        className: \"border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200\",\n                        children: \"Close & New Sale\"\n                    }, \"close\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1177,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        type: \"primary\",\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1199,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: ()=>{\n                            // If already printed once, reset the flag to allow printing again\n                            if (hasPrinted) {\n                                setHasPrinted(false);\n                            }\n                            handlePrintReceipt();\n                        },\n                        className: \"bg-blue-600 hover:bg-blue-700\",\n                        children: hasPrinted ? \"Print Again\" : \"Print Receipt\"\n                    }, \"print\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1196,\n                        columnNumber: 11\n                    }, void 0)\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: receiptUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"receipt-image-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            src: receiptUrl,\n                            alt: \"Receipt\",\n                            className: \"receipt-image\",\n                            style: {\n                                maxWidth: \"100%\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1216,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1215,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-64 items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            size: \"large\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1225,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1224,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 1213,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 1152,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n        lineNumber: 530,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SalesFormPanel, \"Q/g9tGgox4eIYTijyg3KJWlCIZ8=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm,\n        _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery,\n        _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation\n    ];\n});\n_c = SalesFormPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SalesFormPanel);\nvar _c;\n$RefreshReg$(_c, \"SalesFormPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx\n"));

/***/ })

});