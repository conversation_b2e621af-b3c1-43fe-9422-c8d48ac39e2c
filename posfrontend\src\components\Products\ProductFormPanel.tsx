"use client";

import React, { useEffect } from "react";
import { Form, Input, Button, InputNumber, DatePicker, Select } from "antd";
import { Product, CreateProductDto, UpdateProductDto } from "@/reduxRTK/services/productApi";
import SlidingPanel from "@/components/ui/SlidingPanel";
import { useProductCreate } from "@/hooks/products/useProductCreate";
import { useProductUpdate } from "@/hooks/products/useProductUpdate";
import { useGetAllCategoriesQuery, Category } from "@/reduxRTK/services/categoryApi";
import { User } from "@/types/user";
import { DollarOutlined, ShoppingOutlined, BarcodeOutlined, CalendarOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import "./product-panels.css";

const { Option } = Select;

interface ProductFormPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  product?: Product | null;
  currentUser?: User | null;
}

const ProductFormPanel: React.FC<ProductFormPanelProps> = ({
  isOpen,
  onClose,
  onSuccess,
  product,
  currentUser, // Available for role-based logic if needed in the future
}) => {
  const [form] = Form.useForm();
  const isEditMode = !!product;

  // Check if user has permission to edit/create products
  const userRole = currentUser?.role || 'cashier';

  // Get categories for dropdown
  const { data: categoriesData, isLoading: isCategoriesLoading } = useGetAllCategoriesQuery({
    page: 1,
    limit: 100,
  });
  // Extract categories from the response with type assertion to fix TypeScript error
  const categories = (categoriesData as any)?.data?.categories || [];

  // Hooks for creating and updating products
  const { createProduct, isCreating } = useProductCreate(onSuccess);
  const { updateProduct, isUpdating } = useProductUpdate(onSuccess);

  const isSubmitting = isCreating || isUpdating;

  // Reset form when panel opens/closes or product changes
  useEffect(() => {
    if (isOpen) {
      if (isEditMode && product) {
        // For edit mode, set form values from product
        form.setFieldsValue({
          ...product,
          expiryDate: product.expiryDate ? dayjs(product.expiryDate) : undefined,
          price: parseFloat(product.price),
          cost: parseFloat(product.cost),
        });
      } else {
        // For create mode, reset form
        form.resetFields();
      }
    }
  }, [isOpen, product, form, isEditMode]);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    try {
      const formattedValues = {
        ...values,
        expiryDate: values.expiryDate ? values.expiryDate.format('YYYY-MM-DD') : null,
      };

      if (isEditMode && product) {
        await updateProduct(product.id, formattedValues as UpdateProductDto);
      } else {
        await createProduct(formattedValues as CreateProductDto);
      }
    } catch (error) {
      console.error("Form submission error:", error);
    }
  };

  // Panel title based on mode and user role
  const panelTitle = isEditMode
    ? `Edit Product${userRole === 'admin' ? ' (Admin)' : ''}`
    : `Add New Product${userRole === 'admin' ? ' (Admin)' : ''}`;

  // Panel footer with action buttons
  const panelFooter = (
    <div className="flex justify-end space-x-2">
      <Button
        onClick={onClose}
        disabled={isSubmitting}
        className="text-gray-700 hover:text-gray-900"
        style={{ borderColor: '#d9d9d9', background: '#f5f5f5' }}
      >
        Cancel
      </Button>
      <Button
        type="primary"
        loading={isSubmitting}
        onClick={() => form.submit()}
      >
        {isEditMode ? "Update" : "Create"}
      </Button>
    </div>
  );

  return (
    <SlidingPanel
      isOpen={isOpen}
      onClose={onClose}
      title={panelTitle}
      width="500px"
      footer={panelFooter}
    >
      <div className="p-4">
        {/* Form heading with icon */}
        <div className="mb-6 border-b border-gray-200 pb-4">
          <h2 className="text-xl font-bold text-gray-800 flex items-center">
            {isEditMode ? (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Editing Product: {product?.name}
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
                Create New Product
              </>
            )}
          </h2>
          <p className="text-gray-600 mt-1">
            {isEditMode
              ? "Update product information using the form below"
              : "Fill in the details to create a new product"}
          </p>
        </div>

        {/* Required fields indicator */}
        <div className="mb-4 text-sm text-gray-600">
          <span className="text-red-500 mr-1">*</span> indicates required fields
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="product-form"
          requiredMark={true}
        >
          {/* Basic Information Section */}
          <div className="mb-4">
            <h3 className="text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2">
              Basic Information
            </h3>

            <Form.Item
              name="name"
              label={<span className="flex items-center"><ShoppingOutlined className="mr-1" /> Product Name</span>}
              rules={[{ required: true, message: "Please enter product name" }]}
              tooltip="The name of the product as it will appear to customers"
            >
              <Input placeholder="Enter product name" />
            </Form.Item>

            {/* Category */}
            <Form.Item
              name="categoryId"
              label={<span className="flex items-center"><ShoppingOutlined className="mr-1" /> Category</span>}
              rules={[{ required: true, message: "Please select a category" }]}
              tooltip="The category this product belongs to"
            >
              <Select
                placeholder="Select category"
                loading={isCategoriesLoading}
                disabled={isCategoriesLoading}
              >
                {categories.map((category: Category) => (
                  <Option key={category.id} value={category.id}>
                    {category.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          {/* Pricing Section */}
          <div className="mb-4">
            <h3 className="text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2">
              Pricing Information
            </h3>

            <div className="grid grid-cols-2 gap-4">
              <Form.Item
                name="price"
                label={<span className="flex items-center"><DollarOutlined className="mr-1" /> Selling Price (GHS)</span>}
                rules={[{ required: true, message: "Please enter selling price" }]}
                tooltip="The price at which you sell the product to customers"
              >
                <InputNumber
                  min={0}
                  step={0.01}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="0.00"

                />
              </Form.Item>

              <Form.Item
                name="cost"
                label={<span className="flex items-center"><DollarOutlined className="mr-1" /> Purchase Cost (GHS)</span>}
                rules={[{ required: true, message: "Please enter purchase cost" }]}
                tooltip="The cost price you pay to acquire the product"
              >
                <InputNumber
                  min={0}
                  step={0.01}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="0.00"

                />
              </Form.Item>
            </div>
          </div>

          {/* Inventory Section */}
          <div className="mb-4">
            <h3 className="text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2">
              Inventory Management
            </h3>

            <div className="grid grid-cols-2 gap-4">
              <Form.Item
                name="stockQuantity"
                label={<span className="flex items-center"><ShoppingOutlined className="mr-1" /> Stock Quantity</span>}
                initialValue={0}
                tooltip="Current quantity in stock"
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="0"

                />
              </Form.Item>

              <Form.Item
                name="minStockLevel"
                label={<span className="flex items-center"><ShoppingOutlined className="mr-1" /> Min Stock Level</span>}
                initialValue={5}
                tooltip="Minimum quantity before restock alert"
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="5"

                />
              </Form.Item>
            </div>
          </div>

          {/* Product Details Section */}
          <div className="mb-4">
            <h3 className="text-gray-800 text-lg font-medium mb-2 border-b border-gray-200 pb-2">
              Product Details
            </h3>

            <div className="grid grid-cols-2 gap-4">
              <Form.Item
                name="sku"
                label={<span className="flex items-center"><BarcodeOutlined className="mr-1" /> SKU (Optional)</span>}
                tooltip="Stock Keeping Unit - unique identifier for inventory management"
              >
                <Input placeholder="Enter SKU" />
              </Form.Item>

              <Form.Item
                name="barcode"
                label={<span className="flex items-center"><BarcodeOutlined className="mr-1" /> Barcode (Optional)</span>}
                tooltip="Product barcode for scanning"
              >
                <Input placeholder="Enter barcode" />
              </Form.Item>
            </div>

            <Form.Item
              name="expiryDate"
              label={<span className="flex items-center"><CalendarOutlined className="mr-1" /> Expiry Date (Optional)</span>}
              tooltip="Date when the product expires"
            >
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
          </div>


        </Form>
      </div>
    </SlidingPanel>
  );
};

export default ProductFormPanel;




