"use client";

import React, { useEffect, useState } from "react";
import { Button } from "antd";
import { CloseOutlined } from "@ant-design/icons";

interface SlidingPanelProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  width?: string | number;
  footer?: React.ReactNode;
}

const SlidingPanel: React.FC<SlidingPanelProps> = ({
  isOpen,
  onClose,
  title,
  children,
  width = "400px",
  footer,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isRendered, setIsRendered] = useState(false);
  const [windowWidth, setWindowWidth] = useState(
    typeof window !== 'undefined' ? window.innerWidth : 1024
  );

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    console.log("SlidingPanel - isOpen changed:", isOpen, "title:", title);
    if (isOpen) {
      setIsRendered(true);
      console.log("SlidingPanel - Setting isRendered to true");
      // Small delay to ensure the panel is rendered before animating
      setTimeout(() => {
        setIsVisible(true);
        console.log("SlidingPanel - Setting isVisible to true");
      }, 50);
    } else {
      setIsVisible(false);
      console.log("SlidingPanel - Setting isVisible to false");
      // Wait for animation to complete before unmounting
      const timer = setTimeout(() => {
        setIsRendered(false);
        console.log("SlidingPanel - Setting isRendered to false");
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [isOpen, title]);

  if (!isRendered) return null;

  // Calculate responsive width based on screen size
  const getResponsiveWidth = () => {
    // For small screens (mobile), use full width
    if (windowWidth < 640) {
      return '100vw'; // 100% of viewport width
    }
    // For medium screens
    if (windowWidth < 1024) {
      return '450px';
    }
    // For larger screens, use the provided width or default
    return width;
  };

  return (
    <div className="fixed inset-0 z-[9999] overflow-hidden">
      {/* Backdrop */}
      <div
        className={`absolute inset-0 bg-black transition-opacity duration-300 ${
          isVisible ? "opacity-50" : "opacity-0"
        }`}
        onClick={onClose}
      />

      {/* Panel */}
      <div
        className={`absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform ${
          isVisible ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ width: getResponsiveWidth() }}
      >
        {/* Header */}
        <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50">
          <h2 className="text-lg font-medium text-gray-800 truncate">{title}</h2>
          <Button
            type="text"
            icon={<CloseOutlined style={{ color: '#333' }} />}
            onClick={onClose}
            aria-label="Close panel"
            style={{ color: '#333', borderColor: 'transparent', background: 'transparent' }}
          />
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4 pt-6 bg-white">
          {children}
        </div>

        {/* Footer */}
        {footer && (
          <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
            {footer}
          </div>
        )}
      </div>
    </div>
  );
};

export default SlidingPanel;
