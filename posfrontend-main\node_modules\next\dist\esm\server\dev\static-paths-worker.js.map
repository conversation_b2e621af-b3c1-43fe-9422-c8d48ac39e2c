{"version": 3, "sources": ["../../../src/server/dev/static-paths-worker.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../config-shared'\n\nimport '../require-hook'\nimport '../node-environment'\n\nimport {\n  buildAppStaticPaths,\n  buildStaticPaths,\n  reduceAppConfig,\n} from '../../build/utils'\nimport { collectSegments } from '../../build/segment-config/app/app-segments'\nimport type { PartialStaticPathsResult } from '../../build/utils'\nimport { loadComponents } from '../load-components'\nimport { setHttpClientAndAgentOptions } from '../setup-http-agent-env'\nimport type { IncrementalCache } from '../lib/incremental-cache'\nimport { isAppPageRouteModule } from '../route-modules/checks'\nimport {\n  checkIsRoutePPREnabled,\n  type ExperimentalPPRConfig,\n} from '../lib/experimental/ppr'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\ntype RuntimeConfig = {\n  pprConfig: ExperimentalPPRConfig | undefined\n  configFileName: string\n  publicRuntimeConfig: { [key: string]: any }\n  serverRuntimeConfig: { [key: string]: any }\n  dynamicIO: boolean\n}\n\n// we call getStaticPaths in a separate process to ensure\n// side-effects aren't relied on in dev that will break\n// during a production build\nexport async function loadStaticPaths({\n  dir,\n  distDir,\n  pathname,\n  config,\n  httpAgentOptions,\n  locales,\n  defaultLocale,\n  isAppPath,\n  page,\n  isrFlushToDisk,\n  fetchCacheKeyPrefix,\n  maxMemoryCacheSize,\n  requestHeaders,\n  cacheHandler,\n  cacheLifeProfiles,\n  nextConfigOutput,\n  buildId,\n  authInterrupts,\n}: {\n  dir: string\n  distDir: string\n  pathname: string\n  config: RuntimeConfig\n  httpAgentOptions: NextConfigComplete['httpAgentOptions']\n  locales?: string[]\n  defaultLocale?: string\n  isAppPath: boolean\n  page: string\n  isrFlushToDisk?: boolean\n  fetchCacheKeyPrefix?: string\n  maxMemoryCacheSize?: number\n  requestHeaders: IncrementalCache['requestHeaders']\n  cacheHandler?: string\n  cacheLifeProfiles?: {\n    [profile: string]: import('../../server/use-cache/cache-life').CacheLife\n  }\n  nextConfigOutput: 'standalone' | 'export' | undefined\n  buildId: string\n  authInterrupts: boolean\n}): Promise<PartialStaticPathsResult> {\n  // update work memory runtime-config\n  require('../../shared/lib/runtime-config.external').setConfig(config)\n  setHttpClientAndAgentOptions({\n    httpAgentOptions,\n  })\n\n  const components = await loadComponents({\n    distDir,\n    // In `pages/`, the page is the same as the pathname.\n    page: page || pathname,\n    isAppPath,\n    isDev: true,\n  })\n\n  if (isAppPath) {\n    const segments = await collectSegments(components)\n\n    const isRoutePPREnabled =\n      isAppPageRouteModule(components.routeModule) &&\n      checkIsRoutePPREnabled(config.pprConfig, reduceAppConfig(segments))\n\n    return buildAppStaticPaths({\n      dir,\n      page: pathname,\n      dynamicIO: config.dynamicIO,\n      segments,\n      configFileName: config.configFileName,\n      distDir,\n      requestHeaders,\n      cacheHandler,\n      cacheLifeProfiles,\n      isrFlushToDisk,\n      fetchCacheKeyPrefix,\n      maxMemoryCacheSize,\n      ComponentMod: components.ComponentMod,\n      nextConfigOutput,\n      isRoutePPREnabled,\n      buildId,\n      authInterrupts,\n    })\n  } else if (!components.getStaticPaths) {\n    // We shouldn't get to this point since the worker should only be called for\n    // SSG pages with getStaticPaths.\n    throw new InvariantError(\n      `Failed to load page with getStaticPaths for ${pathname}`\n    )\n  }\n\n  return buildStaticPaths({\n    page: pathname,\n    getStaticPaths: components.getStaticPaths,\n    configFileName: config.configFileName,\n    locales,\n    defaultLocale,\n  })\n}\n"], "names": ["buildAppStaticPaths", "buildStaticPaths", "reduceAppConfig", "collectSegments", "loadComponents", "setHttpClientAndAgentOptions", "isAppPageRouteModule", "checkIsRoutePPREnabled", "InvariantError", "loadStaticPaths", "dir", "distDir", "pathname", "config", "httpAgentOptions", "locales", "defaultLocale", "isAppPath", "page", "isrFlushToDisk", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "requestHeaders", "cache<PERSON><PERSON><PERSON>", "cacheLifeProfiles", "nextConfigOutput", "buildId", "authInterrupts", "require", "setConfig", "components", "isDev", "segments", "isRoutePPREnabled", "routeModule", "pprConfig", "dynamicIO", "configFileName", "ComponentMod", "getStaticPaths"], "mappings": "AAEA,OAAO,kBAAiB;AACxB,OAAO,sBAAqB;AAE5B,SACEA,mBAAmB,EACnBC,gBAAgB,EAChBC,eAAe,QACV,oBAAmB;AAC1B,SAASC,eAAe,QAAQ,8CAA6C;AAE7E,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,4BAA4B,QAAQ,0BAAyB;AAEtE,SAASC,oBAAoB,QAAQ,0BAAyB;AAC9D,SACEC,sBAAsB,QAEjB,0BAAyB;AAChC,SAASC,cAAc,QAAQ,mCAAkC;AAUjE,yDAAyD;AACzD,uDAAuD;AACvD,4BAA4B;AAC5B,OAAO,eAAeC,gBAAgB,EACpCC,GAAG,EACHC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,gBAAgB,EAChBC,OAAO,EACPC,aAAa,EACbC,SAAS,EACTC,IAAI,EACJC,cAAc,EACdC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,iBAAiB,EACjBC,gBAAgB,EAChBC,OAAO,EACPC,cAAc,EAsBf;IACC,oCAAoC;IACpCC,QAAQ,4CAA4CC,SAAS,CAAChB;IAC9DR,6BAA6B;QAC3BS;IACF;IAEA,MAAMgB,aAAa,MAAM1B,eAAe;QACtCO;QACA,qDAAqD;QACrDO,MAAMA,QAAQN;QACdK;QACAc,OAAO;IACT;IAEA,IAAId,WAAW;QACb,MAAMe,WAAW,MAAM7B,gBAAgB2B;QAEvC,MAAMG,oBACJ3B,qBAAqBwB,WAAWI,WAAW,KAC3C3B,uBAAuBM,OAAOsB,SAAS,EAAEjC,gBAAgB8B;QAE3D,OAAOhC,oBAAoB;YACzBU;YACAQ,MAAMN;YACNwB,WAAWvB,OAAOuB,SAAS;YAC3BJ;YACAK,gBAAgBxB,OAAOwB,cAAc;YACrC1B;YACAW;YACAC;YACAC;YACAL;YACAC;YACAC;YACAiB,cAAcR,WAAWQ,YAAY;YACrCb;YACAQ;YACAP;YACAC;QACF;IACF,OAAO,IAAI,CAACG,WAAWS,cAAc,EAAE;QACrC,4EAA4E;QAC5E,iCAAiC;QACjC,MAAM,IAAI/B,eACR,CAAC,4CAA4C,EAAEI,UAAU;IAE7D;IAEA,OAAOX,iBAAiB;QACtBiB,MAAMN;QACN2B,gBAAgBT,WAAWS,cAAc;QACzCF,gBAAgBxB,OAAOwB,cAAc;QACrCtB;QACAC;IACF;AACF"}