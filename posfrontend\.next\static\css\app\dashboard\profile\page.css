/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/dashboard/profile/profile.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/* Profile page styles */

/* Tab styles */
.profile-tabs .ant-tabs-nav::before {
  border-bottom-color: #e8e8e8 !important;
}

.profile-tabs .ant-tabs-tab {
  color: rgba(0, 0, 0, 0.85) !important;
  padding: 12px 16px !important;
  opacity: 0.8;
}

.profile-tabs .ant-tabs-tab:hover {
  color: #1890ff !important;
  opacity: 1;
}

.profile-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #1890ff !important;
  opacity: 1;
}

.profile-tabs .ant-tabs-ink-bar {
  background-color: #1890ff !important;
  height: 3px !important;
}

/* Light theme overrides */
.ant-tabs-content {
  color: rgba(0, 0, 0, 0.85) !important;
}

/* Form styles */
.ant-form-item-label > label {
  color: rgba(0, 0, 0, 0.85) !important;
}

/* Avatar uploader styles */
.avatar-uploader .ant-upload {
  width: 100%;
}

.avatar-uploader .ant-upload-select-picture-card {
  width: 128px;
  height: 128px;
  margin: 0 auto;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f5f5f5;
  border-color: #d9d9d9;
}

.avatar-uploader .ant-upload-select-picture-card:hover {
  border-color: #1890ff;
}

/* Light mode input styles */
.ant-input-affix-wrapper-dark,
.ant-input-dark {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.85) !important;
}

.ant-input-affix-wrapper-dark:hover,
.ant-input-dark:hover {
  border-color: #40a9ff !important;
}

.ant-input-affix-wrapper-dark:focus,
.ant-input-dark:focus,
.ant-input-affix-wrapper-dark-focused,
.ant-input-dark-focused {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* Button styles */
.ant-btn-primary {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}

.ant-btn-primary:hover {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .profile-tabs .ant-tabs-tab {
    padding: 8px 12px !important;
  }
}

