{"version": 3, "sources": ["../../../src/server/lib/revalidate.ts"], "sourcesContent": ["import { CACHE_ONE_YEAR } from '../../lib/constants'\n\n/**\n * The revalidate option used internally for pages. A value of `false` means\n * that the page should not be revalidated. A number means that the page\n * should be revalidated after the given number of seconds (this also includes\n * `1` which means to revalidate after 1 second). A value of `0` is not a valid\n * value for this option.\n */\nexport type Revalidate = number | false\nexport type ExpireTime = number\n\nexport function formatRevalidate({\n  revalidate,\n  expireTime,\n}: {\n  revalidate: Revalidate\n  expireTime?: ExpireTime\n}): string {\n  const swrHeader =\n    typeof revalidate === 'number' && expireTime !== undefined\n      ? revalidate >= expireTime\n        ? ''\n        : `stale-while-revalidate=${expireTime - revalidate}`\n      : 'stale-while-revalidate'\n\n  if (revalidate === 0) {\n    return 'private, no-cache, no-store, max-age=0, must-revalidate'\n  } else if (typeof revalidate === 'number') {\n    return `s-maxage=${revalidate}, ${swrHeader}`\n  }\n\n  return `s-maxage=${CACHE_ONE_YEAR}, ${swrHeader}`\n}\n"], "names": ["CACHE_ONE_YEAR", "formatRevalidate", "revalidate", "expireTime", "swr<PERSON><PERSON><PERSON>", "undefined"], "mappings": "AAAA,SAASA,cAAc,QAAQ,sBAAqB;AAYpD,OAAO,SAASC,iBAAiB,EAC/BC,UAAU,EACVC,UAAU,EAIX;IACC,MAAMC,YACJ,OAAOF,eAAe,YAAYC,eAAeE,YAC7CH,cAAcC,aACZ,KACA,CAAC,uBAAuB,EAAEA,aAAaD,YAAY,GACrD;IAEN,IAAIA,eAAe,GAAG;QACpB,OAAO;IACT,OAAO,IAAI,OAAOA,eAAe,UAAU;QACzC,OAAO,CAAC,SAAS,EAAEA,WAAW,EAAE,EAAEE,WAAW;IAC/C;IAEA,OAAO,CAAC,SAAS,EAAEJ,eAAe,EAAE,EAAEI,WAAW;AACnD"}