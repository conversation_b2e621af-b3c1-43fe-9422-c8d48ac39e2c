import { ApiResponse } from "@/types/user";

const BASEURL = "http://localhost:5005/api/v1";
// const BASEURL = "https://nexapoapi.up.railway.app/api/v1";

// 🔐 API CALLER WITH AUTH (for all requests except login)
export const apiCallerWithAuth = async (
  payloaddata: { [key: string]: any },
  urlpath: string,
  token: string,
): Promise<ApiResponse<any>> => {
  // Changed to ApiResponse to include success, message, and data
  try {
    // Log request details for debugging (remove in production)
    console.log("API Request:", {
      url: `${BASEURL}${urlpath}`,
      method: "POST",
      payload: payloaddata,
      hasToken: !!token,
    });

    const headers: HeadersInit = {
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    };

    const options: RequestInit = {
      method: "POST",
      headers,
      body: JSON.stringify(payloaddata),
      credentials: "same-origin", // Changed from "include" to "same-origin"
      mode: "cors", // Explicitly set CORS mode
    };

    // Make the fetch request
    const fetchResult = await fetch(`${BASEURL}${urlpath}`, options);

    // Try to parse the JSON response
    let result;
    try {
      result = await fetchResult.json();
    } catch (parseError) {
      console.error("Failed to parse JSON response:", parseError);
      return {
        success: false,
        message: `Failed to parse server response: ${fetchResult.statusText || "Unknown error"}`,
      };
    }

    // Check if the request was successful
    if (fetchResult.ok) {
      return {
        success: true,
        message: result.message || "Action completed successfully",
        data: result.data || null,
      };
    } else {
      // Log the error response for debugging
      console.error("API Error Response:", {
        status: fetchResult.status,
        statusText: fetchResult.statusText,
        result,
      });

      return {
        success: false,
        message: result.message || `Request failed with status: ${fetchResult.status} ${fetchResult.statusText}`,
      };
    }
  } catch (error: any) {
    // Log detailed error information
    console.error("API call failed:", error);

    // Check if it's a network error
    const isNetworkError = error instanceof TypeError && error.message.includes('fetch');

    return {
      success: false,
      message: isNetworkError
        ? "Network error: Unable to connect to the server. Please check your internet connection."
        : `Error: ${error.message || "Unable to complete action. An unknown error occurred!"}`,
    };
  }
};

// 🔓 LOGIN ONLY API CALLER (no token)
export const ApiCaller = async (
  urlpath: string,
  payloaddata: { [key: string]: any },
): Promise<ApiResponse<any>> => {
  // Changed to ApiResponse to include success, message, and data
  try {
    // Log request details for debugging (remove in production)
    console.log("Login API Request:", {
      url: `${BASEURL}${urlpath}`,
      method: "POST",
      payload: payloaddata,
    });

    const headers: HeadersInit = {
      Accept: "application/json",
      "Content-Type": "application/json",
    };

    const options: RequestInit = {
      method: "POST",
      headers,
      body: JSON.stringify(payloaddata),
      credentials: "same-origin", // Match the authenticated API caller
      mode: "cors", // Explicitly set CORS mode
    };

    // Make the fetch request
    const fetchResult = await fetch(`${BASEURL}${urlpath}`, options);

    // Try to parse the JSON response
    let result;
    try {
      result = await fetchResult.json();
    } catch (parseError) {
      console.error("Failed to parse JSON response:", parseError);
      return {
        success: false,
        message: `Failed to parse server response: ${fetchResult.statusText || "Unknown error"}`,
      };
    }

    // Check if the request was successful
    if (fetchResult.ok) {
      return {
        success: true,
        message: result.message || "Login successful",
        data: result.data || null,
      };
    } else {
      // Log the error response for debugging
      console.error("Login API Error Response:", {
        status: fetchResult.status,
        statusText: fetchResult.statusText,
        result,
      });

      return {
        success: false,
        message: result.message || `Login failed with status: ${fetchResult.status} ${fetchResult.statusText}`,
      };
    }
  } catch (error: any) {
    // Log detailed error information
    console.error("Login API call failed:", error);

    // Check if it's a network error
    const isNetworkError = error instanceof TypeError && error.message.includes('fetch');

    return {
      success: false,
      message: isNetworkError
        ? "Network error: Unable to connect to the server. Please check your internet connection."
        : `Error: ${error.message || "Unable to login. An unknown error occurred!"}`,
    };
  }
};
