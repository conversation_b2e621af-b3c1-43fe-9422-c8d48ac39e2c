{"version": 3, "sources": ["../../src/lib/fallback.ts"], "sourcesContent": ["/**\n * Describes the different fallback modes that a given page can have.\n */\nexport const enum FallbackMode {\n  /**\n   * A BLOCKING_STATIC_RENDER fallback will block the request until the page is\n   * generated. No fallback page will be rendered, and users will have to wait\n   * to render the page.\n   */\n  BLOCKING_STATIC_RENDER = 'BLOCKING_STATIC_RENDER',\n\n  /**\n   * When set to PRERENDER, a fallback page will be sent to users in place of\n   * forcing them to wait for the page to be generated. This allows the user to\n   * see a rendered page earlier.\n   */\n  PRERENDER = 'PRERENDER',\n\n  /**\n   * When set to NOT_FOUND, pages that are not already prerendered will result\n   * in a not found response.\n   */\n  NOT_FOUND = 'NOT_FOUND',\n}\n\n/**\n * The fallback value returned from the `getStaticPaths` function.\n */\nexport type GetStaticPathsFallback = boolean | 'blocking'\n\n/**\n * Parses the fallback field from the prerender manifest.\n *\n * @param fallbackField The fallback field from the prerender manifest.\n * @returns The fallback mode.\n */\nexport function parseFallbackField(\n  fallbackField: string | boolean | null | undefined\n): FallbackMode | undefined {\n  if (typeof fallbackField === 'string') {\n    return FallbackMode.PRERENDER\n  } else if (fallbackField === null) {\n    return FallbackMode.BLOCKING_STATIC_RENDER\n  } else if (fallbackField === false) {\n    return FallbackMode.NOT_FOUND\n  } else if (fallbackField === undefined) {\n    return undefined\n  } else {\n    throw new Error(\n      `Invalid fallback option: ${fallbackField}. Fallback option must be a string, null, undefined, or false.`\n    )\n  }\n}\n\nexport function fallbackModeToFallbackField(\n  fallback: FallbackMode,\n  page: string | undefined\n): string | false | null {\n  switch (fallback) {\n    case FallbackMode.BLOCKING_STATIC_RENDER:\n      return null\n    case FallbackMode.NOT_FOUND:\n      return false\n    case FallbackMode.PRERENDER:\n      if (!page) {\n        throw new Error(\n          `Invariant: expected a page to be provided when fallback mode is \"${fallback}\"`\n        )\n      }\n\n      return page\n    default:\n      throw new Error(`Invalid fallback mode: ${fallback}`)\n  }\n}\n\n/**\n * Parses the fallback from the static paths result.\n *\n * @param result The result from the static paths function.\n * @returns The fallback mode.\n */\nexport function parseStaticPathsResult(\n  result: GetStaticPathsFallback\n): FallbackMode {\n  if (result === true) {\n    return FallbackMode.PRERENDER\n  } else if (result === 'blocking') {\n    return FallbackMode.BLOCKING_STATIC_RENDER\n  } else {\n    return FallbackMode.NOT_FOUND\n  }\n}\n\n/**\n * Converts the fallback mode to a static paths result.\n *\n * @param fallback The fallback mode.\n * @returns The static paths fallback result.\n */\nexport function fallbackModeToStaticPathsResult(\n  fallback: FallbackMode\n): GetStaticPathsFallback {\n  switch (fallback) {\n    case FallbackMode.PRERENDER:\n      return true\n    case FallbackMode.BLOCKING_STATIC_RENDER:\n      return 'blocking'\n    case FallbackMode.NOT_FOUND:\n    default:\n      return false\n  }\n}\n"], "names": ["FallbackMode", "fallbackModeToFallbackField", "fallbackModeToStaticPathsResult", "parseFallbackField", "parseStaticPathsResult", "fallback<PERSON><PERSON>", "undefined", "Error", "fallback", "page", "result"], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;;;;;IACiBA,YAAY;eAAZA;;IAmDFC,2BAA2B;eAA3BA;;IA8CAC,+BAA+B;eAA/BA;;IAhEAC,kBAAkB;eAAlBA;;IA8CAC,sBAAsB;eAAtBA;;;AA/ET,IAAA,AAAWJ,sCAAAA;IAChB;;;;GAIC;IAGD;;;;GAIC;IAGD;;;GAGC;WAlBeA;;AAiCX,SAASG,mBACdE,aAAkD;IAElD,IAAI,OAAOA,kBAAkB,UAAU;QACrC;IACF,OAAO,IAAIA,kBAAkB,MAAM;QACjC;IACF,OAAO,IAAIA,kBAAkB,OAAO;QAClC;IACF,OAAO,IAAIA,kBAAkBC,WAAW;QACtC,OAAOA;IACT,OAAO;QACL,MAAM,IAAIC,MACR,CAAC,yBAAyB,EAAEF,cAAc,8DAA8D,CAAC;IAE7G;AACF;AAEO,SAASJ,4BACdO,QAAsB,EACtBC,IAAwB;IAExB,OAAQD;QACN;YACE,OAAO;QACT;YACE,OAAO;QACT;YACE,IAAI,CAACC,MAAM;gBACT,MAAM,IAAIF,MACR,CAAC,iEAAiE,EAAEC,SAAS,CAAC,CAAC;YAEnF;YAEA,OAAOC;QACT;YACE,MAAM,IAAIF,MAAM,CAAC,uBAAuB,EAAEC,UAAU;IACxD;AACF;AAQO,SAASJ,uBACdM,MAA8B;IAE9B,IAAIA,WAAW,MAAM;QACnB;IACF,OAAO,IAAIA,WAAW,YAAY;QAChC;IACF,OAAO;QACL;IACF;AACF;AAQO,SAASR,gCACdM,QAAsB;IAEtB,OAAQA;QACN;YACE,OAAO;QACT;YACE,OAAO;QACT;QACA;YACE,OAAO;IACX;AACF"}