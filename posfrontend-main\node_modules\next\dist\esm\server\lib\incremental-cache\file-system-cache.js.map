{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/file-system-cache.ts"], "sourcesContent": ["import type { RouteMetadata } from '../../../export/routes/types'\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON>, CacheHandlerContext, CacheHandlerValue } from './'\nimport type { CacheFs } from '../../../shared/lib/utils'\nimport {\n  CachedRouteKind,\n  IncrementalCacheKind,\n  type CachedFetchValue,\n} from '../../response-cache'\n\nimport { LRUCache } from '../lru-cache'\nimport path from '../../../shared/lib/isomorphic/path'\nimport {\n  NEXT_CACHE_TAGS_HEADER,\n  NEXT_DATA_SUFFIX,\n  NEXT_META_SUFFIX,\n  RSC_PREFETCH_SUFFIX,\n  RSC_SEGMENT_SUFFIX,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SUFFIX,\n} from '../../../lib/constants'\nimport { tagsManifest } from './tags-manifest.external'\n\ntype FileSystemCacheContext = Omit<\n  CacheHandlerContext,\n  'fs' | 'serverDistDir'\n> & {\n  fs: CacheFs\n  serverDistDir: string\n}\n\nlet memoryCache: LRUCache<CacheHandlerValue> | undefined\n\nexport default class FileSystemCache implements CacheHandler {\n  private fs: FileSystemCacheContext['fs']\n  private flushToDisk?: FileSystemCacheContext['flushToDisk']\n  private serverDistDir: FileSystemCacheContext['serverDistDir']\n  private revalidatedTags: string[]\n  private debug: boolean\n\n  constructor(ctx: FileSystemCacheContext) {\n    this.fs = ctx.fs\n    this.flushToDisk = ctx.flushToDisk\n    this.serverDistDir = ctx.serverDistDir\n    this.revalidatedTags = ctx.revalidatedTags\n    this.debug = !!process.env.NEXT_PRIVATE_DEBUG_CACHE\n\n    if (ctx.maxMemoryCacheSize) {\n      if (!memoryCache) {\n        if (this.debug) {\n          console.log('using memory store for fetch cache')\n        }\n\n        memoryCache = new LRUCache(ctx.maxMemoryCacheSize, function length({\n          value,\n        }) {\n          if (!value) {\n            return 25\n          } else if (value.kind === CachedRouteKind.REDIRECT) {\n            return JSON.stringify(value.props).length\n          } else if (value.kind === CachedRouteKind.IMAGE) {\n            throw new Error('invariant image should not be incremental-cache')\n          } else if (value.kind === CachedRouteKind.FETCH) {\n            return JSON.stringify(value.data || '').length\n          } else if (value.kind === CachedRouteKind.APP_ROUTE) {\n            return value.body.length\n          }\n          // rough estimate of size of cache value\n          return (\n            value.html.length +\n            (JSON.stringify(\n              value.kind === CachedRouteKind.APP_PAGE\n                ? value.rscData\n                : value.pageData\n            )?.length || 0)\n          )\n        })\n      }\n    } else if (this.debug) {\n      console.log('not using memory store for fetch cache')\n    }\n  }\n\n  public resetRequestCache(): void {}\n\n  public async revalidateTag(\n    ...args: Parameters<CacheHandler['revalidateTag']>\n  ) {\n    let [tags] = args\n    tags = typeof tags === 'string' ? [tags] : tags\n\n    if (this.debug) {\n      console.log('revalidateTag', tags)\n    }\n\n    if (tags.length === 0) {\n      return\n    }\n\n    for (const tag of tags) {\n      const data = tagsManifest.items[tag] || {}\n      data.revalidatedAt = Date.now()\n      tagsManifest.items[tag] = data\n    }\n  }\n\n  public async get(...args: Parameters<CacheHandler['get']>) {\n    const [key, ctx] = args\n    const { tags, softTags, kind, isRoutePPREnabled, isFallback } = ctx\n\n    let data = memoryCache?.get(key)\n\n    if (this.debug) {\n      console.log('get', key, tags, kind, !!data)\n    }\n\n    // let's check the disk for seed data\n    if (!data && process.env.NEXT_RUNTIME !== 'edge') {\n      if (kind === IncrementalCacheKind.APP_ROUTE) {\n        try {\n          const filePath = this.getFilePath(\n            `${key}.body`,\n            IncrementalCacheKind.APP_ROUTE\n          )\n          const fileData = await this.fs.readFile(filePath)\n          const { mtime } = await this.fs.stat(filePath)\n\n          const meta = JSON.parse(\n            await this.fs.readFile(\n              filePath.replace(/\\.body$/, NEXT_META_SUFFIX),\n              'utf8'\n            )\n          )\n\n          const cacheEntry: CacheHandlerValue = {\n            lastModified: mtime.getTime(),\n            value: {\n              kind: CachedRouteKind.APP_ROUTE,\n              body: fileData,\n              headers: meta.headers,\n              status: meta.status,\n            },\n          }\n          return cacheEntry\n        } catch {\n          return null\n        }\n      }\n\n      try {\n        const filePath = this.getFilePath(\n          kind === IncrementalCacheKind.FETCH ? key : `${key}.html`,\n          kind\n        )\n\n        const fileData = await this.fs.readFile(filePath, 'utf8')\n        const { mtime } = await this.fs.stat(filePath)\n\n        if (kind === IncrementalCacheKind.FETCH) {\n          if (!this.flushToDisk) return null\n\n          const lastModified = mtime.getTime()\n          const parsedData: CachedFetchValue = JSON.parse(fileData)\n          data = {\n            lastModified,\n            value: parsedData,\n          }\n\n          if (data.value?.kind === CachedRouteKind.FETCH) {\n            const storedTags = data.value?.tags\n\n            // update stored tags if a new one is being added\n            // TODO: remove this when we can send the tags\n            // via header on GET same as SET\n            if (!tags?.every((tag) => storedTags?.includes(tag))) {\n              if (this.debug) {\n                console.log('tags vs storedTags mismatch', tags, storedTags)\n              }\n              await this.set(key, data.value, {\n                tags,\n                isRoutePPREnabled,\n              })\n            }\n          }\n        } else if (kind === IncrementalCacheKind.APP_PAGE) {\n          // We try to load the metadata file, but if it fails, we don't\n          // error. We also don't load it if this is a fallback.\n          let meta: RouteMetadata | undefined\n          try {\n            meta = JSON.parse(\n              await this.fs.readFile(\n                filePath.replace(/\\.html$/, NEXT_META_SUFFIX),\n                'utf8'\n              )\n            )\n          } catch {}\n\n          let maybeSegmentData: Map<string, Buffer> | undefined\n          if (meta?.segmentPaths) {\n            // Collect all the segment data for this page.\n            // TODO: To optimize file system reads, we should consider creating\n            // separate cache entries for each segment, rather than storing them\n            // all on the page's entry. Though the behavior is\n            // identical regardless.\n            const segmentData: Map<string, Buffer> = new Map()\n            maybeSegmentData = segmentData\n            const segmentsDir = key + RSC_SEGMENTS_DIR_SUFFIX\n            await Promise.all(\n              meta.segmentPaths.map(async (segmentPath: string) => {\n                const segmentDataFilePath = this.getFilePath(\n                  segmentPath === '/'\n                    ? segmentsDir + '/_index' + RSC_SEGMENT_SUFFIX\n                    : segmentsDir + segmentPath + RSC_SEGMENT_SUFFIX,\n                  IncrementalCacheKind.APP_PAGE\n                )\n                try {\n                  segmentData.set(\n                    segmentPath,\n                    await this.fs.readFile(segmentDataFilePath)\n                  )\n                } catch {\n                  // This shouldn't happen, but if for some reason we fail to\n                  // load a segment from the filesystem, treat it the same as if\n                  // the segment is dynamic and does not have a prefetch.\n                }\n              })\n            )\n          }\n\n          let rscData: Buffer | undefined\n          if (!isFallback) {\n            rscData = await this.fs.readFile(\n              this.getFilePath(\n                `${key}${isRoutePPREnabled ? RSC_PREFETCH_SUFFIX : RSC_SUFFIX}`,\n                IncrementalCacheKind.APP_PAGE\n              )\n            )\n          }\n\n          data = {\n            lastModified: mtime.getTime(),\n            value: {\n              kind: CachedRouteKind.APP_PAGE,\n              html: fileData,\n              rscData,\n              postponed: meta?.postponed,\n              headers: meta?.headers,\n              status: meta?.status,\n              segmentData: maybeSegmentData,\n            },\n          }\n        } else if (kind === IncrementalCacheKind.PAGES) {\n          let meta: RouteMetadata | undefined\n          let pageData: string | object = {}\n\n          if (!isFallback) {\n            pageData = JSON.parse(\n              await this.fs.readFile(\n                this.getFilePath(\n                  `${key}${NEXT_DATA_SUFFIX}`,\n                  IncrementalCacheKind.PAGES\n                ),\n                'utf8'\n              )\n            )\n          }\n\n          data = {\n            lastModified: mtime.getTime(),\n            value: {\n              kind: CachedRouteKind.PAGES,\n              html: fileData,\n              pageData,\n              headers: meta?.headers,\n              status: meta?.status,\n            },\n          }\n        } else {\n          throw new Error(\n            `Invariant: Unexpected route kind ${kind} in file system cache.`\n          )\n        }\n\n        if (data) {\n          memoryCache?.set(key, data)\n        }\n      } catch {\n        return null\n      }\n    }\n\n    if (\n      data?.value?.kind === CachedRouteKind.APP_PAGE ||\n      data?.value?.kind === CachedRouteKind.PAGES\n    ) {\n      let cacheTags: undefined | string[]\n      const tagsHeader = data.value.headers?.[NEXT_CACHE_TAGS_HEADER]\n\n      if (typeof tagsHeader === 'string') {\n        cacheTags = tagsHeader.split(',')\n      }\n\n      if (cacheTags?.length) {\n        const isStale = cacheTags.some((tag) => {\n          return (\n            tagsManifest?.items[tag]?.revalidatedAt &&\n            tagsManifest?.items[tag].revalidatedAt >=\n              (data?.lastModified || Date.now())\n          )\n        })\n\n        // we trigger a blocking validation if an ISR page\n        // had a tag revalidated, if we want to be a background\n        // revalidation instead we return data.lastModified = -1\n        if (isStale) {\n          return null\n        }\n      }\n    } else if (data?.value?.kind === CachedRouteKind.FETCH) {\n      const combinedTags = [...(tags || []), ...(softTags || [])]\n\n      const wasRevalidated = combinedTags.some((tag) => {\n        if (this.revalidatedTags.includes(tag)) {\n          return true\n        }\n\n        return (\n          tagsManifest?.items[tag]?.revalidatedAt &&\n          tagsManifest?.items[tag].revalidatedAt >=\n            (data?.lastModified || Date.now())\n        )\n      })\n      // When revalidate tag is called we don't return\n      // stale data so it's updated right away\n      if (wasRevalidated) {\n        data = undefined\n      }\n    }\n\n    return data ?? null\n  }\n\n  public async set(...args: Parameters<CacheHandler['set']>) {\n    const [key, data, ctx] = args\n    const { isFallback } = ctx\n    memoryCache?.set(key, {\n      value: data,\n      lastModified: Date.now(),\n    })\n\n    if (this.debug) {\n      console.log('set', key)\n    }\n\n    if (!this.flushToDisk || !data) return\n\n    if (data.kind === CachedRouteKind.APP_ROUTE) {\n      const filePath = this.getFilePath(\n        `${key}.body`,\n        IncrementalCacheKind.APP_ROUTE\n      )\n      await this.fs.mkdir(path.dirname(filePath))\n      await this.fs.writeFile(filePath, data.body)\n\n      const meta: RouteMetadata = {\n        headers: data.headers,\n        status: data.status,\n        postponed: undefined,\n        segmentPaths: undefined,\n      }\n\n      await this.fs.writeFile(\n        filePath.replace(/\\.body$/, NEXT_META_SUFFIX),\n        JSON.stringify(meta, null, 2)\n      )\n    } else if (\n      data.kind === CachedRouteKind.PAGES ||\n      data.kind === CachedRouteKind.APP_PAGE\n    ) {\n      const isAppPath = data.kind === CachedRouteKind.APP_PAGE\n      const htmlPath = this.getFilePath(\n        `${key}.html`,\n        isAppPath ? IncrementalCacheKind.APP_PAGE : IncrementalCacheKind.PAGES\n      )\n      await this.fs.mkdir(path.dirname(htmlPath))\n      await this.fs.writeFile(htmlPath, data.html)\n\n      // Fallbacks don't generate a data file.\n      if (!isFallback) {\n        await this.fs.writeFile(\n          this.getFilePath(\n            `${key}${\n              isAppPath\n                ? ctx.isRoutePPREnabled\n                  ? RSC_PREFETCH_SUFFIX\n                  : RSC_SUFFIX\n                : NEXT_DATA_SUFFIX\n            }`,\n            isAppPath\n              ? IncrementalCacheKind.APP_PAGE\n              : IncrementalCacheKind.PAGES\n          ),\n          isAppPath ? data.rscData : JSON.stringify(data.pageData)\n        )\n      }\n\n      if (data?.kind === CachedRouteKind.APP_PAGE) {\n        const meta: RouteMetadata = {\n          headers: data.headers,\n          status: data.status,\n          postponed: data.postponed,\n          segmentPaths: undefined,\n        }\n\n        await this.fs.writeFile(\n          htmlPath.replace(/\\.html$/, NEXT_META_SUFFIX),\n          JSON.stringify(meta)\n        )\n      }\n    } else if (data.kind === CachedRouteKind.FETCH) {\n      const filePath = this.getFilePath(key, IncrementalCacheKind.FETCH)\n      await this.fs.mkdir(path.dirname(filePath))\n      await this.fs.writeFile(\n        filePath,\n        JSON.stringify({\n          ...data,\n          tags: ctx.tags,\n        })\n      )\n    }\n  }\n\n  private getFilePath(pathname: string, kind: IncrementalCacheKind): string {\n    switch (kind) {\n      case IncrementalCacheKind.FETCH:\n        // we store in .next/cache/fetch-cache so it can be persisted\n        // across deploys\n        return path.join(\n          this.serverDistDir,\n          '..',\n          'cache',\n          'fetch-cache',\n          pathname\n        )\n      case IncrementalCacheKind.PAGES:\n        return path.join(this.serverDistDir, 'pages', pathname)\n      case IncrementalCacheKind.IMAGE:\n      case IncrementalCacheKind.APP_PAGE:\n      case IncrementalCacheKind.APP_ROUTE:\n        return path.join(this.serverDistDir, 'app', pathname)\n      default:\n        throw new Error(`Unexpected file path kind: ${kind}`)\n    }\n  }\n}\n"], "names": ["CachedRouteKind", "IncrementalCacheKind", "L<PERSON><PERSON><PERSON>", "path", "NEXT_CACHE_TAGS_HEADER", "NEXT_DATA_SUFFIX", "NEXT_META_SUFFIX", "RSC_PREFETCH_SUFFIX", "RSC_SEGMENT_SUFFIX", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SUFFIX", "tagsManifest", "memoryCache", "FileSystemCache", "constructor", "ctx", "fs", "flushToDisk", "serverDistDir", "revalidatedTags", "debug", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "maxMemoryCacheSize", "console", "log", "length", "value", "JSON", "kind", "REDIRECT", "stringify", "props", "IMAGE", "Error", "FETCH", "data", "APP_ROUTE", "body", "html", "APP_PAGE", "rscData", "pageData", "resetRequestCache", "revalidateTag", "args", "tags", "tag", "items", "revalidatedAt", "Date", "now", "get", "key", "softTags", "isRoutePPREnabled", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_RUNTIME", "filePath", "getFilePath", "fileData", "readFile", "mtime", "stat", "meta", "parse", "replace", "cacheEntry", "lastModified", "getTime", "headers", "status", "parsedData", "storedTags", "every", "includes", "set", "maybeSegmentData", "segmentPaths", "segmentData", "Map", "segmentsDir", "Promise", "all", "map", "segmentPath", "segmentDataFilePath", "postponed", "PAGES", "cacheTags", "<PERSON><PERSON><PERSON><PERSON>", "split", "isStale", "some", "combinedTags", "wasRevalidated", "undefined", "mkdir", "dirname", "writeFile", "isAppPath", "htmlPath", "pathname", "join"], "mappings": "AAGA,SACEA,eAAe,EACfC,oBAAoB,QAEf,uBAAsB;AAE7B,SAASC,QAAQ,QAAQ,eAAc;AACvC,OAAOC,UAAU,sCAAqC;AACtD,SACEC,sBAAsB,EACtBC,gBAAgB,EAChBC,gBAAgB,EAChBC,mBAAmB,EACnBC,kBAAkB,EAClBC,uBAAuB,EACvBC,UAAU,QACL,yBAAwB;AAC/B,SAASC,YAAY,QAAQ,2BAA0B;AAUvD,IAAIC;AAEJ,eAAe,MAAMC;IAOnBC,YAAYC,GAA2B,CAAE;QACvC,IAAI,CAACC,EAAE,GAAGD,IAAIC,EAAE;QAChB,IAAI,CAACC,WAAW,GAAGF,IAAIE,WAAW;QAClC,IAAI,CAACC,aAAa,GAAGH,IAAIG,aAAa;QACtC,IAAI,CAACC,eAAe,GAAGJ,IAAII,eAAe;QAC1C,IAAI,CAACC,KAAK,GAAG,CAAC,CAACC,QAAQC,GAAG,CAACC,wBAAwB;QAEnD,IAAIR,IAAIS,kBAAkB,EAAE;YAC1B,IAAI,CAACZ,aAAa;gBAChB,IAAI,IAAI,CAACQ,KAAK,EAAE;oBACdK,QAAQC,GAAG,CAAC;gBACd;gBAEAd,cAAc,IAAIV,SAASa,IAAIS,kBAAkB,EAAE,SAASG,OAAO,EACjEC,KAAK,EACN;wBAeIC;oBAdH,IAAI,CAACD,OAAO;wBACV,OAAO;oBACT,OAAO,IAAIA,MAAME,IAAI,KAAK9B,gBAAgB+B,QAAQ,EAAE;wBAClD,OAAOF,KAAKG,SAAS,CAACJ,MAAMK,KAAK,EAAEN,MAAM;oBAC3C,OAAO,IAAIC,MAAME,IAAI,KAAK9B,gBAAgBkC,KAAK,EAAE;wBAC/C,MAAM,IAAIC,MAAM;oBAClB,OAAO,IAAIP,MAAME,IAAI,KAAK9B,gBAAgBoC,KAAK,EAAE;wBAC/C,OAAOP,KAAKG,SAAS,CAACJ,MAAMS,IAAI,IAAI,IAAIV,MAAM;oBAChD,OAAO,IAAIC,MAAME,IAAI,KAAK9B,gBAAgBsC,SAAS,EAAE;wBACnD,OAAOV,MAAMW,IAAI,CAACZ,MAAM;oBAC1B;oBACA,wCAAwC;oBACxC,OACEC,MAAMY,IAAI,CAACb,MAAM,GAChBE,CAAAA,EAAAA,kBAAAA,KAAKG,SAAS,CACbJ,MAAME,IAAI,KAAK9B,gBAAgByC,QAAQ,GACnCb,MAAMc,OAAO,GACbd,MAAMe,QAAQ,sBAHnBd,gBAIEF,MAAM,KAAI,CAAA;gBAEjB;YACF;QACF,OAAO,IAAI,IAAI,CAACP,KAAK,EAAE;YACrBK,QAAQC,GAAG,CAAC;QACd;IACF;IAEOkB,oBAA0B,CAAC;IAElC,MAAaC,cACX,GAAGC,IAA+C,EAClD;QACA,IAAI,CAACC,KAAK,GAAGD;QACbC,OAAO,OAAOA,SAAS,WAAW;YAACA;SAAK,GAAGA;QAE3C,IAAI,IAAI,CAAC3B,KAAK,EAAE;YACdK,QAAQC,GAAG,CAAC,iBAAiBqB;QAC/B;QAEA,IAAIA,KAAKpB,MAAM,KAAK,GAAG;YACrB;QACF;QAEA,KAAK,MAAMqB,OAAOD,KAAM;YACtB,MAAMV,OAAO1B,aAAasC,KAAK,CAACD,IAAI,IAAI,CAAC;YACzCX,KAAKa,aAAa,GAAGC,KAAKC,GAAG;YAC7BzC,aAAasC,KAAK,CAACD,IAAI,GAAGX;QAC5B;IACF;IAEA,MAAagB,IAAI,GAAGP,IAAqC,EAAE;YA0LvDT,aACAA,cAyBSA;QAnNX,MAAM,CAACiB,KAAKvC,IAAI,GAAG+B;QACnB,MAAM,EAAEC,IAAI,EAAEQ,QAAQ,EAAEzB,IAAI,EAAE0B,iBAAiB,EAAEC,UAAU,EAAE,GAAG1C;QAEhE,IAAIsB,OAAOzB,+BAAAA,YAAayC,GAAG,CAACC;QAE5B,IAAI,IAAI,CAAClC,KAAK,EAAE;YACdK,QAAQC,GAAG,CAAC,OAAO4B,KAAKP,MAAMjB,MAAM,CAAC,CAACO;QACxC;QAEA,qCAAqC;QACrC,IAAI,CAACA,QAAQhB,QAAQC,GAAG,CAACoC,YAAY,KAAK,QAAQ;YAChD,IAAI5B,SAAS7B,qBAAqBqC,SAAS,EAAE;gBAC3C,IAAI;oBACF,MAAMqB,WAAW,IAAI,CAACC,WAAW,CAC/B,GAAGN,IAAI,KAAK,CAAC,EACbrD,qBAAqBqC,SAAS;oBAEhC,MAAMuB,WAAW,MAAM,IAAI,CAAC7C,EAAE,CAAC8C,QAAQ,CAACH;oBACxC,MAAM,EAAEI,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC/C,EAAE,CAACgD,IAAI,CAACL;oBAErC,MAAMM,OAAOpC,KAAKqC,KAAK,CACrB,MAAM,IAAI,CAAClD,EAAE,CAAC8C,QAAQ,CACpBH,SAASQ,OAAO,CAAC,WAAW7D,mBAC5B;oBAIJ,MAAM8D,aAAgC;wBACpCC,cAAcN,MAAMO,OAAO;wBAC3B1C,OAAO;4BACLE,MAAM9B,gBAAgBsC,SAAS;4BAC/BC,MAAMsB;4BACNU,SAASN,KAAKM,OAAO;4BACrBC,QAAQP,KAAKO,MAAM;wBACrB;oBACF;oBACA,OAAOJ;gBACT,EAAE,OAAM;oBACN,OAAO;gBACT;YACF;YAEA,IAAI;gBACF,MAAMT,WAAW,IAAI,CAACC,WAAW,CAC/B9B,SAAS7B,qBAAqBmC,KAAK,GAAGkB,MAAM,GAAGA,IAAI,KAAK,CAAC,EACzDxB;gBAGF,MAAM+B,WAAW,MAAM,IAAI,CAAC7C,EAAE,CAAC8C,QAAQ,CAACH,UAAU;gBAClD,MAAM,EAAEI,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC/C,EAAE,CAACgD,IAAI,CAACL;gBAErC,IAAI7B,SAAS7B,qBAAqBmC,KAAK,EAAE;wBAUnCC;oBATJ,IAAI,CAAC,IAAI,CAACpB,WAAW,EAAE,OAAO;oBAE9B,MAAMoD,eAAeN,MAAMO,OAAO;oBAClC,MAAMG,aAA+B5C,KAAKqC,KAAK,CAACL;oBAChDxB,OAAO;wBACLgC;wBACAzC,OAAO6C;oBACT;oBAEA,IAAIpC,EAAAA,eAAAA,KAAKT,KAAK,qBAAVS,aAAYP,IAAI,MAAK9B,gBAAgBoC,KAAK,EAAE;4BAC3BC;wBAAnB,MAAMqC,cAAarC,eAAAA,KAAKT,KAAK,qBAAVS,aAAYU,IAAI;wBAEnC,iDAAiD;wBACjD,8CAA8C;wBAC9C,gCAAgC;wBAChC,IAAI,EAACA,wBAAAA,KAAM4B,KAAK,CAAC,CAAC3B,MAAQ0B,8BAAAA,WAAYE,QAAQ,CAAC5B,QAAO;4BACpD,IAAI,IAAI,CAAC5B,KAAK,EAAE;gCACdK,QAAQC,GAAG,CAAC,+BAA+BqB,MAAM2B;4BACnD;4BACA,MAAM,IAAI,CAACG,GAAG,CAACvB,KAAKjB,KAAKT,KAAK,EAAE;gCAC9BmB;gCACAS;4BACF;wBACF;oBACF;gBACF,OAAO,IAAI1B,SAAS7B,qBAAqBwC,QAAQ,EAAE;oBACjD,8DAA8D;oBAC9D,sDAAsD;oBACtD,IAAIwB;oBACJ,IAAI;wBACFA,OAAOpC,KAAKqC,KAAK,CACf,MAAM,IAAI,CAAClD,EAAE,CAAC8C,QAAQ,CACpBH,SAASQ,OAAO,CAAC,WAAW7D,mBAC5B;oBAGN,EAAE,OAAM,CAAC;oBAET,IAAIwE;oBACJ,IAAIb,wBAAAA,KAAMc,YAAY,EAAE;wBACtB,8CAA8C;wBAC9C,mEAAmE;wBACnE,oEAAoE;wBACpE,kDAAkD;wBAClD,wBAAwB;wBACxB,MAAMC,cAAmC,IAAIC;wBAC7CH,mBAAmBE;wBACnB,MAAME,cAAc5B,MAAM7C;wBAC1B,MAAM0E,QAAQC,GAAG,CACfnB,KAAKc,YAAY,CAACM,GAAG,CAAC,OAAOC;4BAC3B,MAAMC,sBAAsB,IAAI,CAAC3B,WAAW,CAC1C0B,gBAAgB,MACZJ,cAAc,YAAY1E,qBAC1B0E,cAAcI,cAAc9E,oBAChCP,qBAAqBwC,QAAQ;4BAE/B,IAAI;gCACFuC,YAAYH,GAAG,CACbS,aACA,MAAM,IAAI,CAACtE,EAAE,CAAC8C,QAAQ,CAACyB;4BAE3B,EAAE,OAAM;4BACN,2DAA2D;4BAC3D,8DAA8D;4BAC9D,uDAAuD;4BACzD;wBACF;oBAEJ;oBAEA,IAAI7C;oBACJ,IAAI,CAACe,YAAY;wBACff,UAAU,MAAM,IAAI,CAAC1B,EAAE,CAAC8C,QAAQ,CAC9B,IAAI,CAACF,WAAW,CACd,GAAGN,MAAME,oBAAoBjD,sBAAsBG,YAAY,EAC/DT,qBAAqBwC,QAAQ;oBAGnC;oBAEAJ,OAAO;wBACLgC,cAAcN,MAAMO,OAAO;wBAC3B1C,OAAO;4BACLE,MAAM9B,gBAAgByC,QAAQ;4BAC9BD,MAAMqB;4BACNnB;4BACA8C,SAAS,EAAEvB,wBAAAA,KAAMuB,SAAS;4BAC1BjB,OAAO,EAAEN,wBAAAA,KAAMM,OAAO;4BACtBC,MAAM,EAAEP,wBAAAA,KAAMO,MAAM;4BACpBQ,aAAaF;wBACf;oBACF;gBACF,OAAO,IAAIhD,SAAS7B,qBAAqBwF,KAAK,EAAE;oBAC9C,IAAIxB;oBACJ,IAAItB,WAA4B,CAAC;oBAEjC,IAAI,CAACc,YAAY;wBACfd,WAAWd,KAAKqC,KAAK,CACnB,MAAM,IAAI,CAAClD,EAAE,CAAC8C,QAAQ,CACpB,IAAI,CAACF,WAAW,CACd,GAAGN,MAAMjD,kBAAkB,EAC3BJ,qBAAqBwF,KAAK,GAE5B;oBAGN;oBAEApD,OAAO;wBACLgC,cAAcN,MAAMO,OAAO;wBAC3B1C,OAAO;4BACLE,MAAM9B,gBAAgByF,KAAK;4BAC3BjD,MAAMqB;4BACNlB;4BACA4B,OAAO,EAAEN,wBAAAA,KAAMM,OAAO;4BACtBC,MAAM,EAAEP,wBAAAA,KAAMO,MAAM;wBACtB;oBACF;gBACF,OAAO;oBACL,MAAM,IAAIrC,MACR,CAAC,iCAAiC,EAAEL,KAAK,sBAAsB,CAAC;gBAEpE;gBAEA,IAAIO,MAAM;oBACRzB,+BAAAA,YAAaiE,GAAG,CAACvB,KAAKjB;gBACxB;YACF,EAAE,OAAM;gBACN,OAAO;YACT;QACF;QAEA,IACEA,CAAAA,yBAAAA,cAAAA,KAAMT,KAAK,qBAAXS,YAAaP,IAAI,MAAK9B,gBAAgByC,QAAQ,IAC9CJ,CAAAA,yBAAAA,eAAAA,KAAMT,KAAK,qBAAXS,aAAaP,IAAI,MAAK9B,gBAAgByF,KAAK,EAC3C;gBAEmBpD;YADnB,IAAIqD;YACJ,MAAMC,cAAatD,sBAAAA,KAAKT,KAAK,CAAC2C,OAAO,qBAAlBlC,mBAAoB,CAACjC,uBAAuB;YAE/D,IAAI,OAAOuF,eAAe,UAAU;gBAClCD,YAAYC,WAAWC,KAAK,CAAC;YAC/B;YAEA,IAAIF,6BAAAA,UAAW/D,MAAM,EAAE;gBACrB,MAAMkE,UAAUH,UAAUI,IAAI,CAAC,CAAC9C;wBAE5BrC;oBADF,OACEA,CAAAA,iCAAAA,0BAAAA,aAAcsC,KAAK,CAACD,IAAI,qBAAxBrC,wBAA0BuC,aAAa,KACvCvC,CAAAA,gCAAAA,aAAcsC,KAAK,CAACD,IAAI,CAACE,aAAa,KACnCb,CAAAA,CAAAA,wBAAAA,KAAMgC,YAAY,KAAIlB,KAAKC,GAAG,EAAC;gBAEtC;gBAEA,kDAAkD;gBAClD,uDAAuD;gBACvD,wDAAwD;gBACxD,IAAIyC,SAAS;oBACX,OAAO;gBACT;YACF;QACF,OAAO,IAAIxD,CAAAA,yBAAAA,eAAAA,KAAMT,KAAK,qBAAXS,aAAaP,IAAI,MAAK9B,gBAAgBoC,KAAK,EAAE;YACtD,MAAM2D,eAAe;mBAAKhD,QAAQ,EAAE;mBAAOQ,YAAY,EAAE;aAAE;YAE3D,MAAMyC,iBAAiBD,aAAaD,IAAI,CAAC,CAAC9C;oBAMtCrC;gBALF,IAAI,IAAI,CAACQ,eAAe,CAACyD,QAAQ,CAAC5B,MAAM;oBACtC,OAAO;gBACT;gBAEA,OACErC,CAAAA,iCAAAA,0BAAAA,aAAcsC,KAAK,CAACD,IAAI,qBAAxBrC,wBAA0BuC,aAAa,KACvCvC,CAAAA,gCAAAA,aAAcsC,KAAK,CAACD,IAAI,CAACE,aAAa,KACnCb,CAAAA,CAAAA,wBAAAA,KAAMgC,YAAY,KAAIlB,KAAKC,GAAG,EAAC;YAEtC;YACA,gDAAgD;YAChD,wCAAwC;YACxC,IAAI4C,gBAAgB;gBAClB3D,OAAO4D;YACT;QACF;QAEA,OAAO5D,QAAQ;IACjB;IAEA,MAAawC,IAAI,GAAG/B,IAAqC,EAAE;QACzD,MAAM,CAACQ,KAAKjB,MAAMtB,IAAI,GAAG+B;QACzB,MAAM,EAAEW,UAAU,EAAE,GAAG1C;QACvBH,+BAAAA,YAAaiE,GAAG,CAACvB,KAAK;YACpB1B,OAAOS;YACPgC,cAAclB,KAAKC,GAAG;QACxB;QAEA,IAAI,IAAI,CAAChC,KAAK,EAAE;YACdK,QAAQC,GAAG,CAAC,OAAO4B;QACrB;QAEA,IAAI,CAAC,IAAI,CAACrC,WAAW,IAAI,CAACoB,MAAM;QAEhC,IAAIA,KAAKP,IAAI,KAAK9B,gBAAgBsC,SAAS,EAAE;YAC3C,MAAMqB,WAAW,IAAI,CAACC,WAAW,CAC/B,GAAGN,IAAI,KAAK,CAAC,EACbrD,qBAAqBqC,SAAS;YAEhC,MAAM,IAAI,CAACtB,EAAE,CAACkF,KAAK,CAAC/F,KAAKgG,OAAO,CAACxC;YACjC,MAAM,IAAI,CAAC3C,EAAE,CAACoF,SAAS,CAACzC,UAAUtB,KAAKE,IAAI;YAE3C,MAAM0B,OAAsB;gBAC1BM,SAASlC,KAAKkC,OAAO;gBACrBC,QAAQnC,KAAKmC,MAAM;gBACnBgB,WAAWS;gBACXlB,cAAckB;YAChB;YAEA,MAAM,IAAI,CAACjF,EAAE,CAACoF,SAAS,CACrBzC,SAASQ,OAAO,CAAC,WAAW7D,mBAC5BuB,KAAKG,SAAS,CAACiC,MAAM,MAAM;QAE/B,OAAO,IACL5B,KAAKP,IAAI,KAAK9B,gBAAgByF,KAAK,IACnCpD,KAAKP,IAAI,KAAK9B,gBAAgByC,QAAQ,EACtC;YACA,MAAM4D,YAAYhE,KAAKP,IAAI,KAAK9B,gBAAgByC,QAAQ;YACxD,MAAM6D,WAAW,IAAI,CAAC1C,WAAW,CAC/B,GAAGN,IAAI,KAAK,CAAC,EACb+C,YAAYpG,qBAAqBwC,QAAQ,GAAGxC,qBAAqBwF,KAAK;YAExE,MAAM,IAAI,CAACzE,EAAE,CAACkF,KAAK,CAAC/F,KAAKgG,OAAO,CAACG;YACjC,MAAM,IAAI,CAACtF,EAAE,CAACoF,SAAS,CAACE,UAAUjE,KAAKG,IAAI;YAE3C,wCAAwC;YACxC,IAAI,CAACiB,YAAY;gBACf,MAAM,IAAI,CAACzC,EAAE,CAACoF,SAAS,CACrB,IAAI,CAACxC,WAAW,CACd,GAAGN,MACD+C,YACItF,IAAIyC,iBAAiB,GACnBjD,sBACAG,aACFL,kBACJ,EACFgG,YACIpG,qBAAqBwC,QAAQ,GAC7BxC,qBAAqBwF,KAAK,GAEhCY,YAAYhE,KAAKK,OAAO,GAAGb,KAAKG,SAAS,CAACK,KAAKM,QAAQ;YAE3D;YAEA,IAAIN,CAAAA,wBAAAA,KAAMP,IAAI,MAAK9B,gBAAgByC,QAAQ,EAAE;gBAC3C,MAAMwB,OAAsB;oBAC1BM,SAASlC,KAAKkC,OAAO;oBACrBC,QAAQnC,KAAKmC,MAAM;oBACnBgB,WAAWnD,KAAKmD,SAAS;oBACzBT,cAAckB;gBAChB;gBAEA,MAAM,IAAI,CAACjF,EAAE,CAACoF,SAAS,CACrBE,SAASnC,OAAO,CAAC,WAAW7D,mBAC5BuB,KAAKG,SAAS,CAACiC;YAEnB;QACF,OAAO,IAAI5B,KAAKP,IAAI,KAAK9B,gBAAgBoC,KAAK,EAAE;YAC9C,MAAMuB,WAAW,IAAI,CAACC,WAAW,CAACN,KAAKrD,qBAAqBmC,KAAK;YACjE,MAAM,IAAI,CAACpB,EAAE,CAACkF,KAAK,CAAC/F,KAAKgG,OAAO,CAACxC;YACjC,MAAM,IAAI,CAAC3C,EAAE,CAACoF,SAAS,CACrBzC,UACA9B,KAAKG,SAAS,CAAC;gBACb,GAAGK,IAAI;gBACPU,MAAMhC,IAAIgC,IAAI;YAChB;QAEJ;IACF;IAEQa,YAAY2C,QAAgB,EAAEzE,IAA0B,EAAU;QACxE,OAAQA;YACN,KAAK7B,qBAAqBmC,KAAK;gBAC7B,6DAA6D;gBAC7D,iBAAiB;gBACjB,OAAOjC,KAAKqG,IAAI,CACd,IAAI,CAACtF,aAAa,EAClB,MACA,SACA,eACAqF;YAEJ,KAAKtG,qBAAqBwF,KAAK;gBAC7B,OAAOtF,KAAKqG,IAAI,CAAC,IAAI,CAACtF,aAAa,EAAE,SAASqF;YAChD,KAAKtG,qBAAqBiC,KAAK;YAC/B,KAAKjC,qBAAqBwC,QAAQ;YAClC,KAAKxC,qBAAqBqC,SAAS;gBACjC,OAAOnC,KAAKqG,IAAI,CAAC,IAAI,CAACtF,aAAa,EAAE,OAAOqF;YAC9C;gBACE,MAAM,IAAIpE,MAAM,CAAC,2BAA2B,EAAEL,MAAM;QACxD;IACF;AACF"}