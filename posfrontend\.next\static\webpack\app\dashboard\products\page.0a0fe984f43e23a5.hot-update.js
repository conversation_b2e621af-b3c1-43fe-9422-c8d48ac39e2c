"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/products/page",{

/***/ "(app-pages-browser)/./src/components/Products/ProductTable.tsx":
/*!**************************************************!*\
  !*** ./src/components/Products/ProductTable.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/notification/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/checkbox/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteFilled.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarcodeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CalendarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ResponsiveTable */ \"(app-pages-browser)/./src/components/ui/ResponsiveTable.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ProductTable = (param)=>{\n    let { products, loading, onView, onEdit, onDelete, onBulkDelete, onAdjustStock, isMobile = false } = param;\n    _s();\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__.useSelector)({\n        \"ProductTable.useSelector[user]\": (state)=>state.auth.user\n    }[\"ProductTable.useSelector[user]\"]);\n    const userRole = user === null || user === void 0 ? void 0 : user.role;\n    // State for selected products\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectAll, setSelectAll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle select all checkbox change\n    const handleSelectAllChange = (e)=>{\n        const checked = e.target.checked;\n        setSelectAll(checked);\n        if (checked) {\n            // Select all products that the user can delete\n            const selectableProductIds = products.filter((product)=>canEditDelete(product)).map((product)=>product.id);\n            setSelectedProducts(selectableProductIds);\n        } else {\n            // Deselect all products\n            setSelectedProducts([]);\n        }\n    };\n    // Handle individual checkbox change\n    const handleCheckboxChange = (productId, checked)=>{\n        if (checked) {\n            setSelectedProducts((prev)=>[\n                    ...prev,\n                    productId\n                ]);\n        } else {\n            setSelectedProducts((prev)=>prev.filter((id)=>id !== productId));\n        }\n    };\n    // Handle bulk delete\n    const handleBulkDelete = ()=>{\n        if (selectedProducts.length > 0 && onBulkDelete) {\n            onBulkDelete(selectedProducts);\n            setSelectedProducts([]);\n            setSelectAll(false);\n        } else {\n            _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].warning({\n                message: 'No products selected',\n                description: 'Please select at least one product to delete.'\n            });\n        }\n    };\n    // Format date for display\n    const formatDate = (dateString)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_3___default()(dateString).format(\"MMM D, YYYY\");\n    };\n    // Format currency for display\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-GH', {\n            style: 'currency',\n            currency: 'GHS'\n        }).format(parseFloat(amount));\n    };\n    // Check if user can edit/delete (superadmin can edit all, others only their own)\n    const canEditDelete = (product)=>{\n        if (userRole === \"superadmin\") return true;\n        if (userRole === \"admin\" && (user === null || user === void 0 ? void 0 : user.id) === product.createdBy) return true;\n        return false;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-hidden bg-white\",\n        children: [\n            selectedProducts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 bg-gray-100 border-b flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: [\n                            selectedProducts.length,\n                            \" \",\n                            selectedProducts.length === 1 ? 'product' : 'products',\n                            \" selected\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        type: \"primary\",\n                        danger: true,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: handleBulkDelete,\n                        className: \"ml-2\",\n                        children: \"Delete Selected\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.ResponsiveTableGrid, {\n                columns: isMobile ? \"50px 200px 100px 80px 120px 150px\" : \"50px 200px 100px 80px 100px 120px 150px\",\n                minWidth: isMobile ? \"800px\" : \"1000px\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            checked: selectAll,\n                            onChange: handleSelectAllChange,\n                            disabled: products.filter((product)=>canEditDelete(product)).length === 0\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        sticky: isMobile ? undefined : \"left\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Name\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Price\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Stock\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, undefined),\n                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"SKU\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Created At\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        sticky: isMobile ? undefined : \"right\",\n                        className: \"text-right\",\n                        children: \"Actions\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined),\n                    products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableRow, {\n                            selected: selectedProducts.includes(product.id),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    className: \"text-center\",\n                                    children: canEditDelete(product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        checked: selectedProducts.includes(product.id),\n                                        onChange: (e)=>handleCheckboxChange(product.id, e.target.checked)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    sticky: isMobile ? undefined : \"left\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-[180px] overflow-hidden text-ellipsis font-medium\",\n                                        children: product.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-green-600\",\n                                        children: formatCurrency(product.price)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full \".concat(product.stockQuantity <= 0 ? 'bg-red-500 text-white' : product.stockQuantity <= (product.minStockLevel || 5) ? 'bg-yellow-500 text-white' : 'bg-green-500 text-white'),\n                                        children: product.stockQuantity\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, undefined),\n                                !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-xs\",\n                                        children: product.sku || 'N/A'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: formatDate(product.createdAt)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    sticky: isMobile ? undefined : \"right\",\n                                    className: \"text-right\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                title: \"View\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    onClick: ()=>onView(product.id),\n                                                    type: \"text\",\n                                                    className: \"view-button text-green-500 hover:text-green-400\",\n                                                    size: isMobile ? \"small\" : \"middle\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            userRole === \"admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                title: \"Adjust Stock\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    onClick: ()=>onAdjustStock(product),\n                                                    type: \"text\",\n                                                    className: \"adjust-stock-button text-purple-500 hover:text-purple-400\",\n                                                    size: isMobile ? \"small\" : \"middle\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            canEditDelete(product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        title: \"Edit\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            onClick: ()=>onEdit(product),\n                                                            type: \"text\",\n                                                            className: \"edit-button text-blue-500 hover:text-blue-400\",\n                                                            size: isMobile ? \"small\" : \"middle\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        title: \"Delete\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            onClick: ()=>onDelete(product.id),\n                                                            type: \"text\",\n                                                            className: \"delete-button text-red-500 hover:text-red-400\",\n                                                            size: isMobile ? \"small\" : \"middle\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, product.id, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductTable, \"QVOA1Pi+5rHhJmGQeX8BO2tvPjo=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_4__.useSelector\n    ];\n});\n_c = ProductTable;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductTable);\nvar _c;\n$RefreshReg$(_c, \"ProductTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Products/ProductTable.tsx\n"));

/***/ })

});