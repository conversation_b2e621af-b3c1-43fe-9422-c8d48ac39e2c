{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/fetch-cache.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ache<PERSON><PERSON>ler<PERSON>ontext, CacheHandlerValue } from './'\nimport {\n  CachedRout<PERSON>Kind,\n  IncrementalCacheKind,\n  type IncrementalCacheValue,\n} from '../../response-cache'\n\nimport { LRUCache } from '../lru-cache'\nimport {\n  CACHE_ONE_YEAR,\n  NEXT_CACHE_SOFT_TAGS_HEADER,\n} from '../../../lib/constants'\n\nlet rateLimitedUntil = 0\nlet memoryCache: LRUCache<CacheHandlerValue> | undefined\n\ninterface NextFetchCacheParams {\n  internal?: boolean\n  fetchType?: string\n  fetchIdx?: number\n  fetchUrl?: string\n}\n\nconst CACHE_TAGS_HEADER = 'x-vercel-cache-tags' as const\nconst CACHE_HEADERS_HEADER = 'x-vercel-sc-headers' as const\nconst CACHE_STATE_HEADER = 'x-vercel-cache-state' as const\nconst CACHE_REVALIDATE_HEADER = 'x-vercel-revalidate' as const\nconst CACHE_FETCH_URL_HEADER = 'x-vercel-cache-item-name' as const\nconst CACHE_CONTROL_VALUE_HEADER = 'x-vercel-cache-control' as const\n\nconst DEBUG = Boolean(process.env.NEXT_PRIVATE_DEBUG_CACHE)\n\nasync function fetchRetryWithTimeout(\n  url: Parameters<typeof fetch>[0],\n  init: Parameters<typeof fetch>[1],\n  retryIndex = 0\n): Promise<Response> {\n  const controller = new AbortController()\n  const timeout = setTimeout(() => {\n    controller.abort()\n  }, 500)\n\n  return fetch(url, {\n    ...(init || {}),\n    signal: controller.signal,\n  })\n    .catch((err) => {\n      if (retryIndex === 3) {\n        throw err\n      } else {\n        if (DEBUG) {\n          console.log(`Fetch failed for ${url} retry ${retryIndex}`)\n        }\n        return fetchRetryWithTimeout(url, init, retryIndex + 1)\n      }\n    })\n    .finally(() => {\n      clearTimeout(timeout)\n    })\n}\n\nexport default class FetchCache implements CacheHandler {\n  private headers: Record<string, string>\n  private cacheEndpoint?: string\n\n  private hasMatchingTags(arr1: string[], arr2: string[]) {\n    if (arr1.length !== arr2.length) return false\n\n    const set1 = new Set(arr1)\n    const set2 = new Set(arr2)\n\n    if (set1.size !== set2.size) return false\n\n    for (let tag of set1) {\n      if (!set2.has(tag)) return false\n    }\n\n    return true\n  }\n\n  static isAvailable(ctx: {\n    _requestHeaders: CacheHandlerContext['_requestHeaders']\n  }) {\n    return !!(\n      ctx._requestHeaders['x-vercel-sc-host'] || process.env.SUSPENSE_CACHE_URL\n    )\n  }\n\n  constructor(ctx: CacheHandlerContext) {\n    this.headers = {}\n    this.headers['Content-Type'] = 'application/json'\n\n    if (CACHE_HEADERS_HEADER in ctx._requestHeaders) {\n      const newHeaders = JSON.parse(\n        ctx._requestHeaders[CACHE_HEADERS_HEADER] as string\n      )\n      for (const k in newHeaders) {\n        this.headers[k] = newHeaders[k]\n      }\n      delete ctx._requestHeaders[CACHE_HEADERS_HEADER]\n    }\n    const scHost =\n      ctx._requestHeaders['x-vercel-sc-host'] || process.env.SUSPENSE_CACHE_URL\n\n    const scBasePath =\n      ctx._requestHeaders['x-vercel-sc-basepath'] ||\n      process.env.SUSPENSE_CACHE_BASEPATH\n\n    if (process.env.SUSPENSE_CACHE_AUTH_TOKEN) {\n      this.headers['Authorization'] =\n        `Bearer ${process.env.SUSPENSE_CACHE_AUTH_TOKEN}`\n    }\n\n    if (scHost) {\n      const scProto = process.env.SUSPENSE_CACHE_PROTO || 'https'\n      this.cacheEndpoint = `${scProto}://${scHost}${scBasePath || ''}`\n      if (DEBUG) {\n        console.log('using cache endpoint', this.cacheEndpoint)\n      }\n    } else if (DEBUG) {\n      console.log('no cache endpoint available')\n    }\n\n    if (ctx.maxMemoryCacheSize) {\n      if (!memoryCache) {\n        if (DEBUG) {\n          console.log('using memory store for fetch cache')\n        }\n\n        memoryCache = new LRUCache(ctx.maxMemoryCacheSize, function length({\n          value,\n        }) {\n          if (!value) {\n            return 25\n          } else if (value.kind === CachedRouteKind.REDIRECT) {\n            return JSON.stringify(value.props).length\n          } else if (value.kind === CachedRouteKind.IMAGE) {\n            throw new Error('invariant image should not be incremental-cache')\n          } else if (value.kind === CachedRouteKind.FETCH) {\n            return JSON.stringify(value.data || '').length\n          } else if (value.kind === CachedRouteKind.APP_ROUTE) {\n            return value.body.length\n          }\n          // rough estimate of size of cache value\n          return (\n            value.html.length +\n            (JSON.stringify(\n              value.kind === CachedRouteKind.APP_PAGE\n                ? value.rscData\n                : value.pageData\n            )?.length || 0)\n          )\n        })\n      }\n    } else {\n      if (DEBUG) {\n        console.log('not using memory store for fetch cache')\n      }\n    }\n  }\n\n  public resetRequestCache(): void {\n    memoryCache?.reset()\n  }\n\n  public async revalidateTag(\n    ...args: Parameters<CacheHandler['revalidateTag']>\n  ) {\n    let [tags] = args\n    tags = typeof tags === 'string' ? [tags] : tags\n    if (DEBUG) {\n      console.log('revalidateTag', tags)\n    }\n\n    if (!tags.length) return\n\n    if (Date.now() < rateLimitedUntil) {\n      if (DEBUG) {\n        console.log('rate limited ', rateLimitedUntil)\n      }\n      return\n    }\n\n    for (let i = 0; i < Math.ceil(tags.length / 64); i++) {\n      const currentTags = tags.slice(i * 64, i * 64 + 64)\n      try {\n        const res = await fetchRetryWithTimeout(\n          `${this.cacheEndpoint}/v1/suspense-cache/revalidate?tags=${currentTags\n            .map((tag) => encodeURIComponent(tag))\n            .join(',')}`,\n          {\n            method: 'POST',\n            headers: this.headers,\n            // @ts-expect-error not on public type\n            next: { internal: true },\n          }\n        )\n\n        if (res.status === 429) {\n          const retryAfter = res.headers.get('retry-after') || '60000'\n          rateLimitedUntil = Date.now() + parseInt(retryAfter)\n        }\n\n        if (!res.ok) {\n          throw new Error(`Request failed with status ${res.status}.`)\n        }\n      } catch (err) {\n        console.warn(`Failed to revalidate tag`, currentTags, err)\n      }\n    }\n  }\n\n  public async get(...args: Parameters<CacheHandler['get']>) {\n    const [key, ctx] = args\n    const { tags, softTags, kind: kindHint, fetchIdx, fetchUrl } = ctx\n\n    if (kindHint !== IncrementalCacheKind.FETCH) {\n      return null\n    }\n\n    if (Date.now() < rateLimitedUntil) {\n      if (DEBUG) {\n        console.log('rate limited')\n      }\n      return null\n    }\n\n    // memory cache is cleared at the end of each request\n    // so that revalidate events are pulled from upstream\n    // on successive requests\n    let data = memoryCache?.get(key)\n\n    const hasFetchKindAndMatchingTags =\n      data?.value?.kind === CachedRouteKind.FETCH &&\n      this.hasMatchingTags(tags ?? [], data.value.tags ?? [])\n\n    // Get data from fetch cache. Also check if new tags have been\n    // specified with the same cache key (fetch URL)\n    if (this.cacheEndpoint && (!data || !hasFetchKindAndMatchingTags)) {\n      try {\n        const start = Date.now()\n        const fetchParams: NextFetchCacheParams = {\n          internal: true,\n          fetchType: 'cache-get',\n          fetchUrl: fetchUrl,\n          fetchIdx,\n        }\n        const res = await fetch(\n          `${this.cacheEndpoint}/v1/suspense-cache/${key}`,\n          {\n            method: 'GET',\n            headers: {\n              ...this.headers,\n              [CACHE_FETCH_URL_HEADER]: fetchUrl,\n              [CACHE_TAGS_HEADER]: tags?.join(',') || '',\n              [NEXT_CACHE_SOFT_TAGS_HEADER]: softTags?.join(',') || '',\n            } as any,\n            next: fetchParams as NextFetchRequestConfig,\n          }\n        )\n\n        if (res.status === 429) {\n          const retryAfter = res.headers.get('retry-after') || '60000'\n          rateLimitedUntil = Date.now() + parseInt(retryAfter)\n        }\n\n        if (res.status === 404) {\n          if (DEBUG) {\n            console.log(\n              `no fetch cache entry for ${key}, duration: ${\n                Date.now() - start\n              }ms`\n            )\n          }\n          return null\n        }\n\n        if (!res.ok) {\n          console.error(await res.text())\n          throw new Error(`invalid response from cache ${res.status}`)\n        }\n\n        const cached: IncrementalCacheValue = await res.json()\n\n        if (!cached || cached.kind !== CachedRouteKind.FETCH) {\n          DEBUG && console.log({ cached })\n          throw new Error('invalid cache value')\n        }\n\n        // if new tags were specified, merge those tags to the existing tags\n        if (cached.kind === CachedRouteKind.FETCH) {\n          cached.tags ??= []\n          for (const tag of tags ?? []) {\n            if (!cached.tags.includes(tag)) {\n              cached.tags.push(tag)\n            }\n          }\n        }\n\n        const cacheState = res.headers.get(CACHE_STATE_HEADER)\n        const age = res.headers.get('age')\n\n        data = {\n          value: cached,\n          // if it's already stale set it to a time in the past\n          // if not derive last modified from age\n          lastModified:\n            cacheState !== 'fresh'\n              ? Date.now() - CACHE_ONE_YEAR\n              : Date.now() - parseInt(age || '0', 10) * 1000,\n        }\n\n        if (DEBUG) {\n          console.log(\n            `got fetch cache entry for ${key}, duration: ${\n              Date.now() - start\n            }ms, size: ${\n              Object.keys(cached).length\n            }, cache-state: ${cacheState} tags: ${tags?.join(\n              ','\n            )} softTags: ${softTags?.join(',')}`\n          )\n        }\n\n        if (data) {\n          memoryCache?.set(key, data)\n        }\n      } catch (err) {\n        // unable to get data from fetch-cache\n        if (DEBUG) {\n          console.error(`Failed to get from fetch-cache`, err)\n        }\n      }\n    }\n\n    return data || null\n  }\n\n  public async set(...args: Parameters<CacheHandler['set']>) {\n    const [key, data, ctx] = args\n\n    const { fetchCache, fetchIdx, fetchUrl, tags } = ctx\n    if (!fetchCache) return\n\n    if (Date.now() < rateLimitedUntil) {\n      if (DEBUG) {\n        console.log('rate limited')\n      }\n      return\n    }\n\n    memoryCache?.set(key, {\n      value: data,\n      lastModified: Date.now(),\n    })\n\n    if (this.cacheEndpoint) {\n      try {\n        const start = Date.now()\n        if (data !== null && 'revalidate' in data) {\n          this.headers[CACHE_REVALIDATE_HEADER] = data.revalidate.toString()\n        }\n        if (\n          !this.headers[CACHE_REVALIDATE_HEADER] &&\n          data !== null &&\n          'data' in data\n        ) {\n          this.headers[CACHE_CONTROL_VALUE_HEADER] =\n            data.data.headers['cache-control']\n        }\n        const body = JSON.stringify({\n          ...data,\n          // we send the tags in the header instead\n          // of in the body here\n          tags: undefined,\n        })\n\n        if (DEBUG) {\n          console.log('set cache', key)\n        }\n        const fetchParams: NextFetchCacheParams = {\n          internal: true,\n          fetchType: 'cache-set',\n          fetchUrl,\n          fetchIdx,\n        }\n        const res = await fetch(\n          `${this.cacheEndpoint}/v1/suspense-cache/${key}`,\n          {\n            method: 'POST',\n            headers: {\n              ...this.headers,\n              [CACHE_FETCH_URL_HEADER]: fetchUrl || '',\n              [CACHE_TAGS_HEADER]: tags?.join(',') || '',\n            },\n            body: body,\n            next: fetchParams as NextFetchRequestConfig,\n          }\n        )\n\n        if (res.status === 429) {\n          const retryAfter = res.headers.get('retry-after') || '60000'\n          rateLimitedUntil = Date.now() + parseInt(retryAfter)\n        }\n\n        if (!res.ok) {\n          DEBUG && console.log(await res.text())\n          throw new Error(`invalid response ${res.status}`)\n        }\n\n        if (DEBUG) {\n          console.log(\n            `successfully set to fetch-cache for ${key}, duration: ${\n              Date.now() - start\n            }ms, size: ${body.length}`\n          )\n        }\n      } catch (err) {\n        // unable to set to fetch-cache\n        if (DEBUG) {\n          console.error(`Failed to update fetch cache`, err)\n        }\n      }\n    }\n    return\n  }\n}\n"], "names": ["CachedRouteKind", "IncrementalCacheKind", "L<PERSON><PERSON><PERSON>", "CACHE_ONE_YEAR", "NEXT_CACHE_SOFT_TAGS_HEADER", "rateLimitedUntil", "memoryCache", "CACHE_TAGS_HEADER", "CACHE_HEADERS_HEADER", "CACHE_STATE_HEADER", "CACHE_REVALIDATE_HEADER", "CACHE_FETCH_URL_HEADER", "CACHE_CONTROL_VALUE_HEADER", "DEBUG", "Boolean", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "fetchRetryWithTimeout", "url", "init", "retryIndex", "controller", "AbortController", "timeout", "setTimeout", "abort", "fetch", "signal", "catch", "err", "console", "log", "finally", "clearTimeout", "<PERSON><PERSON><PERSON><PERSON>", "hasMatchingTags", "arr1", "arr2", "length", "set1", "Set", "set2", "size", "tag", "has", "isAvailable", "ctx", "_requestHeaders", "SUSPENSE_CACHE_URL", "constructor", "headers", "newHeaders", "JSON", "parse", "k", "scHost", "sc<PERSON><PERSON><PERSON><PERSON>", "SUSPENSE_CACHE_BASEPATH", "SUSPENSE_CACHE_AUTH_TOKEN", "scProto", "SUSPENSE_CACHE_PROTO", "cacheEndpoint", "maxMemoryCacheSize", "value", "kind", "REDIRECT", "stringify", "props", "IMAGE", "Error", "FETCH", "data", "APP_ROUTE", "body", "html", "APP_PAGE", "rscData", "pageData", "resetRequestCache", "reset", "revalidateTag", "args", "tags", "Date", "now", "i", "Math", "ceil", "currentTags", "slice", "res", "map", "encodeURIComponent", "join", "method", "next", "internal", "status", "retryAfter", "get", "parseInt", "ok", "warn", "key", "softTags", "kindHint", "fetchIdx", "fetchUrl", "hasFetchKindAndMatchingTags", "start", "fetchParams", "fetchType", "error", "text", "cached", "json", "includes", "push", "cacheState", "age", "lastModified", "Object", "keys", "set", "fetchCache", "revalidate", "toString", "undefined"], "mappings": "AACA,SACEA,eAAe,EACfC,oBAAoB,QAEf,uBAAsB;AAE7B,SAASC,QAAQ,QAAQ,eAAc;AACvC,SACEC,cAAc,EACdC,2BAA2B,QACtB,yBAAwB;AAE/B,IAAIC,mBAAmB;AACvB,IAAIC;AASJ,MAAMC,oBAAoB;AAC1B,MAAMC,uBAAuB;AAC7B,MAAMC,qBAAqB;AAC3B,MAAMC,0BAA0B;AAChC,MAAMC,yBAAyB;AAC/B,MAAMC,6BAA6B;AAEnC,MAAMC,QAAQC,QAAQC,QAAQC,GAAG,CAACC,wBAAwB;AAE1D,eAAeC,sBACbC,GAAgC,EAChCC,IAAiC,EACjCC,aAAa,CAAC;IAEd,MAAMC,aAAa,IAAIC;IACvB,MAAMC,UAAUC,WAAW;QACzBH,WAAWI,KAAK;IAClB,GAAG;IAEH,OAAOC,MAAMR,KAAK;QAChB,GAAIC,QAAQ,CAAC,CAAC;QACdQ,QAAQN,WAAWM,MAAM;IAC3B,GACGC,KAAK,CAAC,CAACC;QACN,IAAIT,eAAe,GAAG;YACpB,MAAMS;QACR,OAAO;YACL,IAAIjB,OAAO;gBACTkB,QAAQC,GAAG,CAAC,CAAC,iBAAiB,EAAEb,IAAI,OAAO,EAAEE,YAAY;YAC3D;YACA,OAAOH,sBAAsBC,KAAKC,MAAMC,aAAa;QACvD;IACF,GACCY,OAAO,CAAC;QACPC,aAAaV;IACf;AACJ;AAEA,eAAe,MAAMW;IAIXC,gBAAgBC,IAAc,EAAEC,IAAc,EAAE;QACtD,IAAID,KAAKE,MAAM,KAAKD,KAAKC,MAAM,EAAE,OAAO;QAExC,MAAMC,OAAO,IAAIC,IAAIJ;QACrB,MAAMK,OAAO,IAAID,IAAIH;QAErB,IAAIE,KAAKG,IAAI,KAAKD,KAAKC,IAAI,EAAE,OAAO;QAEpC,KAAK,IAAIC,OAAOJ,KAAM;YACpB,IAAI,CAACE,KAAKG,GAAG,CAACD,MAAM,OAAO;QAC7B;QAEA,OAAO;IACT;IAEA,OAAOE,YAAYC,GAElB,EAAE;QACD,OAAO,CAAC,CACNA,CAAAA,IAAIC,eAAe,CAAC,mBAAmB,IAAIjC,QAAQC,GAAG,CAACiC,kBAAkB,AAAD;IAE5E;IAEAC,YAAYH,GAAwB,CAAE;QACpC,IAAI,CAACI,OAAO,GAAG,CAAC;QAChB,IAAI,CAACA,OAAO,CAAC,eAAe,GAAG;QAE/B,IAAI3C,wBAAwBuC,IAAIC,eAAe,EAAE;YAC/C,MAAMI,aAAaC,KAAKC,KAAK,CAC3BP,IAAIC,eAAe,CAACxC,qBAAqB;YAE3C,IAAK,MAAM+C,KAAKH,WAAY;gBAC1B,IAAI,CAACD,OAAO,CAACI,EAAE,GAAGH,UAAU,CAACG,EAAE;YACjC;YACA,OAAOR,IAAIC,eAAe,CAACxC,qBAAqB;QAClD;QACA,MAAMgD,SACJT,IAAIC,eAAe,CAAC,mBAAmB,IAAIjC,QAAQC,GAAG,CAACiC,kBAAkB;QAE3E,MAAMQ,aACJV,IAAIC,eAAe,CAAC,uBAAuB,IAC3CjC,QAAQC,GAAG,CAAC0C,uBAAuB;QAErC,IAAI3C,QAAQC,GAAG,CAAC2C,yBAAyB,EAAE;YACzC,IAAI,CAACR,OAAO,CAAC,gBAAgB,GAC3B,CAAC,OAAO,EAAEpC,QAAQC,GAAG,CAAC2C,yBAAyB,EAAE;QACrD;QAEA,IAAIH,QAAQ;YACV,MAAMI,UAAU7C,QAAQC,GAAG,CAAC6C,oBAAoB,IAAI;YACpD,IAAI,CAACC,aAAa,GAAG,GAAGF,QAAQ,GAAG,EAAEJ,SAASC,cAAc,IAAI;YAChE,IAAI5C,OAAO;gBACTkB,QAAQC,GAAG,CAAC,wBAAwB,IAAI,CAAC8B,aAAa;YACxD;QACF,OAAO,IAAIjD,OAAO;YAChBkB,QAAQC,GAAG,CAAC;QACd;QAEA,IAAIe,IAAIgB,kBAAkB,EAAE;YAC1B,IAAI,CAACzD,aAAa;gBAChB,IAAIO,OAAO;oBACTkB,QAAQC,GAAG,CAAC;gBACd;gBAEA1B,cAAc,IAAIJ,SAAS6C,IAAIgB,kBAAkB,EAAE,SAASxB,OAAO,EACjEyB,KAAK,EACN;wBAeIX;oBAdH,IAAI,CAACW,OAAO;wBACV,OAAO;oBACT,OAAO,IAAIA,MAAMC,IAAI,KAAKjE,gBAAgBkE,QAAQ,EAAE;wBAClD,OAAOb,KAAKc,SAAS,CAACH,MAAMI,KAAK,EAAE7B,MAAM;oBAC3C,OAAO,IAAIyB,MAAMC,IAAI,KAAKjE,gBAAgBqE,KAAK,EAAE;wBAC/C,MAAM,IAAIC,MAAM;oBAClB,OAAO,IAAIN,MAAMC,IAAI,KAAKjE,gBAAgBuE,KAAK,EAAE;wBAC/C,OAAOlB,KAAKc,SAAS,CAACH,MAAMQ,IAAI,IAAI,IAAIjC,MAAM;oBAChD,OAAO,IAAIyB,MAAMC,IAAI,KAAKjE,gBAAgByE,SAAS,EAAE;wBACnD,OAAOT,MAAMU,IAAI,CAACnC,MAAM;oBAC1B;oBACA,wCAAwC;oBACxC,OACEyB,MAAMW,IAAI,CAACpC,MAAM,GAChBc,CAAAA,EAAAA,kBAAAA,KAAKc,SAAS,CACbH,MAAMC,IAAI,KAAKjE,gBAAgB4E,QAAQ,GACnCZ,MAAMa,OAAO,GACbb,MAAMc,QAAQ,sBAHnBzB,gBAIEd,MAAM,KAAI,CAAA;gBAEjB;YACF;QACF,OAAO;YACL,IAAI1B,OAAO;gBACTkB,QAAQC,GAAG,CAAC;YACd;QACF;IACF;IAEO+C,oBAA0B;QAC/BzE,+BAAAA,YAAa0E,KAAK;IACpB;IAEA,MAAaC,cACX,GAAGC,IAA+C,EAClD;QACA,IAAI,CAACC,KAAK,GAAGD;QACbC,OAAO,OAAOA,SAAS,WAAW;YAACA;SAAK,GAAGA;QAC3C,IAAItE,OAAO;YACTkB,QAAQC,GAAG,CAAC,iBAAiBmD;QAC/B;QAEA,IAAI,CAACA,KAAK5C,MAAM,EAAE;QAElB,IAAI6C,KAAKC,GAAG,KAAKhF,kBAAkB;YACjC,IAAIQ,OAAO;gBACTkB,QAAQC,GAAG,CAAC,iBAAiB3B;YAC/B;YACA;QACF;QAEA,IAAK,IAAIiF,IAAI,GAAGA,IAAIC,KAAKC,IAAI,CAACL,KAAK5C,MAAM,GAAG,KAAK+C,IAAK;YACpD,MAAMG,cAAcN,KAAKO,KAAK,CAACJ,IAAI,IAAIA,IAAI,KAAK;YAChD,IAAI;gBACF,MAAMK,MAAM,MAAMzE,sBAChB,GAAG,IAAI,CAAC4C,aAAa,CAAC,mCAAmC,EAAE2B,YACxDG,GAAG,CAAC,CAAChD,MAAQiD,mBAAmBjD,MAChCkD,IAAI,CAAC,MAAM,EACd;oBACEC,QAAQ;oBACR5C,SAAS,IAAI,CAACA,OAAO;oBACrB,sCAAsC;oBACtC6C,MAAM;wBAAEC,UAAU;oBAAK;gBACzB;gBAGF,IAAIN,IAAIO,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaR,IAAIxC,OAAO,CAACiD,GAAG,CAAC,kBAAkB;oBACrD/F,mBAAmB+E,KAAKC,GAAG,KAAKgB,SAASF;gBAC3C;gBAEA,IAAI,CAACR,IAAIW,EAAE,EAAE;oBACX,MAAM,IAAIhC,MAAM,CAAC,2BAA2B,EAAEqB,IAAIO,MAAM,CAAC,CAAC,CAAC;gBAC7D;YACF,EAAE,OAAOpE,KAAK;gBACZC,QAAQwE,IAAI,CAAC,CAAC,wBAAwB,CAAC,EAAEd,aAAa3D;YACxD;QACF;IACF;IAEA,MAAasE,IAAI,GAAGlB,IAAqC,EAAE;YAqBvDV;QApBF,MAAM,CAACgC,KAAKzD,IAAI,GAAGmC;QACnB,MAAM,EAAEC,IAAI,EAAEsB,QAAQ,EAAExC,MAAMyC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAE,GAAG7D;QAE/D,IAAI2D,aAAazG,qBAAqBsE,KAAK,EAAE;YAC3C,OAAO;QACT;QAEA,IAAIa,KAAKC,GAAG,KAAKhF,kBAAkB;YACjC,IAAIQ,OAAO;gBACTkB,QAAQC,GAAG,CAAC;YACd;YACA,OAAO;QACT;QAEA,qDAAqD;QACrD,qDAAqD;QACrD,yBAAyB;QACzB,IAAIwC,OAAOlE,+BAAAA,YAAa8F,GAAG,CAACI;QAE5B,MAAMK,8BACJrC,CAAAA,yBAAAA,cAAAA,KAAMR,KAAK,qBAAXQ,YAAaP,IAAI,MAAKjE,gBAAgBuE,KAAK,IAC3C,IAAI,CAACnC,eAAe,CAAC+C,QAAQ,EAAE,EAAEX,KAAKR,KAAK,CAACmB,IAAI,IAAI,EAAE;QAExD,8DAA8D;QAC9D,gDAAgD;QAChD,IAAI,IAAI,CAACrB,aAAa,IAAK,CAAA,CAACU,QAAQ,CAACqC,2BAA0B,GAAI;YACjE,IAAI;gBACF,MAAMC,QAAQ1B,KAAKC,GAAG;gBACtB,MAAM0B,cAAoC;oBACxCd,UAAU;oBACVe,WAAW;oBACXJ,UAAUA;oBACVD;gBACF;gBACA,MAAMhB,MAAM,MAAMhE,MAChB,GAAG,IAAI,CAACmC,aAAa,CAAC,mBAAmB,EAAE0C,KAAK,EAChD;oBACET,QAAQ;oBACR5C,SAAS;wBACP,GAAG,IAAI,CAACA,OAAO;wBACf,CAACxC,uBAAuB,EAAEiG;wBAC1B,CAACrG,kBAAkB,EAAE4E,CAAAA,wBAAAA,KAAMW,IAAI,CAAC,SAAQ;wBACxC,CAAC1F,4BAA4B,EAAEqG,CAAAA,4BAAAA,SAAUX,IAAI,CAAC,SAAQ;oBACxD;oBACAE,MAAMe;gBACR;gBAGF,IAAIpB,IAAIO,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaR,IAAIxC,OAAO,CAACiD,GAAG,CAAC,kBAAkB;oBACrD/F,mBAAmB+E,KAAKC,GAAG,KAAKgB,SAASF;gBAC3C;gBAEA,IAAIR,IAAIO,MAAM,KAAK,KAAK;oBACtB,IAAIrF,OAAO;wBACTkB,QAAQC,GAAG,CACT,CAAC,yBAAyB,EAAEwE,IAAI,YAAY,EAC1CpB,KAAKC,GAAG,KAAKyB,MACd,EAAE,CAAC;oBAER;oBACA,OAAO;gBACT;gBAEA,IAAI,CAACnB,IAAIW,EAAE,EAAE;oBACXvE,QAAQkF,KAAK,CAAC,MAAMtB,IAAIuB,IAAI;oBAC5B,MAAM,IAAI5C,MAAM,CAAC,4BAA4B,EAAEqB,IAAIO,MAAM,EAAE;gBAC7D;gBAEA,MAAMiB,SAAgC,MAAMxB,IAAIyB,IAAI;gBAEpD,IAAI,CAACD,UAAUA,OAAOlD,IAAI,KAAKjE,gBAAgBuE,KAAK,EAAE;oBACpD1D,SAASkB,QAAQC,GAAG,CAAC;wBAAEmF;oBAAO;oBAC9B,MAAM,IAAI7C,MAAM;gBAClB;gBAEA,oEAAoE;gBACpE,IAAI6C,OAAOlD,IAAI,KAAKjE,gBAAgBuE,KAAK,EAAE;oBACzC4C,OAAOhC,IAAI,KAAK,EAAE;oBAClB,KAAK,MAAMvC,OAAOuC,QAAQ,EAAE,CAAE;wBAC5B,IAAI,CAACgC,OAAOhC,IAAI,CAACkC,QAAQ,CAACzE,MAAM;4BAC9BuE,OAAOhC,IAAI,CAACmC,IAAI,CAAC1E;wBACnB;oBACF;gBACF;gBAEA,MAAM2E,aAAa5B,IAAIxC,OAAO,CAACiD,GAAG,CAAC3F;gBACnC,MAAM+G,MAAM7B,IAAIxC,OAAO,CAACiD,GAAG,CAAC;gBAE5B5B,OAAO;oBACLR,OAAOmD;oBACP,qDAAqD;oBACrD,uCAAuC;oBACvCM,cACEF,eAAe,UACXnC,KAAKC,GAAG,KAAKlF,iBACbiF,KAAKC,GAAG,KAAKgB,SAASmB,OAAO,KAAK,MAAM;gBAChD;gBAEA,IAAI3G,OAAO;oBACTkB,QAAQC,GAAG,CACT,CAAC,0BAA0B,EAAEwE,IAAI,YAAY,EAC3CpB,KAAKC,GAAG,KAAKyB,MACd,UAAU,EACTY,OAAOC,IAAI,CAACR,QAAQ5E,MAAM,CAC3B,eAAe,EAAEgF,WAAW,OAAO,EAAEpC,wBAAAA,KAAMW,IAAI,CAC9C,KACA,WAAW,EAAEW,4BAAAA,SAAUX,IAAI,CAAC,MAAM;gBAExC;gBAEA,IAAItB,MAAM;oBACRlE,+BAAAA,YAAasH,GAAG,CAACpB,KAAKhC;gBACxB;YACF,EAAE,OAAO1C,KAAK;gBACZ,sCAAsC;gBACtC,IAAIjB,OAAO;oBACTkB,QAAQkF,KAAK,CAAC,CAAC,8BAA8B,CAAC,EAAEnF;gBAClD;YACF;QACF;QAEA,OAAO0C,QAAQ;IACjB;IAEA,MAAaoD,IAAI,GAAG1C,IAAqC,EAAE;QACzD,MAAM,CAACsB,KAAKhC,MAAMzB,IAAI,GAAGmC;QAEzB,MAAM,EAAE2C,UAAU,EAAElB,QAAQ,EAAEC,QAAQ,EAAEzB,IAAI,EAAE,GAAGpC;QACjD,IAAI,CAAC8E,YAAY;QAEjB,IAAIzC,KAAKC,GAAG,KAAKhF,kBAAkB;YACjC,IAAIQ,OAAO;gBACTkB,QAAQC,GAAG,CAAC;YACd;YACA;QACF;QAEA1B,+BAAAA,YAAasH,GAAG,CAACpB,KAAK;YACpBxC,OAAOQ;YACPiD,cAAcrC,KAAKC,GAAG;QACxB;QAEA,IAAI,IAAI,CAACvB,aAAa,EAAE;YACtB,IAAI;gBACF,MAAMgD,QAAQ1B,KAAKC,GAAG;gBACtB,IAAIb,SAAS,QAAQ,gBAAgBA,MAAM;oBACzC,IAAI,CAACrB,OAAO,CAACzC,wBAAwB,GAAG8D,KAAKsD,UAAU,CAACC,QAAQ;gBAClE;gBACA,IACE,CAAC,IAAI,CAAC5E,OAAO,CAACzC,wBAAwB,IACtC8D,SAAS,QACT,UAAUA,MACV;oBACA,IAAI,CAACrB,OAAO,CAACvC,2BAA2B,GACtC4D,KAAKA,IAAI,CAACrB,OAAO,CAAC,gBAAgB;gBACtC;gBACA,MAAMuB,OAAOrB,KAAKc,SAAS,CAAC;oBAC1B,GAAGK,IAAI;oBACP,yCAAyC;oBACzC,sBAAsB;oBACtBW,MAAM6C;gBACR;gBAEA,IAAInH,OAAO;oBACTkB,QAAQC,GAAG,CAAC,aAAawE;gBAC3B;gBACA,MAAMO,cAAoC;oBACxCd,UAAU;oBACVe,WAAW;oBACXJ;oBACAD;gBACF;gBACA,MAAMhB,MAAM,MAAMhE,MAChB,GAAG,IAAI,CAACmC,aAAa,CAAC,mBAAmB,EAAE0C,KAAK,EAChD;oBACET,QAAQ;oBACR5C,SAAS;wBACP,GAAG,IAAI,CAACA,OAAO;wBACf,CAACxC,uBAAuB,EAAEiG,YAAY;wBACtC,CAACrG,kBAAkB,EAAE4E,CAAAA,wBAAAA,KAAMW,IAAI,CAAC,SAAQ;oBAC1C;oBACApB,MAAMA;oBACNsB,MAAMe;gBACR;gBAGF,IAAIpB,IAAIO,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaR,IAAIxC,OAAO,CAACiD,GAAG,CAAC,kBAAkB;oBACrD/F,mBAAmB+E,KAAKC,GAAG,KAAKgB,SAASF;gBAC3C;gBAEA,IAAI,CAACR,IAAIW,EAAE,EAAE;oBACXzF,SAASkB,QAAQC,GAAG,CAAC,MAAM2D,IAAIuB,IAAI;oBACnC,MAAM,IAAI5C,MAAM,CAAC,iBAAiB,EAAEqB,IAAIO,MAAM,EAAE;gBAClD;gBAEA,IAAIrF,OAAO;oBACTkB,QAAQC,GAAG,CACT,CAAC,oCAAoC,EAAEwE,IAAI,YAAY,EACrDpB,KAAKC,GAAG,KAAKyB,MACd,UAAU,EAAEpC,KAAKnC,MAAM,EAAE;gBAE9B;YACF,EAAE,OAAOT,KAAK;gBACZ,+BAA+B;gBAC/B,IAAIjB,OAAO;oBACTkB,QAAQkF,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAEnF;gBAChD;YACF;QACF;QACA;IACF;AACF"}