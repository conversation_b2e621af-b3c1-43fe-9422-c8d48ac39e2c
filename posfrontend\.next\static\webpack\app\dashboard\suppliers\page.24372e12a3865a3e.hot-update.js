"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/suppliers/page",{

/***/ "(app-pages-browser)/./src/components/ui/ResponsiveTable.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/ResponsiveTable.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResponsiveTable: () => (/* binding */ ResponsiveTable),\n/* harmony export */   ResponsiveTableGrid: () => (/* binding */ ResponsiveTableGrid),\n/* harmony export */   TableCell: () => (/* binding */ TableCell),\n/* harmony export */   TableHeader: () => (/* binding */ TableHeader),\n/* harmony export */   TableRow: () => (/* binding */ TableRow),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ResponsiveTable,ResponsiveTableGrid,TableHeader,TableCell,TableRow,default auto */ \n\n\n/**\n * Responsive table wrapper that provides horizontal scrolling\n * Uses CSS Grid for better responsiveness\n */ const ResponsiveTable = (param)=>{\n    let { children, className, minWidth = \"800px\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-full overflow-x-auto overflow-y-visible\", \"border border-gray-200 rounded-lg shadow-sm\", \"bg-white\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-w-full\",\n            style: {\n                minWidth\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ResponsiveTable;\n/**\n * CSS Grid-based responsive table for better control\n */ const ResponsiveTableGrid = (param)=>{\n    let { children, columns, className, minWidth = \"800px\" } = param;\n    const isMobile =  true && window.innerWidth < 768;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-full overflow-x-auto overflow-y-visible\", \"border border-gray-200 rounded-lg shadow-sm\", \"bg-white\", // Ensure smooth scrolling on mobile\n        \"scroll-smooth\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"gap-0\", isMobile ? \"grid\" : \"block\" // Use grid only on mobile\n            ),\n            style: isMobile ? {\n                gridTemplateColumns: columns,\n                minWidth,\n                width: \"max-content\"\n            } : {},\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ResponsiveTableGrid;\nconst TableHeader = (param)=>{\n    let { children, className, sticky } = param;\n    // For mobile, disable sticky positioning to allow proper scrolling\n    const isMobile =  true && window.innerWidth < 768;\n    const stickyClasses = {\n        left: !isMobile ? \"sticky left-0 z-20 bg-gray-50 border-r border-gray-200\" : \"\",\n        right: !isMobile ? \"sticky right-0 z-20 bg-gray-50 border-l border-gray-200\" : \"\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-gray-50 border-b border-gray-200\", \"font-medium text-xs text-gray-700 uppercase tracking-wider\", \"px-3 py-3 text-left\", \"sticky top-0 z-10\", sticky && stickyClasses[sticky], className),\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = TableHeader;\nconst TableCell = (param)=>{\n    let { children, className, sticky } = param;\n    // For mobile, disable sticky positioning to allow proper scrolling\n    const isMobile =  true && window.innerWidth < 768;\n    const stickyClasses = {\n        left: !isMobile ? \"sticky left-0 z-10 bg-white border-r border-gray-200\" : \"\",\n        right: !isMobile ? \"sticky right-0 z-10 bg-white border-l border-gray-200\" : \"\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-3 py-4 text-sm text-gray-900\", \"border-b border-gray-200\", \"whitespace-nowrap\", sticky && stickyClasses[sticky], className),\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, undefined);\n};\n_c3 = TableCell;\nconst TableRow = (param)=>{\n    let { children, className, selected = false, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"contents\", selected && \"bg-blue-50\", onClick && \"cursor-pointer hover:bg-gray-50\", className),\n        onClick: onClick,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, undefined);\n};\n_c4 = TableRow;\n// Export default as ResponsiveTable for backward compatibility\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResponsiveTable);\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"ResponsiveTable\");\n$RefreshReg$(_c1, \"ResponsiveTableGrid\");\n$RefreshReg$(_c2, \"TableHeader\");\n$RefreshReg$(_c3, \"TableCell\");\n$RefreshReg$(_c4, \"TableRow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/ResponsiveTable.tsx\n"));

/***/ })

});