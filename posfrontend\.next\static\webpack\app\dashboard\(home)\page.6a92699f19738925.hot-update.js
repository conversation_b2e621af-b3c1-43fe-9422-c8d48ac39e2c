"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/(home)/page",{

/***/ "(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/ScanOutlined.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@ant-design/icons-svg/es/asn/ScanOutlined.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// This icon file is generated automatically.\nvar ScanOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M136 384h56c4.4 0 8-3.6 8-8V200h176c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H196c-37.6 0-68 30.4-68 68v180c0 4.4 3.6 8 8 8zm512-184h176v176c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V196c0-37.6-30.4-68-68-68H648c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zM376 824H200V648c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v180c0 37.6 30.4 68 68 68h180c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm512-184h-56c-4.4 0-8 3.6-8 8v176H648c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h180c37.6 0 68-30.4 68-68V648c0-4.4-3.6-8-8-8zm16-164H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z\" } }] }, \"name\": \"scan\", \"theme\": \"outlined\" };\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ScanOutlined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9pY29ucy1zdmcvZXMvYXNuL1NjYW5PdXRsaW5lZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxxQkFBcUIsVUFBVSx5QkFBeUIsa0RBQWtELGlCQUFpQiwwQkFBMEIsNGpCQUE0akIsR0FBRztBQUNwdEIsaUVBQWUsWUFBWSxFQUFDIiwic291cmNlcyI6WyJFOlxcUFJPSkVDVFNcXHBvc1xccG9zZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQGFudC1kZXNpZ25cXGljb25zLXN2Z1xcZXNcXGFzblxcU2Nhbk91dGxpbmVkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgaWNvbiBmaWxlIGlzIGdlbmVyYXRlZCBhdXRvbWF0aWNhbGx5LlxudmFyIFNjYW5PdXRsaW5lZCA9IHsgXCJpY29uXCI6IHsgXCJ0YWdcIjogXCJzdmdcIiwgXCJhdHRyc1wiOiB7IFwidmlld0JveFwiOiBcIjY0IDY0IDg5NiA4OTZcIiwgXCJmb2N1c2FibGVcIjogXCJmYWxzZVwiIH0sIFwiY2hpbGRyZW5cIjogW3sgXCJ0YWdcIjogXCJwYXRoXCIsIFwiYXR0cnNcIjogeyBcImRcIjogXCJNMTM2IDM4NGg1NmM0LjQgMCA4LTMuNiA4LThWMjAwaDE3NmM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOEgxOTZjLTM3LjYgMC02OCAzMC40LTY4IDY4djE4MGMwIDQuNCAzLjYgOCA4IDh6bTUxMi0xODRoMTc2djE3NmMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04VjE5NmMwLTM3LjYtMzAuNC02OC02OC02OEg2NDhjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDh6TTM3NiA4MjRIMjAwVjY0OGMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2MTgwYzAgMzcuNiAzMC40IDY4IDY4IDY4aDE4MGM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOHptNTEyLTE4NGgtNTZjLTQuNCAwLTggMy42LTggOHYxNzZINjQ4Yy00LjQgMC04IDMuNi04IDh2NTZjMCA0LjQgMy42IDggOCA4aDE4MGMzNy42IDAgNjgtMzAuNCA2OC02OFY2NDhjMC00LjQtMy42LTgtOC04em0xNi0xNjRIMTIwYy00LjQgMC04IDMuNi04IDh2NTZjMCA0LjQgMy42IDggOCA4aDc4NGM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOHpcIiB9IH1dIH0sIFwibmFtZVwiOiBcInNjYW5cIiwgXCJ0aGVtZVwiOiBcIm91dGxpbmVkXCIgfTtcbmV4cG9ydCBkZWZhdWx0IFNjYW5PdXRsaW5lZDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/ScanOutlined.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ScanOutlined.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@ant-design/icons/es/icons/ScanOutlined.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ant_design_icons_svg_es_asn_ScanOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ant-design/icons-svg/es/asn/ScanOutlined */ \"(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/ScanOutlined.js\");\n/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/AntdIcon */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/components/AntdIcon.js\");\n\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\n\n\nvar ScanOutlined = function ScanOutlined(props, ref) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n        ref: ref,\n        icon: _ant_design_icons_svg_es_asn_ScanOutlined__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    }));\n};\n_c = ScanOutlined;\n/**![scan](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEzNiAzODRoNTZjNC40IDAgOC0zLjYgOC04VjIwMGgxNzZjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LThIMTk2Yy0zNy42IDAtNjggMzAuNC02OCA2OHYxODBjMCA0LjQgMy42IDggOCA4em01MTItMTg0aDE3NnYxNzZjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOFYxOTZjMC0zNy42LTMwLjQtNjgtNjgtNjhINjQ4Yy00LjQgMC04IDMuNi04IDh2NTZjMCA0LjQgMy42IDggOCA4ek0zNzYgODI0SDIwMFY2NDhjMC00LjQtMy42LTgtOC04aC01NmMtNC40IDAtOCAzLjYtOCA4djE4MGMwIDM3LjYgMzAuNCA2OCA2OCA2OGgxODBjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6bTUxMi0xODRoLTU2Yy00LjQgMC04IDMuNi04IDh2MTc2SDY0OGMtNC40IDAtOCAzLjYtOCA4djU2YzAgNC40IDMuNiA4IDggOGgxODBjMzcuNiAwIDY4LTMwLjQgNjgtNjhWNjQ4YzAtNC40LTMuNi04LTgtOHptMTYtMTY0SDEyMGMtNC40IDAtOCAzLjYtOCA4djU2YzAgNC40IDMuNiA4IDggOGg3ODRjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6IiAvPjwvc3ZnPg==) */ var RefIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(ScanOutlined);\n_c1 = RefIcon;\nif (true) {\n    RefIcon.displayName = 'ScanOutlined';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RefIcon);\nvar _c, _c1;\n$RefreshReg$(_c, \"ScanOutlined\");\n$RefreshReg$(_c1, \"RefIcon\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ScanOutlined.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx":
/*!*************************************************!*\
  !*** ./src/components/Sales/SalesFormPanel.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/image/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ScanOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PrinterOutlined.js\");\n/* harmony import */ var _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/reduxRTK/services/salesApi */ \"(app-pages-browser)/./src/reduxRTK/services/salesApi.ts\");\n/* harmony import */ var _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/reduxRTK/services/productApi */ \"(app-pages-browser)/./src/reduxRTK/services/productApi.ts\");\n/* harmony import */ var _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/reduxRTK/services/userStoreApi */ \"(app-pages-browser)/./src/reduxRTK/services/userStoreApi.ts\");\n/* harmony import */ var _components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/SlidingPanel */ \"(app-pages-browser)/./src/components/ui/SlidingPanel.tsx\");\n/* harmony import */ var _hooks_useBarcodeScanner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useBarcodeScanner */ \"(app-pages-browser)/./src/hooks/useBarcodeScanner.ts\");\n/* harmony import */ var _utils_showMessage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/showMessage */ \"(app-pages-browser)/./src/utils/showMessage.ts\");\n/* harmony import */ var _utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/cloudinaryUtils */ \"(app-pages-browser)/./src/utils/cloudinaryUtils.ts\");\n/* harmony import */ var _sales_panels_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./sales-panels.css */ \"(app-pages-browser)/./src/components/Sales/sales-panels.css\");\n/* harmony import */ var _modern_sales_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./modern-sales.css */ \"(app-pages-browser)/./src/components/Sales/modern-sales.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst SalesFormPanel = (param)=>{\n    let { isOpen, onClose, onSuccess } = param;\n    var _productsData_data_products, _productsData_data;\n    _s();\n    const [form] = _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"].useForm();\n    const [productForm] = _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"].useForm();\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalAmount, setTotalAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingReceipt, setIsGeneratingReceipt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptPreviewVisible, setReceiptPreviewVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptUrl, setReceiptUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPrinted, setHasPrinted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Barcode scanner functionality\n    const { isOpen: isScannerOpen, openScanner, closeScanner, handleBarcodeScanned, isLoading: isScannerLoading } = (0,_hooks_useBarcodeScanner__WEBPACK_IMPORTED_MODULE_6__.useBarcodeScanner)({\n        onProductFound: {\n            \"SalesFormPanel.useBarcodeScanner\": (product)=>{\n                setSelectedProduct(product);\n                setQuantity(1);\n                if (productForm) {\n                    productForm.setFieldsValue({\n                        productId: product.id,\n                        quantity: 1\n                    });\n                }\n                // Auto-add to cart after scanning\n                setTimeout({\n                    \"SalesFormPanel.useBarcodeScanner\": ()=>{\n                        handleAddItem();\n                    }\n                }[\"SalesFormPanel.useBarcodeScanner\"], 100);\n            }\n        }[\"SalesFormPanel.useBarcodeScanner\"],\n        onProductNotFound: {\n            \"SalesFormPanel.useBarcodeScanner\": (barcode)=>{\n                console.log('Product not found for barcode:', barcode);\n            }\n        }[\"SalesFormPanel.useBarcodeScanner\"]\n    });\n    // Debug state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            console.log(\"Items state changed:\", items);\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items\n    ]);\n    const { data: productsData, isLoading: isLoadingProducts, refetch: refetchProducts } = (0,_reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery)({\n        page: 1,\n        limit: 1000,\n        search: searchTerm\n    }, {\n        // Force refetch when component mounts and when panel opens\n        refetchOnMountOrArgChange: true,\n        refetchOnFocus: false,\n        refetchOnReconnect: true\n    });\n    // Debug products data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (productsData) {\n                var _productsData_data, _productsData_data_products, _productsData_data1;\n                console.log(\"🛒 Products loaded:\", {\n                    total: ((_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.total) || 0,\n                    productsCount: ((_productsData_data1 = productsData.data) === null || _productsData_data1 === void 0 ? void 0 : (_productsData_data_products = _productsData_data1.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.length) || 0,\n                    isLoading: isLoadingProducts\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        productsData,\n        isLoadingProducts\n    ]);\n    // Get current user ID from auth state\n    const getCurrentUserId = ()=>{\n        if (true) {\n            var _state_auth_user, _state_auth;\n            // @ts-ignore - Redux state is exposed for debugging\n            const state = window.__REDUX_STATE;\n            return (state === null || state === void 0 ? void 0 : (_state_auth = state.auth) === null || _state_auth === void 0 ? void 0 : (_state_auth_user = _state_auth.user) === null || _state_auth_user === void 0 ? void 0 : _state_auth_user.id) || 0;\n        }\n        return 0;\n    };\n    // Fetch user stores\n    const { data: userStoresData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery)(getCurrentUserId());\n    // Fetch default store\n    const { data: defaultStoreData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery)(getCurrentUserId());\n    // Set default store when data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (defaultStoreData === null || defaultStoreData === void 0 ? void 0 : defaultStoreData.data) {\n                setSelectedStore(defaultStoreData.data);\n                form.setFieldsValue({\n                    storeId: defaultStoreData.data.id\n                });\n            } else if ((userStoresData === null || userStoresData === void 0 ? void 0 : userStoresData.data) && userStoresData.data.length > 0) {\n                setSelectedStore(userStoresData.data[0]);\n                form.setFieldsValue({\n                    storeId: userStoresData.data[0].id\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        defaultStoreData,\n        userStoresData,\n        form\n    ]);\n    // Create sale mutation\n    const [createSale, { isLoading: isSubmitting }] = (0,_reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation)();\n    // Calculate total amount whenever items change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (items && items.length > 0) {\n                const total = items.reduce({\n                    \"SalesFormPanel.useEffect.total\": (sum, item)=>sum + item.price * item.quantity\n                }[\"SalesFormPanel.useEffect.total\"], 0);\n                setTotalAmount(total);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: total\n                    });\n                }\n                // Debug log to check items state\n                console.log(\"Current items in useEffect:\", items);\n            } else {\n                setTotalAmount(0);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: 0\n                    });\n                }\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items,\n        form\n    ]);\n    // Handle panel open/close\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (isOpen) {\n                // When panel opens, ensure we have fresh product data\n                console.log(\"🛒 Sales panel opened - fetching fresh product data\");\n                refetchProducts();\n            } else {\n                // Reset forms when panel is closed\n                if (form) {\n                    form.resetFields();\n                }\n                if (productForm) {\n                    productForm.resetFields();\n                }\n                setItems([]);\n                setSelectedProduct(null);\n                setQuantity(1);\n                setTotalAmount(0);\n                setReceiptUrl(null);\n                setReceiptPreviewVisible(false);\n                setHasPrinted(false);\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        isOpen,\n        form,\n        refetchProducts\n    ]);\n    // Handle adding an item to the sale\n    const handleAddItem = ()=>{\n        if (!selectedProduct) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_7__.showMessage)(\"error\", \"Please select a product\");\n            return;\n        }\n        if (quantity <= 0) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_7__.showMessage)(\"error\", \"Quantity must be greater than 0\");\n            return;\n        }\n        if (selectedProduct.stockQuantity < quantity) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_7__.showMessage)(\"error\", \"Only \".concat(selectedProduct.stockQuantity, \" units available in stock\"));\n            return;\n        }\n        console.log(\"Adding item with product:\", selectedProduct);\n        // Check if product already exists in items\n        const existingItemIndex = items.findIndex((item)=>item.productId === selectedProduct.id);\n        if (existingItemIndex >= 0) {\n            // Update existing item\n            const updatedItems = [\n                ...items\n            ];\n            const newQuantity = updatedItems[existingItemIndex].quantity + quantity;\n            if (newQuantity > selectedProduct.stockQuantity) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_7__.showMessage)(\"error\", \"Cannot add more than \".concat(selectedProduct.stockQuantity, \" units of this product\"));\n                return;\n            }\n            updatedItems[existingItemIndex].quantity = newQuantity;\n            // Make sure the product name is set\n            if (!updatedItems[existingItemIndex].productName) {\n                updatedItems[existingItemIndex].productName = selectedProduct.name;\n            }\n            // Update the state with the new array\n            console.log(\"Updating existing item. New items array:\", updatedItems);\n            setItems([\n                ...updatedItems\n            ]); // Create a new array reference to force re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_7__.showMessage)(\"success\", \"Updated quantity of \".concat(selectedProduct.name));\n        } else {\n            // Add new item\n            const newItem = {\n                productId: selectedProduct.id,\n                productName: selectedProduct.name,\n                quantity,\n                price: typeof selectedProduct.price === \"string\" ? parseFloat(selectedProduct.price) : selectedProduct.price\n            };\n            // Create a new array with the new item\n            const newItems = [\n                ...items,\n                newItem\n            ];\n            // Update the state with the new array\n            console.log(\"Adding new item. New items array:\", newItems);\n            setItems(newItems); // This should trigger a re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_7__.showMessage)(\"success\", \"Added \".concat(quantity, \" \").concat(selectedProduct.name, \" to sale\"));\n        }\n        // Reset selection\n        setSelectedProduct(null);\n        setQuantity(1);\n        if (productForm) {\n            productForm.setFieldsValue({\n                productId: undefined,\n                quantity: 1\n            });\n        }\n    };\n    // Handle removing an item from the sale\n    const handleRemoveItem = (index)=>{\n        const updatedItems = [\n            ...items\n        ];\n        updatedItems.splice(index, 1);\n        setItems(updatedItems);\n    };\n    // Handle printing receipt - directly trigger print dialog\n    const handlePrintReceipt = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n            if (!receiptUrl || hasPrinted) {\n                console.log(\"Skipping print: \", !receiptUrl ? \"No receipt URL\" : \"Already printed\");\n                return;\n            }\n            console.log(\"Printing receipt:\", receiptUrl);\n            // Mark as printed immediately to prevent multiple print dialogs\n            setHasPrinted(true);\n            // Create a hidden iframe to load the image\n            const iframe = document.createElement(\"iframe\");\n            iframe.style.display = \"none\";\n            document.body.appendChild(iframe);\n            // Set up the iframe content with the image and print CSS\n            iframe.onload = ({\n                \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                    if (iframe.contentWindow) {\n                        // Write the HTML content to the iframe\n                        iframe.contentWindow.document.write('\\n          <!DOCTYPE html>\\n          <html>\\n            <head>\\n              <title>Print Receipt</title>\\n              <style>\\n                body {\\n                  margin: 0;\\n                  padding: 0;\\n                  display: flex;\\n                  justify-content: center;\\n                  align-items: center;\\n                  height: 100vh;\\n                }\\n                img {\\n                  max-width: 100%;\\n                  max-height: 100vh;\\n                }\\n                @media print {\\n                  body {\\n                    margin: 0;\\n                    padding: 0;\\n                  }\\n                  img {\\n                    width: 100%;\\n                    height: auto;\\n                  }\\n                }\\n              </style>\\n            </head>\\n            <body>\\n              <img src=\"'.concat(receiptUrl, '\" alt=\"Receipt\" />\\n            </body>\\n          </html>\\n        '));\n                        // Close the document\n                        iframe.contentWindow.document.close();\n                        // Use a single print trigger with a delay to ensure the image is loaded\n                        setTimeout({\n                            \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                if (iframe.contentWindow) {\n                                    try {\n                                        // Print the iframe content\n                                        iframe.contentWindow.focus();\n                                        iframe.contentWindow.print();\n                                    } catch (e) {\n                                        console.error(\"Error printing receipt:\", e);\n                                    }\n                                    // Remove the iframe after printing\n                                    setTimeout({\n                                        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                            document.body.removeChild(iframe);\n                                        }\n                                    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 1000);\n                                }\n                            }\n                        }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 500);\n                    }\n                }\n            })[\"SalesFormPanel.useCallback[handlePrintReceipt]\"];\n            // Set the iframe source to trigger the onload event\n            iframe.src = \"about:blank\";\n        }\n    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], [\n        receiptUrl,\n        hasPrinted\n    ]);\n    // Effect to automatically print receipt when modal is shown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (receiptPreviewVisible && receiptUrl && !hasPrinted) {\n                // Add a small delay to ensure the receipt image is loaded\n                const timer = setTimeout({\n                    \"SalesFormPanel.useEffect.timer\": ()=>{\n                        handlePrintReceipt(); // This now handles the hasPrinted state internally\n                    }\n                }[\"SalesFormPanel.useEffect.timer\"], 800);\n                return ({\n                    \"SalesFormPanel.useEffect\": ()=>clearTimeout(timer)\n                })[\"SalesFormPanel.useEffect\"];\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        receiptPreviewVisible,\n        receiptUrl,\n        hasPrinted,\n        handlePrintReceipt\n    ]);\n    // Handle form submission\n    const handleSubmit = async ()=>{\n        try {\n            var _userStoresData_data;\n            if (items.length === 0) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_7__.showMessage)(\"error\", \"Please add at least one item to the sale\");\n                return;\n            }\n            // Validate form fields\n            const values = await form.validateFields();\n            // Check if store is selected\n            if (!selectedStore) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_7__.showMessage)(\"error\", \"No store information available. Please set up your store in your profile settings.\");\n                return;\n            }\n            // Set loading state for receipt generation\n            setIsGeneratingReceipt(true);\n            // Get store information for receipt\n            const storeInfo = selectedStore || (userStoresData === null || userStoresData === void 0 ? void 0 : (_userStoresData_data = userStoresData.data) === null || _userStoresData_data === void 0 ? void 0 : _userStoresData_data.find((store)=>store.id === values.storeId)) || {\n                name: \"POS System\"\n            };\n            // Generate receipt HTML\n            const receiptHTML = (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_8__.generateReceiptHTML)({\n                id: Date.now(),\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                transactionDate: new Date().toISOString(),\n                items: items.map((item)=>({\n                        productName: item.productName,\n                        quantity: item.quantity,\n                        price: item.price\n                    }))\n            }, storeInfo);\n            // Generate receipt image and get URL\n            let receiptUrl = \"https://receipt.example.com/placeholder\";\n            try {\n                receiptUrl = await (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_8__.generateReceiptImage)(receiptHTML);\n            } catch (error) {\n                console.error(\"Failed to generate receipt image:\", error);\n            // Continue with placeholder URL if image generation fails\n            }\n            const saleData = {\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                items: items.map((item)=>({\n                        productId: item.productId,\n                        quantity: item.quantity,\n                        price: item.price\n                    })),\n                receiptUrl,\n                storeId: selectedStore === null || selectedStore === void 0 ? void 0 : selectedStore.id\n            };\n            const response = await createSale(saleData).unwrap();\n            if (response.success) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_7__.showMessage)(\"success\", \"Sale created successfully\");\n                // Store the receipt URL for preview\n                setReceiptUrl(receiptUrl);\n                // Show receipt preview modal and offer print option\n                setReceiptPreviewVisible(true);\n                // Refresh product data to get updated stock quantities\n                refetchProducts();\n                // Trigger the success callback to refresh the list WITHOUT closing the panel\n                setTimeout(()=>{\n                    if (onSuccess) {\n                        // Call refetch directly instead of closing the panel\n                        refetchProducts();\n                    }\n                }, 300);\n            // Keep the panel open until the user explicitly closes it\n            // This ensures the receipt modal stays visible\n            } else {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_7__.showMessage)(\"error\", response.message || \"Failed to create sale\");\n            }\n        } catch (error) {\n            var _error_data;\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_7__.showMessage)(\"error\", ((_error_data = error.data) === null || _error_data === void 0 ? void 0 : _error_data.message) || \"An error occurred while creating the sale\");\n        } finally{\n            setIsGeneratingReceipt(false);\n        }\n    };\n    // Debug log to check items state when rendering\n    console.log(\"Rendering with items:\", items);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        title: \"Point of Sale\",\n        isOpen: isOpen,\n        onClose: onClose,\n        width: \"95%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sales-form min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between border-b border-slate-200 bg-white/80 backdrop-blur-sm px-6 py-4 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"text-xl text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"m-0 text-xl font-bold text-slate-800\",\n                                                children: \"New Transaction\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-slate-500 m-0\",\n                                                children: \"Point of Sale System\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-slate-500 m-0\",\n                                        children: \"Total Amount\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent\",\n                                        children: [\n                                            \"GHS \",\n                                            totalAmount.toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 527,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-6 lg:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 rounded-2xl border border-slate-200 bg-white/70 backdrop-blur-sm p-6 shadow-xl shadow-slate-200/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg mr-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"text-sm text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 555,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-bold text-slate-800 m-0\",\n                                                                            children: \"Product Selection\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 558,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-slate-500 m-0\",\n                                                                            children: \"Choose products for this transaction\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 561,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-sm text-slate-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-1 text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \" Required fields\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    form: productForm,\n                                                    layout: \"vertical\",\n                                                    initialValues: {\n                                                        quantity: 1\n                                                    },\n                                                    className: \"product-form\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 gap-6 md:grid-cols-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"md:col-span-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"].Item, {\n                                                                        name: \"productId\",\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-slate-700 font-medium\",\n                                                                            children: [\n                                                                                \"Product \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-500\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 583,\n                                                                                    columnNumber: 37\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 582,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        className: \"mb-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            showSearch: true,\n                                                                            placeholder: isLoadingProducts ? \"Loading products...\" : \"🔍 Search and select a product...\",\n                                                                            optionFilterProp: \"children\",\n                                                                            loading: isLoadingProducts,\n                                                                            disabled: isLoadingProducts,\n                                                                            onChange: (value)=>{\n                                                                                var _productsData_data;\n                                                                                const product = productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products.find((p)=>p.id === value);\n                                                                                console.log(\"Selected product:\", product);\n                                                                                if (product) {\n                                                                                    // Make a deep copy to avoid reference issues\n                                                                                    setSelectedProduct({\n                                                                                        ...product,\n                                                                                        // Ensure price is properly formatted\n                                                                                        price: typeof product.price === \"string\" ? product.price : String(product.price)\n                                                                                    });\n                                                                                } else {\n                                                                                    setSelectedProduct(null);\n                                                                                }\n                                                                            },\n                                                                            onSearch: setSearchTerm,\n                                                                            filterOption: false,\n                                                                            className: \"modern-select\",\n                                                                            size: \"large\",\n                                                                            suffixIcon: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                spin: true,\n                                                                                className: \"text-blue-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 623,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-center bg-gradient-to-r mr-3 mt-4\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"text-slate-400 \"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 626,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 625,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            notFoundContent: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-center py-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        size: \"small\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 633,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"ml-2 text-slate-500\",\n                                                                                        children: \"Loading products...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 634,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 632,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center py-4 text-slate-500\",\n                                                                                children: \"No products found\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 637,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            children: productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : (_productsData_data_products = _productsData_data.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"].Option, {\n                                                                                    value: product.id,\n                                                                                    disabled: product.stockQuantity <= 0,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"font-medium text-slate-800\",\n                                                                                                        children: product.name\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 651,\n                                                                                                        columnNumber: 35\n                                                                                                    }, undefined),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-sm text-slate-500\",\n                                                                                                        children: [\n                                                                                                            \"GHS \",\n                                                                                                            Number(product.price).toFixed(2)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 652,\n                                                                                                        columnNumber: 35\n                                                                                                    }, undefined)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                lineNumber: 650,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-sm px-2 py-1 rounded-full \".concat(product.stockQuantity <= 0 ? 'bg-red-100 text-red-600' : product.stockQuantity <= 5 ? 'bg-yellow-100 text-yellow-600' : 'bg-green-100 text-green-600'),\n                                                                                                children: product.stockQuantity <= 0 ? \"Out of Stock\" : \"Stock: \".concat(product.stockQuantity)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                lineNumber: 656,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 649,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, product.id, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 644,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 588,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 579,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 578,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"].Item, {\n                                                                        name: \"quantity\",\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-slate-700 font-medium\",\n                                                                            children: [\n                                                                                \"Quantity \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-500\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 679,\n                                                                                    columnNumber: 38\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 678,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        className: \"mb-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            min: 1,\n                                                                            value: quantity,\n                                                                            onChange: (value)=>setQuantity(value || 1),\n                                                                            style: {\n                                                                                width: \"100%\"\n                                                                            },\n                                                                            className: \"modern-input\",\n                                                                            size: \"large\",\n                                                                            placeholder: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 684,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 675,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4 space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    type: \"primary\",\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 700,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    onClick: handleAddItem,\n                                                                    className: \"h-14 w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 border-0 rounded-xl font-semibold text-base shadow-lg shadow-blue-200 transition-all duration-200 hover:shadow-xl hover:shadow-blue-300 hover:-translate-y-0.5\",\n                                                                    disabled: !selectedProduct,\n                                                                    size: \"large\",\n                                                                    children: \"Add to Cart\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 698,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 710,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    onClick: openScanner,\n                                                                    loading: isScannerLoading,\n                                                                    className: \"h-12 w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 border-0 rounded-xl font-semibold text-base shadow-lg shadow-purple-200 transition-all duration-200 hover:shadow-xl hover:shadow-purple-300 hover:-translate-y-0.5 text-white\",\n                                                                    size: \"large\",\n                                                                    children: isScannerLoading ? 'Searching...' : 'Scan Barcode'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 709,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 rounded-2xl border border-slate-200 bg-white/70 backdrop-blur-sm p-6 shadow-xl shadow-slate-200/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"text-sm text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 725,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-bold text-slate-800 m-0\",\n                                                                    children: \"Cart Items\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 729,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-500 m-0\",\n                                                                    children: \"Items added to this transaction\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-[400px] overflow-x-auto overflow-y-auto\",\n                                                    children: items.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-2xl border border-slate-200 bg-gradient-to-br from-slate-50 to-blue-50 p-12 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center w-16 h-16 bg-gradient-to-r from-slate-400 to-slate-500 rounded-full mx-auto mb-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"text-2xl text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 736,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-slate-700 mb-2\",\n                                                                children: \"Cart is Empty\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 739,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-slate-500\",\n                                                                children: \"Add products to start building your transaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-hidden rounded-2xl border border-slate-200 bg-white shadow-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-slate-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-12 gap-4 text-sm font-semibold text-slate-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-5\",\n                                                                            children: \"Product\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 746,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 text-center\",\n                                                                            children: \"Qty\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 747,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 text-right\",\n                                                                            children: \"Price\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 748,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 text-right\",\n                                                                            children: \"Subtotal\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 749,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-1 text-center\",\n                                                                            children: \"Action\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 750,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 745,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 744,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"divide-y divide-slate-100\",\n                                                                children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"px-6 py-4 hover:bg-slate-50 transition-colors duration-150\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-12 gap-4 items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-5\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-semibold text-slate-800\",\n                                                                                        children: item.productName || \"Unknown Product\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 761,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 760,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-2 text-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"inline-flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full text-sm font-medium\",\n                                                                                        children: item.quantity\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 766,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 765,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-2 text-right text-slate-600 font-medium\",\n                                                                                    children: [\n                                                                                        \"GHS \",\n                                                                                        Number(item.price).toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 770,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-2 text-right font-bold text-slate-800\",\n                                                                                    children: [\n                                                                                        \"GHS \",\n                                                                                        (Number(item.price) * item.quantity).toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 773,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-1 text-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 778,\n                                                                                            columnNumber: 41\n                                                                                        }, void 0),\n                                                                                        onClick: ()=>handleRemoveItem(index),\n                                                                                        type: \"text\",\n                                                                                        danger: true,\n                                                                                        className: \"text-red-500 hover:bg-red-50 hover:text-red-600 rounded-lg\",\n                                                                                        size: \"small\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 777,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 776,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 759,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, \"\".concat(item.productId, \"-\").concat(index), false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 755,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 753,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-emerald-50 to-green-50 px-6 py-4 border-t border-slate-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-12 gap-4 items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-9 text-right\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg font-bold text-slate-800\",\n                                                                                children: \"Total:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 793,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 792,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-3 text-right\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    totalAmount.toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 796,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 795,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 790,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 733,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sticky top-4 rounded-2xl border border-slate-200 bg-white/70 backdrop-blur-sm p-6 shadow-xl shadow-slate-200/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg mr-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"text-sm text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 813,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 812,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-bold text-slate-800 m-0\",\n                                                                children: \"Checkout\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 816,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-slate-500 m-0\",\n                                                                children: \"Complete your transaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 817,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 815,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 811,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                form: form,\n                                                layout: \"vertical\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 rounded-xl border border-slate-200 bg-gradient-to-br from-slate-50 to-blue-50 p-5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-slate-600\",\n                                                                        children: \"Items:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 824,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full text-sm font-bold\",\n                                                                        children: items.length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 825,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 823,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-slate-600\",\n                                                                        children: \"Total Quantity:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 830,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-800 rounded-full text-sm font-bold\",\n                                                                        children: items.reduce((sum, item)=>sum + item.quantity, 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 831,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 829,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-slate-600\",\n                                                                        children: \"Subtotal:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 836,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-semibold text-slate-800\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 837,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 835,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"my-4 border-t border-slate-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-bold text-slate-800\",\n                                                                        children: \"Total:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 843,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 844,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    selectedStore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"mb-3 block text-slate-700 font-medium\",\n                                                                children: \"Store\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 853,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center rounded-xl border border-slate-200 bg-gradient-to-r from-blue-50 to-indigo-50 p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg mr-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"text-sm text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 856,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 855,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-semibold text-slate-800\",\n                                                                        children: selectedStore.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 858,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"hidden\",\n                                                                        name: \"storeId\",\n                                                                        value: selectedStore.id\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 861,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 854,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 852,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 rounded-xl border border-orange-200 bg-gradient-to-r from-orange-50 to-yellow-50 p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-lg mr-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"text-sm text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 872,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 871,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-orange-800 m-0\",\n                                                                            children: \"Store Setup Required\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 875,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-orange-600 m-0\",\n                                                                            children: \"Please set up your store in profile settings.\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 876,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 874,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 870,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 869,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"].Item, {\n                                                        name: \"paymentMethod\",\n                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-700 font-medium\",\n                                                            children: [\n                                                                \"Payment Method \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 888,\n                                                                    columnNumber: 40\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 887,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        rules: [\n                                                            {\n                                                                required: true,\n                                                                message: \"Please select a payment method\"\n                                                            }\n                                                        ],\n                                                        initialValue: \"cash\",\n                                                        className: \"mb-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"\",\n                                                            size: \"large\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"].Option, {\n                                                                    value: \"cash\",\n                                                                    children: \"\\uD83D\\uDCB5 Cash\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 901,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"].Option, {\n                                                                    value: \"card\",\n                                                                    children: \"\\uD83D\\uDCB3 Card\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"].Option, {\n                                                                    value: \"mobile_money\",\n                                                                    children: \"\\uD83D\\uDCF1 Mobile Money\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 907,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 900,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 884,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-8 space-y-4\",\n                                                        children: [\n                                                            receiptPreviewVisible ? // Show \"Done\" button when receipt is visible\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 918,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                onClick: ()=>{\n                                                                    // Close the modal and reset the receipt state\n                                                                    setReceiptPreviewVisible(false);\n                                                                    setReceiptUrl(null);\n                                                                    setHasPrinted(false);\n                                                                    // Reset the forms to start a new sale\n                                                                    form.resetFields();\n                                                                    productForm.resetFields();\n                                                                    setItems([]);\n                                                                    setSelectedProduct(null);\n                                                                    setQuantity(1);\n                                                                    setTotalAmount(0);\n                                                                },\n                                                                className: \"h-14 w-full bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 border-0 rounded-xl font-semibold text-base shadow-lg shadow-emerald-200 transition-all duration-200 hover:shadow-xl hover:shadow-emerald-300 hover:-translate-y-0.5\",\n                                                                size: \"large\",\n                                                                children: \"Start New Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 916,\n                                                                columnNumber: 23\n                                                            }, undefined) : // Show \"Complete Sale\" button when creating a sale\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 942,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                onClick: handleSubmit,\n                                                                loading: isSubmitting || isGeneratingReceipt,\n                                                                disabled: items.length === 0,\n                                                                className: \"h-14 w-full bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 border-0 rounded-xl font-semibold text-base shadow-lg shadow-emerald-200 transition-all duration-200 hover:shadow-xl hover:shadow-emerald-300 hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:transform-none disabled:hover:shadow-lg\",\n                                                                size: \"large\",\n                                                                children: isGeneratingReceipt ? \"Generating Receipt\" : \"Complete Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 940,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                onClick: onClose,\n                                                                className: \"h-12 w-full border-slate-300 bg-slate-100 text-slate-700 hover:bg-slate-200 rounded-xl font-medium transition-all duration-200 hover:shadow-md\",\n                                                                size: \"large\",\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 913,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 821,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 809,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 548,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 547,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 525,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-gray-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            className: \"mr-2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 974,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Receipt Preview\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 975,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 973,\n                    columnNumber: 11\n                }, void 0),\n                open: receiptPreviewVisible,\n                onCancel: ()=>{\n                    // Close the modal and reset the receipt state\n                    setReceiptPreviewVisible(false);\n                    setReceiptUrl(null);\n                    setHasPrinted(false);\n                    // Reset the forms to start a new sale\n                    form.resetFields();\n                    productForm.resetFields();\n                    setItems([]);\n                    setSelectedProduct(null);\n                    setQuantity(1);\n                    setTotalAmount(0);\n                },\n                width: 500,\n                centered: true,\n                className: \"receipt-preview-modal\",\n                footer: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        onClick: ()=>{\n                            // Close the modal and reset the receipt state\n                            setReceiptPreviewVisible(false);\n                            setReceiptUrl(null);\n                            setHasPrinted(false);\n                            // Reset the forms to start a new sale\n                            form.resetFields();\n                            productForm.resetFields();\n                            setItems([]);\n                            setSelectedProduct(null);\n                            setQuantity(1);\n                            setTotalAmount(0);\n                        },\n                        className: \"border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200\",\n                        children: \"Close & New Sale\"\n                    }, \"close\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 997,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        type: \"primary\",\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1020,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: ()=>{\n                            // If already printed once, reset the flag to allow printing again\n                            if (hasPrinted) {\n                                setHasPrinted(false);\n                            }\n                            handlePrintReceipt();\n                        },\n                        className: \"bg-blue-600 hover:bg-blue-700\",\n                        children: hasPrinted ? \"Print Again\" : \"Print Receipt\"\n                    }, \"print\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1017,\n                        columnNumber: 11\n                    }, void 0)\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: receiptUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"receipt-image-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            src: receiptUrl,\n                            alt: \"Receipt\",\n                            className: \"receipt-image\",\n                            style: {\n                                maxWidth: \"100%\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1037,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1036,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-64 items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            size: \"large\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1046,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1045,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 1034,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 971,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n        lineNumber: 519,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SalesFormPanel, \"TKh6ocyo2D3eQflpIJfjDvmZe38=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"].useForm,\n        _hooks_useBarcodeScanner__WEBPACK_IMPORTED_MODULE_6__.useBarcodeScanner,\n        _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery,\n        _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation\n    ];\n});\n_c = SalesFormPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SalesFormPanel);\nvar _c;\n$RefreshReg$(_c, \"SalesFormPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx\n"));

/***/ })

});