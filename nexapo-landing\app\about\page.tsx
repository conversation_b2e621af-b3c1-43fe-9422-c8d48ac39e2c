'use client'

import Image from 'next/image'
import Link from 'next/link'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ToTop } from '../../components'

export default function About() {

  return (
    <div className="min-h-screen bg-white">
      <Navbar currentPage="about" />

      {/* Hero Section */}
      <section className="pt-36 pb-20 bg-gradient-to-br from-blue-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-block bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-semibold mb-6">
              🇬🇭 Made in Ghana • For Ghana
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              About <span className="text-blue-600">NEXAPO</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              We're revolutionizing how Ghanaian businesses operate with cutting-edge
              Point of Sale technology designed specifically for the local market.
            </p>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-12 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">2025</div>
                <div className="text-gray-600">Founded</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">100%</div>
                <div className="text-gray-600">Ghanaian</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">24/7</div>
                <div className="text-gray-600">Support</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">∞</div>
                <div className="text-gray-600">Possibilities</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-block bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-semibold mb-6">
                📖 Our Story
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Built for Ghana, By Ghanaians
              </h2>
              <div className="space-y-6 text-gray-600">
                <p className="text-lg leading-relaxed">
                  NEXAPO was born from a simple observation: Ghanaian businesses deserve
                  world-class technology that understands their unique needs. Traditional
                  cash registers and imported POS systems often fall short of addressing
                  local business practices and requirements.
                </p>
                <p className="text-lg leading-relaxed">
                  As Ghana's first locally-developed smart POS system, NEXAPO bridges
                  this gap by offering powerful features tailored specifically for
                  Ghanaian businesses - from small shops to large enterprises.
                </p>
                <p className="text-lg leading-relaxed">
                  We believe that every business, regardless of size, should have access
                  to professional-grade tools that help them grow, compete, and succeed
                  in today's digital economy.
                </p>
              </div>
            </div>
            <div className="relative">
              <div className="bg-blue-100 rounded-2xl p-8">
                <Image
                  src="/images/admin.png"
                  alt="NEXAPO Dashboard"
                  width={600}
                  height={400}
                  className="rounded-lg shadow-lg w-full h-auto"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Our Mission & Vision
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Driving the digital transformation of Ghanaian businesses with locally-built solutions.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12">
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-blue-100 hover:shadow-xl transition-shadow">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6">
                <span className="text-3xl">🎯</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Mission</h3>
              <p className="text-gray-600 leading-relaxed">
                To empower every Ghanaian business with intelligent, affordable, and
                easy-to-use technology that drives growth, improves efficiency, and
                enhances customer experience. We believe technology should serve businesses,
                not complicate them.
              </p>
              <div className="mt-6 flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span className="text-sm text-gray-500">Empowering local businesses since 2025</span>
              </div>
            </div>
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-purple-100 hover:shadow-xl transition-shadow">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-6">
                <span className="text-3xl">🌟</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Vision</h3>
              <p className="text-gray-600 leading-relaxed">
                To become the leading business management platform in West Africa,
                setting the standard for locally-developed solutions that truly
                understand and serve African businesses. We envision a future where
                every business has access to world-class technology.
              </p>
              <div className="mt-6 flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                <span className="text-sm text-gray-500">Leading West Africa's digital future</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose NEXAPO */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Why Choose NEXAPO?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We're not just another POS system. We're your local technology partner
              committed to your success.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "🇬🇭",
                title: "Made in Ghana",
                description: "Built by Ghanaians for Ghanaian businesses, understanding local needs and practices."
              },
              {
                icon: "💡",
                title: "Innovation First",
                description: "Cutting-edge features and continuous updates to keep your business ahead of the curve."
              },
              {
                icon: "🤝",
                title: "Local Support",
                description: "Dedicated Ghanaian support team available 24/7 to help you succeed."
              },
              {
                icon: "💰",
                title: "Affordable Pricing",
                description: "Transparent, fair pricing designed for businesses of all sizes in Ghana."
              },
              {
                icon: "🔒",
                title: "Secure & Reliable",
                description: "Bank-level security with 99.9% uptime guarantee for your peace of mind."
              },
              {
                icon: "📱",
                title: "Mobile-First",
                description: "Works perfectly on any device - phone, tablet, or computer."
              }
            ].map((feature, index) => (
              <div key={index} className="text-center p-6">
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Join the Revolution?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Be among the first businesses in Ghana to experience the power of NEXAPO.
            Start today and see the difference.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-yellow-500 hover:bg-yellow-600 text-blue-900 font-bold text-lg px-8 py-4 rounded-lg transition-colors"
            >
              🚀 Request Demo
            </Link>
            <Link
              href="/contact"
              className="bg-transparent hover:bg-white/10 text-white font-semibold text-lg px-8 py-4 rounded-lg border-2 border-white transition-colors"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </section>

      <Footer />
      <ScrollToTop />
    </div>
  )
}
