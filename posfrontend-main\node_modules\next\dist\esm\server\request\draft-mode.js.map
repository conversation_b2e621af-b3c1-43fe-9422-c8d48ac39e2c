{"version": 3, "sources": ["../../../src/server/request/draft-mode.ts"], "sourcesContent": ["import { getExpectedRequestStore } from '../app-render/work-unit-async-storage.external'\n\nimport type { DraftModeProvider } from '../async-storage/draft-mode-provider'\n\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  postponeWithTracking,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\n\n/**\n * In this version of Next.js `draftMode()` returns a Promise however you can still reference the properties of the underlying draftMode object\n * synchronously to facilitate migration. The `UnsafeUnwrappedDraftMode` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `draftMode()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedDraftMode` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `draftMode()` value can be awaited or you should call `draftMode()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedDraftMode` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `draftMode()` will only return a Promise and you will not be able to access the underlying draftMode object directly\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedDraftMode` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedDraftMode = DraftMode\n\nexport function draftMode(): Promise<DraftMode> {\n  const callingExpression = 'draftMode'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache' ||\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-ppr' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      // Return empty draft mode\n      if (\n        process.env.NODE_ENV === 'development' &&\n        !workStore?.isPrefetchRequest\n      ) {\n        const route = workStore?.route\n        return createExoticDraftModeWithDevWarnings(null, route)\n      } else {\n        return createExoticDraftMode(null)\n      }\n    }\n  }\n\n  const requestStore = getExpectedRequestStore(callingExpression)\n\n  const cachedDraftMode = CachedDraftModes.get(requestStore.draftMode)\n  if (cachedDraftMode) {\n    return cachedDraftMode\n  }\n\n  let promise\n  if (process.env.NODE_ENV === 'development' && !workStore?.isPrefetchRequest) {\n    const route = workStore?.route\n    promise = createExoticDraftModeWithDevWarnings(\n      requestStore.draftMode,\n      route\n    )\n  } else {\n    promise = createExoticDraftMode(requestStore.draftMode)\n  }\n  CachedDraftModes.set(requestStore.draftMode, promise)\n  return promise\n}\n\ninterface CacheLifetime {}\nconst CachedDraftModes = new WeakMap<CacheLifetime, Promise<DraftMode>>()\n\nfunction createExoticDraftMode(\n  underlyingProvider: null | DraftModeProvider\n): Promise<DraftMode> {\n  const instance = new DraftMode(underlyingProvider)\n  const promise = Promise.resolve(instance)\n\n  Object.defineProperty(promise, 'isEnabled', {\n    get() {\n      return instance.isEnabled\n    },\n    set(newValue) {\n      Object.defineProperty(promise, 'isEnabled', {\n        value: newValue,\n        writable: true,\n        enumerable: true,\n      })\n    },\n    enumerable: true,\n    configurable: true,\n  })\n  ;(promise as any).enable = instance.enable.bind(instance)\n  ;(promise as any).disable = instance.disable.bind(instance)\n\n  return promise\n}\n\nfunction createExoticDraftModeWithDevWarnings(\n  underlyingProvider: null | DraftModeProvider,\n  route: undefined | string\n): Promise<DraftMode> {\n  const instance = new DraftMode(underlyingProvider)\n  const promise = Promise.resolve(instance)\n\n  Object.defineProperty(promise, 'isEnabled', {\n    get() {\n      const expression = '`draftMode().isEnabled`'\n      syncIODev(route, expression)\n      return instance.isEnabled\n    },\n    set(newValue) {\n      Object.defineProperty(promise, 'isEnabled', {\n        value: newValue,\n        writable: true,\n        enumerable: true,\n      })\n    },\n    enumerable: true,\n    configurable: true,\n  })\n\n  Object.defineProperty(promise, 'enable', {\n    value: function get() {\n      const expression = '`draftMode().enable()`'\n      syncIODev(route, expression)\n      return instance.enable.apply(instance, arguments as any)\n    },\n  })\n\n  Object.defineProperty(promise, 'disable', {\n    value: function get() {\n      const expression = '`draftMode().disable()`'\n      syncIODev(route, expression)\n      return instance.disable.apply(instance, arguments as any)\n    },\n  })\n\n  return promise\n}\n\nclass DraftMode {\n  /**\n   * @internal - this declaration is stripped via `tsc --stripInternal`\n   */\n  private readonly _provider: null | DraftModeProvider\n\n  constructor(provider: null | DraftModeProvider) {\n    this._provider = provider\n  }\n  get isEnabled() {\n    if (this._provider !== null) {\n      return this._provider.isEnabled\n    }\n    return false\n  }\n  public enable() {\n    // We we have a store we want to track dynamic data access to ensure we\n    // don't statically generate routes that manipulate draft mode.\n    trackDynamicDraftMode('draftMode().enable()')\n    if (this._provider !== null) {\n      this._provider.enable()\n    }\n  }\n  public disable() {\n    trackDynamicDraftMode('draftMode().disable()')\n    if (this._provider !== null) {\n      this._provider.disable()\n    }\n  }\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst noop = () => {}\n\nconst warnForSyncAccess = process.env.__NEXT_DISABLE_SYNC_DYNAMIC_API_WARNINGS\n  ? noop\n  : createDedupedByCallsiteServerErrorLoggerDev(createDraftModeAccessError)\n\nfunction createDraftModeAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`draftMode()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction trackDynamicDraftMode(expression: string) {\n  const store = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (store) {\n    // We we have a store we want to track dynamic data access to ensure we\n    // don't statically generate routes that manipulate draft mode.\n    if (workUnitStore) {\n      if (workUnitStore.type === 'cache') {\n        throw new Error(\n          `Route ${store.route} used \"${expression}\" inside \"use cache\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n        )\n      } else if (workUnitStore.type === 'unstable-cache') {\n        throw new Error(\n          `Route ${store.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n        )\n      } else if (workUnitStore.phase === 'after') {\n        throw new Error(\n          `Route ${store.route} used \"${expression}\" inside \\`after\\`. The enabled status of draftMode can be read inside \\`after\\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`\n        )\n      }\n    }\n\n    if (store.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'prerender') {\n        // dynamicIO Prerender\n        const error = new Error(\n          `Route ${store.route} used ${expression} without first calling \\`await connection()\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`\n        )\n        abortAndThrowOnSynchronousRequestDataAccess(\n          store.route,\n          expression,\n          error,\n          workUnitStore\n        )\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // PPR Prerender\n        postponeWithTracking(\n          store.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        // legacy Prerender\n        workUnitStore.revalidate = 0\n\n        const err = new DynamicServerError(\n          `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n        )\n        store.dynamicUsageDescription = expression\n        store.dynamicUsageStack = err.stack\n\n        throw err\n      } else if (\n        process.env.NODE_ENV === 'development' &&\n        workUnitStore &&\n        workUnitStore.type === 'request'\n      ) {\n        workUnitStore.usedDynamic = true\n      }\n    }\n  }\n}\n"], "names": ["getExpectedRequestStore", "workAsyncStorage", "workUnitAsyncStorage", "abortAndThrowOnSynchronousRequestDataAccess", "postponeWithTracking", "trackSynchronousRequestDataAccessInDev", "createDedupedByCallsiteServerErrorLoggerDev", "StaticGenBailoutError", "DynamicServerError", "draftMode", "callingExpression", "workStore", "getStore", "workUnitStore", "type", "process", "env", "NODE_ENV", "isPrefetchRequest", "route", "createExoticDraftModeWithDevWarnings", "createExoticDraftMode", "requestStore", "cachedDraftMode", "CachedDraftModes", "get", "promise", "set", "WeakMap", "underlyingProvider", "instance", "DraftMode", "Promise", "resolve", "Object", "defineProperty", "isEnabled", "newValue", "value", "writable", "enumerable", "configurable", "enable", "bind", "disable", "expression", "syncIODev", "apply", "arguments", "constructor", "provider", "_provider", "trackDynamicDraftMode", "prerenderPhase", "warnForSyncAccess", "noop", "__NEXT_DISABLE_SYNC_DYNAMIC_API_WARNINGS", "createDraftModeAccessError", "prefix", "Error", "store", "phase", "dynamicShouldError", "error", "dynamicTracking", "revalidate", "err", "dynamicUsageDescription", "dynamicUsageStack", "stack", "usedDynamic"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,iDAAgD;AAIxF,SAASC,gBAAgB,QAAQ,4CAA2C;AAC5E,SAASC,oBAAoB,QAAQ,iDAAgD;AACrF,SACEC,2CAA2C,EAC3CC,oBAAoB,EACpBC,sCAAsC,QACjC,kCAAiC;AACxC,SAASC,2CAA2C,QAAQ,oDAAmD;AAC/G,SAASC,qBAAqB,QAAQ,oDAAmD;AACzF,SAASC,kBAAkB,QAAQ,+CAA8C;AAyBjF,OAAO,SAASC;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYV,iBAAiBW,QAAQ;IAC3C,MAAMC,gBAAgBX,qBAAqBU,QAAQ;IAEnD,IAAIC,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,oBACvBD,cAAcC,IAAI,KAAK,eACvBD,cAAcC,IAAI,KAAK,mBACvBD,cAAcC,IAAI,KAAK,oBACvB;YACA,0BAA0B;YAC1B,IACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,EAACN,6BAAAA,UAAWO,iBAAiB,GAC7B;gBACA,MAAMC,QAAQR,6BAAAA,UAAWQ,KAAK;gBAC9B,OAAOC,qCAAqC,MAAMD;YACpD,OAAO;gBACL,OAAOE,sBAAsB;YAC/B;QACF;IACF;IAEA,MAAMC,eAAetB,wBAAwBU;IAE7C,MAAMa,kBAAkBC,iBAAiBC,GAAG,CAACH,aAAab,SAAS;IACnE,IAAIc,iBAAiB;QACnB,OAAOA;IACT;IAEA,IAAIG;IACJ,IAAIX,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiB,EAACN,6BAAAA,UAAWO,iBAAiB,GAAE;QAC3E,MAAMC,QAAQR,6BAAAA,UAAWQ,KAAK;QAC9BO,UAAUN,qCACRE,aAAab,SAAS,EACtBU;IAEJ,OAAO;QACLO,UAAUL,sBAAsBC,aAAab,SAAS;IACxD;IACAe,iBAAiBG,GAAG,CAACL,aAAab,SAAS,EAAEiB;IAC7C,OAAOA;AACT;AAGA,MAAMF,mBAAmB,IAAII;AAE7B,SAASP,sBACPQ,kBAA4C;IAE5C,MAAMC,WAAW,IAAIC,UAAUF;IAC/B,MAAMH,UAAUM,QAAQC,OAAO,CAACH;IAEhCI,OAAOC,cAAc,CAACT,SAAS,aAAa;QAC1CD;YACE,OAAOK,SAASM,SAAS;QAC3B;QACAT,KAAIU,QAAQ;YACVH,OAAOC,cAAc,CAACT,SAAS,aAAa;gBAC1CY,OAAOD;gBACPE,UAAU;gBACVC,YAAY;YACd;QACF;QACAA,YAAY;QACZC,cAAc;IAChB;IACEf,QAAgBgB,MAAM,GAAGZ,SAASY,MAAM,CAACC,IAAI,CAACb;IAC9CJ,QAAgBkB,OAAO,GAAGd,SAASc,OAAO,CAACD,IAAI,CAACb;IAElD,OAAOJ;AACT;AAEA,SAASN,qCACPS,kBAA4C,EAC5CV,KAAyB;IAEzB,MAAMW,WAAW,IAAIC,UAAUF;IAC/B,MAAMH,UAAUM,QAAQC,OAAO,CAACH;IAEhCI,OAAOC,cAAc,CAACT,SAAS,aAAa;QAC1CD;YACE,MAAMoB,aAAa;YACnBC,UAAU3B,OAAO0B;YACjB,OAAOf,SAASM,SAAS;QAC3B;QACAT,KAAIU,QAAQ;YACVH,OAAOC,cAAc,CAACT,SAAS,aAAa;gBAC1CY,OAAOD;gBACPE,UAAU;gBACVC,YAAY;YACd;QACF;QACAA,YAAY;QACZC,cAAc;IAChB;IAEAP,OAAOC,cAAc,CAACT,SAAS,UAAU;QACvCY,OAAO,SAASb;YACd,MAAMoB,aAAa;YACnBC,UAAU3B,OAAO0B;YACjB,OAAOf,SAASY,MAAM,CAACK,KAAK,CAACjB,UAAUkB;QACzC;IACF;IAEAd,OAAOC,cAAc,CAACT,SAAS,WAAW;QACxCY,OAAO,SAASb;YACd,MAAMoB,aAAa;YACnBC,UAAU3B,OAAO0B;YACjB,OAAOf,SAASc,OAAO,CAACG,KAAK,CAACjB,UAAUkB;QAC1C;IACF;IAEA,OAAOtB;AACT;AAEA,MAAMK;IAMJkB,YAAYC,QAAkC,CAAE;QAC9C,IAAI,CAACC,SAAS,GAAGD;IACnB;IACA,IAAId,YAAY;QACd,IAAI,IAAI,CAACe,SAAS,KAAK,MAAM;YAC3B,OAAO,IAAI,CAACA,SAAS,CAACf,SAAS;QACjC;QACA,OAAO;IACT;IACOM,SAAS;QACd,uEAAuE;QACvE,+DAA+D;QAC/DU,sBAAsB;QACtB,IAAI,IAAI,CAACD,SAAS,KAAK,MAAM;YAC3B,IAAI,CAACA,SAAS,CAACT,MAAM;QACvB;IACF;IACOE,UAAU;QACfQ,sBAAsB;QACtB,IAAI,IAAI,CAACD,SAAS,KAAK,MAAM;YAC3B,IAAI,CAACA,SAAS,CAACP,OAAO;QACxB;IACF;AACF;AAEA,SAASE,UAAU3B,KAAyB,EAAE0B,UAAkB;IAC9D,MAAMhC,gBAAgBX,qBAAqBU,QAAQ;IACnD,IACEC,iBACAA,cAAcC,IAAI,KAAK,aACvBD,cAAcwC,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAM/B,eAAeT;QACrBR,uCAAuCiB;IACzC;IACA,gCAAgC;IAChCgC,kBAAkBnC,OAAO0B;AAC3B;AAEA,MAAMU,OAAO,KAAO;AAEpB,MAAMD,oBAAoBvC,QAAQC,GAAG,CAACwC,wCAAwC,GAC1ED,OACAjD,4CAA4CmD;AAEhD,SAASA,2BACPtC,KAAyB,EACzB0B,UAAkB;IAElB,MAAMa,SAASvC,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,IAAIwC,MACT,GAAGD,OAAO,KAAK,EAAEb,WAAW,EAAE,CAAC,GAC7B,CAAC,0DAA0D,CAAC,GAC5D,CAAC,8DAA8D,CAAC;AAEtE;AAEA,SAASO,sBAAsBP,UAAkB;IAC/C,MAAMe,QAAQ3D,iBAAiBW,QAAQ;IACvC,MAAMC,gBAAgBX,qBAAqBU,QAAQ;IACnD,IAAIgD,OAAO;QACT,uEAAuE;QACvE,+DAA+D;QAC/D,IAAI/C,eAAe;YACjB,IAAIA,cAAcC,IAAI,KAAK,SAAS;gBAClC,MAAM,IAAI6C,MACR,CAAC,MAAM,EAAEC,MAAMzC,KAAK,CAAC,OAAO,EAAE0B,WAAW,uNAAuN,CAAC;YAErQ,OAAO,IAAIhC,cAAcC,IAAI,KAAK,kBAAkB;gBAClD,MAAM,IAAI6C,MACR,CAAC,MAAM,EAAEC,MAAMzC,KAAK,CAAC,OAAO,EAAE0B,WAAW,gQAAgQ,CAAC;YAE9S,OAAO,IAAIhC,cAAcgD,KAAK,KAAK,SAAS;gBAC1C,MAAM,IAAIF,MACR,CAAC,MAAM,EAAEC,MAAMzC,KAAK,CAAC,OAAO,EAAE0B,WAAW,0MAA0M,CAAC;YAExP;QACF;QAEA,IAAIe,MAAME,kBAAkB,EAAE;YAC5B,MAAM,IAAIvD,sBACR,CAAC,MAAM,EAAEqD,MAAMzC,KAAK,CAAC,8EAA8E,EAAE0B,WAAW,4HAA4H,CAAC;QAEjP;QAEA,IAAIhC,eAAe;YACjB,IAAIA,cAAcC,IAAI,KAAK,aAAa;gBACtC,sBAAsB;gBACtB,MAAMiD,QAAQ,IAAIJ,MAChB,CAAC,MAAM,EAAEC,MAAMzC,KAAK,CAAC,MAAM,EAAE0B,WAAW,+HAA+H,CAAC;gBAE1K1C,4CACEyD,MAAMzC,KAAK,EACX0B,YACAkB,OACAlD;YAEJ,OAAO,IAAIA,cAAcC,IAAI,KAAK,iBAAiB;gBACjD,gBAAgB;gBAChBV,qBACEwD,MAAMzC,KAAK,EACX0B,YACAhC,cAAcmD,eAAe;YAEjC,OAAO,IAAInD,cAAcC,IAAI,KAAK,oBAAoB;gBACpD,mBAAmB;gBACnBD,cAAcoD,UAAU,GAAG;gBAE3B,MAAMC,MAAM,IAAI1D,mBACd,CAAC,MAAM,EAAEoD,MAAMzC,KAAK,CAAC,mDAAmD,EAAE0B,WAAW,6EAA6E,CAAC;gBAErKe,MAAMO,uBAAuB,GAAGtB;gBAChCe,MAAMQ,iBAAiB,GAAGF,IAAIG,KAAK;gBAEnC,MAAMH;YACR,OAAO,IACLnD,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBJ,iBACAA,cAAcC,IAAI,KAAK,WACvB;gBACAD,cAAcyD,WAAW,GAAG;YAC9B;QACF;IACF;AACF"}