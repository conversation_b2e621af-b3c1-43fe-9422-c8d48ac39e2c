@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-white text-gray-800;
  }
}

.ant-message {
  z-index: 9999 !important;
}

/* Sliding Panel z-index override to ensure it's above all UI elements including navbar */
.fixed.inset-0.z-\[9999\] {
  z-index: 9999 !important;
}

/* Ensure slide panels are above everything */
[class*="z-[9999]"] {
  z-index: 9999 !important;
}

/* Ensure slide panel content is properly positioned and visible */
.fixed.inset-0.z-\[9999\] .absolute.top-0.right-0.bottom-0 {
  z-index: 9999 !important;
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

/* Override any potential conflicts with sidebar or navbar positioning */
.fixed.inset-0.z-\[9999\] {
  left: 0 !important;
  right: 0 !important;
  top: 0 !important;
  bottom: 0 !important;
}

/* Light mode input styles */
.ant-input,
.ant-input-affix-wrapper,
.ant-select-selector,
.ant-select-selection-item,
.ant-select-selection-placeholder,
.ant-select-dropdown,
.ant-select-item,
.ant-input-number-input {
  color: #333 !important;
}

.ant-input::placeholder,
.ant-input-number-input::placeholder {
  color: rgba(0, 0, 0, 0.45) !important;
}

.ant-input-disabled,
.ant-input[disabled] {
  color: rgba(0, 0, 0, 0.25) !important;
  background-color: #f5f5f5 !important;
}

/* Custom dropdown styles */
.payment-dropdown-dark .ant-select-item {
  background-color: #ffffff !important;
  color: #333 !important;
}

.payment-dropdown-dark .ant-select-item-option-active,
.payment-dropdown-dark .ant-select-item-option-selected {
  background-color: #f5f5f5 !important;
}

.payment-dropdown-dark .ant-select-item-option-content {
  color: #333 !important;
}

/* Global select dropdown styles */
.ant-select-dropdown {
  background-color: #ffffff !important;
  border: 1px solid #d9d9d9 !important;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08) !important;
}

.ant-select-dropdown-hidden {
  display: none;
}

.ant-select-dropdown-placement-bottomLeft {
  background-color: #ffffff !important;
}

.ant-select-dropdown .ant-select-item {
  background-color: #ffffff !important;
  color: #333 !important;
}

.ant-select-dropdown .ant-select-item-option-content {
  color: #333 !important;
}

.ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: #f5f5f5 !important;
  color: #333 !important;
}

.ant-select-dropdown .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  background-color: #f5f5f5 !important;
  color: #333 !important;
}

/* Additional fixes for dropdown */
.ant-select-dropdown-menu {
  background-color: #ffffff !important;
}

.ant-select-dropdown-menu-item {
  color: #333 !important;
  background-color: #ffffff !important;
}

.ant-select-dropdown-menu-item:hover,
.ant-select-dropdown-menu-item-active {
  background-color: #f5f5f5 !important;
}

/* Tab styles */
.ant-tabs-tab {
  color: #333 !important;
}

.ant-tabs-tab:hover {
  color: #1890ff !important;
}

.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #1890ff !important;
  font-weight: 600 !important;
}

.ant-tabs-ink-bar {
  background-color: #1890ff !important;
}

.ant-tabs-nav::before {
  border-bottom-color: #e8e8e8 !important;
}

/* Payment tabs specific styles */
.payment-tabs .ant-tabs-nav-list {
  margin-left: 16px;
}

.payment-tabs .ant-tabs-tab {
  padding: 12px 16px !important;
  color: #333 !important;
  opacity: 0.8;
}

.payment-tabs .ant-tabs-tab:hover {
  color: #1890ff !important;
  opacity: 1;
}

.payment-tabs .ant-tabs-tab.ant-tabs-tab-active {
  opacity: 1;
}

.payment-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #1890ff !important;
  font-weight: 600 !important;
}

/* Fix for select input */
.ant-select-selector {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
}

.ant-select-selection-item {
  color: #333 !important;
}

.ant-select-arrow {
  color: rgba(0, 0, 0, 0.25) !important;
}

.ant-select-focused .ant-select-selector,
.ant-select-selector:hover,
.ant-select-selector:focus,
.ant-select:hover .ant-select-selector {
  border-color: #40a9ff !important;
  background-color: #ffffff !important;
}

@layer utilities {
  /* Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

   .custom-scrollbar {
    @apply overflow-auto;
  }

  .custom-scrollbar::-webkit-scrollbar {
    @apply size-2;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply relative flex-1 rounded-full bg-neutral-200/40;
  }


  .chat-height {
    @apply h-[calc(100vh_-_8.125rem)] lg:h-[calc(100vh_-_5.625rem)];
  }

  .inbox-height {
    @apply h-[calc(100vh_-_8.125rem)] lg:h-[calc(100vh_-_5.625rem)];
  }
}

/* third-party libraries CSS */

.tableCheckbox:checked ~ div span {
  @apply opacity-100;
}

.tableCheckbox:checked ~ div {
  @apply border-primary bg-primary;
}

.apexcharts-legend-text {
  @apply !text-gray-700;
}

.apexcharts-text {
  @apply !fill-gray-700;
}

.apexcharts-yaxis-label {
  @apply text-xs;
}

.apexcharts-xaxis-label {
  @apply text-body-sm font-medium;
}

.apexcharts-xcrosshairs {
  @apply !fill-stroke;
}

.apexcharts-gridline {
  @apply !stroke-stroke;
}

.apexcharts-series.apexcharts-pie-series path {
  @apply !stroke-white;
}

.apexcharts-legend-series {
  @apply !inline-flex gap-1.5;
}

.apexcharts-tooltip.apexcharts-theme-light {
  @apply !rounded-[7px] !text-base !font-medium !text-dark !shadow-card-2;
}

.apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title {
  @apply !border-gray-3 !bg-gray-2;
}

.apexcharts-xaxistooltip,
.apexcharts-yaxistooltip {
  @apply !border-gray-3 !bg-gray-2 !text-dark-5;
}

.apexcharts-xaxistooltip-bottom:after {
  @apply !border-b-gray;
}

.apexcharts-xaxistooltip-bottom:before {
  @apply !border-b-gray;
}

.apexcharts-xaxistooltip-bottom {
  @apply !rounded !border-none !bg-gray !text-sm !font-medium !text-dark;
}

.apexcharts-tooltip-series-group {
  @apply !pb-px !pl-2.5 !pr-3.5;
}

.apexcharts-tooltip-series-group .apexcharts-tooltip-text {
  @apply !text-base !font-medium;
}

.apexcharts-datalabels-group .apexcharts-datalabel-label {
  @apply !fill-gray-700;
}

.apexcharts-datalabels-group .apexcharts-datalabel-value {
  @apply !fill-gray-800;
}

.flatpickr-wrapper {
  @apply w-full;
}

.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
  @apply !fill-primary;
}

.flatpickr-calendar.arrowTop:before {
  @apply !border-b-gray-3;
}

.flatpickr-calendar.arrowTop:after {
  @apply !border-b-gray-3;
}

.flatpickr-calendar {
  @apply !p-6 !shadow-3 !bg-white !text-dark-5 2xsm:!w-auto;
}

.flatpickr-day {
  @apply !text-dark-5 hover:!border-gray-3 hover:!bg-gray-2;
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  @apply !top-7 !fill-dark-5 !text-dark-5;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month.flatpickr-prev-month {
  @apply !left-7;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,
.flatpickr-months .flatpickr-next-month.flatpickr-next-month {
  @apply !right-7;
}

span.flatpickr-weekday,
.flatpickr-months .flatpickr-month {
  @apply !fill-dark-5 !text-dark-5;
}

.flatpickr-day.inRange {
  box-shadow:
    -5px 0 0 #f3f4f6,
    5px 0 0 #f3f4f6 !important;
}

.flatpickr-day.inRange,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.today.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day:hover,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day:focus,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.nextMonthDay:focus {
  @apply !border-gray-2 !bg-gray-2;
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.selected,
.flatpickr-day.endRange {
  @apply !text-white;
}

.flatpickr-day.today {
  @apply !border-none hover:!bg-gray-2 hover:!text-dark;
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
  background: #3c50e0;
  @apply !border-primary !bg-primary hover:!border-primary hover:!bg-primary;
}

.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n + 1)) {
  box-shadow: -10px 0 0 #3c50e0;
}

.map-btn .jvm-zoom-btn {
  @apply flex h-7.5 w-7.5 items-center justify-center rounded border border-stroke bg-white px-0 pb-0.5 pt-0 text-2xl leading-none text-dark-5 hover:border-primary hover:bg-primary hover:text-white;
}

.mapOne .jvm-zoom-btn {
  @apply !bottom-0 !left-auto !top-auto;
}

.mapOne .jvm-zoom-btn.jvm-zoomin {
  @apply !right-10;
}

.mapOne .jvm-zoom-btn.jvm-zoomout {
  @apply !right-0;
}

.mapTwo .jvm-zoom-btn {
  @apply !bottom-0 !top-auto;
}

.mapTwo .jvm-zoom-btn.jvm-zoomin {
  @apply !left-0;
}

.mapTwo .jvm-zoom-btn.jvm-zoomout {
  @apply !left-10;
}

.taskCheckbox:checked ~ .box span {
  @apply opacity-100;
}

.taskCheckbox:checked ~ p {
  @apply line-through;
}

.taskCheckbox:checked ~ .box {
  @apply border-primary bg-primary;
}

.custom-input-date::-webkit-calendar-picker-indicator {
  background: transparent;
}

.data-stats-slider-outer .swiper-button-next:after,
.data-stats-slider-outer .swiper-button-prev:after,
.carouselOne .swiper-button-next:after,
.carouselOne .swiper-button-prev:after,
.carouselThree .swiper-button-next:after,
.carouselThree .swiper-button-prev:after {
  @apply hidden;
}

.data-stats-slider-outer .swiper-button-next svg,
.data-stats-slider-outer .swiper-button-prev svg,
.carouselOne .swiper-button-next svg,
.carouselOne .swiper-button-prev svg,
.carouselThree .swiper-button-next svg,
.carouselThree .swiper-button-prev svg {
  @apply size-auto;
}

.carouselOne .swiper-button-next,
.carouselOne .swiper-button-prev,
.carouselThree .swiper-button-next,
.carouselThree .swiper-button-prev {
  @apply h-10 w-10 rounded-full bg-white !text-dark-5 shadow-card-2 sm:h-12.5 sm:w-12.5;
}

.carouselOne .swiper-button-prev,
.carouselThree .swiper-button-prev {
  @apply sm:!left-10;
}

.carouselOne .swiper-button-next,
.carouselThree .swiper-button-next {
  @apply sm:!right-10;
}

.carouselTwo .swiper-pagination-bullet,
.carouselThree .swiper-pagination-bullet {
  @apply h-[5px] w-7.5 rounded-none bg-white/50;
}

.carouselTwo .swiper-pagination-bullet-active,
.carouselThree .swiper-pagination-bullet-active {
  @apply bg-white;
}

.carouselTwo .swiper-pagination,
.carouselThree .swiper-pagination {
  @apply xl:!bottom-8;
}

.data-stats-slider-outer .swiper-button-next,
.data-stats-slider-outer .swiper-button-prev {
  @apply top-1/2 h-11.5 w-11.5 rounded-full border border-stroke bg-white !text-dark drop-shadow-card;
}

.data-stats-slider-outer .swiper-button-next.swiper-button-disabled,
.data-stats-slider-outer .swiper-button-prev.swiper-button-disabled {
  @apply hidden;
}

.data-stats-slider-outer .swiper-button-prev {
  @apply -left-[23px];
}

.data-stats-slider-outer .swiper-button-next {
  @apply -right-[23px];
}

.data-table-common .datatable-search {
  @apply relative !ml-0 w-100 overflow-hidden rounded;
}

.data-table-one .datatable-search input {
  @apply h-[46px] w-full rounded border border-stroke bg-transparent px-5 outline-none focus:border-primary;
}

.data-table-common .datatable-selector {
  @apply relative z-20 inline-flex bg-transparent p-0 font-medium text-dark-5 outline-none;
}

.data-table-common .datatable-top {
  @apply flex flex-col gap-4 border-b border-stroke px-7.5 py-4.5 after:hidden sm:flex-row-reverse sm:items-center sm:justify-between sm:gap-x-4 sm:gap-y-0;
}

.data-table-common .datatable-dropdown label {
  @apply inline-flex items-center gap-2.5 font-medium capitalize text-dark;
}

.datatable-table .datatable-sorter {
  @apply before:hidden after:hidden;
}

.datatable-table > thead > tr:first-child > th {
  @apply border-transparent px-2.5 pb-2.5 pt-9 font-medium;
}

.data-table-common .datatable-table > tbody > tr > td:first-child,
.data-table-common .datatable-table > thead > tr > th:first-child {
  @apply pl-8;
}

.data-table-common .datatable-table > tbody > tr > td:last-child,
.data-table-common .datatable-table > thead > tr > th:last-child {
  @apply pr-8;
}

.data-table-common .datatable-table > thead > tr:last-child > th {
  @apply !border-b border-stroke pb-6;
}

.data-table-common .datatable-table > thead > tr:last-child > th input {
  @apply h-[34px] w-full rounded border border-stroke bg-transparent px-3 text-sm outline-none focus:border-primary;
}

.data-table-common .datatable-table > tbody > tr {
  @apply hover:bg-primary hover:bg-opacity-5;
}

.data-table-one .datatable-table > tbody > tr > td:first-child {
  @apply text-primary;
}

.data-table-common .datatable-table > tbody > tr > td {
  @apply border-b border-stroke py-5 font-medium;
}

.data-table-one .datatable-bottom {
  @apply flex flex-col gap-4 px-8 py-7 after:hidden sm:flex-row-reverse sm:items-center sm:justify-between sm:gap-0 sm:space-x-4;
}

.data-table-common .datatable-wrapper.no-footer .datatable-container {
  @apply border-none;
}

.data-table-common .datatable-info {
  @apply !m-0 font-medium;
}

.data-table-common .datatable-pagination {
  @apply !m-0;
}

.data-table-common .datatable-pagination a {
  @apply flex h-8 w-8 cursor-pointer items-center justify-center rounded p-0 font-medium text-dark-5 hover:bg-primary hover:text-white dark:text-dark-6;
}

.data-table-common .datatable-pagination .datatable-active a {
  @apply bg-primary text-white;
}

.data-table-common .datatable-pagination li.datatable-hidden {
  @apply !visible;
}

.data-table-two .datatable-bottom {
  @apply flex flex-col gap-4 px-8 py-7 after:hidden sm:flex-row sm:items-center sm:justify-between sm:gap-0 sm:space-x-4;
}

.data-table-two .datatable-search input {
  @apply h-11.5 w-full rounded border border-stroke bg-gray-2 px-5 outline-none focus:border-primary;
}

.rangeSliderCommon .noUi-target {
  @apply border-none bg-transparent shadow-none;
}

.rangeSliderCommon .noUi-connects {
  @apply h-1.5 rounded-full bg-stroke;
}

.rangeSliderCommon .noUi-connect {
  @apply h-1.5 rounded-full bg-primary;
}

.rangeSliderOne .noUi-horizontal .noUi-handle {
  @apply -top-2 h-5.5 w-5.5 rounded-full border-none bg-primary shadow-none;
}

.rangeSliderTwo .noUi-horizontal .noUi-handle {
  @apply -top-2 h-6 w-6 rounded-full border-[6px] border-primary bg-white shadow-none;
}

.noUi-horizontal .noUi-handle:after,
.noUi-horizontal .noUi-handle:before {
  @apply hidden;
}

input[type="search"]::-webkit-search-cancel-button {
  @apply appearance-none;
}

.custom-input-date::-webkit-calendar-picker-indicator {
  background-position: center;
  background-repeat: no-repeat;
  background-size: 20px;
}

.custom-gradient-1 {
  background-image: linear-gradient(145deg, #eef 0%, #fff8fc 100%);
}
