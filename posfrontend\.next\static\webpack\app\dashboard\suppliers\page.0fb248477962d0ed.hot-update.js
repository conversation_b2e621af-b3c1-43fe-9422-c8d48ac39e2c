"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/suppliers/page",{

/***/ "(app-pages-browser)/./src/components/ui/SlidingPanel.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/SlidingPanel.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_CloseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CloseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CloseOutlined.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst SlidingPanel = (param)=>{\n    let { isOpen, onClose, title, children, width = \"400px\", footer } = param;\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRendered, setIsRendered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [windowWidth, setWindowWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)( true ? window.innerWidth : 0);\n    // Handle window resize\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SlidingPanel.useEffect\": ()=>{\n            const handleResize = {\n                \"SlidingPanel.useEffect.handleResize\": ()=>{\n                    setWindowWidth(window.innerWidth);\n                }\n            }[\"SlidingPanel.useEffect.handleResize\"];\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"SlidingPanel.useEffect\": ()=>{\n                    window.removeEventListener('resize', handleResize);\n                }\n            })[\"SlidingPanel.useEffect\"];\n        }\n    }[\"SlidingPanel.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SlidingPanel.useEffect\": ()=>{\n            console.log(\"SlidingPanel - isOpen changed:\", isOpen, \"title:\", title);\n            if (isOpen) {\n                setIsRendered(true);\n                console.log(\"SlidingPanel - Setting isRendered to true\");\n                // Small delay to ensure the panel is rendered before animating\n                setTimeout({\n                    \"SlidingPanel.useEffect\": ()=>{\n                        setIsVisible(true);\n                        console.log(\"SlidingPanel - Setting isVisible to true\");\n                    }\n                }[\"SlidingPanel.useEffect\"], 50);\n            } else {\n                setIsVisible(false);\n                console.log(\"SlidingPanel - Setting isVisible to false\");\n                // Wait for animation to complete before unmounting\n                const timer = setTimeout({\n                    \"SlidingPanel.useEffect.timer\": ()=>{\n                        setIsRendered(false);\n                        console.log(\"SlidingPanel - Setting isRendered to false\");\n                    }\n                }[\"SlidingPanel.useEffect.timer\"], 300);\n                return ({\n                    \"SlidingPanel.useEffect\": ()=>clearTimeout(timer)\n                })[\"SlidingPanel.useEffect\"];\n            }\n        }\n    }[\"SlidingPanel.useEffect\"], [\n        isOpen,\n        title\n    ]);\n    if (!isRendered) return null;\n    // Calculate responsive width based on screen size\n    const getResponsiveWidth = ()=>{\n        // For small screens (mobile), use full width\n        if (windowWidth < 640) {\n            return '100vw'; // 100% of viewport width\n        }\n        // For medium screens\n        if (windowWidth < 1024) {\n            return '450px';\n        }\n        // For larger screens, use the provided width or default\n        return width;\n    };\n    // Render the panel using a portal to ensure it's at the body level\n    const panelContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-[99999] overflow-hidden\",\n        style: {\n            zIndex: 99999\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black transition-opacity duration-300 \".concat(isVisible ? \"opacity-50\" : \"opacity-0\"),\n                onClick: onClose,\n                style: {\n                    zIndex: 99999\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform \".concat(isVisible ? \"translate-x-0\" : \"translate-x-full\"),\n                style: {\n                    width: getResponsiveWidth(),\n                    zIndex: 99999\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-medium text-gray-800 truncate\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_antd__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                type: \"text\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    style: {\n                                        color: '#333'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 19\n                                }, void 0),\n                                onClick: onClose,\n                                \"aria-label\": \"Close panel\",\n                                style: {\n                                    color: '#333',\n                                    borderColor: 'transparent',\n                                    background: 'transparent'\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto p-4 pt-6 bg-white\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, undefined),\n                    footer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-200 bg-gray-50\",\n                        children: footer\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SlidingPanel, \"sb9mljCr9lGLLPaevMatKyoxyc4=\");\n_c = SlidingPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SlidingPanel);\nvar _c;\n$RefreshReg$(_c, \"SlidingPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL1NsaWRpbmdQYW5lbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFbUQ7QUFFckI7QUFDb0I7QUFXbEQsTUFBTUssZUFBNEM7UUFBQyxFQUNqREMsTUFBTSxFQUNOQyxPQUFPLEVBQ1BDLEtBQUssRUFDTEMsUUFBUSxFQUNSQyxRQUFRLE9BQU8sRUFDZkMsTUFBTSxFQUNQOztJQUNDLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHWCwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNZLFlBQVlDLGNBQWMsR0FBR2IsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDYyxhQUFhQyxlQUFlLEdBQUdmLCtDQUFRQSxDQUM1QyxLQUE2QixHQUFHZ0IsT0FBT0MsVUFBVSxHQUFHLENBQUk7SUFHMUQsdUJBQXVCO0lBQ3ZCbEIsZ0RBQVNBO2tDQUFDO1lBQ1IsTUFBTW1CO3VEQUFlO29CQUNuQkgsZUFBZUMsT0FBT0MsVUFBVTtnQkFDbEM7O1lBRUFELE9BQU9HLGdCQUFnQixDQUFDLFVBQVVEO1lBQ2xDOzBDQUFPO29CQUNMRixPQUFPSSxtQkFBbUIsQ0FBQyxVQUFVRjtnQkFDdkM7O1FBQ0Y7aUNBQUcsRUFBRTtJQUVMbkIsZ0RBQVNBO2tDQUFDO1lBQ1JzQixRQUFRQyxHQUFHLENBQUMsa0NBQWtDbEIsUUFBUSxVQUFVRTtZQUNoRSxJQUFJRixRQUFRO2dCQUNWUyxjQUFjO2dCQUNkUSxRQUFRQyxHQUFHLENBQUM7Z0JBQ1osK0RBQStEO2dCQUMvREM7OENBQVc7d0JBQ1RaLGFBQWE7d0JBQ2JVLFFBQVFDLEdBQUcsQ0FBQztvQkFDZDs2Q0FBRztZQUNMLE9BQU87Z0JBQ0xYLGFBQWE7Z0JBQ2JVLFFBQVFDLEdBQUcsQ0FBQztnQkFDWixtREFBbUQ7Z0JBQ25ELE1BQU1FLFFBQVFEO29EQUFXO3dCQUN2QlYsY0FBYzt3QkFDZFEsUUFBUUMsR0FBRyxDQUFDO29CQUNkO21EQUFHO2dCQUNIOzhDQUFPLElBQU1HLGFBQWFEOztZQUM1QjtRQUNGO2lDQUFHO1FBQUNwQjtRQUFRRTtLQUFNO0lBRWxCLElBQUksQ0FBQ00sWUFBWSxPQUFPO0lBRXhCLGtEQUFrRDtJQUNsRCxNQUFNYyxxQkFBcUI7UUFDekIsNkNBQTZDO1FBQzdDLElBQUlaLGNBQWMsS0FBSztZQUNyQixPQUFPLFNBQVMseUJBQXlCO1FBQzNDO1FBQ0EscUJBQXFCO1FBQ3JCLElBQUlBLGNBQWMsTUFBTTtZQUN0QixPQUFPO1FBQ1Q7UUFDQSx3REFBd0Q7UUFDeEQsT0FBT047SUFDVDtJQUVBLG1FQUFtRTtJQUNuRSxNQUFNbUIsNkJBQ0osOERBQUNDO1FBQUlDLFdBQVU7UUFBMENDLE9BQU87WUFBRUMsUUFBUTtRQUFNOzswQkFFOUUsOERBQUNIO2dCQUNDQyxXQUFXLDZEQUVWLE9BRENuQixZQUFZLGVBQWU7Z0JBRTdCc0IsU0FBUzNCO2dCQUNUeUIsT0FBTztvQkFBRUMsUUFBUTtnQkFBTTs7Ozs7OzBCQUl6Qiw4REFBQ0g7Z0JBQ0NDLFdBQVcsMElBRVYsT0FEQ25CLFlBQVksa0JBQWtCO2dCQUVoQ29CLE9BQU87b0JBQUV0QixPQUFPa0I7b0JBQXNCSyxRQUFRO2dCQUFNOztrQ0FHcEQsOERBQUNIO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0k7Z0NBQUdKLFdBQVU7MENBQThDdkI7Ozs7OzswQ0FDNUQsOERBQUNMLDBFQUFNQTtnQ0FDTGlDLE1BQUs7Z0NBQ0xDLG9CQUFNLDhEQUFDakMsNkZBQWFBO29DQUFDNEIsT0FBTzt3Q0FBRU0sT0FBTztvQ0FBTzs7Ozs7O2dDQUM1Q0osU0FBUzNCO2dDQUNUZ0MsY0FBVztnQ0FDWFAsT0FBTztvQ0FBRU0sT0FBTztvQ0FBUUUsYUFBYTtvQ0FBZUMsWUFBWTtnQ0FBYzs7Ozs7Ozs7Ozs7O2tDQUtsRiw4REFBQ1g7d0JBQUlDLFdBQVU7a0NBQ1p0Qjs7Ozs7O29CQUlGRSx3QkFDQyw4REFBQ21CO3dCQUFJQyxXQUFVO2tDQUNacEI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1iO0dBN0dNTjtLQUFBQTtBQStHTixpRUFBZUEsWUFBWUEsRUFBQyIsInNvdXJjZXMiOlsiRTpcXFBST0pFQ1RTXFxwb3NcXHBvc2Zyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxTbGlkaW5nUGFuZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgY3JlYXRlUG9ydGFsIH0gZnJvbSBcInJlYWN0LWRvbVwiO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcImFudGRcIjtcbmltcG9ydCB7IENsb3NlT3V0bGluZWQgfSBmcm9tIFwiQGFudC1kZXNpZ24vaWNvbnNcIjtcblxuaW50ZXJmYWNlIFNsaWRpbmdQYW5lbFByb3BzIHtcbiAgaXNPcGVuOiBib29sZWFuO1xuICBvbkNsb3NlOiAoKSA9PiB2b2lkO1xuICB0aXRsZTogc3RyaW5nO1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xuICB3aWR0aD86IHN0cmluZyB8IG51bWJlcjtcbiAgZm9vdGVyPzogUmVhY3QuUmVhY3ROb2RlO1xufVxuXG5jb25zdCBTbGlkaW5nUGFuZWw6IFJlYWN0LkZDPFNsaWRpbmdQYW5lbFByb3BzPiA9ICh7XG4gIGlzT3BlbixcbiAgb25DbG9zZSxcbiAgdGl0bGUsXG4gIGNoaWxkcmVuLFxuICB3aWR0aCA9IFwiNDAwcHhcIixcbiAgZm9vdGVyLFxufSkgPT4ge1xuICBjb25zdCBbaXNWaXNpYmxlLCBzZXRJc1Zpc2libGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNSZW5kZXJlZCwgc2V0SXNSZW5kZXJlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFt3aW5kb3dXaWR0aCwgc2V0V2luZG93V2lkdGhdID0gdXNlU3RhdGUoXG4gICAgdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyB3aW5kb3cuaW5uZXJXaWR0aCA6IDEwMjRcbiAgKTtcblxuICAvLyBIYW5kbGUgd2luZG93IHJlc2l6ZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZVJlc2l6ZSA9ICgpID0+IHtcbiAgICAgIHNldFdpbmRvd1dpZHRoKHdpbmRvdy5pbm5lcldpZHRoKTtcbiAgICB9O1xuXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIGhhbmRsZVJlc2l6ZSk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCBoYW5kbGVSZXNpemUpO1xuICAgIH07XG4gIH0sIFtdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnNvbGUubG9nKFwiU2xpZGluZ1BhbmVsIC0gaXNPcGVuIGNoYW5nZWQ6XCIsIGlzT3BlbiwgXCJ0aXRsZTpcIiwgdGl0bGUpO1xuICAgIGlmIChpc09wZW4pIHtcbiAgICAgIHNldElzUmVuZGVyZWQodHJ1ZSk7XG4gICAgICBjb25zb2xlLmxvZyhcIlNsaWRpbmdQYW5lbCAtIFNldHRpbmcgaXNSZW5kZXJlZCB0byB0cnVlXCIpO1xuICAgICAgLy8gU21hbGwgZGVsYXkgdG8gZW5zdXJlIHRoZSBwYW5lbCBpcyByZW5kZXJlZCBiZWZvcmUgYW5pbWF0aW5nXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgc2V0SXNWaXNpYmxlKHRydWUpO1xuICAgICAgICBjb25zb2xlLmxvZyhcIlNsaWRpbmdQYW5lbCAtIFNldHRpbmcgaXNWaXNpYmxlIHRvIHRydWVcIik7XG4gICAgICB9LCA1MCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHNldElzVmlzaWJsZShmYWxzZSk7XG4gICAgICBjb25zb2xlLmxvZyhcIlNsaWRpbmdQYW5lbCAtIFNldHRpbmcgaXNWaXNpYmxlIHRvIGZhbHNlXCIpO1xuICAgICAgLy8gV2FpdCBmb3IgYW5pbWF0aW9uIHRvIGNvbXBsZXRlIGJlZm9yZSB1bm1vdW50aW5nXG4gICAgICBjb25zdCB0aW1lciA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBzZXRJc1JlbmRlcmVkKGZhbHNlKTtcbiAgICAgICAgY29uc29sZS5sb2coXCJTbGlkaW5nUGFuZWwgLSBTZXR0aW5nIGlzUmVuZGVyZWQgdG8gZmFsc2VcIik7XG4gICAgICB9LCAzMDApO1xuICAgICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lcik7XG4gICAgfVxuICB9LCBbaXNPcGVuLCB0aXRsZV0pO1xuXG4gIGlmICghaXNSZW5kZXJlZCkgcmV0dXJuIG51bGw7XG5cbiAgLy8gQ2FsY3VsYXRlIHJlc3BvbnNpdmUgd2lkdGggYmFzZWQgb24gc2NyZWVuIHNpemVcbiAgY29uc3QgZ2V0UmVzcG9uc2l2ZVdpZHRoID0gKCkgPT4ge1xuICAgIC8vIEZvciBzbWFsbCBzY3JlZW5zIChtb2JpbGUpLCB1c2UgZnVsbCB3aWR0aFxuICAgIGlmICh3aW5kb3dXaWR0aCA8IDY0MCkge1xuICAgICAgcmV0dXJuICcxMDB2dyc7IC8vIDEwMCUgb2Ygdmlld3BvcnQgd2lkdGhcbiAgICB9XG4gICAgLy8gRm9yIG1lZGl1bSBzY3JlZW5zXG4gICAgaWYgKHdpbmRvd1dpZHRoIDwgMTAyNCkge1xuICAgICAgcmV0dXJuICc0NTBweCc7XG4gICAgfVxuICAgIC8vIEZvciBsYXJnZXIgc2NyZWVucywgdXNlIHRoZSBwcm92aWRlZCB3aWR0aCBvciBkZWZhdWx0XG4gICAgcmV0dXJuIHdpZHRoO1xuICB9O1xuXG4gIC8vIFJlbmRlciB0aGUgcGFuZWwgdXNpbmcgYSBwb3J0YWwgdG8gZW5zdXJlIGl0J3MgYXQgdGhlIGJvZHkgbGV2ZWxcbiAgY29uc3QgcGFuZWxDb250ZW50ID0gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCB6LVs5OTk5OV0gb3ZlcmZsb3ctaGlkZGVuXCIgc3R5bGU9e3sgekluZGV4OiA5OTk5OSB9fT5cbiAgICAgIHsvKiBCYWNrZHJvcCAqL31cbiAgICAgIDxkaXZcbiAgICAgICAgY2xhc3NOYW1lPXtgYWJzb2x1dGUgaW5zZXQtMCBiZy1ibGFjayB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgaXNWaXNpYmxlID8gXCJvcGFjaXR5LTUwXCIgOiBcIm9wYWNpdHktMFwiXG4gICAgICAgIH1gfVxuICAgICAgICBvbkNsaWNrPXtvbkNsb3NlfVxuICAgICAgICBzdHlsZT17eyB6SW5kZXg6IDk5OTk5IH19XG4gICAgICAvPlxuXG4gICAgICB7LyogUGFuZWwgKi99XG4gICAgICA8ZGl2XG4gICAgICAgIGNsYXNzTmFtZT17YGFic29sdXRlIHRvcC0wIHJpZ2h0LTAgYm90dG9tLTAgZmxleCBmbGV4LWNvbCBiZy13aGl0ZSB0ZXh0LWdyYXktODAwIHNoYWRvdy14bCB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgdHJhbnNmb3JtICR7XG4gICAgICAgICAgaXNWaXNpYmxlID8gXCJ0cmFuc2xhdGUteC0wXCIgOiBcInRyYW5zbGF0ZS14LWZ1bGxcIlxuICAgICAgICB9YH1cbiAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGdldFJlc3BvbnNpdmVXaWR0aCgpLCB6SW5kZXg6IDk5OTk5IH19XG4gICAgICA+XG4gICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB4LTQgcHktMyBib3JkZXItYiBib3JkZXItZ3JheS0yMDAgYmctZ3JheS01MFwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS04MDAgdHJ1bmNhdGVcIj57dGl0bGV9PC9oMj5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICBpY29uPXs8Q2xvc2VPdXRsaW5lZCBzdHlsZT17eyBjb2xvcjogJyMzMzMnIH19IC8+fVxuICAgICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJDbG9zZSBwYW5lbFwiXG4gICAgICAgICAgICBzdHlsZT17eyBjb2xvcjogJyMzMzMnLCBib3JkZXJDb2xvcjogJ3RyYW5zcGFyZW50JywgYmFja2dyb3VuZDogJ3RyYW5zcGFyZW50JyB9fVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBDb250ZW50ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy15LWF1dG8gcC00IHB0LTYgYmctd2hpdGVcIj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBGb290ZXIgKi99XG4gICAgICAgIHtmb290ZXIgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBweS0zIGJvcmRlci10IGJvcmRlci1ncmF5LTIwMCBiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgICB7Zm9vdGVyfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBTbGlkaW5nUGFuZWw7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIkJ1dHRvbiIsIkNsb3NlT3V0bGluZWQiLCJTbGlkaW5nUGFuZWwiLCJpc09wZW4iLCJvbkNsb3NlIiwidGl0bGUiLCJjaGlsZHJlbiIsIndpZHRoIiwiZm9vdGVyIiwiaXNWaXNpYmxlIiwic2V0SXNWaXNpYmxlIiwiaXNSZW5kZXJlZCIsInNldElzUmVuZGVyZWQiLCJ3aW5kb3dXaWR0aCIsInNldFdpbmRvd1dpZHRoIiwid2luZG93IiwiaW5uZXJXaWR0aCIsImhhbmRsZVJlc2l6ZSIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiY29uc29sZSIsImxvZyIsInNldFRpbWVvdXQiLCJ0aW1lciIsImNsZWFyVGltZW91dCIsImdldFJlc3BvbnNpdmVXaWR0aCIsInBhbmVsQ29udGVudCIsImRpdiIsImNsYXNzTmFtZSIsInN0eWxlIiwiekluZGV4Iiwib25DbGljayIsImgyIiwidHlwZSIsImljb24iLCJjb2xvciIsImFyaWEtbGFiZWwiLCJib3JkZXJDb2xvciIsImJhY2tncm91bmQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/SlidingPanel.tsx\n"));

/***/ })

});