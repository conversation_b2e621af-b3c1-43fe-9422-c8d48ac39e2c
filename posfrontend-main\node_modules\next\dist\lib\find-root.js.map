{"version": 3, "sources": ["../../src/lib/find-root.ts"], "sourcesContent": ["import { dirname } from 'path'\nimport findUp from 'next/dist/compiled/find-up'\n\nexport function findRootLockFile(cwd: string) {\n  return findUp.sync(\n    ['pnpm-lock.yaml', 'package-lock.json', 'yarn.lock', 'bun.lockb'],\n    {\n      cwd,\n    }\n  )\n}\n\nexport function findRootDir(cwd: string) {\n  const lockFile = findRootLockFile(cwd)\n  return lockFile ? dirname(lockFile) : undefined\n}\n"], "names": ["findRootDir", "findRootLockFile", "cwd", "findUp", "sync", "lockFile", "dirname", "undefined"], "mappings": ";;;;;;;;;;;;;;;IAYgBA,WAAW;eAAXA;;IATAC,gBAAgB;eAAhBA;;;sBAHQ;+DACL;;;;;;AAEZ,SAASA,iBAAiBC,GAAW;IAC1C,OAAOC,eAAM,CAACC,IAAI,CAChB;QAAC;QAAkB;QAAqB;QAAa;KAAY,EACjE;QACEF;IACF;AAEJ;AAEO,SAASF,YAAYE,GAAW;IACrC,MAAMG,WAAWJ,iBAAiBC;IAClC,OAAOG,WAAWC,IAAAA,aAAO,EAACD,YAAYE;AACxC"}