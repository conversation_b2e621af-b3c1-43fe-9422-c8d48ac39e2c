"use client";

import React from "react";
import { Des<PERSON><PERSON>, <PERSON><PERSON>, Spin } from "antd";
import SlidingPanel from "@/components/ui/SlidingPanel";
import { useCategoryDetail } from "@/hooks/categories/useCategoryDetail";
import { LoadingOutlined, TagOutlined, CalendarOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { UserRole } from "@/types/user";
import "./category-panels.css";

interface CategoryDetailPanelProps {
  isOpen: boolean;
  onClose: () => void;
  categoryId: number | null;
  onEdit?: (categoryId: number) => void;
}

const CategoryDetailPanel: React.FC<CategoryDetailPanelProps> = ({
  isOpen,
  onClose,
  categoryId,
  onEdit,
}) => {
  const { category, isLoading } = useCategoryDetail(categoryId);

  // Format date for display
  const formatDate = (dateString?: string | null) => {
    if (!dateString) return "N/A";
    try {
      return dayjs(dateString).format("MMM D, YYYY");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Get current user from Redux store
  const user = useSelector((state: RootState) => state.auth.user);
  const userRole = user?.role as UserRole;

  // Check if user can edit (only admin can edit categories)
  const canEdit = userRole === "admin" && user?.id === category?.createdBy;

  // Panel footer with close button and edit button (if user can edit)
  const panelFooter = (
    <div className="flex justify-end space-x-2">
      <Button
        onClick={onClose}
        className="text-gray-700 hover:text-gray-900"
        style={{ borderColor: '#d9d9d9', background: '#f5f5f5' }}
      >
        Close
      </Button>
      {onEdit && category && canEdit && (
        <Button
          type="primary"
          onClick={() => onEdit(category.id)}
        >
          Edit
        </Button>
      )}
    </div>
  );

  return (
    <SlidingPanel
      isOpen={isOpen}
      onClose={onClose}
      title="Category Details"
      width="500px" // This will be overridden on mobile by the SlidingPanel component
      footer={panelFooter}
    >
      {isLoading ? (
        <div className="flex justify-center items-center h-full min-h-[300px]">
          <Spin indicator={<LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} spin />} />
        </div>
      ) : category ? (
        <>
          {/* Category detail heading with icon */}
          <div className="mb-6 border-b border-gray-200 pb-4">
            <h2 className="text-xl font-bold text-gray-800 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
              Category: {category.name}
            </h2>
            <p className="text-gray-600 mt-1 flex items-center">
              Complete category information and details
            </p>
          </div>

          <Descriptions
            bordered
            column={1}
            className="category-detail-light"
            labelStyle={{ color: '#333', backgroundColor: '#f5f5f5' }}
            contentStyle={{ color: '#333', backgroundColor: '#ffffff' }}
          >
            <Descriptions.Item label={<span><TagOutlined /> Category ID</span>}>
              {category.id}
            </Descriptions.Item>

            <Descriptions.Item label={<span><TagOutlined /> Name</span>}>
              {category.name}
            </Descriptions.Item>

            <Descriptions.Item label="Description">
              {category.description || "No description provided"}
            </Descriptions.Item>

            <Descriptions.Item label={<span><CalendarOutlined /> Created At</span>}>
              {formatDate(category.createdAt)}
            </Descriptions.Item>

            {category.updatedAt && (
              <Descriptions.Item label={<span><CalendarOutlined /> Last Updated</span>}>
                {formatDate(category.updatedAt)}
              </Descriptions.Item>
            )}
          </Descriptions>
        </>
      ) : (
        <div className="text-center py-8 text-gray-800">Category not found</div>
      )}
    </SlidingPanel>
  );
};

export default CategoryDetailPanel;
