"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/sales/page",{

/***/ "(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx":
/*!*************************************************!*\
  !*** ./src/components/Sales/SalesFormPanel.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/image/index.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DatabaseOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PrinterOutlined.js\");\n/* harmony import */ var _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/reduxRTK/services/salesApi */ \"(app-pages-browser)/./src/reduxRTK/services/salesApi.ts\");\n/* harmony import */ var _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/reduxRTK/services/productApi */ \"(app-pages-browser)/./src/reduxRTK/services/productApi.ts\");\n/* harmony import */ var _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/reduxRTK/services/userStoreApi */ \"(app-pages-browser)/./src/reduxRTK/services/userStoreApi.ts\");\n/* harmony import */ var _components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/SlidingPanel */ \"(app-pages-browser)/./src/components/ui/SlidingPanel.tsx\");\n/* harmony import */ var _utils_showMessage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/showMessage */ \"(app-pages-browser)/./src/utils/showMessage.ts\");\n/* harmony import */ var _utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/cloudinaryUtils */ \"(app-pages-browser)/./src/utils/cloudinaryUtils.ts\");\n/* harmony import */ var _sales_panels_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./sales-panels.css */ \"(app-pages-browser)/./src/components/Sales/sales-panels.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst SalesFormPanel = (param)=>{\n    let { isOpen, onClose, onSuccess } = param;\n    var _productsData_data, _productsData_data_products, _productsData_data1;\n    _s();\n    const [form] = _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm();\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalAmount, setTotalAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingReceipt, setIsGeneratingReceipt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptPreviewVisible, setReceiptPreviewVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptUrl, setReceiptUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPrinted, setHasPrinted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            console.log(\"Items state changed:\", items);\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items\n    ]);\n    const { data: productsData, isLoading: isLoadingProducts, refetch: refetchProducts, error: productsError, isFetching: isFetchingProducts } = (0,_reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery)({\n        page: 1,\n        limit: 1000,\n        search: searchTerm\n    }, {\n        // Always fetch fresh data from database\n        refetchOnMountOrArgChange: true,\n        refetchOnFocus: true,\n        refetchOnReconnect: true,\n        // Skip caching entirely for sales form to ensure fresh stock data\n        skip: false\n    });\n    // Enhanced products data monitoring and error handling\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (productsData) {\n                var _productsData_data, _productsData_data_products, _productsData_data1;\n                console.log(\"🛒 Fresh products loaded from database:\", {\n                    total: ((_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.total) || 0,\n                    productsCount: ((_productsData_data1 = productsData.data) === null || _productsData_data1 === void 0 ? void 0 : (_productsData_data_products = _productsData_data1.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.length) || 0,\n                    isLoading: isLoadingProducts,\n                    isFetching: isFetchingProducts,\n                    timestamp: new Date().toISOString()\n                });\n            }\n            if (productsError) {\n                console.error(\"❌ Error loading products:\", productsError);\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Failed to load products. Please try again.\");\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        productsData,\n        isLoadingProducts,\n        isFetchingProducts,\n        productsError\n    ]);\n    // Get current user ID from auth state\n    const getCurrentUserId = ()=>{\n        if (true) {\n            var _state_auth_user, _state_auth;\n            // @ts-ignore - Redux state is exposed for debugging\n            const state = window.__REDUX_STATE;\n            return (state === null || state === void 0 ? void 0 : (_state_auth = state.auth) === null || _state_auth === void 0 ? void 0 : (_state_auth_user = _state_auth.user) === null || _state_auth_user === void 0 ? void 0 : _state_auth_user.id) || 0;\n        }\n        return 0;\n    };\n    // Fetch user stores\n    const { data: userStoresData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery)(getCurrentUserId());\n    // Fetch default store\n    const { data: defaultStoreData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery)(getCurrentUserId());\n    // Set default store when data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (defaultStoreData === null || defaultStoreData === void 0 ? void 0 : defaultStoreData.data) {\n                setSelectedStore(defaultStoreData.data);\n                form.setFieldsValue({\n                    storeId: defaultStoreData.data.id\n                });\n            } else if ((userStoresData === null || userStoresData === void 0 ? void 0 : userStoresData.data) && userStoresData.data.length > 0) {\n                setSelectedStore(userStoresData.data[0]);\n                form.setFieldsValue({\n                    storeId: userStoresData.data[0].id\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        defaultStoreData,\n        userStoresData,\n        form\n    ]);\n    // Create sale mutation\n    const [createSale, { isLoading: isSubmitting }] = (0,_reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation)();\n    // Calculate total amount whenever items change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (items && items.length > 0) {\n                const total = items.reduce({\n                    \"SalesFormPanel.useEffect.total\": (sum, item)=>sum + item.price * item.quantity\n                }[\"SalesFormPanel.useEffect.total\"], 0);\n                setTotalAmount(total);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: total\n                    });\n                }\n                // Debug log to check items state\n                console.log(\"Current items in useEffect:\", items);\n            } else {\n                setTotalAmount(0);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: 0\n                    });\n                }\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items,\n        form\n    ]);\n    // Enhanced panel open/close handling with forced data refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (isOpen) {\n                // When panel opens, ALWAYS fetch fresh product data from database\n                console.log(\"🛒 Sales panel opened - forcing fresh product data fetch from database\");\n                // Force refetch to ensure we get the latest stock quantities\n                refetchProducts().then({\n                    \"SalesFormPanel.useEffect\": (result)=>{\n                        if (result.data) {\n                            var _result_data_data_products, _result_data_data;\n                            console.log(\"✅ Fresh product data successfully loaded:\", {\n                                productsCount: ((_result_data_data = result.data.data) === null || _result_data_data === void 0 ? void 0 : (_result_data_data_products = _result_data_data.products) === null || _result_data_data_products === void 0 ? void 0 : _result_data_data_products.length) || 0,\n                                timestamp: new Date().toISOString()\n                            });\n                        }\n                    }\n                }[\"SalesFormPanel.useEffect\"]).catch({\n                    \"SalesFormPanel.useEffect\": (error)=>{\n                        console.error(\"❌ Failed to fetch fresh product data:\", error);\n                        (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Failed to load current product data. Stock quantities may not be accurate.\");\n                    }\n                }[\"SalesFormPanel.useEffect\"]);\n            } else {\n                // Reset form when panel is closed\n                console.log(\"🛒 Sales panel closed - resetting form state\");\n                if (form) {\n                    form.resetFields();\n                }\n                setItems([]);\n                setSelectedProduct(null);\n                setQuantity(1);\n                setTotalAmount(0);\n                setReceiptUrl(null);\n                setReceiptPreviewVisible(false);\n                setHasPrinted(false);\n                setSearchTerm(\"\"); // Reset search term to ensure fresh data on next open\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        isOpen,\n        form,\n        refetchProducts\n    ]);\n    // Handle adding an item to the sale\n    const handleAddItem = ()=>{\n        if (!selectedProduct) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Please select a product\");\n            return;\n        }\n        if (quantity <= 0) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Quantity must be greater than 0\");\n            return;\n        }\n        if (selectedProduct.stockQuantity < quantity) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Only \".concat(selectedProduct.stockQuantity, \" units available in stock\"));\n            return;\n        }\n        console.log(\"Adding item with product:\", selectedProduct);\n        // Check if product already exists in items\n        const existingItemIndex = items.findIndex((item)=>item.productId === selectedProduct.id);\n        if (existingItemIndex >= 0) {\n            // Update existing item\n            const updatedItems = [\n                ...items\n            ];\n            const newQuantity = updatedItems[existingItemIndex].quantity + quantity;\n            if (newQuantity > selectedProduct.stockQuantity) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Cannot add more than \".concat(selectedProduct.stockQuantity, \" units of this product\"));\n                return;\n            }\n            updatedItems[existingItemIndex].quantity = newQuantity;\n            // Make sure the product name is set\n            if (!updatedItems[existingItemIndex].productName) {\n                updatedItems[existingItemIndex].productName = selectedProduct.name;\n            }\n            // Update the state with the new array\n            console.log(\"Updating existing item. New items array:\", updatedItems);\n            setItems([\n                ...updatedItems\n            ]); // Create a new array reference to force re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Updated quantity of \".concat(selectedProduct.name));\n        } else {\n            // Add new item\n            const newItem = {\n                productId: selectedProduct.id,\n                productName: selectedProduct.name,\n                quantity,\n                price: typeof selectedProduct.price === \"string\" ? parseFloat(selectedProduct.price) : selectedProduct.price\n            };\n            // Create a new array with the new item\n            const newItems = [\n                ...items,\n                newItem\n            ];\n            // Update the state with the new array\n            console.log(\"Adding new item. New items array:\", newItems);\n            setItems(newItems); // This should trigger a re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Added \".concat(quantity, \" \").concat(selectedProduct.name, \" to sale\"));\n        }\n        // Reset selection\n        setSelectedProduct(null);\n        setQuantity(1);\n        if (form) {\n            form.setFieldsValue({\n                productId: undefined,\n                quantity: 1\n            });\n        }\n    };\n    // Handle removing an item from the sale\n    const handleRemoveItem = (index)=>{\n        const updatedItems = [\n            ...items\n        ];\n        updatedItems.splice(index, 1);\n        setItems(updatedItems);\n    };\n    // Handle printing receipt - directly trigger print dialog\n    const handlePrintReceipt = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n            if (!receiptUrl || hasPrinted) {\n                console.log(\"Skipping print: \", !receiptUrl ? \"No receipt URL\" : \"Already printed\");\n                return;\n            }\n            console.log(\"Printing receipt:\", receiptUrl);\n            // Mark as printed immediately to prevent multiple print dialogs\n            setHasPrinted(true);\n            // Create a hidden iframe to load the image\n            const iframe = document.createElement(\"iframe\");\n            iframe.style.display = \"none\";\n            document.body.appendChild(iframe);\n            // Set up the iframe content with the image and print CSS\n            iframe.onload = ({\n                \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                    if (iframe.contentWindow) {\n                        // Write the HTML content to the iframe\n                        iframe.contentWindow.document.write('\\n          <!DOCTYPE html>\\n          <html>\\n            <head>\\n              <title>Print Receipt</title>\\n              <style>\\n                body {\\n                  margin: 0;\\n                  padding: 0;\\n                  display: flex;\\n                  justify-content: center;\\n                  align-items: center;\\n                  height: 100vh;\\n                }\\n                img {\\n                  max-width: 100%;\\n                  max-height: 100vh;\\n                }\\n                @media print {\\n                  body {\\n                    margin: 0;\\n                    padding: 0;\\n                  }\\n                  img {\\n                    width: 100%;\\n                    height: auto;\\n                  }\\n                }\\n              </style>\\n            </head>\\n            <body>\\n              <img src=\"'.concat(receiptUrl, '\" alt=\"Receipt\" />\\n            </body>\\n          </html>\\n        '));\n                        // Close the document\n                        iframe.contentWindow.document.close();\n                        // Use a single print trigger with a delay to ensure the image is loaded\n                        setTimeout({\n                            \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                if (iframe.contentWindow) {\n                                    try {\n                                        // Print the iframe content\n                                        iframe.contentWindow.focus();\n                                        iframe.contentWindow.print();\n                                    } catch (e) {\n                                        console.error(\"Error printing receipt:\", e);\n                                    }\n                                    // Remove the iframe after printing\n                                    setTimeout({\n                                        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                            document.body.removeChild(iframe);\n                                        }\n                                    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 1000);\n                                }\n                            }\n                        }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 500);\n                    }\n                }\n            })[\"SalesFormPanel.useCallback[handlePrintReceipt]\"];\n            // Set the iframe source to trigger the onload event\n            iframe.src = \"about:blank\";\n        }\n    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], [\n        receiptUrl,\n        hasPrinted\n    ]);\n    // Effect to automatically print receipt when modal is shown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (receiptPreviewVisible && receiptUrl && !hasPrinted) {\n                // Add a small delay to ensure the receipt image is loaded\n                const timer = setTimeout({\n                    \"SalesFormPanel.useEffect.timer\": ()=>{\n                        handlePrintReceipt(); // This now handles the hasPrinted state internally\n                    }\n                }[\"SalesFormPanel.useEffect.timer\"], 800);\n                return ({\n                    \"SalesFormPanel.useEffect\": ()=>clearTimeout(timer)\n                })[\"SalesFormPanel.useEffect\"];\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        receiptPreviewVisible,\n        receiptUrl,\n        hasPrinted,\n        handlePrintReceipt\n    ]);\n    // Handle form submission\n    const handleSubmit = async ()=>{\n        try {\n            var _userStoresData_data;\n            if (items.length === 0) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Please add at least one item to the sale\");\n                return;\n            }\n            // Validate form fields\n            const values = await form.validateFields();\n            // Check if store is selected\n            if (!selectedStore) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"No store information available. Please set up your store in your profile settings.\");\n                return;\n            }\n            // Set loading state for receipt generation\n            setIsGeneratingReceipt(true);\n            // Get store information for receipt\n            const storeInfo = selectedStore || (userStoresData === null || userStoresData === void 0 ? void 0 : (_userStoresData_data = userStoresData.data) === null || _userStoresData_data === void 0 ? void 0 : _userStoresData_data.find((store)=>store.id === values.storeId)) || {\n                name: \"POS System\"\n            };\n            // Generate receipt HTML\n            const receiptHTML = (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__.generateReceiptHTML)({\n                id: Date.now(),\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                transactionDate: new Date().toISOString(),\n                items: items.map((item)=>({\n                        productName: item.productName,\n                        quantity: item.quantity,\n                        price: item.price\n                    }))\n            }, storeInfo);\n            // Generate receipt image and get URL\n            let receiptUrl = \"https://receipt.example.com/placeholder\";\n            try {\n                receiptUrl = await (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__.generateReceiptImage)(receiptHTML);\n            } catch (error) {\n                console.error(\"Failed to generate receipt image:\", error);\n            // Continue with placeholder URL if image generation fails\n            }\n            const saleData = {\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                items: items.map((item)=>({\n                        productId: item.productId,\n                        quantity: item.quantity,\n                        price: item.price\n                    })),\n                receiptUrl,\n                storeId: selectedStore === null || selectedStore === void 0 ? void 0 : selectedStore.id\n            };\n            const response = await createSale(saleData).unwrap();\n            if (response.success) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Sale created successfully\");\n                // Store the receipt URL for preview\n                setReceiptUrl(receiptUrl);\n                // Show receipt preview modal and offer print option\n                setReceiptPreviewVisible(true);\n                // Force refresh product data to get updated stock quantities after sale\n                console.log(\"🔄 Sale completed - refreshing product data to update stock quantities\");\n                refetchProducts().then((result)=>{\n                    if (result.data) {\n                        console.log(\"✅ Product data refreshed after sale - stock quantities updated\");\n                    }\n                }).catch((error)=>{\n                    console.error(\"❌ Failed to refresh product data after sale:\", error);\n                });\n                // Trigger the success callback to refresh the sales list\n                setTimeout(()=>{\n                    if (onSuccess) {\n                        console.log(\"📊 Triggering sales list refresh\");\n                        onSuccess();\n                    }\n                }, 300);\n            // Keep the panel open until the user explicitly closes it\n            // This ensures the receipt modal stays visible\n            } else {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", response.message || \"Failed to create sale\");\n            }\n        } catch (error) {\n            var _error_data;\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", ((_error_data = error.data) === null || _error_data === void 0 ? void 0 : _error_data.message) || \"An error occurred while creating the sale\");\n        } finally{\n            setIsGeneratingReceipt(false);\n        }\n    };\n    // Debug log to check items state when rendering\n    console.log(\"Rendering with items:\", items);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        title: \"Point of Sale System\",\n        isOpen: isOpen,\n        onClose: onClose,\n        width: \"98%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sales-form min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-20 border-b border-gray-200 bg-white px-6 py-4 shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"text-xl text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-gray-800\",\n                                                    children: \"Point of Sale\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: (selectedStore === null || selectedStore === void 0 ? void 0 : selectedStore.name) || 'NEXAPO POS System'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Transaction Total\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-green-600\",\n                                                    children: [\n                                                        \"GHS \",\n                                                        totalAmount.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Items\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl font-semibold text-gray-700\",\n                                                    children: items.length\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 529,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-6 xl:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"xl:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-blue-500\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"text-sm text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 570,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-bold text-gray-800\",\n                                                                            children: \"Product Selection\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 572,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        (isLoadingProducts || isFetchingProducts) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 rounded-full bg-blue-100 px-3 py-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    className: \"text-blue-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 577,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-blue-600\",\n                                                                                    children: \"Loading fresh data...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 578,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 576,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 584,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            onClick: ()=>{\n                                                                                console.log(\"🔄 Manual refresh triggered\");\n                                                                                refetchProducts();\n                                                                                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"warning\", \"Refreshing product data...\");\n                                                                            },\n                                                                            loading: isLoadingProducts || isFetchingProducts,\n                                                                            size: \"small\",\n                                                                            className: \"border-blue-300 text-blue-600 hover:bg-blue-50\",\n                                                                            title: \"Refresh product data from database\",\n                                                                            children: \"Refresh\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 583,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"rounded-full bg-white px-3 py-1 text-xs text-gray-600 shadow-sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"mr-1 text-red-500\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 598,\n                                                                                    columnNumber: 25\n                                                                                }, undefined),\n                                                                                \" Required fields\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 597,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        (productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 flex items-center space-x-2 text-xs text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 604,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        productsData.data.products.length,\n                                                                        \" products loaded from database\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 605,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-600\",\n                                                                    children: [\n                                                                        \"• Last updated: \",\n                                                                        new Date().toLocaleTimeString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        form: form,\n                                                        layout: \"vertical\",\n                                                        initialValues: {\n                                                            paymentMethod: \"cash\",\n                                                            quantity: 1\n                                                        },\n                                                        className: \"pos-unified-form\",\n                                                        onFinish: handleSubmit,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 gap-6 lg:grid-cols-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"lg:col-span-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                                            name: \"productId\",\n                                                                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"flex items-center text-sm font-semibold text-gray-700\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"mr-2 text-blue-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 632,\n                                                                                        columnNumber: 31\n                                                                                    }, void 0),\n                                                                                    \"Select Product \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"ml-1 text-red-500\",\n                                                                                        children: \"*\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 633,\n                                                                                        columnNumber: 46\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 631,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            className: \"mb-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                showSearch: true,\n                                                                                placeholder: isLoadingProducts || isFetchingProducts ? \"🔄 Loading fresh product data from database...\" : productsError ? \"❌ Error loading products - click refresh\" : \"🔍 Search and select a product...\",\n                                                                                optionFilterProp: \"children\",\n                                                                                loading: isLoadingProducts || isFetchingProducts,\n                                                                                disabled: isLoadingProducts || isFetchingProducts,\n                                                                                onChange: (value)=>{\n                                                                                    var _productsData_data;\n                                                                                    const product = productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products.find((p)=>p.id === value);\n                                                                                    console.log(\"Selected product from fresh database data:\", product);\n                                                                                    if (product) {\n                                                                                        // Make a deep copy to avoid reference issues\n                                                                                        setSelectedProduct({\n                                                                                            ...product,\n                                                                                            // Ensure price is properly formatted\n                                                                                            price: typeof product.price === \"string\" ? product.price : String(product.price)\n                                                                                        });\n                                                                                        console.log(\"✅ Product selected with current stock:\", product.stockQuantity);\n                                                                                    } else {\n                                                                                        setSelectedProduct(null);\n                                                                                    }\n                                                                                },\n                                                                                onSearch: (value)=>{\n                                                                                    setSearchTerm(value);\n                                                                                    // Trigger a fresh search from database\n                                                                                    if (value.length > 2) {\n                                                                                        console.log(\"🔍 Searching products in database:\", value);\n                                                                                    }\n                                                                                },\n                                                                                filterOption: false,\n                                                                                className: \"rounded-lg border-gray-300 text-gray-800 shadow-sm\",\n                                                                                size: \"large\",\n                                                                                suffixIcon: isLoadingProducts || isFetchingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    spin: true,\n                                                                                    className: \"text-blue-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 682,\n                                                                                    columnNumber: 33\n                                                                                }, void 0) : productsError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-500\",\n                                                                                    children: \"❌\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 684,\n                                                                                    columnNumber: 33\n                                                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    className: \"text-gray-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 686,\n                                                                                    columnNumber: 33\n                                                                                }, void 0),\n                                                                                notFoundContent: isLoadingProducts || isFetchingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-center py-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                            size: \"small\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 692,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"ml-2 text-gray-500\",\n                                                                                            children: \"Loading fresh data from database...\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 693,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 691,\n                                                                                    columnNumber: 33\n                                                                                }, void 0) : productsError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"py-6 text-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-red-500\",\n                                                                                            children: \"❌ Failed to load products\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 697,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                            size: \"small\",\n                                                                                            onClick: ()=>refetchProducts(),\n                                                                                            className: \"mt-2\",\n                                                                                            children: \"Try Again\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 698,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 696,\n                                                                                    columnNumber: 33\n                                                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"py-4 text-center text-gray-500\",\n                                                                                    children: \"No products found in database\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 707,\n                                                                                    columnNumber: 33\n                                                                                }, void 0),\n                                                                                children: productsData === null || productsData === void 0 ? void 0 : (_productsData_data1 = productsData.data) === null || _productsData_data1 === void 0 ? void 0 : (_productsData_data_products = _productsData_data1.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Option, {\n                                                                                        value: product.id,\n                                                                                        disabled: product.stockQuantity <= 0,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center justify-between py-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex-1\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"font-medium text-gray-800\",\n                                                                                                            children: product.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 721,\n                                                                                                            columnNumber: 37\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"text-sm text-gray-500\",\n                                                                                                            children: [\n                                                                                                                \"GHS \",\n                                                                                                                Number(product.price).toFixed(2)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 724,\n                                                                                                            columnNumber: 37\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 720,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-right\",\n                                                                                                    children: product.stockQuantity <= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: \"rounded-full bg-red-100 px-2 py-1 text-xs font-medium text-red-600\",\n                                                                                                        children: \"Out of Stock\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 730,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: \"rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-600\",\n                                                                                                        children: [\n                                                                                                            \"Stock: \",\n                                                                                                            product.stockQuantity\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 734,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 728,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 719,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, product.id, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 714,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 638,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 628,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                                            name: \"quantity\",\n                                                                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"flex items-center text-sm font-semibold text-gray-700\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mr-2\",\n                                                                                        children: \"\\uD83D\\uDCE6\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 751,\n                                                                                        columnNumber: 31\n                                                                                    }, void 0),\n                                                                                    \"Quantity \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"ml-1 text-red-500\",\n                                                                                        children: \"*\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 752,\n                                                                                        columnNumber: 40\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 750,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            className: \"mb-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                min: 1,\n                                                                                max: (selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.stockQuantity) || 999,\n                                                                                value: quantity,\n                                                                                onChange: (value)=>setQuantity(value || 1),\n                                                                                style: {\n                                                                                    width: \"100%\"\n                                                                                },\n                                                                                className: \"rounded-lg border-gray-300 text-gray-800 shadow-sm\",\n                                                                                size: \"large\",\n                                                                                placeholder: \"Enter quantity\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 757,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 747,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 746,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 626,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            selectedProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 rounded-lg border border-blue-200 bg-blue-50 p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-semibold text-gray-800\",\n                                                                                    children: selectedProduct.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 776,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"Price: GHS \",\n                                                                                        Number(selectedProduct.price).toFixed(2),\n                                                                                        \" | Available: \",\n                                                                                        selectedProduct.stockQuantity,\n                                                                                        \" units\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 779,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 775,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-right\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"Subtotal\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 785,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-lg font-bold text-green-600\",\n                                                                                    children: [\n                                                                                        \"GHS \",\n                                                                                        (Number(selectedProduct.price) * quantity).toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 786,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 784,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 774,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 773,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 796,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                onClick: handleAddItem,\n                                                                className: \"mt-6 h-14 w-full rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 text-lg font-semibold shadow-lg hover:from-blue-600 hover:to-blue-700 hover:shadow-xl\",\n                                                                disabled: !selectedProduct,\n                                                                size: \"large\",\n                                                                children: \"\\uD83D\\uDED2 Add to Cart\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 794,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-8\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                                    name: \"paymentMethod\",\n                                                                    label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex items-center text-sm font-semibold text-gray-700\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mr-2\",\n                                                                                children: \"\\uD83D\\uDCB3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 811,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            \"Payment Method \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-1 text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 812,\n                                                                                columnNumber: 44\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 810,\n                                                                        columnNumber: 27\n                                                                    }, void 0),\n                                                                    rules: [\n                                                                        {\n                                                                            required: true,\n                                                                            message: \"Please select a payment method\"\n                                                                        }\n                                                                    ],\n                                                                    initialValue: \"cash\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"rounded-lg border-gray-300 text-gray-800 shadow-sm\",\n                                                                        size: \"large\",\n                                                                        placeholder: \"Select payment method\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Option, {\n                                                                                value: \"cash\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between py-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"mr-3 text-lg\",\n                                                                                                    children: \"\\uD83D\\uDCB5\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 831,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"font-medium\",\n                                                                                                            children: \"Cash Payment\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 833,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"text-xs text-gray-500\",\n                                                                                                            children: \"Physical cash transaction\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 834,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 832,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 830,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"rounded-full bg-green-100 px-2 py-1 text-xs text-green-600\",\n                                                                                            children: \"Instant\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 837,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 829,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 828,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Option, {\n                                                                                value: \"card\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between py-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"mr-3 text-lg\",\n                                                                                                    children: \"\\uD83D\\uDCB3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 845,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"font-medium\",\n                                                                                                            children: \"Card Payment\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 847,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"text-xs text-gray-500\",\n                                                                                                            children: \"Credit/Debit card\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 848,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 846,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 844,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-600\",\n                                                                                            children: \"Secure\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 851,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 843,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 842,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Option, {\n                                                                                value: \"mobile_money\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between py-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"mr-3 text-lg\",\n                                                                                                    children: \"\\uD83D\\uDCF1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 859,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"font-medium\",\n                                                                                                            children: \"Mobile Money\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 861,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"text-xs text-gray-500\",\n                                                                                                            children: \"MTN, Vodafone, AirtelTigo\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 862,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 860,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 858,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"rounded-full bg-purple-100 px-2 py-1 text-xs text-purple-600\",\n                                                                                            children: \"Popular\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 865,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 857,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 856,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 823,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-b border-gray-100 bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-green-500\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"text-sm text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 884,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 883,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-bold text-gray-800\",\n                                                                        children: \"Shopping Cart\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 886,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"rounded-full bg-white px-3 py-1 text-sm font-medium text-gray-600 shadow-sm\",\n                                                                        children: [\n                                                                            items.length,\n                                                                            \" \",\n                                                                            items.length === 1 ? 'item' : 'items'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 891,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"rounded-full bg-green-100 px-3 py-1 text-sm font-bold text-green-700\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 894,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 890,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 880,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-[400px] overflow-x-auto overflow-y-auto\",\n                                                    children: items.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center justify-center py-16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-gray-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"text-3xl text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 903,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"mb-2 text-lg font-semibold text-gray-600\",\n                                                                children: \"Your cart is empty\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 906,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Add products to start a new transaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 909,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"divide-y divide-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-12 gap-4 bg-gray-50 px-6 py-3 text-sm font-semibold text-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-5\",\n                                                                        children: \"Product\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 917,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-2 text-center\",\n                                                                        children: \"Qty\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 918,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-2 text-right\",\n                                                                        children: \"Price\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 919,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-2 text-right\",\n                                                                        children: \"Subtotal\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 920,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-1 text-center\",\n                                                                        children: \"Action\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 921,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 916,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-12 gap-4 px-6 py-4 transition-colors hover:bg-gray-50\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-5 flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"text-blue-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 931,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 930,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-semibold text-gray-800\",\n                                                                                            children: item.productName || \"Unknown Product\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 934,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-gray-500\",\n                                                                                            children: [\n                                                                                                \"Item #\",\n                                                                                                index + 1\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 937,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 933,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 929,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"rounded-full bg-blue-100 px-3 py-1 text-sm font-semibold text-blue-700\",\n                                                                                children: item.quantity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 943,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 942,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 flex items-center justify-end\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-800\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    Number(item.price).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 948,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 947,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 flex items-center justify-end\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-bold text-green-600\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    (Number(item.price) * item.quantity).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 953,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 952,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-1 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 959,\n                                                                                    columnNumber: 37\n                                                                                }, void 0),\n                                                                                onClick: ()=>handleRemoveItem(index),\n                                                                                type: \"text\",\n                                                                                danger: true,\n                                                                                className: \"rounded-full text-red-500 hover:bg-red-50 hover:text-red-600\",\n                                                                                size: \"small\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 958,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 957,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, \"\".concat(item.productId, \"-\").concat(index), true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 925,\n                                                                    columnNumber: 25\n                                                                }, undefined)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border-t border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-semibold text-gray-800\",\n                                                                            children: \"Cart Total\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 972,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-2xl font-bold text-green-600\",\n                                                                            children: [\n                                                                                \"GHS \",\n                                                                                totalAmount.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 975,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 971,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 970,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 900,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 879,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 564,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sticky top-24 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-b border-gray-100 bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-purple-500\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"text-sm text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 992,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-bold text-gray-800\",\n                                                            children: \"Checkout\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 994,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 990,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 989,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 overflow-hidden rounded-lg border border-gray-200 bg-gradient-to-br from-gray-50 to-gray-100 shadow-inner\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border-b border-gray-200 bg-white px-4 py-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-gray-800\",\n                                                                    children: \"Order Summary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1003,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1002,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"flex items-center text-gray-600\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mr-2\",\n                                                                                        children: \"\\uD83D\\uDCE6\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 1008,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    \" Items\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1007,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"rounded-full bg-blue-100 px-2 py-1 text-sm font-semibold text-blue-700\",\n                                                                                children: items.length\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1010,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1006,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"flex items-center text-gray-600\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mr-2\",\n                                                                                        children: \"\\uD83D\\uDD22\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 1016,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    \" Total Quantity\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1015,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-gray-800\",\n                                                                                children: items.reduce((sum, item)=>sum + item.quantity, 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1018,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1014,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between border-t border-gray-300 pt-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"flex items-center text-lg font-semibold text-gray-800\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mr-2\",\n                                                                                        children: \"\\uD83D\\uDCB0\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 1024,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    \" Total Amount\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1023,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-2xl font-bold text-green-600\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    totalAmount.toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1026,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1022,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1005,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 1001,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    selectedStore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"mb-3 block text-sm font-semibold text-gray-700\",\n                                                                children: \"Store Information\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1036,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border border-green-200 bg-green-50 p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-green-500\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"text-white\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 1042,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1041,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-semibold text-gray-800\",\n                                                                                        children: selectedStore.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 1045,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"Active Store\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 1048,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1044,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1040,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"hidden\",\n                                                                        name: \"storeId\",\n                                                                        value: selectedStore.id\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1053,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1039,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 23\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 rounded-lg border border-orange-200 bg-orange-50 p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-orange-500\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1064,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1063,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold text-orange-800\",\n                                                                            children: \"No Store Selected\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 1067,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-orange-600\",\n                                                                            children: \"Please set up your store in profile settings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 1070,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1066,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 1062,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 1061,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-8 space-y-4\",\n                                                        children: [\n                                                            receiptPreviewVisible ? // Enhanced \"New Sale\" button when receipt is visible\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1084,\n                                                                    columnNumber: 33\n                                                                }, void 0),\n                                                                onClick: ()=>{\n                                                                    // Close the modal and reset the receipt state\n                                                                    setReceiptPreviewVisible(false);\n                                                                    setReceiptUrl(null);\n                                                                    setHasPrinted(false);\n                                                                    // Reset the form to start a new sale\n                                                                    form.resetFields();\n                                                                    setItems([]);\n                                                                    setSelectedProduct(null);\n                                                                    setQuantity(1);\n                                                                    setTotalAmount(0);\n                                                                },\n                                                                className: \"h-16 w-full rounded-lg bg-gradient-to-r from-green-500 to-green-600 text-lg font-bold shadow-lg hover:from-green-600 hover:to-green-700 hover:shadow-xl\",\n                                                                size: \"large\",\n                                                                children: \"\\uD83D\\uDED2 Start New Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1082,\n                                                                columnNumber: 25\n                                                            }, undefined) : // Enhanced \"Complete Sale\" button\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1107,\n                                                                    columnNumber: 33\n                                                                }, void 0),\n                                                                onClick: handleSubmit,\n                                                                loading: isSubmitting || isGeneratingReceipt,\n                                                                disabled: items.length === 0,\n                                                                className: \"h-16 w-full rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 text-lg font-bold shadow-lg hover:from-blue-600 hover:to-blue-700 hover:shadow-xl disabled:from-gray-400 disabled:to-gray-500\",\n                                                                size: \"large\",\n                                                                children: isGeneratingReceipt ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 1116,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        \"Generating Receipt...\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1115,\n                                                                    columnNumber: 29\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        \"\\uD83D\\uDCB3 Complete Sale - GHS \",\n                                                                        totalAmount.toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1120,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1105,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                onClick: onClose,\n                                                                className: \"h-12 w-full rounded-lg border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400\",\n                                                                size: \"large\",\n                                                                children: \"❌ Cancel Transaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1127,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 1079,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 999,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 988,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 987,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 562,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 527,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-gray-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            className: \"mr-2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1146,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Receipt Preview\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1147,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 1145,\n                    columnNumber: 11\n                }, void 0),\n                open: receiptPreviewVisible,\n                onCancel: ()=>{\n                    // Close the modal and reset the receipt state\n                    setReceiptPreviewVisible(false);\n                    setReceiptUrl(null);\n                    setHasPrinted(false);\n                    // Reset the form to start a new sale\n                    form.resetFields();\n                    setItems([]);\n                    setSelectedProduct(null);\n                    setQuantity(1);\n                    setTotalAmount(0);\n                },\n                width: 500,\n                centered: true,\n                className: \"receipt-preview-modal\",\n                footer: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        onClick: ()=>{\n                            // Close the modal and reset the receipt state\n                            setReceiptPreviewVisible(false);\n                            setReceiptUrl(null);\n                            setHasPrinted(false);\n                            // Reset the form to start a new sale\n                            form.resetFields();\n                            setItems([]);\n                            setSelectedProduct(null);\n                            setQuantity(1);\n                            setTotalAmount(0);\n                        },\n                        className: \"border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200\",\n                        children: \"Close & New Sale\"\n                    }, \"close\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1168,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        type: \"primary\",\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1190,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: ()=>{\n                            // If already printed once, reset the flag to allow printing again\n                            if (hasPrinted) {\n                                setHasPrinted(false);\n                            }\n                            handlePrintReceipt();\n                        },\n                        className: \"bg-blue-600 hover:bg-blue-700\",\n                        children: hasPrinted ? \"Print Again\" : \"Print Receipt\"\n                    }, \"print\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1187,\n                        columnNumber: 11\n                    }, void 0)\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: receiptUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"receipt-image-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            src: receiptUrl,\n                            alt: \"Receipt\",\n                            className: \"receipt-image\",\n                            style: {\n                                maxWidth: \"100%\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1207,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1206,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-64 items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            size: \"large\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1216,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1215,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 1204,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 1143,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n        lineNumber: 521,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SalesFormPanel, \"Q/g9tGgox4eIYTijyg3KJWlCIZ8=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm,\n        _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery,\n        _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation\n    ];\n});\n_c = SalesFormPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SalesFormPanel);\nvar _c;\n$RefreshReg$(_c, \"SalesFormPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx\n"));

/***/ })

});