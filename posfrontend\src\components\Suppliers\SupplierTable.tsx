"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Checkbox, notification } from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import {
  EditOutlined,
  EyeOutlined,
  DeleteOutlined,
  MailOutlined,
  PhoneOutlined,
  UserOutlined,
  HomeOutlined,
  CalendarOutlined,
  DeleteFilled
} from "@ant-design/icons";
import { ResponsiveTableGrid, TableHeader, TableCell, TableRow } from "@/components/ui/ResponsiveTable";
import { Supplier } from "@/reduxRTK/services/supplierApi";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { UserRole } from "@/types/user";

interface SupplierTableProps {
  suppliers: Supplier[];
  loading: boolean;
  onView: (supplierId: number) => void;
  onEdit: (supplier: Supplier) => void;
  onDelete: (supplierId: number) => void;
  onBulkDelete?: (supplierIds: number[]) => void;
  isMobile?: boolean;
}

const SupplierTable: React.FC<SupplierTableProps> = ({
  suppliers,
  loading,
  onView,
  onEdit,
  onDelete,
  onBulkDelete,
  isMobile = false,
}) => {
  const user = useSelector((state: RootState) => state.auth.user);
  const userRole = user?.role as UserRole;

  // State for selected suppliers
  const [selectedSuppliers, setSelectedSuppliers] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Handle select all checkbox change
  const handleSelectAllChange = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked;
    setSelectAll(checked);

    if (checked) {
      // Select all suppliers that the user can delete
      const selectableSupplierIds = suppliers
        .filter(supplier => canEditDelete(supplier))
        .map(supplier => supplier.id);
      setSelectedSuppliers(selectableSupplierIds);
    } else {
      // Deselect all suppliers
      setSelectedSuppliers([]);
    }
  };

  // Handle individual checkbox change
  const handleCheckboxChange = (supplierId: number, checked: boolean) => {
    if (checked) {
      setSelectedSuppliers(prev => [...prev, supplierId]);
    } else {
      setSelectedSuppliers(prev => prev.filter(id => id !== supplierId));
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedSuppliers.length > 0 && onBulkDelete) {
      onBulkDelete(selectedSuppliers);
      setSelectedSuppliers([]);
      setSelectAll(false);
    } else {
      notification.warning({
        message: 'No suppliers selected',
        description: 'Please select at least one supplier to delete.',
      });
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return dayjs(dateString).format("MMM D, YYYY");
  };

  // Check if user can edit/delete (admin can edit/delete all suppliers they can see)
  const canEditDelete = (supplier: Supplier) => {
    // Admin can edit/delete all suppliers they can see
    // The backend already filters suppliers based on permissions
    return userRole === "admin";
  };

  return (
    <div className="overflow-hidden bg-white">
      {/* Bulk Delete Button - Show only when suppliers are selected */}
      {selectedSuppliers.length > 0 && (
        <div className="p-2 bg-gray-100 border-b flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            {selectedSuppliers.length} {selectedSuppliers.length === 1 ? 'supplier' : 'suppliers'} selected
          </span>
          <Button
            type="primary"
            danger
            icon={<DeleteFilled />}
            onClick={handleBulkDelete}
            className="ml-2"
          >
            Delete Selected
          </Button>
        </div>
      )}

      <ResponsiveTableGrid
        columns={isMobile ? "50px 200px 150px 120px 120px 150px" : "50px 200px 150px 120px 150px 120px 150px"}
        minWidth={isMobile ? "800px" : "1000px"}
      >
        {/* Table Headers */}
        <TableHeader className="text-center">
          <Checkbox
            checked={selectAll}
            onChange={handleSelectAllChange}
            disabled={suppliers.filter(supplier => canEditDelete(supplier)).length === 0}
          />
        </TableHeader>
        <TableHeader sticky="left">
          <span className="flex items-center">
            <UserOutlined className="mr-1" />
            Name
          </span>
        </TableHeader>
        <TableHeader>
          <span className="flex items-center">
            <MailOutlined className="mr-1" />
            Email
          </span>
        </TableHeader>
        <TableHeader>
          <span className="flex items-center">
            <PhoneOutlined className="mr-1" />
            Phone
          </span>
        </TableHeader>
        {!isMobile && (
          <TableHeader>
            <span className="flex items-center">
              <UserOutlined className="mr-1" />
              Contact Person
            </span>
          </TableHeader>
        )}
        <TableHeader>
          <span className="flex items-center">
            <CalendarOutlined className="mr-1" />
            Created At
          </span>
        </TableHeader>
        <TableHeader sticky="right" className="text-right">
          Actions
        </TableHeader>
        {/* Table Rows */}
        {suppliers.map((supplier) => (
          <TableRow
            key={supplier.id}
            selected={selectedSuppliers.includes(supplier.id)}
          >
            {/* Checkbox Column */}
            <TableCell className="text-center">
              {canEditDelete(supplier) && (
                <Checkbox
                  checked={selectedSuppliers.includes(supplier.id)}
                  onChange={(e) => handleCheckboxChange(supplier.id, e.target.checked)}
                />
              )}
            </TableCell>

            {/* Name Column - Always visible */}
            <TableCell sticky="left">
              <div className="max-w-[180px] overflow-hidden text-ellipsis font-medium">
                {supplier.name}
              </div>
            </TableCell>

            {/* Email Column */}
            <TableCell>
              <span className="text-blue-600">
                {supplier.email || 'N/A'}
              </span>
            </TableCell>

            {/* Phone Column */}
            <TableCell>
              <span className="font-mono text-sm">
                {supplier.phone || 'N/A'}
              </span>
            </TableCell>

            {/* Contact Person Column - Desktop only */}
            {!isMobile && (
              <TableCell>
                {supplier.contactPerson || 'N/A'}
              </TableCell>
            )}

            {/* Created At Column */}
            <TableCell>
              {formatDate(supplier.createdAt)}
            </TableCell>

            {/* Actions Column - Always visible */}
            <TableCell sticky="right" className="text-right">
              <div className="flex justify-end space-x-1">
                <Tooltip title="View">
                  <Button
                    icon={<EyeOutlined />}
                    onClick={() => onView(supplier.id)}
                    type="text"
                    className="view-button text-green-500 hover:text-green-400"
                    size={isMobile ? "small" : "middle"}
                  />
                </Tooltip>

                {canEditDelete(supplier) && (
                  <>
                    <Tooltip title="Edit">
                      <Button
                        icon={<EditOutlined />}
                        onClick={() => onEdit(supplier)}
                        type="text"
                        className="edit-button text-blue-500 hover:text-blue-400"
                        size={isMobile ? "small" : "middle"}
                      />
                    </Tooltip>
                    <Tooltip title="Delete">
                      <Button
                        icon={<DeleteOutlined />}
                        onClick={() => onDelete(supplier.id)}
                        type="text"
                        className="delete-button text-red-500 hover:text-red-400"
                        size={isMobile ? "small" : "middle"}
                      />
                    </Tooltip>
                  </>
                )}
              </div>
            </TableCell>
          </TableRow>
        ))}
      </ResponsiveTableGrid>
    </div>
  );
};

export default SupplierTable;
