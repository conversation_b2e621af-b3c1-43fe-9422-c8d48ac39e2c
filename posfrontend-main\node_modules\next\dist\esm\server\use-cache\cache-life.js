import { workAsyncStorage } from '../app-render/work-async-storage.external';
import { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external';
function validateCacheLife(profile) {
    if (profile.stale !== undefined) {
        if (profile.stale === false) {
            throw new Error('Pass `Infinity` instead of `false` if you want to cache on the client forever ' + 'without checking with the server.');
        } else if (typeof profile.stale !== 'number') {
            throw new Error('The stale option must be a number of seconds.');
        }
    }
    if (profile.revalidate !== undefined) {
        if (profile.revalidate === false) {
            throw new Error('Pass `Infinity` instead of `false` if you do not want to revalidate by time.');
        } else if (typeof profile.revalidate !== 'number') {
            throw new Error('The revalidate option must be a number of seconds.');
        }
    }
    if (profile.expire !== undefined) {
        if (profile.expire === false) {
            throw new Error('Pass `Infinity` instead of `false` if you want to cache on the client forever ' + 'without checking with the server.');
        } else if (typeof profile.expire !== 'number') {
            throw new Error('The expire option must be a number of seconds.');
        }
    }
    if (profile.revalidate !== undefined && profile.expire !== undefined) {
        if (profile.revalidate > profile.expire) {
            throw new Error('If providing both the revalidate and expire options, ' + 'the expire option must be greater than the revalidate option.' + 'The expire option indicates how many seconds from the start ' + 'until it can no longer be used.');
        }
    }
    if (profile.stale !== undefined && profile.expire !== undefined) {
        if (profile.stale > profile.expire) {
            throw new Error('If providing both the stale and expire options, ' + 'the expire option must be greater than the stale option.' + 'The expire option indicates how many seconds from the start ' + 'until it can no longer be used.');
        }
    }
}
export function cacheLife(profile) {
    if (!process.env.__NEXT_DYNAMIC_IO) {
        throw new Error('cacheLife() is only available with the experimental.dynamicIO config.');
    }
    const workUnitStore = workUnitAsyncStorage.getStore();
    if (!workUnitStore || workUnitStore.type !== 'cache') {
        throw new Error('cacheLife() can only be called inside a "use cache" function.');
    }
    if (typeof profile === 'string') {
        const workStore = workAsyncStorage.getStore();
        if (!workStore) {
            throw new Error('cacheLife() can only be called during App Router rendering at the moment.');
        }
        if (!workStore.cacheLifeProfiles) {
            throw new Error('cacheLifeProfiles should always be provided. This is a bug in Next.js.');
        }
        // TODO: This should be globally available and not require an AsyncLocalStorage.
        const configuredProfile = workStore.cacheLifeProfiles[profile];
        if (configuredProfile === undefined) {
            if (workStore.cacheLifeProfiles[profile.trim()]) {
                throw new Error(`Unknown cacheLife profile "${profile}" is not configured in next.config.js\n` + `Did you mean "${profile.trim()}" without the spaces?`);
            }
            throw new Error(`Unknown cacheLife profile "${profile}" is not configured in next.config.js\n` + 'module.exports = {\n' + '  experimental: {\n' + '    cacheLife: {\n' + `      "${profile}": ...\n` + '    }\n' + '  }\n' + '}');
        }
        profile = configuredProfile;
    } else if (typeof profile !== 'object' || profile === null || Array.isArray(profile)) {
        throw new Error('Invalid cacheLife() option. Either pass a profile name or object.');
    } else {
        validateCacheLife(profile);
    }
    if (profile.revalidate !== undefined) {
        // Track the explicit revalidate time.
        if (workUnitStore.explicitRevalidate === undefined || workUnitStore.explicitRevalidate > profile.revalidate) {
            workUnitStore.explicitRevalidate = profile.revalidate;
        }
    }
    if (profile.expire !== undefined) {
        // Track the explicit expire time.
        if (workUnitStore.explicitExpire === undefined || workUnitStore.explicitExpire > profile.expire) {
            workUnitStore.explicitExpire = profile.expire;
        }
    }
    if (profile.stale !== undefined) {
        // Track the explicit stale time.
        if (workUnitStore.explicitStale === undefined || workUnitStore.explicitStale > profile.stale) {
            workUnitStore.explicitStale = profile.stale;
        }
    }
}

//# sourceMappingURL=cache-life.js.map