"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/expenses/page",{

/***/ "(app-pages-browser)/./src/components/Expenses/ExpenseTable.tsx":
/*!**************************************************!*\
  !*** ./src/components/Expenses/ExpenseTable.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/checkbox/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteFilled_DeleteOutlined_EditOutlined_EyeOutlined_ReloadOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteFilled,DeleteOutlined,EditOutlined,EyeOutlined,ReloadOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteFilled.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteFilled_DeleteOutlined_EditOutlined_EyeOutlined_ReloadOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteFilled,DeleteOutlined,EditOutlined,EyeOutlined,ReloadOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteFilled_DeleteOutlined_EditOutlined_EyeOutlined_ReloadOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteFilled,DeleteOutlined,EditOutlined,EyeOutlined,ReloadOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteFilled_DeleteOutlined_EditOutlined_EyeOutlined_ReloadOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteFilled,DeleteOutlined,EditOutlined,EyeOutlined,ReloadOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteFilled_DeleteOutlined_EditOutlined_EyeOutlined_ReloadOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteFilled,DeleteOutlined,EditOutlined,EyeOutlined,ReloadOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteFilled_DeleteOutlined_EditOutlined_EyeOutlined_ReloadOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteFilled,DeleteOutlined,EditOutlined,EyeOutlined,ReloadOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _hooks_useResponsiveTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useResponsiveTable */ \"(app-pages-browser)/./src/hooks/useResponsiveTable.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ExpenseTable = (param)=>{\n    let { expenses, loading = false, onEdit, onDelete, onBulkDelete, onView, selectedExpenses = [], onSelectionChange, isMobile: propIsMobile = false } = param;\n    _s();\n    // Use hook for responsive detection, fallback to prop\n    const hookIsMobile = (0,_hooks_useResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.useResponsiveTable)();\n    const isMobile = propIsMobile || hookIsMobile;\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__.useSelector)({\n        \"ExpenseTable.useSelector[user]\": (state)=>state.auth.user\n    }[\"ExpenseTable.useSelector[user]\"]);\n    const userRole = user === null || user === void 0 ? void 0 : user.role;\n    // Check permissions\n    const canEdit = userRole === \"admin\" || userRole === \"superadmin\";\n    const canDelete = userRole === \"admin\" || userRole === \"superadmin\";\n    // Handle individual checkbox change\n    const handleCheckboxChange = (expenseId, checked)=>{\n        if (!onSelectionChange) return;\n        const newSelection = checked ? [\n            ...selectedExpenses,\n            expenseId\n        ] : selectedExpenses.filter((id)=>id !== expenseId);\n        onSelectionChange(newSelection);\n    };\n    // Handle select all checkbox\n    const handleSelectAll = (e)=>{\n        if (!onSelectionChange) return;\n        if (e.target.checked) {\n            const allIds = expenses.map((expense)=>expense.id);\n            onSelectionChange(allIds);\n        } else {\n            onSelectionChange([]);\n        }\n    };\n    // Check if all expenses are selected\n    const isAllSelected = expenses.length > 0 && selectedExpenses.length === expenses.length;\n    const isIndeterminate = selectedExpenses.length > 0 && selectedExpenses.length < expenses.length;\n    // Format payment method for display\n    const formatPaymentMethod = (method)=>{\n        const methods = {\n            cash: {\n                label: 'Cash',\n                color: 'green'\n            },\n            card: {\n                label: 'Card',\n                color: 'blue'\n            },\n            mobile_money: {\n                label: 'Mobile Money',\n                color: 'purple'\n            },\n            bank_transfer: {\n                label: 'Bank Transfer',\n                color: 'orange'\n            },\n            cheque: {\n                label: 'Cheque',\n                color: 'cyan'\n            }\n        };\n        const methodInfo = methods[method] || {\n            label: method,\n            color: 'default'\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            color: methodInfo.color,\n            children: methodInfo.label\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n            lineNumber: 98,\n            columnNumber: 12\n        }, undefined);\n    };\n    // Format amount with currency\n    const formatAmount = (amount)=>{\n        return \"₵\".concat(parseFloat(amount).toFixed(2));\n    };\n    if (isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                selectedExpenses.length > 0 && canDelete && onBulkDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-700 text-sm\",\n                                children: [\n                                    selectedExpenses.length,\n                                    \" expense(s) selected\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                type: \"primary\",\n                                danger: true,\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteFilled_DeleteOutlined_EditOutlined_EyeOutlined_ReloadOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 23\n                                }, void 0),\n                                onClick: ()=>onBulkDelete(selectedExpenses),\n                                children: \"Delete Selected\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 11\n                }, undefined),\n                expenses.map((expense)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg p-4 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            onSelectionChange && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                checked: selectedExpenses.includes(expense.id),\n                                                onChange: (e)=>handleCheckboxChange(expense.id, e.target.checked),\n                                                className: \"mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 text-sm\",\n                                                        children: expense.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(expense.expenseDate).format(\"MMM DD, YYYY\")\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1\",\n                                        children: [\n                                            onView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                title: \"View Details\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    type: \"text\",\n                                                    size: \"small\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteFilled_DeleteOutlined_EditOutlined_EyeOutlined_ReloadOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    onClick: ()=>onView(expense)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            canEdit && onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                title: \"Edit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    type: \"text\",\n                                                    size: \"small\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteFilled_DeleteOutlined_EditOutlined_EyeOutlined_ReloadOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    onClick: ()=>onEdit(expense)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            canDelete && onDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                title: \"Delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    type: \"text\",\n                                                    size: \"small\",\n                                                    danger: true,\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteFilled_DeleteOutlined_EditOutlined_EyeOutlined_ReloadOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    onClick: ()=>onDelete(expense.id)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-bold text-green-600\",\n                                                children: formatAmount(expense.amount)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            formatPaymentMethod(expense.paymentMethod)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    expense.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full mr-2\",\n                                                style: {\n                                                    backgroundColor: expense.category.color\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: expense.category.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    expense.vendor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteFilled_DeleteOutlined_EditOutlined_EyeOutlined_ReloadOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            expense.vendor\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    expense.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-2\",\n                                        children: expense.description\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    expense.isRecurring && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        color: \"blue\",\n                                        className: \"mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteFilled_DeleteOutlined_EditOutlined_EyeOutlined_ReloadOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            expense.recurringFrequency\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, expense.id, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Desktop Table View\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-x-auto\",\n        children: [\n            selectedExpenses.length > 0 && canDelete && onBulkDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-700\",\n                            children: [\n                                selectedExpenses.length,\n                                \" expense(s) selected\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            type: \"primary\",\n                            danger: true,\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteFilled_DeleteOutlined_EditOutlined_EyeOutlined_ReloadOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 21\n                            }, void 0),\n                            onClick: ()=>onBulkDelete(selectedExpenses),\n                            children: \"Delete Selected\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        className: \"bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: [\n                                onSelectionChange && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-left\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        indeterminate: isIndeterminate,\n                                        checked: isAllSelected,\n                                        onChange: handleSelectAll\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: \"Expense\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: \"Amount\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: \"Category\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: \"Date\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: \"Payment\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: \"Vendor\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        className: \"bg-white divide-y divide-gray-200\",\n                        children: expenses.map((expense)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-gray-50\",\n                                children: [\n                                    onSelectionChange && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 whitespace-nowrap\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            checked: selectedExpenses.includes(expense.id),\n                                            onChange: (e)=>handleCheckboxChange(expense.id, e.target.checked)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: expense.title\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                expense.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500 truncate max-w-xs\",\n                                                    children: expense.description\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                expense.isRecurring && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    color: \"blue\",\n                                                    className: \"mt-1 text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteFilled_DeleteOutlined_EditOutlined_EyeOutlined_ReloadOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        expense.recurringFrequency\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 whitespace-nowrap\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold text-green-600\",\n                                            children: formatAmount(expense.amount)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 whitespace-nowrap\",\n                                        children: expense.category ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full mr-2\",\n                                                    style: {\n                                                        backgroundColor: expense.category.color\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: expense.category.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"No category\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                        children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(expense.expenseDate).format(\"MMM DD, YYYY\")\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 whitespace-nowrap\",\n                                        children: formatPaymentMethod(expense.paymentMethod)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                        children: expense.vendor || '-'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-end space-x-2\",\n                                            children: [\n                                                onView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    title: \"View Details\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        type: \"text\",\n                                                        size: \"small\",\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteFilled_DeleteOutlined_EditOutlined_EyeOutlined_ReloadOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        onClick: ()=>onView(expense)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                canEdit && onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    title: \"Edit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        type: \"text\",\n                                                        size: \"small\",\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteFilled_DeleteOutlined_EditOutlined_EyeOutlined_ReloadOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        onClick: ()=>onEdit(expense)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                canDelete && onDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    title: \"Delete\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        type: \"text\",\n                                                        size: \"small\",\n                                                        danger: true,\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteFilled_DeleteOutlined_EditOutlined_EyeOutlined_ReloadOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        onClick: ()=>onDelete(expense.id)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, expense.id, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseTable.tsx\",\n        lineNumber: 233,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ExpenseTable, \"0FsLPU7pAYfF34MH2wThSQtpZUY=\", false, function() {\n    return [\n        _hooks_useResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.useResponsiveTable,\n        react_redux__WEBPACK_IMPORTED_MODULE_4__.useSelector\n    ];\n});\n_c = ExpenseTable;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ExpenseTable);\nvar _c;\n$RefreshReg$(_c, \"ExpenseTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Expenses/ExpenseTable.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useResponsiveTable.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useResponsiveTable.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useResponsiveTable: () => (/* binding */ useResponsiveTable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useResponsiveTable auto */ \n/**\n * Hook to determine if we should use mobile table layout\n * Returns true for mobile devices (width < 768px)\n */ const useResponsiveTable = ()=>{\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useResponsiveTable.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"useResponsiveTable.useEffect.checkScreenSize\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"useResponsiveTable.useEffect.checkScreenSize\"];\n            // Check on mount\n            checkScreenSize();\n            // Add event listener for window resize\n            window.addEventListener('resize', checkScreenSize);\n            // Cleanup\n            return ({\n                \"useResponsiveTable.useEffect\": ()=>window.removeEventListener('resize', checkScreenSize)\n            })[\"useResponsiveTable.useEffect\"];\n        }\n    }[\"useResponsiveTable.useEffect\"], []);\n    return isMobile;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VSZXNwb25zaXZlVGFibGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O3dFQUU0QztBQUU1Qzs7O0NBR0MsR0FDTSxNQUFNRSxxQkFBcUI7SUFDaEMsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdKLCtDQUFRQSxDQUFDO0lBRXpDQyxnREFBU0E7d0NBQUM7WUFDUixNQUFNSTtnRUFBa0I7b0JBQ3RCRCxZQUFZRSxPQUFPQyxVQUFVLEdBQUc7Z0JBQ2xDOztZQUVBLGlCQUFpQjtZQUNqQkY7WUFFQSx1Q0FBdUM7WUFDdkNDLE9BQU9FLGdCQUFnQixDQUFDLFVBQVVIO1lBRWxDLFVBQVU7WUFDVjtnREFBTyxJQUFNQyxPQUFPRyxtQkFBbUIsQ0FBQyxVQUFVSjs7UUFDcEQ7dUNBQUcsRUFBRTtJQUVMLE9BQU9GO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsiRTpcXFBST0pFQ1RTXFxwb3NcXHBvc2Zyb250ZW5kXFxzcmNcXGhvb2tzXFx1c2VSZXNwb25zaXZlVGFibGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5cbi8qKlxuICogSG9vayB0byBkZXRlcm1pbmUgaWYgd2Ugc2hvdWxkIHVzZSBtb2JpbGUgdGFibGUgbGF5b3V0XG4gKiBSZXR1cm5zIHRydWUgZm9yIG1vYmlsZSBkZXZpY2VzICh3aWR0aCA8IDc2OHB4KVxuICovXG5leHBvcnQgY29uc3QgdXNlUmVzcG9uc2l2ZVRhYmxlID0gKCkgPT4ge1xuICBjb25zdCBbaXNNb2JpbGUsIHNldElzTW9iaWxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGNoZWNrU2NyZWVuU2l6ZSA9ICgpID0+IHtcbiAgICAgIHNldElzTW9iaWxlKHdpbmRvdy5pbm5lcldpZHRoIDwgNzY4KTtcbiAgICB9O1xuXG4gICAgLy8gQ2hlY2sgb24gbW91bnRcbiAgICBjaGVja1NjcmVlblNpemUoKTtcblxuICAgIC8vIEFkZCBldmVudCBsaXN0ZW5lciBmb3Igd2luZG93IHJlc2l6ZVxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCBjaGVja1NjcmVlblNpemUpO1xuXG4gICAgLy8gQ2xlYW51cFxuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgY2hlY2tTY3JlZW5TaXplKTtcbiAgfSwgW10pO1xuXG4gIHJldHVybiBpc01vYmlsZTtcbn07XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSZXNwb25zaXZlVGFibGUiLCJpc01vYmlsZSIsInNldElzTW9iaWxlIiwiY2hlY2tTY3JlZW5TaXplIiwid2luZG93IiwiaW5uZXJXaWR0aCIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useResponsiveTable.ts\n"));

/***/ })

});