"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/sales/page",{

/***/ "(app-pages-browser)/./src/components/Sales/SalesTable.tsx":
/*!*********************************************!*\
  !*** ./src/components/Sales/SalesTable.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/notification/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/checkbox/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EyeOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteFilled.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EyeOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EyeOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EyeOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CalendarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EyeOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EyeOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _hooks_useResponsiveTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useResponsiveTable */ \"(app-pages-browser)/./src/hooks/useResponsiveTable.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _sales_panels_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./sales-panels.css */ \"(app-pages-browser)/./src/components/Sales/sales-panels.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst SalesTable = (param)=>{\n    let { sales, loading, onViewSale, onDelete, onBulkDelete, isMobile: propIsMobile = false } = param;\n    _s();\n    // Use hook for responsive detection, fallback to prop\n    const hookIsMobile = (0,_hooks_useResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.useResponsiveTable)();\n    const isMobile = propIsMobile || hookIsMobile;\n    const { user } = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__.useSelector)({\n        \"SalesTable.useSelector\": (state)=>state.auth\n    }[\"SalesTable.useSelector\"]);\n    // State for selected sales\n    const [selectedSales, setSelectedSales] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectAll, setSelectAll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check if user can delete sales\n    const canDeleteSale = (user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"superadmin\";\n    // The backend already filters sales based on user role and relationships\n    const filteredSales = sales;\n    // Handle select all checkbox change\n    const handleSelectAllChange = (e)=>{\n        const checked = e.target.checked;\n        setSelectAll(checked);\n        if (checked) {\n            // Select all sales that the user can delete\n            const selectableSaleIds = canDeleteSale ? filteredSales.map((sale)=>sale.id) : [];\n            setSelectedSales(selectableSaleIds);\n        } else {\n            // Deselect all sales\n            setSelectedSales([]);\n        }\n    };\n    // Handle individual checkbox change\n    const handleCheckboxChange = (saleId, checked)=>{\n        if (checked) {\n            setSelectedSales((prev)=>[\n                    ...prev,\n                    saleId\n                ]);\n        } else {\n            setSelectedSales((prev)=>prev.filter((id)=>id !== saleId));\n        }\n    };\n    // Handle bulk delete\n    const handleBulkDelete = ()=>{\n        if (selectedSales.length > 0 && onBulkDelete) {\n            onBulkDelete(selectedSales);\n            setSelectedSales([]);\n            setSelectAll(false);\n        } else {\n            _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].warning({\n                message: \"No sales selected\",\n                description: \"Please select at least one sale to delete.\"\n            });\n        }\n    };\n    // Format currency for display\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-GH\", {\n            style: \"currency\",\n            currency: \"GHS\"\n        }).format(parseFloat(amount));\n    };\n    // Format date for display\n    const formatDate = (dateString)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_3___default()(dateString).format(\"MMM D, YYYY HH:mm\");\n    };\n    // Handle delete sale\n    const handleDeleteSale = (saleId)=>{\n        onDelete(saleId);\n    };\n    // Check if we have any sales after filtering\n    const hasSales = filteredSales.length > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-hidden bg-white\",\n        children: [\n            selectedSales.length > 0 && canDeleteSale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between border-b bg-gray-100 p-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: [\n                            selectedSales.length,\n                            \" \",\n                            selectedSales.length === 1 ? \"sale\" : \"sales\",\n                            \" selected\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        type: \"primary\",\n                        danger: true,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: handleBulkDelete,\n                        className: \"ml-2\",\n                        children: \"Delete Selected\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, undefined),\n            !hasSales && sales.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-60 flex-col items-center justify-center bg-gray-50 text-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: 'You don\"t have access to view these sales.'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-sm text-gray-500\",\n                        children: \"You can only view sales created by you or your team.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, undefined),\n            hasSales && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"min-w-full divide-y divide-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        className: \"bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: [\n                                canDeleteSale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    scope: \"col\",\n                                    className: \"w-10 px-3 py-3 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        checked: selectAll,\n                                        onChange: handleSelectAllChange,\n                                        disabled: filteredSales.length === 0\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    scope: \"col\",\n                                    className: \"sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"ID\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    scope: \"col\",\n                                    className: \"px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Total Amount\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    scope: \"col\",\n                                    className: \"px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Payment Method\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    scope: \"col\",\n                                    className: \"px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Date\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    scope: \"col\",\n                                    className: \"sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-700\",\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        className: \"divide-y divide-gray-200 bg-white\",\n                        children: filteredSales.map((sale)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: selectedSales.includes(sale.id) ? \"bg-blue-50\" : \"\",\n                                children: [\n                                    canDeleteSale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"whitespace-nowrap px-3 py-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            checked: selectedSales.includes(sale.id),\n                                            onChange: (e)=>handleCheckboxChange(sale.id, e.target.checked)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky left-0 z-10 whitespace-nowrap bg-white px-3 py-4 text-gray-800\",\n                                        children: sale.id\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"whitespace-nowrap px-3 py-4 text-gray-800\",\n                                        children: formatCurrency(sale.totalAmount)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"whitespace-nowrap px-3 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex rounded-full px-2 text-xs font-semibold leading-5 \".concat(sale.paymentMethod === \"cash\" ? \"bg-green-800 text-green-100\" : sale.paymentMethod === \"card\" ? \"bg-blue-800 text-blue-100\" : \"bg-purple-800 text-purple-100\"),\n                                            children: sale.paymentMethod.replace(\"_\", \" \").toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"whitespace-nowrap px-3 py-4 text-gray-800\",\n                                        children: formatDate(sale.transactionDate)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky right-0 z-10 whitespace-nowrap bg-white px-3 py-4 text-right text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-end space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"View Details\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        onClick: ()=>onViewSale(sale),\n                                                        type: \"text\",\n                                                        className: \"view-button\",\n                                                        size: isMobile ? \"small\" : \"middle\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                ((user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"superadmin\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    title: \"Delete\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EyeOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 33\n                                                        }, void 0),\n                                                        onClick: ()=>handleDeleteSale(sale.id),\n                                                        type: \"text\",\n                                                        className: \"delete-button\",\n                                                        size: isMobile ? \"small\" : \"middle\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, sale.id, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesTable.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SalesTable, \"WMgmjkg10qWR7GdOhcZ0wZ2gtRQ=\", false, function() {\n    return [\n        _hooks_useResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.useResponsiveTable,\n        react_redux__WEBPACK_IMPORTED_MODULE_5__.useSelector\n    ];\n});\n_c = SalesTable;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SalesTable);\nvar _c;\n$RefreshReg$(_c, \"SalesTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sales/SalesTable.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useResponsiveTable.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useResponsiveTable.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useResponsiveTable: () => (/* binding */ useResponsiveTable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useResponsiveTable auto */ \n/**\n * Hook to determine if we should use mobile table layout\n * Returns true for mobile devices (width < 768px)\n */ const useResponsiveTable = ()=>{\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useResponsiveTable.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"useResponsiveTable.useEffect.checkScreenSize\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"useResponsiveTable.useEffect.checkScreenSize\"];\n            // Check on mount\n            checkScreenSize();\n            // Add event listener for window resize\n            window.addEventListener('resize', checkScreenSize);\n            // Cleanup\n            return ({\n                \"useResponsiveTable.useEffect\": ()=>window.removeEventListener('resize', checkScreenSize)\n            })[\"useResponsiveTable.useEffect\"];\n        }\n    }[\"useResponsiveTable.useEffect\"], []);\n    return isMobile;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VSZXNwb25zaXZlVGFibGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O3dFQUU0QztBQUU1Qzs7O0NBR0MsR0FDTSxNQUFNRSxxQkFBcUI7SUFDaEMsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdKLCtDQUFRQSxDQUFDO0lBRXpDQyxnREFBU0E7d0NBQUM7WUFDUixNQUFNSTtnRUFBa0I7b0JBQ3RCRCxZQUFZRSxPQUFPQyxVQUFVLEdBQUc7Z0JBQ2xDOztZQUVBLGlCQUFpQjtZQUNqQkY7WUFFQSx1Q0FBdUM7WUFDdkNDLE9BQU9FLGdCQUFnQixDQUFDLFVBQVVIO1lBRWxDLFVBQVU7WUFDVjtnREFBTyxJQUFNQyxPQUFPRyxtQkFBbUIsQ0FBQyxVQUFVSjs7UUFDcEQ7dUNBQUcsRUFBRTtJQUVMLE9BQU9GO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsiRTpcXFBST0pFQ1RTXFxwb3NcXHBvc2Zyb250ZW5kXFxzcmNcXGhvb2tzXFx1c2VSZXNwb25zaXZlVGFibGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5cbi8qKlxuICogSG9vayB0byBkZXRlcm1pbmUgaWYgd2Ugc2hvdWxkIHVzZSBtb2JpbGUgdGFibGUgbGF5b3V0XG4gKiBSZXR1cm5zIHRydWUgZm9yIG1vYmlsZSBkZXZpY2VzICh3aWR0aCA8IDc2OHB4KVxuICovXG5leHBvcnQgY29uc3QgdXNlUmVzcG9uc2l2ZVRhYmxlID0gKCkgPT4ge1xuICBjb25zdCBbaXNNb2JpbGUsIHNldElzTW9iaWxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGNoZWNrU2NyZWVuU2l6ZSA9ICgpID0+IHtcbiAgICAgIHNldElzTW9iaWxlKHdpbmRvdy5pbm5lcldpZHRoIDwgNzY4KTtcbiAgICB9O1xuXG4gICAgLy8gQ2hlY2sgb24gbW91bnRcbiAgICBjaGVja1NjcmVlblNpemUoKTtcblxuICAgIC8vIEFkZCBldmVudCBsaXN0ZW5lciBmb3Igd2luZG93IHJlc2l6ZVxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCBjaGVja1NjcmVlblNpemUpO1xuXG4gICAgLy8gQ2xlYW51cFxuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgY2hlY2tTY3JlZW5TaXplKTtcbiAgfSwgW10pO1xuXG4gIHJldHVybiBpc01vYmlsZTtcbn07XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSZXNwb25zaXZlVGFibGUiLCJpc01vYmlsZSIsInNldElzTW9iaWxlIiwiY2hlY2tTY3JlZW5TaXplIiwid2luZG93IiwiaW5uZXJXaWR0aCIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useResponsiveTable.ts\n"));

/***/ })

});