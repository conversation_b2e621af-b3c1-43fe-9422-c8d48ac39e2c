{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/shared-revalidate-timings.ts"], "sourcesContent": ["import type { PrerenderManifest } from '../../../build'\nimport type { DeepReadonly } from '../../../shared/lib/deep-readonly'\nimport type { Revalidate } from '../revalidate'\n\n/**\n * A shared cache of revalidate timings for routes. This cache is used so we\n * don't have to modify the prerender manifest when we want to update the\n * revalidate timings for a route.\n */\nexport class SharedRevalidateTimings {\n  /**\n   * The in-memory cache of revalidate timings for routes. This cache is\n   * populated when the cache is updated with new timings.\n   */\n  private static readonly timings = new Map<string, Revalidate>()\n\n  constructor(\n    /**\n     * The prerender manifest that contains the initial revalidate timings for\n     * routes.\n     */\n    private readonly prerenderManifest: DeepReadonly<\n      Pick<PrerenderManifest, 'routes' | 'dynamicRoutes'>\n    >\n  ) {}\n\n  /**\n   * Try to get the revalidate timings for a route. This will first try to get\n   * the timings from the in-memory cache. If the timings are not present in the\n   * in-memory cache, then the timings will be sourced from the prerender\n   * manifest.\n   *\n   * @param route the route to get the revalidate timings for\n   * @returns the revalidate timings for the route, or undefined if the timings\n   *          are not present in the in-memory cache or the prerender manifest\n   */\n  public get(route: string): Revalidate | undefined {\n    // This is a copy on write cache that is updated when the cache is updated.\n    // If the cache is never written to, then the timings will be sourced from\n    // the prerender manifest.\n    let revalidate = SharedRevalidateTimings.timings.get(route)\n    if (typeof revalidate !== 'undefined') return revalidate\n\n    revalidate = this.prerenderManifest.routes[route]?.initialRevalidateSeconds\n    if (typeof revalidate !== 'undefined') return revalidate\n\n    revalidate = this.prerenderManifest.dynamicRoutes[route]?.fallbackRevalidate\n    if (typeof revalidate !== 'undefined') return revalidate\n\n    return undefined\n  }\n\n  /**\n   * Set the revalidate timings for a route.\n   *\n   * @param route the route to set the revalidate timings for\n   * @param revalidate the revalidate timings for the route\n   */\n  public set(route: string, revalidate: Revalidate) {\n    SharedRevalidateTimings.timings.set(route, revalidate)\n  }\n\n  /**\n   * Clear the in-memory cache of revalidate timings for routes.\n   */\n  public clear() {\n    SharedRevalidateTimings.timings.clear()\n  }\n}\n"], "names": ["SharedRevalidateTimings", "timings", "Map", "constructor", "prerenderManifest", "get", "route", "revalidate", "routes", "initialRevalidateSeconds", "dynamicRoutes", "fallbackRevalidate", "undefined", "set", "clear"], "mappings": "AAIA;;;;CAIC,GACD,OAAO,MAAMA;gBACX;;;GAGC,QACuBC,UAAU,IAAIC;IAEtCC,YACE;;;KAGC,GACD,AAAiBC,iBAEhB,CACD;aAHiBA,oBAAAA;IAGhB;IAEH;;;;;;;;;GASC,GACD,AAAOC,IAAIC,KAAa,EAA0B;YAOnC,sCAGA;QATb,2EAA2E;QAC3E,0EAA0E;QAC1E,0BAA0B;QAC1B,IAAIC,aAAaP,wBAAwBC,OAAO,CAACI,GAAG,CAACC;QACrD,IAAI,OAAOC,eAAe,aAAa,OAAOA;QAE9CA,cAAa,uCAAA,IAAI,CAACH,iBAAiB,CAACI,MAAM,CAACF,MAAM,qBAApC,qCAAsCG,wBAAwB;QAC3E,IAAI,OAAOF,eAAe,aAAa,OAAOA;QAE9CA,cAAa,8CAAA,IAAI,CAACH,iBAAiB,CAACM,aAAa,CAACJ,MAAM,qBAA3C,4CAA6CK,kBAAkB;QAC5E,IAAI,OAAOJ,eAAe,aAAa,OAAOA;QAE9C,OAAOK;IACT;IAEA;;;;;GAKC,GACD,AAAOC,IAAIP,KAAa,EAAEC,UAAsB,EAAE;QAChDP,wBAAwBC,OAAO,CAACY,GAAG,CAACP,OAAOC;IAC7C;IAEA;;GAEC,GACD,AAAOO,QAAQ;QACbd,wBAAwBC,OAAO,CAACa,KAAK;IACvC;AACF"}