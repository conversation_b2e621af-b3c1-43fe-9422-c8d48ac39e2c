{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/index.ts"], "sourcesContent": ["import type { CacheFs } from '../../../shared/lib/utils'\nimport type { PrerenderManifest } from '../../../build'\nimport {\n  type IncrementalCacheValue,\n  type IncrementalCacheEntry,\n  type IncrementalCache as IncrementalCacheType,\n  IncrementalCacheKind,\n  CachedRouteKind,\n} from '../../response-cache'\nimport type { Revalidate } from '../revalidate'\nimport type { DeepReadonly } from '../../../shared/lib/deep-readonly'\n\nimport FetchCache from './fetch-cache'\nimport FileSystemCache from './file-system-cache'\nimport { normalizePagePath } from '../../../shared/lib/page-path/normalize-page-path'\n\nimport {\n  CACHE_ONE_YEAR,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n} from '../../../lib/constants'\nimport { toRoute } from '../to-route'\nimport { SharedRevalidateTimings } from './shared-revalidate-timings'\nimport { workUnitAsyncStorageInstance } from '../../app-render/work-unit-async-storage-instance'\nimport {\n  getPrerenderResumeDataCache,\n  getRenderResumeDataCache,\n} from '../../app-render/work-unit-async-storage.external'\n\nexport interface CacheHandlerContext {\n  fs?: CacheFs\n  dev?: boolean\n  flushToDisk?: boolean\n  serverDistDir?: string\n  maxMemoryCacheSize?: number\n  fetchCacheKeyPrefix?: string\n  prerenderManifest?: PrerenderManifest\n  revalidatedTags: string[]\n  _requestHeaders: IncrementalCache['requestHeaders']\n}\n\nexport interface CacheHandlerValue {\n  lastModified?: number\n  age?: number\n  cacheState?: string\n  value: IncrementalCacheValue | null\n}\n\nexport class CacheHandler {\n  // eslint-disable-next-line\n  constructor(_ctx: CacheHandlerContext) {}\n\n  public async get(\n    ..._args: Parameters<IncrementalCache['get']>\n  ): Promise<CacheHandlerValue | null> {\n    return {} as any\n  }\n\n  public async set(\n    ..._args: Parameters<IncrementalCache['set']>\n  ): Promise<void> {}\n\n  public async revalidateTag(\n    ..._args: Parameters<IncrementalCache['revalidateTag']>\n  ): Promise<void> {}\n\n  public resetRequestCache(): void {}\n}\n\nexport class IncrementalCache implements IncrementalCacheType {\n  readonly dev?: boolean\n  readonly disableForTestmode?: boolean\n  readonly cacheHandler?: CacheHandler\n  readonly hasCustomCacheHandler: boolean\n  readonly prerenderManifest: DeepReadonly<PrerenderManifest>\n  readonly requestHeaders: Record<string, undefined | string | string[]>\n  readonly requestProtocol?: 'http' | 'https'\n  readonly allowedRevalidateHeaderKeys?: string[]\n  readonly minimalMode?: boolean\n  readonly fetchCacheKeyPrefix?: string\n  readonly revalidatedTags?: string[]\n  readonly isOnDemandRevalidate?: boolean\n  readonly hasDynamicIO?: boolean\n\n  private readonly locks = new Map<string, Promise<void>>()\n\n  /**\n   * The revalidate timings for routes. This will source the timings from the\n   * prerender manifest until the in-memory cache is updated with new timings.\n   */\n  private readonly revalidateTimings: SharedRevalidateTimings\n\n  constructor({\n    fs,\n    dev,\n    dynamicIO,\n    flushToDisk,\n    fetchCache,\n    minimalMode,\n    serverDistDir,\n    requestHeaders,\n    requestProtocol,\n    maxMemoryCacheSize,\n    getPrerenderManifest,\n    fetchCacheKeyPrefix,\n    CurCacheHandler,\n    allowedRevalidateHeaderKeys,\n  }: {\n    fs?: CacheFs\n    dev: boolean\n    dynamicIO: boolean\n    fetchCache?: boolean\n    minimalMode?: boolean\n    serverDistDir?: string\n    flushToDisk?: boolean\n    requestProtocol?: 'http' | 'https'\n    allowedRevalidateHeaderKeys?: string[]\n    requestHeaders: IncrementalCache['requestHeaders']\n    maxMemoryCacheSize?: number\n    getPrerenderManifest: () => DeepReadonly<PrerenderManifest>\n    fetchCacheKeyPrefix?: string\n    CurCacheHandler?: typeof CacheHandler\n  }) {\n    const debug = !!process.env.NEXT_PRIVATE_DEBUG_CACHE\n    this.hasCustomCacheHandler = Boolean(CurCacheHandler)\n\n    const cacheHandlersSymbol = Symbol.for('@next/cache-handlers')\n    const _globalThis: typeof globalThis & {\n      [cacheHandlersSymbol]?: {\n        FetchCache?: typeof CacheHandler\n      }\n    } = globalThis\n\n    if (!CurCacheHandler) {\n      // if we have a global cache handler available leverage it\n      const globalCacheHandler = _globalThis[cacheHandlersSymbol]\n\n      if (globalCacheHandler?.FetchCache) {\n        CurCacheHandler = globalCacheHandler.FetchCache\n      } else {\n        if (fs && serverDistDir) {\n          if (debug) {\n            console.log('using filesystem cache handler')\n          }\n          CurCacheHandler = FileSystemCache\n        }\n        if (\n          FetchCache.isAvailable({ _requestHeaders: requestHeaders }) &&\n          minimalMode &&\n          fetchCache\n        ) {\n          if (debug) {\n            console.log('using fetch cache handler')\n          }\n          CurCacheHandler = FetchCache\n        }\n      }\n    } else if (debug) {\n      console.log('using custom cache handler', CurCacheHandler.name)\n    }\n\n    if (process.env.__NEXT_TEST_MAX_ISR_CACHE) {\n      // Allow cache size to be overridden for testing purposes\n      maxMemoryCacheSize = parseInt(process.env.__NEXT_TEST_MAX_ISR_CACHE, 10)\n    }\n    this.dev = dev\n    this.hasDynamicIO = dynamicIO\n    this.disableForTestmode = process.env.NEXT_PRIVATE_TEST_PROXY === 'true'\n    // this is a hack to avoid Webpack knowing this is equal to this.minimalMode\n    // because we replace this.minimalMode to true in production bundles.\n    const minimalModeKey = 'minimalMode'\n    this[minimalModeKey] = minimalMode\n    this.requestHeaders = requestHeaders\n    this.requestProtocol = requestProtocol\n    this.allowedRevalidateHeaderKeys = allowedRevalidateHeaderKeys\n    this.prerenderManifest = getPrerenderManifest()\n    this.revalidateTimings = new SharedRevalidateTimings(this.prerenderManifest)\n    this.fetchCacheKeyPrefix = fetchCacheKeyPrefix\n    let revalidatedTags: string[] = []\n\n    if (\n      requestHeaders[PRERENDER_REVALIDATE_HEADER] ===\n      this.prerenderManifest?.preview?.previewModeId\n    ) {\n      this.isOnDemandRevalidate = true\n    }\n\n    if (\n      minimalMode &&\n      typeof requestHeaders[NEXT_CACHE_REVALIDATED_TAGS_HEADER] === 'string' &&\n      requestHeaders[NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER] ===\n        this.prerenderManifest?.preview?.previewModeId\n    ) {\n      revalidatedTags =\n        requestHeaders[NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(',')\n    }\n\n    if (CurCacheHandler) {\n      this.cacheHandler = new CurCacheHandler({\n        dev,\n        fs,\n        flushToDisk,\n        serverDistDir,\n        revalidatedTags,\n        maxMemoryCacheSize,\n        _requestHeaders: requestHeaders,\n        fetchCacheKeyPrefix,\n      })\n    }\n  }\n\n  private calculateRevalidate(\n    pathname: string,\n    fromTime: number,\n    dev: boolean,\n    isFallback: boolean | undefined\n  ): Revalidate {\n    // in development we don't have a prerender-manifest\n    // and default to always revalidating to allow easier debugging\n    if (dev)\n      return Math.floor(performance.timeOrigin + performance.now() - 1000)\n\n    // if an entry isn't present in routes we fallback to a default\n    // of revalidating after 1 second unless it's a fallback request.\n    const initialRevalidateSeconds =\n      this.revalidateTimings.get(toRoute(pathname)) ?? (isFallback ? false : 1)\n\n    const revalidateAfter =\n      typeof initialRevalidateSeconds === 'number'\n        ? initialRevalidateSeconds * 1000 + fromTime\n        : initialRevalidateSeconds\n\n    return revalidateAfter\n  }\n\n  _getPathname(pathname: string, fetchCache?: boolean) {\n    return fetchCache ? pathname : normalizePagePath(pathname)\n  }\n\n  resetRequestCache() {\n    this.cacheHandler?.resetRequestCache?.()\n  }\n\n  async lock(cacheKey: string) {\n    let unlockNext: () => Promise<void> = () => Promise.resolve()\n    const existingLock = this.locks.get(cacheKey)\n\n    if (existingLock) {\n      await existingLock\n    }\n\n    const newLock = new Promise<void>((resolve) => {\n      unlockNext = async () => {\n        resolve()\n        this.locks.delete(cacheKey) // Remove the lock upon release\n      }\n    })\n\n    this.locks.set(cacheKey, newLock)\n    return unlockNext\n  }\n\n  async revalidateTag(tags: string | string[]): Promise<void> {\n    return this.cacheHandler?.revalidateTag?.(tags)\n  }\n\n  // x-ref: https://github.com/facebook/react/blob/2655c9354d8e1c54ba888444220f63e836925caa/packages/react/src/ReactFetch.js#L23\n  async generateCacheKey(\n    url: string,\n    init: RequestInit | Request = {}\n  ): Promise<string> {\n    // this should be bumped anytime a fix is made to cache entries\n    // that should bust the cache\n    const MAIN_KEY_PREFIX = 'v3'\n\n    const bodyChunks: string[] = []\n\n    const encoder = new TextEncoder()\n    const decoder = new TextDecoder()\n\n    if (init.body) {\n      // handle ReadableStream body\n      if (typeof (init.body as any).getReader === 'function') {\n        const readableBody = init.body as ReadableStream<Uint8Array | string>\n\n        const chunks: Uint8Array[] = []\n\n        try {\n          await readableBody.pipeTo(\n            new WritableStream({\n              write(chunk) {\n                if (typeof chunk === 'string') {\n                  chunks.push(encoder.encode(chunk))\n                  bodyChunks.push(chunk)\n                } else {\n                  chunks.push(chunk)\n                  bodyChunks.push(decoder.decode(chunk, { stream: true }))\n                }\n              },\n            })\n          )\n\n          // Flush the decoder.\n          bodyChunks.push(decoder.decode())\n\n          // Create a new buffer with all the chunks.\n          const length = chunks.reduce((total, arr) => total + arr.length, 0)\n          const arrayBuffer = new Uint8Array(length)\n\n          // Push each of the chunks into the new array buffer.\n          let offset = 0\n          for (const chunk of chunks) {\n            arrayBuffer.set(chunk, offset)\n            offset += chunk.length\n          }\n\n          ;(init as any)._ogBody = arrayBuffer\n        } catch (err) {\n          console.error('Problem reading body', err)\n        }\n      } // handle FormData or URLSearchParams bodies\n      else if (typeof (init.body as any).keys === 'function') {\n        const formData = init.body as FormData\n        ;(init as any)._ogBody = init.body\n        for (const key of new Set([...formData.keys()])) {\n          const values = formData.getAll(key)\n          bodyChunks.push(\n            `${key}=${(\n              await Promise.all(\n                values.map(async (val) => {\n                  if (typeof val === 'string') {\n                    return val\n                  } else {\n                    return await val.text()\n                  }\n                })\n              )\n            ).join(',')}`\n          )\n        }\n        // handle blob body\n      } else if (typeof (init.body as any).arrayBuffer === 'function') {\n        const blob = init.body as Blob\n        const arrayBuffer = await blob.arrayBuffer()\n        bodyChunks.push(await blob.text())\n        ;(init as any)._ogBody = new Blob([arrayBuffer], { type: blob.type })\n      } else if (typeof init.body === 'string') {\n        bodyChunks.push(init.body)\n        ;(init as any)._ogBody = init.body\n      }\n    }\n\n    const headers =\n      typeof (init.headers || {}).keys === 'function'\n        ? Object.fromEntries(init.headers as Headers)\n        : Object.assign({}, init.headers)\n\n    if ('traceparent' in headers) delete headers['traceparent']\n\n    const cacheString = JSON.stringify([\n      MAIN_KEY_PREFIX,\n      this.fetchCacheKeyPrefix || '',\n      url,\n      init.method,\n      headers,\n      init.mode,\n      init.redirect,\n      init.credentials,\n      init.referrer,\n      init.referrerPolicy,\n      init.integrity,\n      init.cache,\n      bodyChunks,\n    ])\n\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      function bufferToHex(buffer: ArrayBuffer): string {\n        return Array.prototype.map\n          .call(new Uint8Array(buffer), (b) => b.toString(16).padStart(2, '0'))\n          .join('')\n      }\n      const buffer = encoder.encode(cacheString)\n      return bufferToHex(await crypto.subtle.digest('SHA-256', buffer))\n    } else {\n      const crypto = require('crypto') as typeof import('crypto')\n      return crypto.createHash('sha256').update(cacheString).digest('hex')\n    }\n  }\n\n  // get data from cache if available\n  async get(\n    cacheKey: string,\n    ctx: {\n      kind: IncrementalCacheKind\n      revalidate?: Revalidate\n      fetchUrl?: string\n      fetchIdx?: number\n      tags?: string[]\n      softTags?: string[]\n      isRoutePPREnabled?: boolean\n      isFallback: boolean | undefined\n    }\n  ): Promise<IncrementalCacheEntry | null> {\n    // unlike other caches if we have a cacheScope we use it even if\n    // testmode would normally disable it or if requestHeaders say 'no-cache'.\n    if (this.hasDynamicIO && ctx.kind === IncrementalCacheKind.FETCH) {\n      const workUnitStore = workUnitAsyncStorageInstance.getStore()\n      const resumeDataCache = workUnitStore\n        ? getRenderResumeDataCache(workUnitStore)\n        : null\n      if (resumeDataCache) {\n        const memoryCacheData = resumeDataCache.fetch.get(cacheKey)\n        if (memoryCacheData?.kind === CachedRouteKind.FETCH) {\n          return {\n            isStale: false,\n            value: memoryCacheData,\n            revalidateAfter: false,\n            isFallback: false,\n          }\n        }\n      }\n    }\n\n    // we don't leverage the prerender cache in dev mode\n    // so that getStaticProps is always called for easier debugging\n    if (\n      this.disableForTestmode ||\n      (this.dev &&\n        (ctx.kind !== IncrementalCacheKind.FETCH ||\n          this.requestHeaders['cache-control'] === 'no-cache'))\n    ) {\n      return null\n    }\n\n    const { isFallback } = ctx\n\n    cacheKey = this._getPathname(\n      cacheKey,\n      ctx.kind === IncrementalCacheKind.FETCH\n    )\n    let entry: IncrementalCacheEntry | null = null\n    let revalidate = ctx.revalidate\n\n    const cacheData = await this.cacheHandler?.get(cacheKey, ctx)\n\n    if (cacheData?.value?.kind === CachedRouteKind.FETCH) {\n      const combinedTags = [...(ctx.tags || []), ...(ctx.softTags || [])]\n      // if a tag was revalidated we don't return stale data\n      if (\n        combinedTags.some((tag) => {\n          return this.revalidatedTags?.includes(tag)\n        })\n      ) {\n        return null\n      }\n\n      revalidate = revalidate || cacheData.value.revalidate\n      const age =\n        (performance.timeOrigin +\n          performance.now() -\n          (cacheData.lastModified || 0)) /\n        1000\n\n      const isStale = age > revalidate\n      const data = cacheData.value.data\n\n      return {\n        isStale: isStale,\n        value: {\n          kind: CachedRouteKind.FETCH,\n          data,\n          revalidate: revalidate,\n        },\n        revalidateAfter:\n          performance.timeOrigin + performance.now() + revalidate * 1000,\n        isFallback,\n      } satisfies IncrementalCacheEntry\n    }\n\n    const curRevalidate = this.revalidateTimings.get(toRoute(cacheKey))\n\n    let isStale: boolean | -1 | undefined\n    let revalidateAfter: Revalidate\n\n    if (cacheData?.lastModified === -1) {\n      isStale = -1\n      revalidateAfter = -1 * CACHE_ONE_YEAR\n    } else {\n      revalidateAfter = this.calculateRevalidate(\n        cacheKey,\n        cacheData?.lastModified || performance.timeOrigin + performance.now(),\n        this.dev ? ctx.kind !== IncrementalCacheKind.FETCH : false,\n        ctx.isFallback\n      )\n      isStale =\n        revalidateAfter !== false &&\n        revalidateAfter < performance.timeOrigin + performance.now()\n          ? true\n          : undefined\n    }\n\n    if (cacheData) {\n      entry = {\n        isStale,\n        curRevalidate,\n        revalidateAfter,\n        value: cacheData.value,\n        isFallback,\n      }\n    }\n\n    if (\n      !cacheData &&\n      this.prerenderManifest.notFoundRoutes.includes(cacheKey)\n    ) {\n      // for the first hit after starting the server the cache\n      // may not have a way to save notFound: true so if\n      // the prerender-manifest marks this as notFound then we\n      // return that entry and trigger a cache set to give it a\n      // chance to update in-memory entries\n      entry = {\n        isStale,\n        value: null,\n        curRevalidate,\n        revalidateAfter,\n        isFallback,\n      }\n      this.set(cacheKey, entry.value, ctx)\n    }\n    return entry\n  }\n\n  // populate the incremental cache with new data\n  async set(\n    pathname: string,\n    data: IncrementalCacheValue | null,\n    ctx: {\n      revalidate?: Revalidate\n      fetchCache?: boolean\n      fetchUrl?: string\n      fetchIdx?: number\n      tags?: string[]\n      isRoutePPREnabled?: boolean\n      isFallback?: boolean\n    }\n  ) {\n    // Even if we otherwise disable caching for testMode or if no fetchCache is configured\n    // we still always stash results in the cacheScope if one exists. This is because this\n    // is a transient in memory cache that populates caches ahead of a dynamic render in dev mode\n    // to allow the RSC debug info to have the right environment associated to it.\n    if (this.hasDynamicIO && data?.kind === CachedRouteKind.FETCH) {\n      const workUnitStore = workUnitAsyncStorageInstance.getStore()\n      const prerenderResumeDataCache = workUnitStore\n        ? getPrerenderResumeDataCache(workUnitStore)\n        : null\n      if (prerenderResumeDataCache) {\n        prerenderResumeDataCache.fetch.set(pathname, data)\n      }\n    }\n\n    if (this.disableForTestmode || (this.dev && !ctx.fetchCache)) return\n\n    pathname = this._getPathname(pathname, ctx.fetchCache)\n\n    // FetchCache has upper limit of 2MB per-entry currently\n    const itemSize = JSON.stringify(data).length\n    if (\n      ctx.fetchCache &&\n      // we don't show this error/warning when a custom cache handler is being used\n      // as it might not have this limit\n      !this.hasCustomCacheHandler &&\n      itemSize > 2 * 1024 * 1024\n    ) {\n      if (this.dev) {\n        throw new Error(\n          `Failed to set Next.js data cache, items over 2MB can not be cached (${itemSize} bytes)`\n        )\n      }\n      return\n    }\n\n    try {\n      // Set the value for the revalidate seconds so if it changes we can\n      // update the cache with the new value.\n      if (typeof ctx.revalidate !== 'undefined' && !ctx.fetchCache) {\n        this.revalidateTimings.set(toRoute(pathname), ctx.revalidate)\n      }\n\n      await this.cacheHandler?.set(pathname, data, ctx)\n    } catch (error) {\n      console.warn('Failed to update prerender cache for', pathname, error)\n    }\n  }\n}\n"], "names": ["IncrementalCacheKind", "CachedRouteKind", "<PERSON><PERSON><PERSON><PERSON>", "FileSystemCache", "normalizePagePath", "CACHE_ONE_YEAR", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "PRERENDER_REVALIDATE_HEADER", "toRoute", "SharedRevalidateTimings", "workUnitAsyncStorageInstance", "getPrerenderResumeDataCache", "getRenderResumeDataCache", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "_ctx", "get", "_args", "set", "revalidateTag", "resetRequestCache", "IncrementalCache", "fs", "dev", "dynamicIO", "flushToDisk", "fetchCache", "minimalMode", "serverDistDir", "requestHeaders", "requestProtocol", "maxMemoryCacheSize", "getPrerenderManifest", "fetchCacheKeyPrefix", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "allowedRevalidateHeaderKeys", "locks", "Map", "debug", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "hasCustomCacheHandler", "Boolean", "cacheHandlersSymbol", "Symbol", "for", "_globalThis", "globalThis", "globalCacheHandler", "console", "log", "isAvailable", "_requestHeaders", "name", "__NEXT_TEST_MAX_ISR_CACHE", "parseInt", "hasDynamicIO", "disableForTestmode", "NEXT_PRIVATE_TEST_PROXY", "minimalModeKey", "prerenderManifest", "revalidateTimings", "revalidatedTags", "preview", "previewModeId", "isOnDemandRevalidate", "split", "cache<PERSON><PERSON><PERSON>", "calculateRevalidate", "pathname", "fromTime", "<PERSON><PERSON><PERSON><PERSON>", "Math", "floor", "performance", "<PERSON><PERSON><PERSON><PERSON>", "now", "initialRevalidateSeconds", "revalidateAfter", "_getPathname", "lock", "cache<PERSON>ey", "unlockNext", "Promise", "resolve", "existingLock", "newLock", "delete", "tags", "generate<PERSON>ache<PERSON>ey", "url", "init", "MAIN_KEY_PREFIX", "bodyChunks", "encoder", "TextEncoder", "decoder", "TextDecoder", "body", "<PERSON><PERSON><PERSON><PERSON>", "readableBody", "chunks", "pipeTo", "WritableStream", "write", "chunk", "push", "encode", "decode", "stream", "length", "reduce", "total", "arr", "arrayBuffer", "Uint8Array", "offset", "_ogBody", "err", "error", "keys", "formData", "key", "Set", "values", "getAll", "all", "map", "val", "text", "join", "blob", "Blob", "type", "headers", "Object", "fromEntries", "assign", "cacheString", "JSON", "stringify", "method", "mode", "redirect", "credentials", "referrer", "referrerPolicy", "integrity", "cache", "NEXT_RUNTIME", "bufferToHex", "buffer", "Array", "prototype", "call", "b", "toString", "padStart", "crypto", "subtle", "digest", "require", "createHash", "update", "ctx", "cacheData", "kind", "FETCH", "workUnitStore", "getStore", "resumeDataCache", "memoryCacheData", "fetch", "isStale", "value", "entry", "revalidate", "combinedTags", "softTags", "some", "tag", "includes", "age", "lastModified", "data", "curRevalidate", "undefined", "notFoundRoutes", "prerenderResumeDataCache", "itemSize", "Error", "warn"], "mappings": "AAEA,SAIEA,oBAAoB,EACpBC,eAAe,QACV,uBAAsB;AAI7B,OAAOC,gBAAgB,gBAAe;AACtC,OAAOC,qBAAqB,sBAAqB;AACjD,SAASC,iBAAiB,QAAQ,oDAAmD;AAErF,SACEC,cAAc,EACdC,kCAAkC,EAClCC,sCAAsC,EACtCC,2BAA2B,QACtB,yBAAwB;AAC/B,SAASC,OAAO,QAAQ,cAAa;AACrC,SAASC,uBAAuB,QAAQ,8BAA6B;AACrE,SAASC,4BAA4B,QAAQ,oDAAmD;AAChG,SACEC,2BAA2B,EAC3BC,wBAAwB,QACnB,oDAAmD;AAqB1D,OAAO,MAAMC;IACX,2BAA2B;IAC3BC,YAAYC,IAAyB,CAAE,CAAC;IAExC,MAAaC,IACX,GAAGC,KAA0C,EACV;QACnC,OAAO,CAAC;IACV;IAEA,MAAaC,IACX,GAAGD,KAA0C,EAC9B,CAAC;IAElB,MAAaE,cACX,GAAGF,KAAoD,EACxC,CAAC;IAEXG,oBAA0B,CAAC;AACpC;AAEA,OAAO,MAAMC;IAuBXP,YAAY,EACVQ,EAAE,EACFC,GAAG,EACHC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,WAAW,EACXC,aAAa,EACbC,cAAc,EACdC,eAAe,EACfC,kBAAkB,EAClBC,oBAAoB,EACpBC,mBAAmB,EACnBC,eAAe,EACfC,2BAA2B,EAgB5B,CAAE;YA4DC,iCAAA,yBASE,kCAAA;aA3GWC,QAAQ,IAAIC;QAuC3B,MAAMC,QAAQ,CAAC,CAACC,QAAQC,GAAG,CAACC,wBAAwB;QACpD,IAAI,CAACC,qBAAqB,GAAGC,QAAQT;QAErC,MAAMU,sBAAsBC,OAAOC,GAAG,CAAC;QACvC,MAAMC,cAIFC;QAEJ,IAAI,CAACd,iBAAiB;YACpB,0DAA0D;YAC1D,MAAMe,qBAAqBF,WAAW,CAACH,oBAAoB;YAE3D,IAAIK,sCAAAA,mBAAoBhD,UAAU,EAAE;gBAClCiC,kBAAkBe,mBAAmBhD,UAAU;YACjD,OAAO;gBACL,IAAIqB,MAAMM,eAAe;oBACvB,IAAIU,OAAO;wBACTY,QAAQC,GAAG,CAAC;oBACd;oBACAjB,kBAAkBhC;gBACpB;gBACA,IACED,WAAWmD,WAAW,CAAC;oBAAEC,iBAAiBxB;gBAAe,MACzDF,eACAD,YACA;oBACA,IAAIY,OAAO;wBACTY,QAAQC,GAAG,CAAC;oBACd;oBACAjB,kBAAkBjC;gBACpB;YACF;QACF,OAAO,IAAIqC,OAAO;YAChBY,QAAQC,GAAG,CAAC,8BAA8BjB,gBAAgBoB,IAAI;QAChE;QAEA,IAAIf,QAAQC,GAAG,CAACe,yBAAyB,EAAE;YACzC,yDAAyD;YACzDxB,qBAAqByB,SAASjB,QAAQC,GAAG,CAACe,yBAAyB,EAAE;QACvE;QACA,IAAI,CAAChC,GAAG,GAAGA;QACX,IAAI,CAACkC,YAAY,GAAGjC;QACpB,IAAI,CAACkC,kBAAkB,GAAGnB,QAAQC,GAAG,CAACmB,uBAAuB,KAAK;QAClE,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMC,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAAGjC;QACvB,IAAI,CAACE,cAAc,GAAGA;QACtB,IAAI,CAACC,eAAe,GAAGA;QACvB,IAAI,CAACK,2BAA2B,GAAGA;QACnC,IAAI,CAAC0B,iBAAiB,GAAG7B;QACzB,IAAI,CAAC8B,iBAAiB,GAAG,IAAIrD,wBAAwB,IAAI,CAACoD,iBAAiB;QAC3E,IAAI,CAAC5B,mBAAmB,GAAGA;QAC3B,IAAI8B,kBAA4B,EAAE;QAElC,IACElC,cAAc,CAACtB,4BAA4B,OAC3C,0BAAA,IAAI,CAACsD,iBAAiB,sBAAtB,kCAAA,wBAAwBG,OAAO,qBAA/B,gCAAiCC,aAAa,GAC9C;YACA,IAAI,CAACC,oBAAoB,GAAG;QAC9B;QAEA,IACEvC,eACA,OAAOE,cAAc,CAACxB,mCAAmC,KAAK,YAC9DwB,cAAc,CAACvB,uCAAuC,OACpD,2BAAA,IAAI,CAACuD,iBAAiB,sBAAtB,mCAAA,yBAAwBG,OAAO,qBAA/B,iCAAiCC,aAAa,GAChD;YACAF,kBACElC,cAAc,CAACxB,mCAAmC,CAAC8D,KAAK,CAAC;QAC7D;QAEA,IAAIjC,iBAAiB;YACnB,IAAI,CAACkC,YAAY,GAAG,IAAIlC,gBAAgB;gBACtCX;gBACAD;gBACAG;gBACAG;gBACAmC;gBACAhC;gBACAsB,iBAAiBxB;gBACjBI;YACF;QACF;IACF;IAEQoC,oBACNC,QAAgB,EAChBC,QAAgB,EAChBhD,GAAY,EACZiD,UAA+B,EACnB;QACZ,oDAAoD;QACpD,+DAA+D;QAC/D,IAAIjD,KACF,OAAOkD,KAAKC,KAAK,CAACC,YAAYC,UAAU,GAAGD,YAAYE,GAAG,KAAK;QAEjE,+DAA+D;QAC/D,iEAAiE;QACjE,MAAMC,2BACJ,IAAI,CAAChB,iBAAiB,CAAC9C,GAAG,CAACR,QAAQ8D,cAAeE,CAAAA,aAAa,QAAQ,CAAA;QAEzE,MAAMO,kBACJ,OAAOD,6BAA6B,WAChCA,2BAA2B,OAAOP,WAClCO;QAEN,OAAOC;IACT;IAEAC,aAAaV,QAAgB,EAAE5C,UAAoB,EAAE;QACnD,OAAOA,aAAa4C,WAAWnE,kBAAkBmE;IACnD;IAEAlD,oBAAoB;YAClB,sCAAA;SAAA,qBAAA,IAAI,CAACgD,YAAY,sBAAjB,uCAAA,mBAAmBhD,iBAAiB,qBAApC,0CAAA;IACF;IAEA,MAAM6D,KAAKC,QAAgB,EAAE;QAC3B,IAAIC,aAAkC,IAAMC,QAAQC,OAAO;QAC3D,MAAMC,eAAe,IAAI,CAAClD,KAAK,CAACpB,GAAG,CAACkE;QAEpC,IAAII,cAAc;YAChB,MAAMA;QACR;QAEA,MAAMC,UAAU,IAAIH,QAAc,CAACC;YACjCF,aAAa;gBACXE;gBACA,IAAI,CAACjD,KAAK,CAACoD,MAAM,CAACN,UAAU,+BAA+B;;YAC7D;QACF;QAEA,IAAI,CAAC9C,KAAK,CAAClB,GAAG,CAACgE,UAAUK;QACzB,OAAOJ;IACT;IAEA,MAAMhE,cAAcsE,IAAuB,EAAiB;YACnD,kCAAA;QAAP,QAAO,qBAAA,IAAI,CAACrB,YAAY,sBAAjB,mCAAA,mBAAmBjD,aAAa,qBAAhC,sCAAA,oBAAmCsE;IAC5C;IAEA,8HAA8H;IAC9H,MAAMC,iBACJC,GAAW,EACXC,OAA8B,CAAC,CAAC,EACf;QACjB,+DAA+D;QAC/D,6BAA6B;QAC7B,MAAMC,kBAAkB;QAExB,MAAMC,aAAuB,EAAE;QAE/B,MAAMC,UAAU,IAAIC;QACpB,MAAMC,UAAU,IAAIC;QAEpB,IAAIN,KAAKO,IAAI,EAAE;YACb,6BAA6B;YAC7B,IAAI,OAAO,AAACP,KAAKO,IAAI,CAASC,SAAS,KAAK,YAAY;gBACtD,MAAMC,eAAeT,KAAKO,IAAI;gBAE9B,MAAMG,SAAuB,EAAE;gBAE/B,IAAI;oBACF,MAAMD,aAAaE,MAAM,CACvB,IAAIC,eAAe;wBACjBC,OAAMC,KAAK;4BACT,IAAI,OAAOA,UAAU,UAAU;gCAC7BJ,OAAOK,IAAI,CAACZ,QAAQa,MAAM,CAACF;gCAC3BZ,WAAWa,IAAI,CAACD;4BAClB,OAAO;gCACLJ,OAAOK,IAAI,CAACD;gCACZZ,WAAWa,IAAI,CAACV,QAAQY,MAAM,CAACH,OAAO;oCAAEI,QAAQ;gCAAK;4BACvD;wBACF;oBACF;oBAGF,qBAAqB;oBACrBhB,WAAWa,IAAI,CAACV,QAAQY,MAAM;oBAE9B,2CAA2C;oBAC3C,MAAME,SAAST,OAAOU,MAAM,CAAC,CAACC,OAAOC,MAAQD,QAAQC,IAAIH,MAAM,EAAE;oBACjE,MAAMI,cAAc,IAAIC,WAAWL;oBAEnC,qDAAqD;oBACrD,IAAIM,SAAS;oBACb,KAAK,MAAMX,SAASJ,OAAQ;wBAC1Ba,YAAYjG,GAAG,CAACwF,OAAOW;wBACvBA,UAAUX,MAAMK,MAAM;oBACxB;;oBAEEnB,KAAa0B,OAAO,GAAGH;gBAC3B,EAAE,OAAOI,KAAK;oBACZrE,QAAQsE,KAAK,CAAC,wBAAwBD;gBACxC;YACF,OACK,IAAI,OAAO,AAAC3B,KAAKO,IAAI,CAASsB,IAAI,KAAK,YAAY;gBACtD,MAAMC,WAAW9B,KAAKO,IAAI;gBACxBP,KAAa0B,OAAO,GAAG1B,KAAKO,IAAI;gBAClC,KAAK,MAAMwB,OAAO,IAAIC,IAAI;uBAAIF,SAASD,IAAI;iBAAG,EAAG;oBAC/C,MAAMI,SAASH,SAASI,MAAM,CAACH;oBAC/B7B,WAAWa,IAAI,CACb,GAAGgB,IAAI,CAAC,EAAE,AACR,CAAA,MAAMvC,QAAQ2C,GAAG,CACfF,OAAOG,GAAG,CAAC,OAAOC;wBAChB,IAAI,OAAOA,QAAQ,UAAU;4BAC3B,OAAOA;wBACT,OAAO;4BACL,OAAO,MAAMA,IAAIC,IAAI;wBACvB;oBACF,GACF,EACAC,IAAI,CAAC,MAAM;gBAEjB;YACA,mBAAmB;YACrB,OAAO,IAAI,OAAO,AAACvC,KAAKO,IAAI,CAASgB,WAAW,KAAK,YAAY;gBAC/D,MAAMiB,OAAOxC,KAAKO,IAAI;gBACtB,MAAMgB,cAAc,MAAMiB,KAAKjB,WAAW;gBAC1CrB,WAAWa,IAAI,CAAC,MAAMyB,KAAKF,IAAI;gBAC7BtC,KAAa0B,OAAO,GAAG,IAAIe,KAAK;oBAAClB;iBAAY,EAAE;oBAAEmB,MAAMF,KAAKE,IAAI;gBAAC;YACrE,OAAO,IAAI,OAAO1C,KAAKO,IAAI,KAAK,UAAU;gBACxCL,WAAWa,IAAI,CAACf,KAAKO,IAAI;gBACvBP,KAAa0B,OAAO,GAAG1B,KAAKO,IAAI;YACpC;QACF;QAEA,MAAMoC,UACJ,OAAO,AAAC3C,CAAAA,KAAK2C,OAAO,IAAI,CAAC,CAAA,EAAGd,IAAI,KAAK,aACjCe,OAAOC,WAAW,CAAC7C,KAAK2C,OAAO,IAC/BC,OAAOE,MAAM,CAAC,CAAC,GAAG9C,KAAK2C,OAAO;QAEpC,IAAI,iBAAiBA,SAAS,OAAOA,OAAO,CAAC,cAAc;QAE3D,MAAMI,cAAcC,KAAKC,SAAS,CAAC;YACjChD;YACA,IAAI,CAAC5D,mBAAmB,IAAI;YAC5B0D;YACAC,KAAKkD,MAAM;YACXP;YACA3C,KAAKmD,IAAI;YACTnD,KAAKoD,QAAQ;YACbpD,KAAKqD,WAAW;YAChBrD,KAAKsD,QAAQ;YACbtD,KAAKuD,cAAc;YACnBvD,KAAKwD,SAAS;YACdxD,KAAKyD,KAAK;YACVvD;SACD;QAED,IAAIvD,QAAQC,GAAG,CAAC8G,YAAY,KAAK,QAAQ;YACvC,SAASC,YAAYC,MAAmB;gBACtC,OAAOC,MAAMC,SAAS,CAAC1B,GAAG,CACvB2B,IAAI,CAAC,IAAIvC,WAAWoC,SAAS,CAACI,IAAMA,EAAEC,QAAQ,CAAC,IAAIC,QAAQ,CAAC,GAAG,MAC/D3B,IAAI,CAAC;YACV;YACA,MAAMqB,SAASzD,QAAQa,MAAM,CAAC+B;YAC9B,OAAOY,YAAY,MAAMQ,OAAOC,MAAM,CAACC,MAAM,CAAC,WAAWT;QAC3D,OAAO;YACL,MAAMO,UAASG,QAAQ;YACvB,OAAOH,QAAOI,UAAU,CAAC,UAAUC,MAAM,CAACzB,aAAasB,MAAM,CAAC;QAChE;IACF;IAEA,mCAAmC;IACnC,MAAMjJ,IACJkE,QAAgB,EAChBmF,GASC,EACsC;YAyCf,oBAEpBC;QA1CJ,gEAAgE;QAChE,0EAA0E;QAC1E,IAAI,IAAI,CAAC7G,YAAY,IAAI4G,IAAIE,IAAI,KAAKxK,qBAAqByK,KAAK,EAAE;YAChE,MAAMC,gBAAgB/J,6BAA6BgK,QAAQ;YAC3D,MAAMC,kBAAkBF,gBACpB7J,yBAAyB6J,iBACzB;YACJ,IAAIE,iBAAiB;gBACnB,MAAMC,kBAAkBD,gBAAgBE,KAAK,CAAC7J,GAAG,CAACkE;gBAClD,IAAI0F,CAAAA,mCAAAA,gBAAiBL,IAAI,MAAKvK,gBAAgBwK,KAAK,EAAE;oBACnD,OAAO;wBACLM,SAAS;wBACTC,OAAOH;wBACP7F,iBAAiB;wBACjBP,YAAY;oBACd;gBACF;YACF;QACF;QAEA,oDAAoD;QACpD,+DAA+D;QAC/D,IACE,IAAI,CAACd,kBAAkB,IACtB,IAAI,CAACnC,GAAG,IACN8I,CAAAA,IAAIE,IAAI,KAAKxK,qBAAqByK,KAAK,IACtC,IAAI,CAAC3I,cAAc,CAAC,gBAAgB,KAAK,UAAS,GACtD;YACA,OAAO;QACT;QAEA,MAAM,EAAE2C,UAAU,EAAE,GAAG6F;QAEvBnF,WAAW,IAAI,CAACF,YAAY,CAC1BE,UACAmF,IAAIE,IAAI,KAAKxK,qBAAqByK,KAAK;QAEzC,IAAIQ,QAAsC;QAC1C,IAAIC,aAAaZ,IAAIY,UAAU;QAE/B,MAAMX,YAAY,QAAM,qBAAA,IAAI,CAAClG,YAAY,qBAAjB,mBAAmBpD,GAAG,CAACkE,UAAUmF;QAEzD,IAAIC,CAAAA,8BAAAA,mBAAAA,UAAWS,KAAK,qBAAhBT,iBAAkBC,IAAI,MAAKvK,gBAAgBwK,KAAK,EAAE;YACpD,MAAMU,eAAe;mBAAKb,IAAI5E,IAAI,IAAI,EAAE;mBAAO4E,IAAIc,QAAQ,IAAI,EAAE;aAAE;YACnE,sDAAsD;YACtD,IACED,aAAaE,IAAI,CAAC,CAACC;oBACV;gBAAP,QAAO,wBAAA,IAAI,CAACtH,eAAe,qBAApB,sBAAsBuH,QAAQ,CAACD;YACxC,IACA;gBACA,OAAO;YACT;YAEAJ,aAAaA,cAAcX,UAAUS,KAAK,CAACE,UAAU;YACrD,MAAMM,MACJ,AAAC5G,CAAAA,YAAYC,UAAU,GACrBD,YAAYE,GAAG,KACdyF,CAAAA,UAAUkB,YAAY,IAAI,CAAA,CAAC,IAC9B;YAEF,MAAMV,UAAUS,MAAMN;YACtB,MAAMQ,OAAOnB,UAAUS,KAAK,CAACU,IAAI;YAEjC,OAAO;gBACLX,SAASA;gBACTC,OAAO;oBACLR,MAAMvK,gBAAgBwK,KAAK;oBAC3BiB;oBACAR,YAAYA;gBACd;gBACAlG,iBACEJ,YAAYC,UAAU,GAAGD,YAAYE,GAAG,KAAKoG,aAAa;gBAC5DzG;YACF;QACF;QAEA,MAAMkH,gBAAgB,IAAI,CAAC5H,iBAAiB,CAAC9C,GAAG,CAACR,QAAQ0E;QAEzD,IAAI4F;QACJ,IAAI/F;QAEJ,IAAIuF,CAAAA,6BAAAA,UAAWkB,YAAY,MAAK,CAAC,GAAG;YAClCV,UAAU,CAAC;YACX/F,kBAAkB,CAAC,IAAI3E;QACzB,OAAO;YACL2E,kBAAkB,IAAI,CAACV,mBAAmB,CACxCa,UACAoF,CAAAA,6BAAAA,UAAWkB,YAAY,KAAI7G,YAAYC,UAAU,GAAGD,YAAYE,GAAG,IACnE,IAAI,CAACtD,GAAG,GAAG8I,IAAIE,IAAI,KAAKxK,qBAAqByK,KAAK,GAAG,OACrDH,IAAI7F,UAAU;YAEhBsG,UACE/F,oBAAoB,SACpBA,kBAAkBJ,YAAYC,UAAU,GAAGD,YAAYE,GAAG,KACtD,OACA8G;QACR;QAEA,IAAIrB,WAAW;YACbU,QAAQ;gBACNF;gBACAY;gBACA3G;gBACAgG,OAAOT,UAAUS,KAAK;gBACtBvG;YACF;QACF;QAEA,IACE,CAAC8F,aACD,IAAI,CAACzG,iBAAiB,CAAC+H,cAAc,CAACN,QAAQ,CAACpG,WAC/C;YACA,wDAAwD;YACxD,kDAAkD;YAClD,wDAAwD;YACxD,yDAAyD;YACzD,qCAAqC;YACrC8F,QAAQ;gBACNF;gBACAC,OAAO;gBACPW;gBACA3G;gBACAP;YACF;YACA,IAAI,CAACtD,GAAG,CAACgE,UAAU8F,MAAMD,KAAK,EAAEV;QAClC;QACA,OAAOW;IACT;IAEA,+CAA+C;IAC/C,MAAM9J,IACJoD,QAAgB,EAChBmH,IAAkC,EAClCpB,GAQC,EACD;QACA,sFAAsF;QACtF,sFAAsF;QACtF,6FAA6F;QAC7F,8EAA8E;QAC9E,IAAI,IAAI,CAAC5G,YAAY,IAAIgI,CAAAA,wBAAAA,KAAMlB,IAAI,MAAKvK,gBAAgBwK,KAAK,EAAE;YAC7D,MAAMC,gBAAgB/J,6BAA6BgK,QAAQ;YAC3D,MAAMmB,2BAA2BpB,gBAC7B9J,4BAA4B8J,iBAC5B;YACJ,IAAIoB,0BAA0B;gBAC5BA,yBAAyBhB,KAAK,CAAC3J,GAAG,CAACoD,UAAUmH;YAC/C;QACF;QAEA,IAAI,IAAI,CAAC/H,kBAAkB,IAAK,IAAI,CAACnC,GAAG,IAAI,CAAC8I,IAAI3I,UAAU,EAAG;QAE9D4C,WAAW,IAAI,CAACU,YAAY,CAACV,UAAU+F,IAAI3I,UAAU;QAErD,wDAAwD;QACxD,MAAMoK,WAAWlD,KAAKC,SAAS,CAAC4C,MAAM1E,MAAM;QAC5C,IACEsD,IAAI3I,UAAU,IACd,6EAA6E;QAC7E,kCAAkC;QAClC,CAAC,IAAI,CAACgB,qBAAqB,IAC3BoJ,WAAW,IAAI,OAAO,MACtB;YACA,IAAI,IAAI,CAACvK,GAAG,EAAE;gBACZ,MAAM,IAAIwK,MACR,CAAC,oEAAoE,EAAED,SAAS,OAAO,CAAC;YAE5F;YACA;QACF;QAEA,IAAI;gBAOI;YANN,mEAAmE;YACnE,uCAAuC;YACvC,IAAI,OAAOzB,IAAIY,UAAU,KAAK,eAAe,CAACZ,IAAI3I,UAAU,EAAE;gBAC5D,IAAI,CAACoC,iBAAiB,CAAC5C,GAAG,CAACV,QAAQ8D,WAAW+F,IAAIY,UAAU;YAC9D;YAEA,QAAM,qBAAA,IAAI,CAAC7G,YAAY,qBAAjB,mBAAmBlD,GAAG,CAACoD,UAAUmH,MAAMpB;QAC/C,EAAE,OAAO7C,OAAO;YACdtE,QAAQ8I,IAAI,CAAC,wCAAwC1H,UAAUkD;QACjE;IACF;AACF"}