'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { Navbar, <PERSON><PERSON>, ScrollToTop } from '../../components'

// Form data interface
interface ContactFormData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  businessName?: string
  subject: string
  message: string
  newsletter: boolean
}

export default function Contact() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<{
    type: 'success' | 'error' | null
    message: string
  }>({ type: null, message: '' })

  // React Hook Form setup
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<ContactFormData>({
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      businessName: '',
      subject: '',
      message: '',
      newsletter: false
    }
  })

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index)
  }

  // Function to scroll to message area
  const scrollToMessage = () => {
    setTimeout(() => {
      const messageElement = document.getElementById('submit-status')
      if (messageElement) {
        // Remove any existing pulse animation first
        messageElement.classList.remove('animate-pulse')

        // Scroll to the message with some offset to ensure it's visible
        const elementPosition = messageElement.offsetTop
        const offsetPosition = elementPosition - 120 // 120px offset from top for better visibility

        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        })

        // Add a brief highlight effect after scrolling
        setTimeout(() => {
          messageElement.classList.add('animate-pulse')
          setTimeout(() => {
            messageElement.classList.remove('animate-pulse')
          }, 2000) // Remove pulse after 2 seconds
        }, 500) // Start pulse after scroll completes
      }
    }, 150) // Slightly longer delay to ensure DOM is updated
  }

  // Form submission handler
  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true)
    setSubmitStatus({ type: null, message: '' })

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (result.success) {
        setSubmitStatus({
          type: 'success',
          message: result.message
        })
        reset() // Clear form
        scrollToMessage() // Scroll to success message
      } else {
        setSubmitStatus({
          type: 'error',
          message: result.message || 'There was an error sending your message. Please try again.'
        })
        scrollToMessage() // Scroll to error message
      }
    } catch (error) {
      console.error('Form submission error:', error)
      setSubmitStatus({
        type: 'error',
        message: 'Network error. Please check your connection and try again.'
      })
      scrollToMessage() // Scroll to error message
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-white">
      <Navbar currentPage="contact" />

      {/* Hero Section */}
      <section className="pt-36 pb-20 bg-gradient-to-br from-blue-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-block bg-gradient-to-r from-green-100 to-blue-100 text-green-800 px-6 py-3 rounded-full text-sm font-semibold mb-6 shadow-lg">
              📞 Free Consultation Available • 🎯 Personalized Demo
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Get Your <span className="text-blue-600 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Demo</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-8">
              Ready to see NEXAPO in action? Schedule a personalized demo and discover
              how our POS system can transform your business operations.
            </p>

            {/* Contact Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12 max-w-4xl mx-auto">
              <div className="text-center bg-white rounded-2xl p-6 shadow-lg border border-blue-100 hover:shadow-xl transition-shadow">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">⏱️</span>
                </div>
                <div className="text-3xl font-bold text-blue-600 mb-2">30min</div>
                <div className="text-gray-600 font-medium">Demo Duration</div>
                <div className="text-sm text-gray-500 mt-1">Quick & comprehensive</div>
              </div>
              <div className="text-center bg-white rounded-2xl p-6 shadow-lg border border-green-100 hover:shadow-xl transition-shadow">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">⚡</span>
                </div>
                <div className="text-3xl font-bold text-green-600 mb-2">24hrs</div>
                <div className="text-gray-600 font-medium">Response Time</div>
                <div className="text-sm text-gray-500 mt-1">Usually within 2 hours</div>
              </div>
              <div className="text-center bg-white rounded-2xl p-6 shadow-lg border border-purple-100 hover:shadow-xl transition-shadow">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🎁</span>
                </div>
                <div className="text-3xl font-bold text-purple-600 mb-2">100%</div>
                <div className="text-gray-600 font-medium">Free Service</div>
                <div className="text-sm text-gray-500 mt-1">No hidden costs</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Multiple Ways to Reach Us
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Choose the method that works best for you. Our team is ready to help you get started with NEXAPO.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <div className="text-center bg-blue-50 rounded-2xl p-8">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📞</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Call Us</h3>
              <p className="text-gray-600 mb-2">+233 24 814 1505</p>
              <p className="text-sm text-gray-500">Mon-Fri 8AM-6PM</p>
            </div>

            <div className="text-center bg-green-50 rounded-2xl p-8">
              <div className="w-16 h-16 bg-green-600 text-white rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📱</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">WhatsApp</h3>
              <p className="text-gray-600 mb-2">+233 24 814 1505</p>
              <p className="text-sm text-gray-500">24/7 Available</p>
            </div>

            <div className="text-center bg-purple-50 rounded-2xl p-8">
              <div className="w-16 h-16 bg-purple-600 text-white rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📧</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Email</h3>
              <p className="text-gray-600 mb-2"><EMAIL></p>
              <p className="text-sm text-gray-500">Response within 1 hours</p>
            </div>

            <div className="text-center bg-orange-50 rounded-2xl p-8">
              <div className="w-16 h-16 bg-orange-600 text-white rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🌐</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Website</h3>
              <p className="text-gray-600 mb-2">www.nexapo.com</p>
              <p className="text-sm text-gray-500">24/7 Online Support</p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Send Us a Message
            </h2>
            <p className="text-xl text-gray-600">
              Fill out the form below and we'll get back to you within 24 hours.
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
            {/* Submit Status Message */}
            {submitStatus.type && (
              <div
                id="submit-status"
                className={`mb-8 p-6 rounded-xl border-2 shadow-lg transform transition-all duration-500 ease-in-out ${
                  submitStatus.type === 'success'
                    ? 'bg-green-50 border-green-300 text-green-800 shadow-green-100'
                    : 'bg-red-50 border-red-300 text-red-800 shadow-red-100'
                }`}
                style={{
                  animation: 'fadeInScale 0.5s ease-out'
                }}
              >
                <div className="flex items-start gap-4">
                  <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                    submitStatus.type === 'success' ? 'bg-green-200' : 'bg-red-200'
                  }`}>
                    <span className="text-xl">
                      {submitStatus.type === 'success' ? '✅' : '❌'}
                    </span>
                  </div>
                  <div className="flex-1">
                    <h3 className={`font-bold text-lg mb-2 ${
                      submitStatus.type === 'success' ? 'text-green-900' : 'text-red-900'
                    }`}>
                      {submitStatus.type === 'success' ? 'Message Sent Successfully!' : 'Message Failed to Send'}
                    </h3>
                    <p className="font-medium leading-relaxed">{submitStatus.message}</p>
                    {submitStatus.type === 'success' && (
                      <div className="mt-3 text-sm text-green-700">
                        <p>🎉 We'll get back to you within 24 hours!</p>
                      </div>
                    )}
                  </div>
                  <button
                    onClick={() => setSubmitStatus({ type: null, message: '' })}
                    className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center transition-colors ${
                      submitStatus.type === 'success'
                        ? 'hover:bg-green-200 text-green-600'
                        : 'hover:bg-red-200 text-red-600'
                    }`}
                    aria-label="Dismiss message"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            )}

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    First Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    {...register('firstName', {
                      required: 'First name is required',
                      minLength: { value: 2, message: 'First name must be at least 2 characters' }
                    })}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 placeholder-gray-500 transition-all hover:border-gray-300 ${
                      errors.firstName ? 'border-red-300' : 'border-gray-200'
                    }`}
                    placeholder="Enter your first name"
                  />
                  {errors.firstName && (
                    <p className="mt-1 text-sm text-red-600">{errors.firstName.message}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    {...register('lastName', {
                      required: 'Last name is required',
                      minLength: { value: 2, message: 'Last name must be at least 2 characters' }
                    })}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 placeholder-gray-500 transition-all hover:border-gray-300 ${
                      errors.lastName ? 'border-red-300' : 'border-gray-200'
                    }`}
                    placeholder="Enter your last name"
                  />
                  {errors.lastName && (
                    <p className="mt-1 text-sm text-red-600">{errors.lastName.message}</p>
                  )}
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="email"
                    {...register('email', {
                      required: 'Email is required',
                      pattern: {
                        value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                        message: 'Please enter a valid email address'
                      }
                    })}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 placeholder-gray-500 transition-all hover:border-gray-300 ${
                      errors.email ? 'border-red-300' : 'border-gray-200'
                    }`}
                    placeholder="Enter your email"
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    {...register('phone', {
                      pattern: {
                        value: /^(\+233|0)[2-9][0-9]{8}$/,
                        message: 'Please enter a valid Ghana phone number'
                      }
                    })}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 placeholder-gray-500 transition-all hover:border-gray-300 ${
                      errors.phone ? 'border-red-300' : 'border-gray-200'
                    }`}
                    placeholder="+233 XX XXX XXXX"
                  />
                  {errors.phone && (
                    <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Business Name
                </label>
                <input
                  type="text"
                  {...register('businessName')}
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 placeholder-gray-500 transition-all hover:border-gray-300"
                  placeholder="Enter your business name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Subject <span className="text-red-500">*</span>
                </label>
                <select
                  {...register('subject', { required: 'Please select a subject' })}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 transition-all hover:border-gray-300 ${
                    errors.subject ? 'border-red-300' : 'border-gray-200'
                  }`}
                >
                  <option value="">Select a subject</option>
                  <option value="demo">Request Personalized Demo</option>
                  <option value="consultation">Business Consultation</option>
                  <option value="pricing">Pricing Information</option>
                  <option value="setup">Setup & Training</option>
                  <option value="partnership">Partnership Opportunity</option>
                  <option value="support">Technical Support</option>
                  <option value="general">General Inquiry</option>
                </select>
                {errors.subject && (
                  <p className="mt-1 text-sm text-red-600">{errors.subject.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Message <span className="text-red-500">*</span>
                </label>
                <textarea
                  {...register('message', {
                    required: 'Message is required',
                    minLength: { value: 10, message: 'Message must be at least 10 characters long' }
                  })}
                  rows={6}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 placeholder-gray-500 transition-all hover:border-gray-300 ${
                    errors.message ? 'border-red-300' : 'border-gray-200'
                  }`}
                  placeholder="Tell us how we can help you..."
                ></textarea>
                {errors.message && (
                  <p className="mt-1 text-sm text-red-600">{errors.message.message}</p>
                )}
              </div>

              <div className="flex items-start gap-3">
                <input
                  type="checkbox"
                  id="newsletter"
                  {...register('newsletter')}
                  className="mt-1 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-600"
                />
                <label htmlFor="newsletter" className="text-sm text-gray-600">
                  I would like to receive updates about NEXAPO features and news
                </label>
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className={`w-full font-bold text-lg py-4 rounded-lg transition-all duration-200 ${
                  isSubmitting
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 hover:shadow-lg transform hover:scale-[1.02]'
                } text-white`}
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Sending Message...
                  </div>
                ) : (
                  'Send Message'
                )}
              </button>

              <p className="text-sm text-gray-600 text-center">
                We typically respond within 24 hours. For urgent matters, please call us directly at{' '}
                <a href="tel:+233248141505" className="text-blue-600 hover:underline">+233 24 814 1505</a>.
              </p>
            </form>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-600">
              Quick answers to common questions about NEXAPO. Click to expand!
            </p>
          </div>

          <div className="space-y-4">
            {[
              {
                question: "Is NEXAPO really free to try?",
                answer: "While NEXAPO is not free to try, we offer a 30-day money-back guarantee. You will be charged at the beginning of your subscription, but if you are not fully satisfied within the first 30 days, you may request a full refund—no questions asked."
              },
              {
                question: "Do I need special hardware to use NEXAPO?",
                answer: "No special hardware required! NEXAPO works on any device - smartphone, tablet, or computer with internet connection. You can use it on your existing phone, tablet, or computer."
              },
              {
                question: "Can I use NEXAPO offline?",
                answer: "NEXAPO works best with internet connection for real-time sync, but basic sales functions work offline and sync when reconnected. This ensures you never miss a sale even with poor connectivity."
              },
              {
                question: "Is my data secure with NEXAPO?",
                answer: "Absolutely! We use bank-level encryption and security measures. Your data is stored securely in the cloud and backed up automatically. We comply with international data protection standards."
              },
              {
                question: "Do you provide training and support?",
                answer: "Yes! We provide free setup, training, and 24/7 support to all our customers. Our Ghana-based team is always ready to help via phone, email, or chat. We also offer video tutorials and documentation."
              },
              {
                question: "Can I cancel anytime?",
                answer: "Yes, you can cancel your subscription anytime with no penalties or hidden fees. We believe in earning your business every month. Your data remains accessible for 30 days after cancellation."
              },
              {
                question: "What payment methods do you accept?",
                answer: "We accept Mobile Money (MTN, Vodafone, AirtelTigo), bank transfers, and major credit/debit cards. All payments are processed securely through our encrypted payment gateway."
              },
              {
                question: "How much does NEXAPO cost?",
                answer: "NEXAPO costs ₵89 per month with discounts for longer commitments: 5% off quarterly (₵254 for 3 months) and 11% off annually (₵951 for 12 months). All plans include full features and support."
              }
            ].map((faq, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-inset"
                >
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900 pr-4">{faq.question}</h3>
                    <div className={`flex-shrink-0 transition-transform duration-200 ${openFAQ === index ? 'rotate-180' : ''}`}>
                      <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </button>
                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  openFAQ === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                }`}>
                  <div className="px-6 pb-6">
                    <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Contact CTA */}
          <div className="text-center mt-12 p-8 bg-blue-50 rounded-2xl">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Still have questions?
            </h3>
            <p className="text-gray-600 mb-6">
              Our team is here to help! Get in touch and we'll answer any questions you have about NEXAPO.
            </p>
            <Link
              href="#contact-form"
              onClick={(e) => {
                e.preventDefault()
                const contactForm = document.querySelector('form')
                contactForm?.scrollIntoView({ behavior: 'smooth' })
              }}
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors inline-flex items-center gap-2"
            >
              💬 Ask a Question
            </Link>
          </div>
        </div>
      </section>

      <Footer />
      <ScrollToTop />
    </div>
  )
}
