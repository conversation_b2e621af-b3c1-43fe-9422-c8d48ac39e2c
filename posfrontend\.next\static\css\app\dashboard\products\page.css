/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/components/Products/product-panels.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
/* Light theme for product panels */
.product-detail-dark .ant-descriptions-item-label {
  background-color: #f5f5f5 !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
}

.product-detail-dark .ant-descriptions-item-content {
  background-color: #ffffff !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
}

.product-detail-dark .ant-descriptions-bordered {
  border-color: #e8e8e8 !important;
}

/* Form styles */
.product-form .ant-form-item-label > label {
  color: rgba(0, 0, 0, 0.85) !important;
}

.product-form .ant-input,
.product-form .ant-input-number,
.product-form .ant-select-selector,
.product-form .ant-picker {
  background-color: #ffffff !important;
  color: rgba(0, 0, 0, 0.85) !important;
  border-color: #d9d9d9 !important;
}

.product-form .ant-input:hover,
.product-form .ant-input-number:hover,
.product-form .ant-select-selector:hover,
.product-form .ant-picker:hover {
  border-color: #40a9ff !important;
}

.product-form .ant-input:focus,
.product-form .ant-input-number:focus,
.product-form .ant-select-selector:focus,
.product-form .ant-picker:focus {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.product-form .ant-input-number-handler-wrap {
  background-color: #f5f5f5 !important;
}

.product-form .ant-input-number-handler {
  color: rgba(0, 0, 0, 0.45) !important;
}

.product-form .ant-input-number-input {
  color: rgba(0, 0, 0, 0.85) !important;
}

/* Ensure placeholders are properly styled */
.product-form .ant-input::-moz-placeholder, .product-form .ant-input-number-input::-moz-placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}
.product-form .ant-input::placeholder,
.product-form .ant-input-number-input::placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

.product-form .ant-select-arrow {
  color: rgba(0, 0, 0, 0.25) !important;
}

.product-form .ant-select-dropdown {
  background-color: #ffffff !important;
}

.product-form .ant-select-item {
  color: rgba(0, 0, 0, 0.85) !important;
}

.product-form .ant-select-item-option-selected {
  background-color: #e6f7ff !important;
}

.product-form .ant-select-item-option-active {
  background-color: #f5f5f5 !important;
}

.product-form .ant-picker-panel {
  background-color: #ffffff !important;
}

.product-form .ant-picker-header {
  color: rgba(0, 0, 0, 0.85) !important;
  border-color: #f0f0f0 !important;
}

.product-form .ant-picker-header button {
  color: rgba(0, 0, 0, 0.25) !important;
}

.product-form .ant-picker-content th {
  color: rgba(0, 0, 0, 0.5) !important;
}

.product-form .ant-picker-cell {
  color: rgba(0, 0, 0, 0.65) !important;
}

.product-form .ant-picker-cell-in-view {
  color: rgba(0, 0, 0, 0.85) !important;
}

.product-form .ant-picker-cell-selected .ant-picker-cell-inner {
  background-color: #1890ff !important;
}

.product-form .ant-form-item-has-error .ant-input,
.product-form .ant-form-item-has-error .ant-input-number,
.product-form .ant-form-item-has-error .ant-select-selector,
.product-form .ant-form-item-has-error .ant-picker {
  border-color: #ff4d4f !important;
}

.product-form .ant-form-item-explain-error {
  color: #ff4d4f !important;
}

/* Product table action buttons */
.view-button {
  color: #10b981 !important; /* green-500 */
}

.view-button:hover {
  color: #34d399 !important; /* green-400 */
}

.edit-button {
  color: #3b82f6 !important; /* blue-500 */
}

.edit-button:hover {
  color: #60a5fa !important; /* blue-400 */
}

.delete-button {
  color: #ef4444 !important; /* red-500 */
}

.delete-button:hover {
  color: #f87171 !important; /* red-400 */
}

.adjust-stock-button {
  color: #a855f7 !important; /* purple-500 */
}

.adjust-stock-button:hover {
  color: #c084fc !important; /* purple-400 */
}

/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/search.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/* Custom styles for search inputs */

/* Light background for input and its wrapper */
.ant-input-affix-wrapper {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
}

/* Ensure hover and focus states maintain light background */
.ant-input-affix-wrapper:hover,
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
  background-color: #ffffff !important;
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* Style for the input inside the wrapper */
.ant-input-affix-wrapper .ant-input {
  background-color: transparent !important;
  color: rgba(0, 0, 0, 0.85) !important;
}

/* Style for the clear icon */
.ant-input-affix-wrapper .ant-input-clear-icon {
  color: rgba(0, 0, 0, 0.25) !important;
}

.ant-input-affix-wrapper .ant-input-clear-icon:hover {
  color: rgba(0, 0, 0, 0.45) !important;
}

/* Style for the placeholder */
.ant-input::-moz-placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}
.ant-input::placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Style for the search icon */
.ant-input-prefix .anticon {
  color: rgba(0, 0, 0, 0.25) !important;
}

