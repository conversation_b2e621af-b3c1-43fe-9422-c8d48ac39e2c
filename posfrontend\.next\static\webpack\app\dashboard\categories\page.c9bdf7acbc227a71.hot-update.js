"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/categories/page",{

/***/ "(app-pages-browser)/./src/components/ui/SlidingPanel.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/SlidingPanel.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_CloseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CloseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CloseOutlined.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst SlidingPanel = (param)=>{\n    let { isOpen, onClose, title, children, width = \"400px\", footer } = param;\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRendered, setIsRendered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [windowWidth, setWindowWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)( true ? window.innerWidth : 0);\n    // Handle window resize\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SlidingPanel.useEffect\": ()=>{\n            const handleResize = {\n                \"SlidingPanel.useEffect.handleResize\": ()=>{\n                    setWindowWidth(window.innerWidth);\n                }\n            }[\"SlidingPanel.useEffect.handleResize\"];\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"SlidingPanel.useEffect\": ()=>{\n                    window.removeEventListener('resize', handleResize);\n                }\n            })[\"SlidingPanel.useEffect\"];\n        }\n    }[\"SlidingPanel.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SlidingPanel.useEffect\": ()=>{\n            console.log(\"SlidingPanel - isOpen changed:\", isOpen, \"title:\", title);\n            if (isOpen) {\n                setIsRendered(true);\n                console.log(\"SlidingPanel - Setting isRendered to true\");\n                // Small delay to ensure the panel is rendered before animating\n                setTimeout({\n                    \"SlidingPanel.useEffect\": ()=>{\n                        setIsVisible(true);\n                        console.log(\"SlidingPanel - Setting isVisible to true\");\n                    }\n                }[\"SlidingPanel.useEffect\"], 50);\n            } else {\n                setIsVisible(false);\n                console.log(\"SlidingPanel - Setting isVisible to false\");\n                // Wait for animation to complete before unmounting\n                const timer = setTimeout({\n                    \"SlidingPanel.useEffect.timer\": ()=>{\n                        setIsRendered(false);\n                        console.log(\"SlidingPanel - Setting isRendered to false\");\n                    }\n                }[\"SlidingPanel.useEffect.timer\"], 300);\n                return ({\n                    \"SlidingPanel.useEffect\": ()=>clearTimeout(timer)\n                })[\"SlidingPanel.useEffect\"];\n            }\n        }\n    }[\"SlidingPanel.useEffect\"], [\n        isOpen,\n        title\n    ]);\n    if (!isRendered) return null;\n    // Calculate responsive width based on screen size\n    const getResponsiveWidth = ()=>{\n        // For small screens (mobile), use full width\n        if (windowWidth < 640) {\n            return '100vw'; // 100% of viewport width\n        }\n        // For medium screens\n        if (windowWidth < 1024) {\n            return '450px';\n        }\n        // For larger screens, use the provided width or default\n        return width;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-[1000] overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black transition-opacity duration-300 \".concat(isVisible ? \"opacity-50\" : \"opacity-0\"),\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 right-0 bottom-0 flex flex-col bg-white text-gray-800 shadow-xl transition-transform duration-300 ease-in-out transform \".concat(isVisible ? \"translate-x-0\" : \"translate-x-full\"),\n                style: {\n                    width: getResponsiveWidth()\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-medium text-gray-800 truncate\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_antd__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                type: \"text\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    style: {\n                                        color: '#333'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 19\n                                }, void 0),\n                                onClick: onClose,\n                                \"aria-label\": \"Close panel\",\n                                style: {\n                                    color: '#333',\n                                    borderColor: 'transparent',\n                                    background: 'transparent'\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto p-4 pt-6 bg-white\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined),\n                    footer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-200 bg-gray-50\",\n                        children: footer\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\SlidingPanel.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SlidingPanel, \"sb9mljCr9lGLLPaevMatKyoxyc4=\");\n_c = SlidingPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SlidingPanel);\nvar _c;\n$RefreshReg$(_c, \"SlidingPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/SlidingPanel.tsx\n"));

/***/ })

});