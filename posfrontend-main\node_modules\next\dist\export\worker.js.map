{"version": 3, "sources": ["../../src/export/worker.ts"], "sourcesContent": ["import type {\n  ExportPagesInput,\n  ExportPageInput,\n  ExportPageResult,\n  ExportRouteResult,\n  ExportedPageFile,\n  FileWriter,\n  WorkerRenderOpts,\n  ExportPagesResult,\n} from './types'\n\nimport '../server/node-environment'\n\nprocess.env.NEXT_IS_EXPORT_WORKER = 'true'\n\nimport { extname, join, dirname, sep } from 'path'\nimport fs from 'fs/promises'\nimport { loadComponents } from '../server/load-components'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { trace } from '../trace'\nimport { setHttpClientAndAgentOptions } from '../server/setup-http-agent-env'\nimport isError from '../lib/is-error'\nimport { addRequestMeta } from '../server/request-meta'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\n\nimport { createRequestResponseMocks } from '../server/lib/mock-request'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { hasNextSupport } from '../server/ci-info'\nimport { exportAppRoute } from './routes/app-route'\nimport { exportAppPage, prospectiveRenderAppPage } from './routes/app-page'\nimport { exportPagesPage } from './routes/pages'\nimport { getParams } from './helpers/get-params'\nimport { createIncrementalCache } from './helpers/create-incremental-cache'\nimport { isPostpone } from '../server/lib/router-utils/is-postpone'\nimport { isDynamicUsageError } from './helpers/is-dynamic-usage-error'\nimport { isBailoutToCSRError } from '../shared/lib/lazy-dynamic/bailout-to-csr'\nimport {\n  turborepoTraceAccess,\n  TurborepoAccessTraceResult,\n} from '../build/turborepo-access-trace'\nimport type { Params } from '../server/request/params'\nimport {\n  getFallbackRouteParams,\n  type FallbackRouteParams,\n} from '../server/request/fallback-params'\nimport { needsExperimentalReact } from '../lib/needs-experimental-react'\nimport type { AppRouteRouteModule } from '../server/route-modules/app-route/module.compiled'\nimport { isStaticGenBailoutError } from '../client/components/static-generation-bailout'\n\nconst envConfig = require('../shared/lib/runtime-config.external')\n\n;(globalThis as any).__NEXT_DATA__ = {\n  nextExport: true,\n}\n\nclass TimeoutError extends Error {\n  code = 'NEXT_EXPORT_TIMEOUT_ERROR'\n}\n\nclass ExportPageError extends Error {\n  code = 'NEXT_EXPORT_PAGE_ERROR'\n}\n\nasync function exportPageImpl(\n  input: ExportPageInput,\n  fileWriter: FileWriter\n): Promise<ExportRouteResult | undefined> {\n  const {\n    path,\n    pathMap,\n    distDir,\n    pagesDataDir,\n    buildExport = false,\n    serverRuntimeConfig,\n    subFolders = false,\n    optimizeCss,\n    disableOptimizedLoading,\n    debugOutput = false,\n    enableExperimentalReact,\n    ampValidatorPath,\n    trailingSlash,\n  } = input\n\n  if (enableExperimentalReact) {\n    process.env.__NEXT_EXPERIMENTAL_REACT = 'true'\n  }\n\n  const {\n    page,\n\n    // The parameters that are currently unknown.\n    _fallbackRouteParams = [],\n\n    // Check if this is an `app/` page.\n    _isAppDir: isAppDir = false,\n\n    // Check if this should error when dynamic usage is detected.\n    _isDynamicError: isDynamicError = false,\n\n    // If this page supports partial prerendering, then we need to pass that to\n    // the renderOpts.\n    _isRoutePPREnabled: isRoutePPREnabled,\n\n    // If this is a prospective render, we don't actually want to persist the\n    // result, we just want to use it to error the build if there's a problem.\n    _isProspectiveRender: isProspectiveRender = false,\n\n    // Pull the original query out.\n    query: originalQuery = {},\n  } = pathMap\n\n  const fallbackRouteParams: FallbackRouteParams | null =\n    getFallbackRouteParams(_fallbackRouteParams)\n\n  let query = { ...originalQuery }\n  const pathname = normalizeAppPath(page)\n  const isDynamic = isDynamicRoute(page)\n  const outDir = isAppDir ? join(distDir, 'server/app') : input.outDir\n\n  const filePath = normalizePagePath(path)\n  const ampPath = `${filePath}.amp`\n  let renderAmpPath = ampPath\n\n  let updatedPath = query.__nextSsgPath || path\n  delete query.__nextSsgPath\n\n  let locale = query.__nextLocale || input.renderOpts.locale\n  delete query.__nextLocale\n\n  if (input.renderOpts.locale) {\n    const localePathResult = normalizeLocalePath(path, input.renderOpts.locales)\n\n    if (localePathResult.detectedLocale) {\n      updatedPath = localePathResult.pathname\n      locale = localePathResult.detectedLocale\n\n      if (locale === input.renderOpts.defaultLocale) {\n        renderAmpPath = `${normalizePagePath(updatedPath)}.amp`\n      }\n    }\n  }\n\n  // We need to show a warning if they try to provide query values\n  // for an auto-exported page since they won't be available\n  const hasOrigQueryValues = Object.keys(originalQuery).length > 0\n\n  // Check if the page is a specified dynamic route\n  const { pathname: nonLocalizedPath } = normalizeLocalePath(\n    path,\n    input.renderOpts.locales\n  )\n\n  let params: Params | undefined\n\n  if (isDynamic && page !== nonLocalizedPath) {\n    const normalizedPage = isAppDir ? normalizeAppPath(page) : page\n\n    params = getParams(normalizedPage, updatedPath)\n  }\n\n  const { req, res } = createRequestResponseMocks({ url: updatedPath })\n\n  // If this is a status code page, then set the response code.\n  for (const statusCode of [404, 500]) {\n    if (\n      [\n        `/${statusCode}`,\n        `/${statusCode}.html`,\n        `/${statusCode}/index.html`,\n      ].some((p) => p === updatedPath || `/${locale}${p}` === updatedPath)\n    ) {\n      res.statusCode = statusCode\n    }\n  }\n\n  // Ensure that the URL has a trailing slash if it's configured.\n  if (trailingSlash && !req.url?.endsWith('/')) {\n    req.url += '/'\n  }\n\n  if (\n    locale &&\n    buildExport &&\n    input.renderOpts.domainLocales &&\n    input.renderOpts.domainLocales.some(\n      (dl) => dl.defaultLocale === locale || dl.locales?.includes(locale || '')\n    )\n  ) {\n    addRequestMeta(req, 'isLocaleDomain', true)\n  }\n\n  envConfig.setConfig({\n    serverRuntimeConfig,\n    publicRuntimeConfig: input.renderOpts.runtimeConfig,\n  })\n\n  const getHtmlFilename = (p: string) =>\n    subFolders ? `${p}${sep}index.html` : `${p}.html`\n\n  let htmlFilename = getHtmlFilename(filePath)\n\n  // dynamic routes can provide invalid extensions e.g. /blog/[...slug] returns an\n  // extension of `.slug]`\n  const pageExt = isDynamic || isAppDir ? '' : extname(page)\n  const pathExt = isDynamic || isAppDir ? '' : extname(path)\n\n  // force output 404.html for backwards compat\n  if (path === '/404.html') {\n    htmlFilename = path\n  }\n  // Make sure page isn't a folder with a dot in the name e.g. `v1.2`\n  else if (pageExt !== pathExt && pathExt !== '') {\n    const isBuiltinPaths = ['/500', '/404'].some(\n      (p) => p === path || p === path + '.html'\n    )\n    // If the ssg path has .html extension, and it's not builtin paths, use it directly\n    // Otherwise, use that as the filename instead\n    const isHtmlExtPath = !isBuiltinPaths && path.endsWith('.html')\n    htmlFilename = isHtmlExtPath ? getHtmlFilename(path) : path\n  } else if (path === '/') {\n    // If the path is the root, just use index.html\n    htmlFilename = 'index.html'\n  }\n\n  const baseDir = join(outDir, dirname(htmlFilename))\n  let htmlFilepath = join(outDir, htmlFilename)\n\n  await fs.mkdir(baseDir, { recursive: true })\n\n  const components = await loadComponents({\n    distDir,\n    page,\n    isAppPath: isAppDir,\n    isDev: false,\n  })\n\n  // Handle App Routes.\n  if (isAppDir && isAppRouteRoute(page)) {\n    return exportAppRoute(\n      req,\n      res,\n      params,\n      page,\n      components.routeModule as AppRouteRouteModule,\n      input.renderOpts.incrementalCache,\n      input.renderOpts.cacheLifeProfiles,\n      htmlFilepath,\n      fileWriter,\n      input.renderOpts.experimental,\n      input.renderOpts.buildId\n    )\n  }\n\n  const renderOpts: WorkerRenderOpts = {\n    ...components,\n    ...input.renderOpts,\n    ampPath: renderAmpPath,\n    params,\n    optimizeCss,\n    disableOptimizedLoading,\n    locale,\n    supportsDynamicResponse: false,\n    experimental: {\n      ...input.renderOpts.experimental,\n      isRoutePPREnabled,\n    },\n  }\n\n  if (hasNextSupport) {\n    renderOpts.isRevalidate = true\n  }\n\n  // Handle App Pages\n  if (isAppDir) {\n    // If this is a prospective render, don't return any metrics or revalidate\n    // timings as we aren't persisting this render (it was only to error).\n    if (isProspectiveRender) {\n      return prospectiveRenderAppPage(\n        req,\n        res,\n        page,\n        pathname,\n        query,\n        fallbackRouteParams,\n        renderOpts\n      )\n    }\n\n    return exportAppPage(\n      req,\n      res,\n      page,\n      path,\n      pathname,\n      query,\n      fallbackRouteParams,\n      renderOpts,\n      htmlFilepath,\n      debugOutput,\n      isDynamicError,\n      fileWriter\n    )\n  }\n\n  return exportPagesPage(\n    req,\n    res,\n    path,\n    page,\n    query,\n    params,\n    htmlFilepath,\n    htmlFilename,\n    ampPath,\n    subFolders,\n    outDir,\n    ampValidatorPath,\n    pagesDataDir,\n    buildExport,\n    isDynamic,\n    hasOrigQueryValues,\n    renderOpts,\n    components,\n    fileWriter\n  )\n}\n\nexport async function exportPages(\n  input: ExportPagesInput\n): Promise<ExportPagesResult> {\n  const {\n    exportPathMap,\n    paths,\n    dir,\n    distDir,\n    outDir,\n    cacheHandler,\n    cacheMaxMemorySize,\n    fetchCacheKeyPrefix,\n    pagesDataDir,\n    renderOpts,\n    nextConfig,\n    options,\n  } = input\n\n  // If the fetch cache was enabled, we need to create an incremental\n  // cache instance for this page.\n  const incrementalCache = await createIncrementalCache({\n    cacheHandler,\n    cacheMaxMemorySize,\n    fetchCacheKeyPrefix,\n    distDir,\n    dir,\n    dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n    // skip writing to disk in minimal mode for now, pending some\n    // changes to better support it\n    flushToDisk: !hasNextSupport,\n    cacheHandlers: nextConfig.experimental.cacheHandlers,\n  })\n\n  renderOpts.incrementalCache = incrementalCache\n\n  const maxConcurrency =\n    nextConfig.experimental.staticGenerationMaxConcurrency ?? 8\n  const results: ExportPagesResult = []\n\n  const exportPageWithRetry = async (path: string, maxAttempts: number) => {\n    const pathMap = exportPathMap[path]\n    const { page } = exportPathMap[path]\n    const pageKey = page !== path ? `${page}: ${path}` : path\n    let attempt = 0\n    let result\n\n    while (attempt < maxAttempts) {\n      try {\n        result = await Promise.race<ExportPageResult | undefined>([\n          exportPage({\n            path,\n            pathMap,\n            distDir,\n            outDir,\n            pagesDataDir,\n            renderOpts,\n            ampValidatorPath:\n              nextConfig.experimental.amp?.validator || undefined,\n            trailingSlash: nextConfig.trailingSlash,\n            serverRuntimeConfig: nextConfig.serverRuntimeConfig,\n            subFolders: nextConfig.trailingSlash && !options.buildExport,\n            buildExport: options.buildExport,\n            optimizeCss: nextConfig.experimental.optimizeCss,\n            disableOptimizedLoading:\n              nextConfig.experimental.disableOptimizedLoading,\n            parentSpanId: input.parentSpanId,\n            httpAgentOptions: nextConfig.httpAgentOptions,\n            debugOutput: options.debugOutput,\n            enableExperimentalReact: needsExperimentalReact(nextConfig),\n          }),\n          // If exporting the page takes longer than the timeout, reject the promise.\n          new Promise((_, reject) => {\n            setTimeout(() => {\n              reject(new TimeoutError())\n            }, nextConfig.staticPageGenerationTimeout * 1000)\n          }),\n        ])\n\n        // If there was an error in the export, throw it immediately. In the catch block, we might retry the export,\n        // or immediately fail the build, depending on user configuration. We might also continue on and attempt other pages.\n        if (result && 'error' in result) {\n          throw new ExportPageError()\n        }\n\n        // If the export succeeds, break out of the retry loop\n        break\n      } catch (err) {\n        // The only error that should be caught here is an ExportError, as `exportPage` doesn't throw and instead returns an object with an `error` property.\n        // This is an overly cautious check to ensure that we don't accidentally catch an unexpected error.\n        if (!(err instanceof ExportPageError || err instanceof TimeoutError)) {\n          throw err\n        }\n\n        if (err instanceof TimeoutError) {\n          // If the export times out, we will restart the worker up to 3 times.\n          maxAttempts = 3\n        }\n\n        // We've reached the maximum number of attempts\n        if (attempt >= maxAttempts - 1) {\n          // Log a message if we've reached the maximum number of attempts.\n          // We only care to do this if maxAttempts was configured.\n          if (maxAttempts > 1) {\n            console.info(\n              `Failed to build ${pageKey} after ${maxAttempts} attempts.`\n            )\n          }\n          // If prerenderEarlyExit is enabled, we'll exit the build immediately.\n          if (nextConfig.experimental.prerenderEarlyExit) {\n            console.error(\n              `Export encountered an error on ${pageKey}, exiting the build.`\n            )\n            process.exit(1)\n          } else {\n            // Otherwise, this is a no-op. The build will continue, and a summary of failed pages will be displayed at the end.\n          }\n        } else {\n          // Otherwise, we have more attempts to make. Wait before retrying\n          if (err instanceof TimeoutError) {\n            console.info(\n              `Failed to build ${pageKey} (attempt ${attempt + 1} of ${maxAttempts}) because it took more than ${nextConfig.staticPageGenerationTimeout} seconds. Retrying again shortly.`\n            )\n          } else {\n            console.info(\n              `Failed to build ${pageKey} (attempt ${attempt + 1} of ${maxAttempts}). Retrying again shortly.`\n            )\n          }\n          await new Promise((r) => setTimeout(r, Math.random() * 500))\n        }\n      }\n\n      attempt++\n    }\n\n    return { result, path, pageKey }\n  }\n\n  for (let i = 0; i < paths.length; i += maxConcurrency) {\n    const subset = paths.slice(i, i + maxConcurrency)\n\n    const subsetResults = await Promise.all(\n      subset.map((path) =>\n        exportPageWithRetry(\n          path,\n          nextConfig.experimental.staticGenerationRetryCount ?? 1\n        )\n      )\n    )\n\n    results.push(...subsetResults)\n  }\n\n  return results\n}\n\nasync function exportPage(\n  input: ExportPageInput\n): Promise<ExportPageResult | undefined> {\n  trace('export-page', input.parentSpanId).setAttribute('path', input.path)\n\n  // Configure the http agent.\n  setHttpClientAndAgentOptions({\n    httpAgentOptions: input.httpAgentOptions,\n  })\n\n  const files: ExportedPageFile[] = []\n  const baseFileWriter: FileWriter = async (\n    type,\n    path,\n    content,\n    encodingOptions = 'utf-8'\n  ) => {\n    await fs.mkdir(dirname(path), { recursive: true })\n    await fs.writeFile(path, content, encodingOptions)\n    files.push({ type, path })\n  }\n\n  const exportPageSpan = trace('export-page-worker', input.parentSpanId)\n\n  const start = Date.now()\n\n  const turborepoAccessTraceResult = new TurborepoAccessTraceResult()\n\n  // Export the page.\n  let result: ExportRouteResult | undefined\n  try {\n    result = await exportPageSpan.traceAsyncFn(() =>\n      turborepoTraceAccess(\n        () => exportPageImpl(input, baseFileWriter),\n        turborepoAccessTraceResult\n      )\n    )\n\n    // If there was no result, then we can exit early.\n    if (!result) return\n\n    // If there was an error, then we can exit early.\n    if ('error' in result) {\n      return { error: result.error, duration: Date.now() - start, files: [] }\n    }\n  } catch (err) {\n    console.error(\n      `Error occurred prerendering page \"${input.path}\". Read more: https://nextjs.org/docs/messages/prerender-error`\n    )\n\n    // bailoutToCSRError errors should not leak to the user as they are not actionable; they're\n    // a framework signal\n    if (!isBailoutToCSRError(err)) {\n      // A static generation bailout error is a framework signal to fail static generation but\n      // and will encode a reason in the error message. If there is a message, we'll print it.\n      // Otherwise there's nothing to show as we don't want to leak an error internal error stack to the user.\n      if (isStaticGenBailoutError(err)) {\n        if (err.message) {\n          console.error(`Error: ${err.message}`)\n        }\n      } else if (isError(err) && err.stack) {\n        console.error(err.stack)\n      } else {\n        console.error(err)\n      }\n    }\n\n    return { error: true, duration: Date.now() - start, files: [] }\n  }\n\n  // Notify the parent process that we processed a page (used by the progress activity indicator)\n  process.send?.([3, { type: 'activity' }])\n\n  // Otherwise we can return the result.\n  return {\n    duration: Date.now() - start,\n    files,\n    ampValidations: result.ampValidations,\n    revalidate: result.revalidate,\n    metadata: result.metadata,\n    ssgNotFound: result.ssgNotFound,\n    hasEmptyPrelude: result.hasEmptyPrelude,\n    hasPostponed: result.hasPostponed,\n    turborepoAccessTraceResult: turborepoAccessTraceResult.serialize(),\n    fetchMetrics: result.fetchMetrics,\n  }\n}\n\nprocess.on('unhandledRejection', (err: unknown) => {\n  // if it's a postpone error, it'll be handled later\n  // when the postponed promise is actually awaited.\n  if (isPostpone(err)) {\n    return\n  }\n\n  // we don't want to log these errors\n  if (isDynamicUsageError(err)) {\n    return\n  }\n\n  console.error(err)\n})\n\nprocess.on('rejectionHandled', () => {\n  // It is ok to await a Promise late in Next.js as it allows for better\n  // prefetching patterns to avoid waterfalls. We ignore logging these.\n  // We should've already errored in anyway unhandledRejection.\n})\n\nconst FATAL_UNHANDLED_NEXT_API_EXIT_CODE = 78\n\nprocess.on('uncaughtException', (err) => {\n  if (isDynamicUsageError(err)) {\n    console.error(\n      'A Next.js API that uses exceptions to signal framework behavior was uncaught. This suggests improper usage of a Next.js API. The original error is printed below and the build will now exit.'\n    )\n    console.error(err)\n    process.exit(FATAL_UNHANDLED_NEXT_API_EXIT_CODE)\n  } else {\n    console.error(err)\n  }\n})\n"], "names": ["exportPages", "process", "env", "NEXT_IS_EXPORT_WORKER", "envConfig", "require", "globalThis", "__NEXT_DATA__", "nextExport", "TimeoutError", "Error", "code", "ExportPageError", "exportPageImpl", "input", "fileWriter", "req", "path", "pathMap", "distDir", "pagesDataDir", "buildExport", "serverRuntimeConfig", "subFolders", "optimizeCss", "disableOptimizedLoading", "debugOutput", "enableExperimentalReact", "ampValidator<PERSON>ath", "trailingSlash", "__NEXT_EXPERIMENTAL_REACT", "page", "_fallbackRouteParams", "_isAppDir", "isAppDir", "_isDynamicError", "isDynamicError", "_isRoutePPREnabled", "isRoutePPREnabled", "_isProspectiveRender", "isProspectiveRender", "query", "originalQuery", "fallbackRouteParams", "getFallbackRouteParams", "pathname", "normalizeAppPath", "isDynamic", "isDynamicRoute", "outDir", "join", "filePath", "normalizePagePath", "ampPath", "renderAmpPath", "updatedPath", "__nextSsgPath", "locale", "__next<PERSON><PERSON><PERSON>", "renderOpts", "localePathResult", "normalizeLocalePath", "locales", "detectedLocale", "defaultLocale", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "length", "nonLocalizedPath", "params", "normalizedPage", "getParams", "res", "createRequestResponseMocks", "url", "statusCode", "some", "p", "endsWith", "domainLocales", "dl", "includes", "addRequestMeta", "setConfig", "publicRuntimeConfig", "runtimeConfig", "getHtmlFilename", "sep", "htmlFilename", "pageExt", "extname", "pathExt", "isBuiltinPaths", "isHtmlExtPath", "baseDir", "dirname", "htmlFilepath", "fs", "mkdir", "recursive", "components", "loadComponents", "isAppPath", "isDev", "isAppRouteRoute", "exportAppRoute", "routeModule", "incrementalCache", "cacheLifeProfiles", "experimental", "buildId", "supportsDynamicResponse", "hasNextSupport", "isRevalidate", "prospectiveRenderAppPage", "exportAppPage", "exportPagesPage", "exportPathMap", "paths", "dir", "cache<PERSON><PERSON><PERSON>", "cacheMaxMemorySize", "fetchCacheKeyPrefix", "nextConfig", "options", "createIncrementalCache", "dynamicIO", "Boolean", "flushToDisk", "cacheHandlers", "maxConcurrency", "staticGenerationMaxConcurrency", "results", "exportPageWithRetry", "maxAttempts", "page<PERSON><PERSON>", "attempt", "result", "Promise", "race", "exportPage", "amp", "validator", "undefined", "parentSpanId", "httpAgentOptions", "needsExperimentalReact", "_", "reject", "setTimeout", "staticPageGenerationTimeout", "err", "console", "info", "prerenderEarlyExit", "error", "exit", "r", "Math", "random", "i", "subset", "slice", "subsetResults", "all", "map", "staticGenerationRetryCount", "push", "trace", "setAttribute", "setHttpClientAndAgentOptions", "files", "baseFileWriter", "type", "content", "encodingOptions", "writeFile", "exportPageSpan", "start", "Date", "now", "turborepoAccessTraceResult", "TurborepoAccessTraceResult", "traceAsyncFn", "turborepoTraceAccess", "duration", "isBailoutToCSRError", "isStaticGenBailoutError", "message", "isError", "stack", "send", "ampValidations", "revalidate", "metadata", "ssgNotFound", "hasEmptyPrelude", "hasPostponed", "serialize", "fetchMetrics", "on", "isPostpone", "isDynamicUsageError", "FATAL_UNHANDLED_NEXT_API_EXIT_CODE"], "mappings": ";;;;+BAyUsBA;;;eAAAA;;;QA9Tf;sBAIqC;iEAC7B;gCACgB;2BACA;mCACG;qCACE;uBACd;mCACuB;gEACzB;6BACW;0BACE;6BAEU;iCACX;wBACD;0BACA;yBACyB;uBACxB;2BACN;wCACa;4BACZ;qCACS;8BACA;sCAI7B;gCAKA;wCACgC;yCAEC;;;;;;AApCxCC,QAAQC,GAAG,CAACC,qBAAqB,GAAG;AAsCpC,MAAMC,YAAYC,QAAQ;AAExBC,WAAmBC,aAAa,GAAG;IACnCC,YAAY;AACd;AAEA,MAAMC,qBAAqBC;;QAA3B,qBACEC,OAAO;;AACT;AAEA,MAAMC,wBAAwBF;;QAA9B,qBACEC,OAAO;;AACT;AAEA,eAAeE,eACbC,KAAsB,EACtBC,UAAsB;QA+GAC;IA7GtB,MAAM,EACJC,IAAI,EACJC,OAAO,EACPC,OAAO,EACPC,YAAY,EACZC,cAAc,KAAK,EACnBC,mBAAmB,EACnBC,aAAa,KAAK,EAClBC,WAAW,EACXC,uBAAuB,EACvBC,cAAc,KAAK,EACnBC,uBAAuB,EACvBC,gBAAgB,EAChBC,aAAa,EACd,GAAGf;IAEJ,IAAIa,yBAAyB;QAC3B1B,QAAQC,GAAG,CAAC4B,yBAAyB,GAAG;IAC1C;IAEA,MAAM,EACJC,IAAI,EAEJ,6CAA6C;IAC7CC,uBAAuB,EAAE,EAEzB,mCAAmC;IACnCC,WAAWC,WAAW,KAAK,EAE3B,6DAA6D;IAC7DC,iBAAiBC,iBAAiB,KAAK,EAEvC,2EAA2E;IAC3E,kBAAkB;IAClBC,oBAAoBC,iBAAiB,EAErC,yEAAyE;IACzE,0EAA0E;IAC1EC,sBAAsBC,sBAAsB,KAAK,EAEjD,+BAA+B;IAC/BC,OAAOC,gBAAgB,CAAC,CAAC,EAC1B,GAAGxB;IAEJ,MAAMyB,sBACJC,IAAAA,sCAAsB,EAACZ;IAEzB,IAAIS,QAAQ;QAAE,GAAGC,aAAa;IAAC;IAC/B,MAAMG,WAAWC,IAAAA,0BAAgB,EAACf;IAClC,MAAMgB,YAAYC,IAAAA,yBAAc,EAACjB;IACjC,MAAMkB,SAASf,WAAWgB,IAAAA,UAAI,EAAC/B,SAAS,gBAAgBL,MAAMmC,MAAM;IAEpE,MAAME,WAAWC,IAAAA,oCAAiB,EAACnC;IACnC,MAAMoC,UAAU,GAAGF,SAAS,IAAI,CAAC;IACjC,IAAIG,gBAAgBD;IAEpB,IAAIE,cAAcd,MAAMe,aAAa,IAAIvC;IACzC,OAAOwB,MAAMe,aAAa;IAE1B,IAAIC,SAAShB,MAAMiB,YAAY,IAAI5C,MAAM6C,UAAU,CAACF,MAAM;IAC1D,OAAOhB,MAAMiB,YAAY;IAEzB,IAAI5C,MAAM6C,UAAU,CAACF,MAAM,EAAE;QAC3B,MAAMG,mBAAmBC,IAAAA,wCAAmB,EAAC5C,MAAMH,MAAM6C,UAAU,CAACG,OAAO;QAE3E,IAAIF,iBAAiBG,cAAc,EAAE;YACnCR,cAAcK,iBAAiBf,QAAQ;YACvCY,SAASG,iBAAiBG,cAAc;YAExC,IAAIN,WAAW3C,MAAM6C,UAAU,CAACK,aAAa,EAAE;gBAC7CV,gBAAgB,GAAGF,IAAAA,oCAAiB,EAACG,aAAa,IAAI,CAAC;YACzD;QACF;IACF;IAEA,gEAAgE;IAChE,0DAA0D;IAC1D,MAAMU,qBAAqBC,OAAOC,IAAI,CAACzB,eAAe0B,MAAM,GAAG;IAE/D,iDAAiD;IACjD,MAAM,EAAEvB,UAAUwB,gBAAgB,EAAE,GAAGR,IAAAA,wCAAmB,EACxD5C,MACAH,MAAM6C,UAAU,CAACG,OAAO;IAG1B,IAAIQ;IAEJ,IAAIvB,aAAahB,SAASsC,kBAAkB;QAC1C,MAAME,iBAAiBrC,WAAWY,IAAAA,0BAAgB,EAACf,QAAQA;QAE3DuC,SAASE,IAAAA,oBAAS,EAACD,gBAAgBhB;IACrC;IAEA,MAAM,EAAEvC,GAAG,EAAEyD,GAAG,EAAE,GAAGC,IAAAA,uCAA0B,EAAC;QAAEC,KAAKpB;IAAY;IAEnE,6DAA6D;IAC7D,KAAK,MAAMqB,cAAc;QAAC;QAAK;KAAI,CAAE;QACnC,IACE;YACE,CAAC,CAAC,EAAEA,YAAY;YAChB,CAAC,CAAC,EAAEA,WAAW,KAAK,CAAC;YACrB,CAAC,CAAC,EAAEA,WAAW,WAAW,CAAC;SAC5B,CAACC,IAAI,CAAC,CAACC,IAAMA,MAAMvB,eAAe,CAAC,CAAC,EAAEE,SAASqB,GAAG,KAAKvB,cACxD;YACAkB,IAAIG,UAAU,GAAGA;QACnB;IACF;IAEA,+DAA+D;IAC/D,IAAI/C,iBAAiB,GAACb,WAAAA,IAAI2D,GAAG,qBAAP3D,SAAS+D,QAAQ,CAAC,OAAM;QAC5C/D,IAAI2D,GAAG,IAAI;IACb;IAEA,IACElB,UACApC,eACAP,MAAM6C,UAAU,CAACqB,aAAa,IAC9BlE,MAAM6C,UAAU,CAACqB,aAAa,CAACH,IAAI,CACjC,CAACI;YAAsCA;eAA/BA,GAAGjB,aAAa,KAAKP,YAAUwB,cAAAA,GAAGnB,OAAO,qBAAVmB,YAAYC,QAAQ,CAACzB,UAAU;QAExE;QACA0B,IAAAA,2BAAc,EAACnE,KAAK,kBAAkB;IACxC;IAEAZ,UAAUgF,SAAS,CAAC;QAClB9D;QACA+D,qBAAqBvE,MAAM6C,UAAU,CAAC2B,aAAa;IACrD;IAEA,MAAMC,kBAAkB,CAACT,IACvBvD,aAAa,GAAGuD,IAAIU,SAAG,CAAC,UAAU,CAAC,GAAG,GAAGV,EAAE,KAAK,CAAC;IAEnD,IAAIW,eAAeF,gBAAgBpC;IAEnC,gFAAgF;IAChF,wBAAwB;IACxB,MAAMuC,UAAU3C,aAAab,WAAW,KAAKyD,IAAAA,aAAO,EAAC5D;IACrD,MAAM6D,UAAU7C,aAAab,WAAW,KAAKyD,IAAAA,aAAO,EAAC1E;IAErD,6CAA6C;IAC7C,IAAIA,SAAS,aAAa;QACxBwE,eAAexE;IACjB,OAEK,IAAIyE,YAAYE,WAAWA,YAAY,IAAI;QAC9C,MAAMC,iBAAiB;YAAC;YAAQ;SAAO,CAAChB,IAAI,CAC1C,CAACC,IAAMA,MAAM7D,QAAQ6D,MAAM7D,OAAO;QAEpC,mFAAmF;QACnF,8CAA8C;QAC9C,MAAM6E,gBAAgB,CAACD,kBAAkB5E,KAAK8D,QAAQ,CAAC;QACvDU,eAAeK,gBAAgBP,gBAAgBtE,QAAQA;IACzD,OAAO,IAAIA,SAAS,KAAK;QACvB,+CAA+C;QAC/CwE,eAAe;IACjB;IAEA,MAAMM,UAAU7C,IAAAA,UAAI,EAACD,QAAQ+C,IAAAA,aAAO,EAACP;IACrC,IAAIQ,eAAe/C,IAAAA,UAAI,EAACD,QAAQwC;IAEhC,MAAMS,iBAAE,CAACC,KAAK,CAACJ,SAAS;QAAEK,WAAW;IAAK;IAE1C,MAAMC,aAAa,MAAMC,IAAAA,8BAAc,EAAC;QACtCnF;QACAY;QACAwE,WAAWrE;QACXsE,OAAO;IACT;IAEA,qBAAqB;IACrB,IAAItE,YAAYuE,IAAAA,gCAAe,EAAC1E,OAAO;QACrC,OAAO2E,IAAAA,wBAAc,EACnB1F,KACAyD,KACAH,QACAvC,MACAsE,WAAWM,WAAW,EACtB7F,MAAM6C,UAAU,CAACiD,gBAAgB,EACjC9F,MAAM6C,UAAU,CAACkD,iBAAiB,EAClCZ,cACAlF,YACAD,MAAM6C,UAAU,CAACmD,YAAY,EAC7BhG,MAAM6C,UAAU,CAACoD,OAAO;IAE5B;IAEA,MAAMpD,aAA+B;QACnC,GAAG0C,UAAU;QACb,GAAGvF,MAAM6C,UAAU;QACnBN,SAASC;QACTgB;QACA9C;QACAC;QACAgC;QACAuD,yBAAyB;QACzBF,cAAc;YACZ,GAAGhG,MAAM6C,UAAU,CAACmD,YAAY;YAChCxE;QACF;IACF;IAEA,IAAI2E,sBAAc,EAAE;QAClBtD,WAAWuD,YAAY,GAAG;IAC5B;IAEA,mBAAmB;IACnB,IAAIhF,UAAU;QACZ,0EAA0E;QAC1E,sEAAsE;QACtE,IAAIM,qBAAqB;YACvB,OAAO2E,IAAAA,iCAAwB,EAC7BnG,KACAyD,KACA1C,MACAc,UACAJ,OACAE,qBACAgB;QAEJ;QAEA,OAAOyD,IAAAA,sBAAa,EAClBpG,KACAyD,KACA1C,MACAd,MACA4B,UACAJ,OACAE,qBACAgB,YACAsC,cACAvE,aACAU,gBACArB;IAEJ;IAEA,OAAOsG,IAAAA,sBAAe,EACpBrG,KACAyD,KACAxD,MACAc,MACAU,OACA6B,QACA2B,cACAR,cACApC,SACA9B,YACA0B,QACArB,kBACAR,cACAC,aACA0B,WACAkB,oBACAN,YACA0C,YACAtF;AAEJ;AAEO,eAAef,YACpBc,KAAuB;IAEvB,MAAM,EACJwG,aAAa,EACbC,KAAK,EACLC,GAAG,EACHrG,OAAO,EACP8B,MAAM,EACNwE,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBvG,YAAY,EACZuC,UAAU,EACViE,UAAU,EACVC,OAAO,EACR,GAAG/G;IAEJ,mEAAmE;IACnE,gCAAgC;IAChC,MAAM8F,mBAAmB,MAAMkB,IAAAA,8CAAsB,EAAC;QACpDL;QACAC;QACAC;QACAxG;QACAqG;QACAO,WAAWC,QAAQJ,WAAWd,YAAY,CAACiB,SAAS;QACpD,6DAA6D;QAC7D,+BAA+B;QAC/BE,aAAa,CAAChB,sBAAc;QAC5BiB,eAAeN,WAAWd,YAAY,CAACoB,aAAa;IACtD;IAEAvE,WAAWiD,gBAAgB,GAAGA;IAE9B,MAAMuB,iBACJP,WAAWd,YAAY,CAACsB,8BAA8B,IAAI;IAC5D,MAAMC,UAA6B,EAAE;IAErC,MAAMC,sBAAsB,OAAOrH,MAAcsH;QAC/C,MAAMrH,UAAUoG,aAAa,CAACrG,KAAK;QACnC,MAAM,EAAEc,IAAI,EAAE,GAAGuF,aAAa,CAACrG,KAAK;QACpC,MAAMuH,UAAUzG,SAASd,OAAO,GAAGc,KAAK,EAAE,EAAEd,MAAM,GAAGA;QACrD,IAAIwH,UAAU;QACd,IAAIC;QAEJ,MAAOD,UAAUF,YAAa;YAC5B,IAAI;oBAUIX;gBATNc,SAAS,MAAMC,QAAQC,IAAI,CAA+B;oBACxDC,WAAW;wBACT5H;wBACAC;wBACAC;wBACA8B;wBACA7B;wBACAuC;wBACA/B,kBACEgG,EAAAA,+BAAAA,WAAWd,YAAY,CAACgC,GAAG,qBAA3BlB,6BAA6BmB,SAAS,KAAIC;wBAC5CnH,eAAe+F,WAAW/F,aAAa;wBACvCP,qBAAqBsG,WAAWtG,mBAAmB;wBACnDC,YAAYqG,WAAW/F,aAAa,IAAI,CAACgG,QAAQxG,WAAW;wBAC5DA,aAAawG,QAAQxG,WAAW;wBAChCG,aAAaoG,WAAWd,YAAY,CAACtF,WAAW;wBAChDC,yBACEmG,WAAWd,YAAY,CAACrF,uBAAuB;wBACjDwH,cAAcnI,MAAMmI,YAAY;wBAChCC,kBAAkBtB,WAAWsB,gBAAgB;wBAC7CxH,aAAamG,QAAQnG,WAAW;wBAChCC,yBAAyBwH,IAAAA,8CAAsB,EAACvB;oBAClD;oBACA,2EAA2E;oBAC3E,IAAIe,QAAQ,CAACS,GAAGC;wBACdC,WAAW;4BACTD,OAAO,IAAI5I;wBACb,GAAGmH,WAAW2B,2BAA2B,GAAG;oBAC9C;iBACD;gBAED,4GAA4G;gBAC5G,qHAAqH;gBACrH,IAAIb,UAAU,WAAWA,QAAQ;oBAC/B,MAAM,IAAI9H;gBACZ;gBAGA;YACF,EAAE,OAAO4I,KAAK;gBACZ,qJAAqJ;gBACrJ,mGAAmG;gBACnG,IAAI,CAAEA,CAAAA,eAAe5I,mBAAmB4I,eAAe/I,YAAW,GAAI;oBACpE,MAAM+I;gBACR;gBAEA,IAAIA,eAAe/I,cAAc;oBAC/B,qEAAqE;oBACrE8H,cAAc;gBAChB;gBAEA,+CAA+C;gBAC/C,IAAIE,WAAWF,cAAc,GAAG;oBAC9B,iEAAiE;oBACjE,yDAAyD;oBACzD,IAAIA,cAAc,GAAG;wBACnBkB,QAAQC,IAAI,CACV,CAAC,gBAAgB,EAAElB,QAAQ,OAAO,EAAED,YAAY,UAAU,CAAC;oBAE/D;oBACA,sEAAsE;oBACtE,IAAIX,WAAWd,YAAY,CAAC6C,kBAAkB,EAAE;wBAC9CF,QAAQG,KAAK,CACX,CAAC,+BAA+B,EAAEpB,QAAQ,oBAAoB,CAAC;wBAEjEvI,QAAQ4J,IAAI,CAAC;oBACf,OAAO;oBACL,mHAAmH;oBACrH;gBACF,OAAO;oBACL,iEAAiE;oBACjE,IAAIL,eAAe/I,cAAc;wBAC/BgJ,QAAQC,IAAI,CACV,CAAC,gBAAgB,EAAElB,QAAQ,UAAU,EAAEC,UAAU,EAAE,IAAI,EAAEF,YAAY,4BAA4B,EAAEX,WAAW2B,2BAA2B,CAAC,iCAAiC,CAAC;oBAEhL,OAAO;wBACLE,QAAQC,IAAI,CACV,CAAC,gBAAgB,EAAElB,QAAQ,UAAU,EAAEC,UAAU,EAAE,IAAI,EAAEF,YAAY,0BAA0B,CAAC;oBAEpG;oBACA,MAAM,IAAII,QAAQ,CAACmB,IAAMR,WAAWQ,GAAGC,KAAKC,MAAM,KAAK;gBACzD;YACF;YAEAvB;QACF;QAEA,OAAO;YAAEC;YAAQzH;YAAMuH;QAAQ;IACjC;IAEA,IAAK,IAAIyB,IAAI,GAAGA,IAAI1C,MAAMnD,MAAM,EAAE6F,KAAK9B,eAAgB;QACrD,MAAM+B,SAAS3C,MAAM4C,KAAK,CAACF,GAAGA,IAAI9B;QAElC,MAAMiC,gBAAgB,MAAMzB,QAAQ0B,GAAG,CACrCH,OAAOI,GAAG,CAAC,CAACrJ,OACVqH,oBACErH,MACA2G,WAAWd,YAAY,CAACyD,0BAA0B,IAAI;QAK5DlC,QAAQmC,IAAI,IAAIJ;IAClB;IAEA,OAAO/B;AACT;AAEA,eAAeQ,WACb/H,KAAsB;IAEtB2J,IAAAA,YAAK,EAAC,eAAe3J,MAAMmI,YAAY,EAAEyB,YAAY,CAAC,QAAQ5J,MAAMG,IAAI;IAExE,4BAA4B;IAC5B0J,IAAAA,+CAA4B,EAAC;QAC3BzB,kBAAkBpI,MAAMoI,gBAAgB;IAC1C;IAEA,MAAM0B,QAA4B,EAAE;IACpC,MAAMC,iBAA6B,OACjCC,MACA7J,MACA8J,SACAC,kBAAkB,OAAO;QAEzB,MAAM9E,iBAAE,CAACC,KAAK,CAACH,IAAAA,aAAO,EAAC/E,OAAO;YAAEmF,WAAW;QAAK;QAChD,MAAMF,iBAAE,CAAC+E,SAAS,CAAChK,MAAM8J,SAASC;QAClCJ,MAAMJ,IAAI,CAAC;YAAEM;YAAM7J;QAAK;IAC1B;IAEA,MAAMiK,iBAAiBT,IAAAA,YAAK,EAAC,sBAAsB3J,MAAMmI,YAAY;IAErE,MAAMkC,QAAQC,KAAKC,GAAG;IAEtB,MAAMC,6BAA6B,IAAIC,gDAA0B;IAEjE,mBAAmB;IACnB,IAAI7C;IACJ,IAAI;QACFA,SAAS,MAAMwC,eAAeM,YAAY,CAAC,IACzCC,IAAAA,0CAAoB,EAClB,IAAM5K,eAAeC,OAAO+J,iBAC5BS;QAIJ,kDAAkD;QAClD,IAAI,CAAC5C,QAAQ;QAEb,iDAAiD;QACjD,IAAI,WAAWA,QAAQ;YACrB,OAAO;gBAAEkB,OAAOlB,OAAOkB,KAAK;gBAAE8B,UAAUN,KAAKC,GAAG,KAAKF;gBAAOP,OAAO,EAAE;YAAC;QACxE;IACF,EAAE,OAAOpB,KAAK;QACZC,QAAQG,KAAK,CACX,CAAC,kCAAkC,EAAE9I,MAAMG,IAAI,CAAC,8DAA8D,CAAC;QAGjH,2FAA2F;QAC3F,qBAAqB;QACrB,IAAI,CAAC0K,IAAAA,iCAAmB,EAACnC,MAAM;YAC7B,wFAAwF;YACxF,wFAAwF;YACxF,wGAAwG;YACxG,IAAIoC,IAAAA,gDAAuB,EAACpC,MAAM;gBAChC,IAAIA,IAAIqC,OAAO,EAAE;oBACfpC,QAAQG,KAAK,CAAC,CAAC,OAAO,EAAEJ,IAAIqC,OAAO,EAAE;gBACvC;YACF,OAAO,IAAIC,IAAAA,gBAAO,EAACtC,QAAQA,IAAIuC,KAAK,EAAE;gBACpCtC,QAAQG,KAAK,CAACJ,IAAIuC,KAAK;YACzB,OAAO;gBACLtC,QAAQG,KAAK,CAACJ;YAChB;QACF;QAEA,OAAO;YAAEI,OAAO;YAAM8B,UAAUN,KAAKC,GAAG,KAAKF;YAAOP,OAAO,EAAE;QAAC;IAChE;IAEA,+FAA+F;IAC/F3K,QAAQ+L,IAAI,oBAAZ/L,QAAQ+L,IAAI,MAAZ/L,SAAe;QAAC;QAAG;YAAE6K,MAAM;QAAW;KAAE;IAExC,sCAAsC;IACtC,OAAO;QACLY,UAAUN,KAAKC,GAAG,KAAKF;QACvBP;QACAqB,gBAAgBvD,OAAOuD,cAAc;QACrCC,YAAYxD,OAAOwD,UAAU;QAC7BC,UAAUzD,OAAOyD,QAAQ;QACzBC,aAAa1D,OAAO0D,WAAW;QAC/BC,iBAAiB3D,OAAO2D,eAAe;QACvCC,cAAc5D,OAAO4D,YAAY;QACjChB,4BAA4BA,2BAA2BiB,SAAS;QAChEC,cAAc9D,OAAO8D,YAAY;IACnC;AACF;AAEAvM,QAAQwM,EAAE,CAAC,sBAAsB,CAACjD;IAChC,mDAAmD;IACnD,kDAAkD;IAClD,IAAIkD,IAAAA,sBAAU,EAAClD,MAAM;QACnB;IACF;IAEA,oCAAoC;IACpC,IAAImD,IAAAA,wCAAmB,EAACnD,MAAM;QAC5B;IACF;IAEAC,QAAQG,KAAK,CAACJ;AAChB;AAEAvJ,QAAQwM,EAAE,CAAC,oBAAoB;AAC7B,sEAAsE;AACtE,qEAAqE;AACrE,6DAA6D;AAC/D;AAEA,MAAMG,qCAAqC;AAE3C3M,QAAQwM,EAAE,CAAC,qBAAqB,CAACjD;IAC/B,IAAImD,IAAAA,wCAAmB,EAACnD,MAAM;QAC5BC,QAAQG,KAAK,CACX;QAEFH,QAAQG,KAAK,CAACJ;QACdvJ,QAAQ4J,IAAI,CAAC+C;IACf,OAAO;QACLnD,QAAQG,KAAK,CAACJ;IAChB;AACF"}