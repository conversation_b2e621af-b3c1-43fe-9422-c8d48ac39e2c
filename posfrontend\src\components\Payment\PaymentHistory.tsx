"use client";
import React from "react";
import { Tag, Spin, Alert, Empty } from "antd";
import { useGetPaymentHistoryQuery } from "@/reduxRTK/services/paymentApi";
import { ResponsiveTableGrid, TableHeader, TableCell, TableRow } from "@/components/ui/ResponsiveTable";
import dayjs from "dayjs";
import { Payment } from "@/types/payment";

interface PaymentHistoryProps {
  limit?: number;
}

const PaymentHistory: React.FC<PaymentHistoryProps> = ({ limit = 10 }) => {
  const { data, error, isLoading } = useGetPaymentHistoryQuery({ page: 1, limit });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Spin size="large" tip="Loading payment history..." />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Error"
        description="Failed to load payment history. Please try again later."
        type="error"
        showIcon
      />
    );
  }

  // Handle both possible data structures
  const payments = data?.data?.payments || data?.payments || [];
  const total = data?.data?.total || data?.total || 0;

  if (!payments || payments.length === 0) {
    return (
      <Empty
        description="No payment history found"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    );
  }

  const getProviderColor = (provider: string) => {
    switch (provider.toLowerCase()) {
      case "mtn":
        return "yellow";
      case "vodafone":
        return "red";
      case "airteltigo":
        return "blue";
      case "paystack":
        return "green";
      default:
        return "default";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "successful":
        return "success";
      case "pending":
        return "warning";
      case "failed":
        return "error";
      default:
        return "default";
    }
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-md border border-gray-200">
      <h2 className="text-xl font-bold mb-4 text-gray-800">Payment History</h2>

      <ResponsiveTableGrid
        columns="1fr 120px 100px 200px 120px"
        minWidth="700px"
        className="mb-4"
      >
        {/* Table Headers */}
        <TableHeader>Date</TableHeader>
        <TableHeader>Amount</TableHeader>
        <TableHeader>Provider</TableHeader>
        <TableHeader>Transaction ID</TableHeader>
        <TableHeader>Status</TableHeader>

        {/* Table Rows */}
        {payments.map((payment: Payment) => (
          <TableRow key={payment.id}>
            <TableCell>
              {dayjs(payment.paidAt).format("MMM D, YYYY h:mm A")}
            </TableCell>
            <TableCell>
              <span className="font-medium text-green-600">
                ₵{typeof payment.amount === 'number' ? payment.amount.toFixed(2) : parseFloat(payment.amount || '0').toFixed(2)}
              </span>
            </TableCell>
            <TableCell>
              <Tag color={getProviderColor(payment.provider)}>
                {payment.provider.toUpperCase()}
              </Tag>
            </TableCell>
            <TableCell>
              <span className="font-mono text-xs">
                {payment.transactionId}
              </span>
            </TableCell>
            <TableCell>
              <Tag color={getStatusColor(payment.status)}>
                {payment.status.toUpperCase()}
              </Tag>
            </TableCell>
          </TableRow>
        ))}
      </ResponsiveTableGrid>

      {/* Pagination Info */}
      {total > limit && (
        <div className="text-sm text-gray-500 text-center mt-4">
          Showing {Math.min(limit, payments.length)} of {total} payments
        </div>
      )}
    </div>
  );
};

export default PaymentHistory;
