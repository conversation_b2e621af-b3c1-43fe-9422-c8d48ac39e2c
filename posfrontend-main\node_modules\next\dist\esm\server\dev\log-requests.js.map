{"version": 3, "sources": ["../../../src/server/dev/log-requests.ts"], "sourcesContent": ["import {\n  blue,\n  bold,\n  gray,\n  green,\n  red,\n  white,\n  yellow,\n} from '../../lib/picocolors'\nimport { stripNextRscUnionQuery } from '../../lib/url'\nimport type { FetchMetric } from '../base-http'\nimport type { NodeNextRequest, NodeNextResponse } from '../base-http/node'\nimport type { LoggingConfig } from '../config-shared'\nimport { getRequestMeta } from '../request-meta'\n\nexport interface RequestLoggingOptions {\n  readonly request: NodeNextRequest\n  readonly response: NodeNextResponse\n  readonly loggingConfig: LoggingConfig | undefined\n  readonly requestDurationInMs: number\n}\n\nexport function logRequests(options: RequestLoggingOptions): void {\n  const { request, response, loggingConfig, requestDurationInMs } = options\n\n  logIncomingRequest({\n    request,\n    requestDurationInMs,\n    statusCode: response.statusCode,\n  })\n\n  if (request.fetchMetrics) {\n    for (const fetchMetric of request.fetchMetrics) {\n      logFetchMetric(fetchMetric, loggingConfig)\n    }\n  }\n}\n\ninterface IncomingRequestOptions {\n  readonly request: NodeNextRequest\n  readonly requestDurationInMs: number\n  readonly statusCode: number\n}\n\nfunction logIncomingRequest(options: IncomingRequestOptions): void {\n  const { request, requestDurationInMs, statusCode } = options\n  const isRSC = getRequestMeta(request, 'isRSCRequest')\n  const url = isRSC ? stripNextRscUnionQuery(request.url) : request.url\n\n  const statusCodeColor =\n    statusCode < 200\n      ? white\n      : statusCode < 300\n        ? green\n        : statusCode < 400\n          ? blue\n          : statusCode < 500\n            ? yellow\n            : red\n\n  const coloredStatus = statusCodeColor(statusCode.toString())\n\n  return writeLine(\n    `${request.method} ${url} ${coloredStatus} in ${requestDurationInMs}ms`\n  )\n}\n\nfunction logFetchMetric(\n  fetchMetric: FetchMetric,\n  loggingConfig: LoggingConfig | undefined\n): void {\n  let {\n    cacheReason,\n    cacheStatus,\n    cacheWarning,\n    end,\n    method,\n    start,\n    status,\n    url,\n  } = fetchMetric\n\n  if (cacheStatus === 'hmr' && !loggingConfig?.fetches?.hmrRefreshes) {\n    // Cache hits during HMR refreshes are intentionally not logged, unless\n    // explicitly enabled in the logging config.\n    return\n  }\n\n  if (loggingConfig?.fetches) {\n    if (url.length > 48 && !loggingConfig.fetches.fullUrl) {\n      url = truncateUrl(url)\n    }\n\n    writeLine(\n      white(\n        `${method} ${url} ${status} in ${Math.round(end - start)}ms ${formatCacheStatus(cacheStatus)}`\n      ),\n      1\n    )\n\n    if (cacheStatus === 'skip' || cacheStatus === 'miss') {\n      writeLine(\n        gray(\n          `Cache ${cacheStatus === 'skip' ? 'skipped' : 'missed'} reason: (${white(cacheReason)})`\n        ),\n        2\n      )\n    }\n  } else if (cacheWarning) {\n    // When logging for fetches is not enabled, we still want to print any\n    // associated warnings, so we print the request first to provide context.\n    writeLine(white(`${method} ${url}`), 1)\n  }\n\n  if (cacheWarning) {\n    writeLine(`${yellow(bold('⚠'))} ${white(cacheWarning)}`, 2)\n  }\n}\n\nfunction writeLine(text: string, indentationLevel = 0): void {\n  process.stdout.write(` ${'│ '.repeat(indentationLevel)}${text}\\n`)\n}\n\nfunction truncate(text: string, maxLength: number): string {\n  return maxLength !== undefined && text.length > maxLength\n    ? text.substring(0, maxLength) + '..'\n    : text\n}\n\nfunction truncateUrl(url: string): string {\n  const { protocol, host, pathname, search } = new URL(url)\n\n  return (\n    protocol +\n    '//' +\n    truncate(host, 16) +\n    truncate(pathname, 24) +\n    truncate(search, 16)\n  )\n}\n\nfunction formatCacheStatus(cacheStatus: FetchMetric['cacheStatus']): string {\n  switch (cacheStatus) {\n    case 'hmr':\n      return green('(HMR cache)')\n    case 'hit':\n      return green('(cache hit)')\n    default:\n      return yellow(`(cache ${cacheStatus})`)\n  }\n}\n"], "names": ["blue", "bold", "gray", "green", "red", "white", "yellow", "stripNextRscUnionQuery", "getRequestMeta", "logRequests", "options", "request", "response", "loggingConfig", "requestDurationInMs", "logIncomingRequest", "statusCode", "fetchMetrics", "fetchMetric", "logFetchMetric", "isRSC", "url", "statusCodeColor", "coloredStatus", "toString", "writeLine", "method", "cacheReason", "cacheStatus", "cacheWarning", "end", "start", "status", "fetches", "hmrRefreshes", "length", "fullUrl", "truncateUrl", "Math", "round", "formatCacheStatus", "text", "indentationLevel", "process", "stdout", "write", "repeat", "truncate", "max<PERSON><PERSON><PERSON>", "undefined", "substring", "protocol", "host", "pathname", "search", "URL"], "mappings": "AAAA,SACEA,IAAI,EACJC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,MAAM,QACD,uBAAsB;AAC7B,SAASC,sBAAsB,QAAQ,gBAAe;AAItD,SAASC,cAAc,QAAQ,kBAAiB;AAShD,OAAO,SAASC,YAAYC,OAA8B;IACxD,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,mBAAmB,EAAE,GAAGJ;IAElEK,mBAAmB;QACjBJ;QACAG;QACAE,YAAYJ,SAASI,UAAU;IACjC;IAEA,IAAIL,QAAQM,YAAY,EAAE;QACxB,KAAK,MAAMC,eAAeP,QAAQM,YAAY,CAAE;YAC9CE,eAAeD,aAAaL;QAC9B;IACF;AACF;AAQA,SAASE,mBAAmBL,OAA+B;IACzD,MAAM,EAAEC,OAAO,EAAEG,mBAAmB,EAAEE,UAAU,EAAE,GAAGN;IACrD,MAAMU,QAAQZ,eAAeG,SAAS;IACtC,MAAMU,MAAMD,QAAQb,uBAAuBI,QAAQU,GAAG,IAAIV,QAAQU,GAAG;IAErE,MAAMC,kBACJN,aAAa,MACTX,QACAW,aAAa,MACXb,QACAa,aAAa,MACXhB,OACAgB,aAAa,MACXV,SACAF;IAEZ,MAAMmB,gBAAgBD,gBAAgBN,WAAWQ,QAAQ;IAEzD,OAAOC,UACL,GAAGd,QAAQe,MAAM,CAAC,CAAC,EAAEL,IAAI,CAAC,EAAEE,cAAc,IAAI,EAAET,oBAAoB,EAAE,CAAC;AAE3E;AAEA,SAASK,eACPD,WAAwB,EACxBL,aAAwC;QAaVA;IAX9B,IAAI,EACFc,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,GAAG,EACHJ,MAAM,EACNK,KAAK,EACLC,MAAM,EACNX,GAAG,EACJ,GAAGH;IAEJ,IAAIU,gBAAgB,SAAS,EAACf,kCAAAA,yBAAAA,cAAeoB,OAAO,qBAAtBpB,uBAAwBqB,YAAY,GAAE;QAClE,uEAAuE;QACvE,4CAA4C;QAC5C;IACF;IAEA,IAAIrB,iCAAAA,cAAeoB,OAAO,EAAE;QAC1B,IAAIZ,IAAIc,MAAM,GAAG,MAAM,CAACtB,cAAcoB,OAAO,CAACG,OAAO,EAAE;YACrDf,MAAMgB,YAAYhB;QACpB;QAEAI,UACEpB,MACE,GAAGqB,OAAO,CAAC,EAAEL,IAAI,CAAC,EAAEW,OAAO,IAAI,EAAEM,KAAKC,KAAK,CAACT,MAAMC,OAAO,GAAG,EAAES,kBAAkBZ,cAAc,GAEhG;QAGF,IAAIA,gBAAgB,UAAUA,gBAAgB,QAAQ;YACpDH,UACEvB,KACE,CAAC,MAAM,EAAE0B,gBAAgB,SAAS,YAAY,SAAS,UAAU,EAAEvB,MAAMsB,aAAa,CAAC,CAAC,GAE1F;QAEJ;IACF,OAAO,IAAIE,cAAc;QACvB,sEAAsE;QACtE,yEAAyE;QACzEJ,UAAUpB,MAAM,GAAGqB,OAAO,CAAC,EAAEL,KAAK,GAAG;IACvC;IAEA,IAAIQ,cAAc;QAChBJ,UAAU,GAAGnB,OAAOL,KAAK,MAAM,CAAC,EAAEI,MAAMwB,eAAe,EAAE;IAC3D;AACF;AAEA,SAASJ,UAAUgB,IAAY,EAAEC,mBAAmB,CAAC;IACnDC,QAAQC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAKC,MAAM,CAACJ,oBAAoBD,KAAK,EAAE,CAAC;AACnE;AAEA,SAASM,SAASN,IAAY,EAAEO,SAAiB;IAC/C,OAAOA,cAAcC,aAAaR,KAAKN,MAAM,GAAGa,YAC5CP,KAAKS,SAAS,CAAC,GAAGF,aAAa,OAC/BP;AACN;AAEA,SAASJ,YAAYhB,GAAW;IAC9B,MAAM,EAAE8B,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,EAAE,GAAG,IAAIC,IAAIlC;IAErD,OACE8B,WACA,OACAJ,SAASK,MAAM,MACfL,SAASM,UAAU,MACnBN,SAASO,QAAQ;AAErB;AAEA,SAASd,kBAAkBZ,WAAuC;IAChE,OAAQA;QACN,KAAK;YACH,OAAOzB,MAAM;QACf,KAAK;YACH,OAAOA,MAAM;QACf;YACE,OAAOG,OAAO,CAAC,OAAO,EAAEsB,YAAY,CAAC,CAAC;IAC1C;AACF"}