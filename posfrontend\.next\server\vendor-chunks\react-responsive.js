"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-responsive";
exports.ids = ["vendor-chunks/react-responsive"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-responsive/dist/esm/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-responsive/dist/esm/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Context: () => (/* binding */ Context),\n/* harmony export */   MediaQuery: () => (/* binding */ MediaQuery),\n/* harmony export */   \"default\": () => (/* binding */ MediaQuery),\n/* harmony export */   toQuery: () => (/* binding */ toQuery),\n/* harmony export */   useMediaQuery: () => (/* binding */ useMediaQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var matchmediaquery__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! matchmediaquery */ \"(ssr)/./node_modules/matchmediaquery/index.js\");\n/* harmony import */ var matchmediaquery__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(matchmediaquery__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var hyphenate_style_name__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! hyphenate-style-name */ \"(ssr)/./node_modules/hyphenate-style-name/index.js\");\n/* harmony import */ var shallow_equal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! shallow-equal */ \"(ssr)/./node_modules/shallow-equal/dist/index.modern.mjs\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n\n\nconst stringOrNumber = prop_types__WEBPACK_IMPORTED_MODULE_3___default().oneOfType([(prop_types__WEBPACK_IMPORTED_MODULE_3___default().string), (prop_types__WEBPACK_IMPORTED_MODULE_3___default().number)]);\n// media types\nconst types = {\n    all: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    grid: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    aural: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    braille: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    handheld: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    print: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    projection: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    screen: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    tty: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    tv: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    embossed: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool)\n};\n// properties that match media queries\nconst matchers = {\n    orientation: prop_types__WEBPACK_IMPORTED_MODULE_3___default().oneOf(['portrait', 'landscape']),\n    scan: prop_types__WEBPACK_IMPORTED_MODULE_3___default().oneOf(['progressive', 'interlace']),\n    aspectRatio: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().string),\n    deviceAspectRatio: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().string),\n    height: stringOrNumber,\n    deviceHeight: stringOrNumber,\n    width: stringOrNumber,\n    deviceWidth: stringOrNumber,\n    color: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    colorIndex: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    monochrome: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    resolution: stringOrNumber,\n    type: Object.keys(types)\n};\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst { type, ...featureMatchers } = matchers;\n// media features\nconst features = {\n    minAspectRatio: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().string),\n    maxAspectRatio: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().string),\n    minDeviceAspectRatio: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().string),\n    maxDeviceAspectRatio: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().string),\n    minHeight: stringOrNumber,\n    maxHeight: stringOrNumber,\n    minDeviceHeight: stringOrNumber,\n    maxDeviceHeight: stringOrNumber,\n    minWidth: stringOrNumber,\n    maxWidth: stringOrNumber,\n    minDeviceWidth: stringOrNumber,\n    maxDeviceWidth: stringOrNumber,\n    minColor: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().number),\n    maxColor: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().number),\n    minColorIndex: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().number),\n    maxColorIndex: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().number),\n    minMonochrome: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().number),\n    maxMonochrome: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().number),\n    minResolution: stringOrNumber,\n    maxResolution: stringOrNumber,\n    ...featureMatchers\n};\nconst all = { ...types, ...features };\nvar mq = {\n    all: all,\n    types: types,\n    matchers: matchers,\n    features: features\n};\n\nconst negate = (cond) => `not ${cond}`;\nconst keyVal = (k, v) => {\n    const realKey = (0,hyphenate_style_name__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(k);\n    // px shorthand\n    if (typeof v === 'number') {\n        v = `${v}px`;\n    }\n    if (v === true) {\n        return realKey;\n    }\n    if (v === false) {\n        return negate(realKey);\n    }\n    return `(${realKey}: ${v})`;\n};\nconst join = (conds) => conds.join(' and ');\nconst toQuery = (obj) => {\n    const rules = [];\n    Object.keys(mq.all).forEach((k) => {\n        const v = obj[k];\n        if (v != null) {\n            rules.push(keyVal(k, v));\n        }\n    });\n    return join(rules);\n};\n\nconst Context = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\n\nconst makeQuery = (settings) => settings.query || toQuery(settings);\nconst hyphenateKeys = (obj) => {\n    if (!obj)\n        return undefined;\n    const keys = Object.keys(obj);\n    return keys.reduce((result, key) => {\n        result[(0,hyphenate_style_name__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(key)] = obj[key];\n        return result;\n    }, {});\n};\nconst useIsUpdate = () => {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        ref.current = true;\n    }, []);\n    return ref.current;\n};\nconst useDevice = (deviceFromProps) => {\n    const deviceFromContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(Context);\n    const getDevice = () => hyphenateKeys(deviceFromProps) || hyphenateKeys(deviceFromContext);\n    const [device, setDevice] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getDevice);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        const newDevice = getDevice();\n        if (!(0,shallow_equal__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(device, newDevice)) {\n            setDevice(newDevice);\n        }\n    }, [deviceFromProps, deviceFromContext]);\n    return device;\n};\nconst useQuery = (settings) => {\n    const getQuery = () => makeQuery(settings);\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getQuery);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        const newQuery = getQuery();\n        if (query !== newQuery) {\n            setQuery(newQuery);\n        }\n    }, [settings]);\n    return query;\n};\nconst useMatchMedia = (query, device) => {\n    const getMatchMedia = () => matchmediaquery__WEBPACK_IMPORTED_MODULE_1___default()(query, device || {}, !!device);\n    const [mq, setMq] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getMatchMedia);\n    const isUpdate = useIsUpdate();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (isUpdate) {\n            // skip on mounting, it has already been set\n            const newMq = getMatchMedia();\n            setMq(newMq);\n            return () => {\n                if (newMq) {\n                    newMq.dispose();\n                }\n            };\n        }\n    }, [query, device]);\n    return mq;\n};\nconst useMatches = (mediaQuery) => {\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(mediaQuery.matches);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        const updateMatches = (ev) => {\n            setMatches(ev.matches);\n        };\n        mediaQuery.addListener(updateMatches);\n        setMatches(mediaQuery.matches);\n        return () => {\n            mediaQuery.removeListener(updateMatches);\n        };\n    }, [mediaQuery]);\n    return matches;\n};\nconst useMediaQuery = (settings, device, onChange) => {\n    const deviceSettings = useDevice(device);\n    const query = useQuery(settings);\n    if (!query)\n        throw new Error('Invalid or missing MediaQuery!');\n    const mq = useMatchMedia(query, deviceSettings);\n    const matches = useMatches(mq);\n    const isUpdate = useIsUpdate();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (isUpdate && onChange) {\n            onChange(matches);\n        }\n    }, [matches]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => () => {\n        if (mq) {\n            mq.dispose();\n        }\n    }, []);\n    return matches;\n};\n\n// ReactNode and ReactElement typings are a little funky for functional components, so the ReactElement cast is needed on the return\nconst MediaQuery = ({ children, device, onChange, ...settings }) => {\n    const matches = useMediaQuery(settings, device, onChange);\n    if (typeof children === 'function') {\n        return children(matches);\n    }\n    return matches ? children : null;\n};\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-responsive/dist/esm/index.js\n");

/***/ })

};
;