/* Modern Sales Form Styling */

/* Modern Select Styling */
.modern-select .ant-select-selector {
  border-radius: 12px !important;
  border: 2px solid #e2e8f0 !important;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
  transition: all 0.2s ease-in-out !important;
  min-height: 48px !important;
  padding-right: 40px !important;
  padding-left: 12px !important;
}

/* Remove any default list styling or icons */
.modern-select .ant-select-selector,
.modern-select .ant-select-selector * {
  list-style: none !important;
  background-image: none !important;
  text-indent: 0 !important;
}

.modern-select .ant-select-selector:hover {
  border-color: #3b82f6 !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.modern-select.ant-select-focused .ant-select-selector {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.modern-select .ant-select-selection-placeholder {
  color: #94a3b8 !important;
  font-weight: 500 !important;
}

/* Fix dropdown arrow positioning */
.modern-select .ant-select-arrow {
  right: 12px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  color: #64748b !important;
  font-size: 14px !important;
}

.modern-select .ant-select-arrow .anticon {
  vertical-align: middle !important;
}

/* Modern Input Number Styling */
.modern-input .ant-input-number {
  border-radius: 12px !important;
  border: 2px solid #e2e8f0 !important;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
  transition: all 0.2s ease-in-out !important;
  min-height: 48px !important;
}

.modern-input .ant-input-number:hover {
  border-color: #3b82f6 !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.modern-input .ant-input-number-focused {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Dropdown Menu Styling */
.ant-select-dropdown {
  border-radius: 16px !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  backdrop-filter: blur(8px) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  max-height: 300px !important;
  overflow-y: auto !important;
}

.ant-select-item {
  border-radius: 8px !important;
  margin: 4px 8px !important;
  padding: 8px 12px !important;
  transition: all 0.2s ease-in-out !important;
  min-height: auto !important;
  line-height: 1.4 !important;
}

.ant-select-item-option {
  padding: 8px 12px !important;
  min-height: 48px !important;
  display: flex !important;
  align-items: center !important;
}

.ant-select-item-option-content {
  flex: 1 !important;
  overflow: hidden !important;
}

.ant-select-item-option-selected {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%) !important;
  color: #1e40af !important;
  font-weight: 600 !important;
}

.ant-select-item-option-active {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
}

/* Fix for payment method display */
.modern-select .ant-select-selection-item {
  display: flex !important;
  align-items: center !important;
  line-height: 1.4 !important;
  padding: 8px 12px !important;
  font-weight: 500 !important;
  color: #334155 !important;
  background: none !important;
  border: none !important;
}

/* Remove any default icons or backgrounds */
.modern-select .ant-select-selection-item::before,
.modern-select .ant-select-selection-item::after {
  display: none !important;
  content: none !important;
}

/* Ensure clean text display without extra elements */
.modern-select .ant-select-selection-item {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol' !important;
}

/* Remove any Ant Design default styling that might add icons */
.modern-select .ant-select-selector .ant-select-selection-item {
  background-image: none !important;
  list-style: none !important;
}

/* Fix icon positioning in selected item */
.modern-select .ant-select-selection-item span {
  display: inline-flex !important;
  align-items: center !important;
  vertical-align: middle !important;
}

/* Form Item Labels */
.ant-form-item-label > label {
  font-weight: 600 !important;
  color: #334155 !important;
}

/* Scrollbar Styling */
.max-h-\[400px\]::-webkit-scrollbar {
  width: 6px;
}

.max-h-\[400px\]::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.max-h-\[400px\]::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  border-radius: 3px;
}

.max-h-\[400px\]::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
}

/* Button Hover Effects */
.ant-btn:not(.ant-btn-primary):hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Loading Spinner */
.ant-spin-dot-item {
  background-color: #3b82f6 !important;
}

/* Modal Styling */
.ant-modal-content {
  border-radius: 20px !important;
  overflow: hidden !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

.ant-modal-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  border-bottom: 1px solid #e2e8f0 !important;
  padding: 20px 24px !important;
}

.ant-modal-title {
  font-weight: 700 !important;
  color: #1e293b !important;
}

/* Backdrop Blur Effect */
.backdrop-blur-sm {
  backdrop-filter: blur(4px) !important;
}

/* Gradient Text */
.bg-clip-text {
  -webkit-background-clip: text !important;
  background-clip: text !important;
}

/* Animation Classes */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

/* Glass Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.7) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Additional fixes for dropdown content */
.ant-select-item-option div {
  white-space: normal !important;
  word-wrap: break-word !important;
}

.ant-select-item-option .flex {
  width: 100% !important;
}

/* Ensure proper spacing in dropdown items */
.ant-select-dropdown .ant-select-item-option {
  height: auto !important;
  min-height: 48px !important;
  padding: 12px 16px !important;
  font-weight: 500 !important;
  color: #334155 !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

/* Fix icon alignment in dropdown options */
.ant-select-dropdown .ant-select-item-option span {
  display: inline-flex !important;
  align-items: center !important;
  vertical-align: middle !important;
}

/* Fix text overflow in selected item */
.modern-select .ant-select-selector .ant-select-selection-item {
  overflow: visible !important;
  text-overflow: clip !important;
  white-space: nowrap !important;
}

/* Specific emoji icon fixes */
.modern-select .ant-select-selection-item,
.ant-select-dropdown .ant-select-item-option {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol' !important;
}

/* Ensure emojis are properly sized and aligned */
.modern-select .ant-select-selection-item::first-letter,
.ant-select-dropdown .ant-select-item-option::first-letter {
  font-size: 16px !important;
  line-height: 1 !important;
  vertical-align: middle !important;
  margin-right: 6px !important;
}

/* Additional icon positioning fixes */
.modern-select .ant-select-selection-item {
  text-align: left !important;
  justify-content: flex-start !important;
}

.ant-select-dropdown .ant-select-item-option {
  text-align: left !important;
  justify-content: flex-start !important;
}

/* Fix for any icon misalignment */
.modern-select .ant-select-selection-item > *,
.ant-select-dropdown .ant-select-item-option > * {
  vertical-align: middle !important;
}

/* Force remove any unwanted icons or styling */
.modern-select .ant-select-selection-item {
  position: relative !important;
}

.modern-select .ant-select-selection-item::before,
.modern-select .ant-select-selection-item::after,
.modern-select .ant-select-selection-item > *::before,
.modern-select .ant-select-selection-item > *::after {
  display: none !important;
  content: none !important;
  background: none !important;
  border: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* Ensure only our text content shows */
.modern-select .ant-select-selection-item {
  overflow: visible !important;
  white-space: nowrap !important;
  text-overflow: clip !important;
}

/* Remove any browser default styling */
.modern-select select,
.modern-select option {
  background-image: none !important;
  list-style-image: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* Fix for product selection icons */
.modern-select .ant-select-selection-search {
  left: 8px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-select .ant-select-selector,
  .modern-input .ant-input-number {
    min-height: 44px !important;
  }

  .ant-select-dropdown {
    border-radius: 12px !important;
  }

  .ant-select-dropdown .ant-select-item-option {
    min-height: 48px !important;
    padding: 10px !important;
  }
}
