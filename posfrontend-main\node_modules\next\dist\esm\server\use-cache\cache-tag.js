import { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external';
import { validateTags } from '../lib/patch-fetch';
export function cacheTag(...tags) {
    if (!process.env.__NEXT_DYNAMIC_IO) {
        throw new Error('cacheTag() is only available with the experimental.dynamicIO config.');
    }
    const workUnitStore = workUnitAsyncStorage.getStore();
    if (!workUnitStore || workUnitStore.type !== 'cache') {
        throw new Error('cacheTag() can only be called inside a "use cache" function.');
    }
    const validTags = validateTags(tags, 'cacheTag()');
    if (!workUnitStore.tags) {
        workUnitStore.tags = validTags;
    } else {
        workUnitStore.tags.push(...validTags);
    }
}

//# sourceMappingURL=cache-tag.js.map