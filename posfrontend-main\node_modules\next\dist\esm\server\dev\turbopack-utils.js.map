{"version": 3, "sources": ["../../../src/server/dev/turbopack-utils.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../config-shared'\nimport loadJsConfig from '../../build/load-jsconfig'\nimport type {\n  ServerFields,\n  SetupOpts,\n} from '../lib/router-utils/setup-dev-bundler'\nimport type {\n  Issue,\n  StyledString,\n  TurbopackResult,\n  Endpoint,\n  Entrypoints as RawEntrypoints,\n  Update as TurbopackUpdate,\n  WrittenEndpoint,\n} from '../../build/swc/types'\nimport {\n  decodeMagicIdentifier,\n  MAGIC_IDENTIFIER_REGEX,\n} from '../../shared/lib/magic-identifier'\nimport { bold, green, magenta, red } from '../../lib/picocolors'\nimport {\n  type HMR_ACTION_TYPES,\n  HMR_ACTIONS_SENT_TO_BROWSER,\n} from './hot-reloader-types'\nimport * as Log from '../../build/output/log'\nimport type { PropagateToWorkersField } from '../lib/router-utils/types'\nimport type { TurbopackManifestLoader } from './turbopack/manifest-loader'\nimport type { AppRoute, Entrypoints, PageRoute } from './turbopack/types'\nimport {\n  type EntryKey,\n  getEntryKey,\n  splitEntryKey,\n} from './turbopack/entry-key'\nimport type ws from 'next/dist/compiled/ws'\nimport isInternal from '../../shared/lib/is-internal'\nimport { isMetadataRoute } from '../../lib/metadata/is-metadata-route'\nimport type { CustomRoutes } from '../../lib/load-custom-routes'\n\nexport async function getTurbopackJsConfig(\n  dir: string,\n  nextConfig: NextConfigComplete\n) {\n  const { jsConfig } = await loadJsConfig(dir, nextConfig)\n  return jsConfig ?? { compilerOptions: {} }\n}\n\n// An error generated from emitted Turbopack issues. This can include build\n// errors caused by issues with user code.\nexport class ModuleBuildError extends Error {\n  name = 'ModuleBuildError'\n}\n\n// An error caused by an internal issue in Turbopack. These should be written\n// to a log file and details should not be shown to the user.\nexport class TurbopackInternalError extends Error {\n  name = 'TurbopackInternalError'\n\n  constructor(cause: Error) {\n    super(cause.message)\n    this.stack = cause.stack\n  }\n}\n\n/**\n * Thin stopgap workaround layer to mimic existing wellknown-errors-plugin in webpack's build\n * to emit certain type of errors into cli.\n */\nexport function isWellKnownError(issue: Issue): boolean {\n  const { title } = issue\n  const formattedTitle = renderStyledStringToErrorAnsi(title)\n  // TODO: add more well known errors\n  if (\n    formattedTitle.includes('Module not found') ||\n    formattedTitle.includes('Unknown module type')\n  ) {\n    return true\n  }\n\n  return false\n}\n\nconst onceErrorSet = new Set()\n/**\n * Check if given issue is a warning to be display only once.\n * This mimics behavior of get-page-static-info's warnOnce.\n * @param issue\n * @returns\n */\nfunction shouldEmitOnceWarning(issue: Issue): boolean {\n  const { severity, title, stage } = issue\n  if (severity === 'warning' && title.value === 'Invalid page configuration') {\n    if (onceErrorSet.has(issue)) {\n      return false\n    }\n    onceErrorSet.add(issue)\n  }\n  if (\n    severity === 'warning' &&\n    stage === 'config' &&\n    renderStyledStringToErrorAnsi(issue.title).includes(\"can't be external\")\n  ) {\n    if (onceErrorSet.has(issue)) {\n      return false\n    }\n    onceErrorSet.add(issue)\n  }\n\n  return true\n}\n\n/// Print out an issue to the console which should not block\n/// the build by throwing out or blocking error overlay.\nexport function printNonFatalIssue(issue: Issue) {\n  if (isRelevantWarning(issue) && shouldEmitOnceWarning(issue)) {\n    Log.warn(formatIssue(issue))\n  }\n}\n\nfunction isNodeModulesIssue(issue: Issue): boolean {\n  if (issue.severity === 'warning' && issue.stage === 'config') {\n    // Override for the externalize issue\n    // `Package foo (serverExternalPackages or default list) can't be external`\n    if (\n      renderStyledStringToErrorAnsi(issue.title).includes(\"can't be external\")\n    ) {\n      return false\n    }\n  }\n\n  return (\n    issue.severity === 'warning' &&\n    (issue.filePath.match(/^(?:.*[\\\\/])?node_modules(?:[\\\\/].*)?$/) !== null ||\n      // Ignore Next.js itself when running next directly in the monorepo where it is not inside\n      // node_modules anyway.\n      // TODO(mischnic) prevent matches when this is published to npm\n      issue.filePath.startsWith('[project]/packages/next/'))\n  )\n}\n\nexport function isRelevantWarning(issue: Issue): boolean {\n  return issue.severity === 'warning' && !isNodeModulesIssue(issue)\n}\n\nexport function formatIssue(issue: Issue) {\n  const { filePath, title, description, source } = issue\n  let { documentationLink } = issue\n  let formattedTitle = renderStyledStringToErrorAnsi(title).replace(\n    /\\n/g,\n    '\\n    '\n  )\n\n  // TODO: Use error codes to identify these\n  // TODO: Generalize adapting Turbopack errors to Next.js errors\n  if (formattedTitle.includes('Module not found')) {\n    // For compatiblity with webpack\n    // TODO: include columns in webpack errors.\n    documentationLink = 'https://nextjs.org/docs/messages/module-not-found'\n  }\n\n  let formattedFilePath = filePath\n    .replace('[project]/', './')\n    .replaceAll('/./', '/')\n    .replace('\\\\\\\\?\\\\', '')\n\n  let message = ''\n\n  if (source && source.range) {\n    const { start } = source.range\n    message = `${formattedFilePath}:${start.line + 1}:${\n      start.column + 1\n    }\\n${formattedTitle}`\n  } else if (formattedFilePath) {\n    message = `${formattedFilePath}\\n${formattedTitle}`\n  } else {\n    message = formattedTitle\n  }\n  message += '\\n'\n\n  if (\n    source?.range &&\n    source.source.content &&\n    // ignore Next.js/React internals, as these can often be huge bundled files.\n    !isInternal(filePath)\n  ) {\n    const { start, end } = source.range\n    const { codeFrameColumns } = require('next/dist/compiled/babel/code-frame')\n\n    message +=\n      codeFrameColumns(\n        source.source.content,\n        {\n          start: {\n            line: start.line + 1,\n            column: start.column + 1,\n          },\n          end: {\n            line: end.line + 1,\n            column: end.column + 1,\n          },\n        },\n        { forceColor: true }\n      ).trim() + '\\n\\n'\n  }\n\n  if (description) {\n    message += renderStyledStringToErrorAnsi(description) + '\\n\\n'\n  }\n\n  // TODO: make it possible to enable this for debugging, but not in tests.\n  // if (detail) {\n  //   message += renderStyledStringToErrorAnsi(detail) + '\\n\\n'\n  // }\n\n  // TODO: Include a trace from the issue.\n\n  if (documentationLink) {\n    message += documentationLink + '\\n\\n'\n  }\n\n  return message\n}\n\ntype IssueKey = `${Issue['severity']}-${Issue['filePath']}-${string}-${string}`\nexport type IssuesMap = Map<IssueKey, Issue>\nexport type EntryIssuesMap = Map<EntryKey, IssuesMap>\nexport type TopLevelIssuesMap = IssuesMap\n\nfunction getIssueKey(issue: Issue): IssueKey {\n  return `${issue.severity}-${issue.filePath}-${JSON.stringify(\n    issue.title\n  )}-${JSON.stringify(issue.description)}`\n}\n\nexport function processTopLevelIssues(\n  currentTopLevelIssues: TopLevelIssuesMap,\n  result: TurbopackResult\n) {\n  currentTopLevelIssues.clear()\n\n  for (const issue of result.issues) {\n    const issueKey = getIssueKey(issue)\n    currentTopLevelIssues.set(issueKey, issue)\n  }\n}\n\nexport function processIssues(\n  currentEntryIssues: EntryIssuesMap,\n  key: EntryKey,\n  result: TurbopackResult,\n  throwIssue: boolean,\n  logErrors: boolean\n) {\n  const newIssues = new Map<IssueKey, Issue>()\n  currentEntryIssues.set(key, newIssues)\n\n  const relevantIssues = new Set()\n\n  for (const issue of result.issues) {\n    if (\n      issue.severity !== 'error' &&\n      issue.severity !== 'fatal' &&\n      issue.severity !== 'warning'\n    )\n      continue\n\n    const issueKey = getIssueKey(issue)\n    newIssues.set(issueKey, issue)\n\n    if (issue.severity !== 'warning') {\n      if (throwIssue) {\n        const formatted = formatIssue(issue)\n        relevantIssues.add(formatted)\n      }\n      // if we throw the issue it will most likely get handed and logged elsewhere\n      else if (logErrors && isWellKnownError(issue)) {\n        const formatted = formatIssue(issue)\n        Log.error(formatted)\n      }\n    }\n  }\n\n  if (relevantIssues.size && throwIssue) {\n    throw new ModuleBuildError([...relevantIssues].join('\\n\\n'))\n  }\n}\n\nexport function renderStyledStringToErrorAnsi(string: StyledString): string {\n  function decodeMagicIdentifiers(str: string): string {\n    return str.replaceAll(MAGIC_IDENTIFIER_REGEX, (ident) => {\n      try {\n        return magenta(`{${decodeMagicIdentifier(ident)}}`)\n      } catch (e) {\n        return magenta(`{${ident} (decoding failed: ${e})}`)\n      }\n    })\n  }\n\n  switch (string.type) {\n    case 'text':\n      return decodeMagicIdentifiers(string.value)\n    case 'strong':\n      return bold(red(decodeMagicIdentifiers(string.value)))\n    case 'code':\n      return green(decodeMagicIdentifiers(string.value))\n    case 'line':\n      return string.value.map(renderStyledStringToErrorAnsi).join('')\n    case 'stack':\n      return string.value.map(renderStyledStringToErrorAnsi).join('\\n')\n    default:\n      throw new Error('Unknown StyledString type', string)\n  }\n}\n\nconst MILLISECONDS_IN_NANOSECOND = BigInt(1_000_000)\n\nexport function msToNs(ms: number): bigint {\n  return BigInt(Math.floor(ms)) * MILLISECONDS_IN_NANOSECOND\n}\n\nexport type ChangeSubscriptions = Map<\n  EntryKey,\n  Promise<AsyncIterableIterator<TurbopackResult>>\n>\n\nexport type HandleWrittenEndpoint = (\n  key: EntryKey,\n  result: TurbopackResult<WrittenEndpoint>\n) => void\n\nexport type StartChangeSubscription = (\n  key: EntryKey,\n  includeIssues: boolean,\n  endpoint: Endpoint,\n  makePayload: (\n    change: TurbopackResult\n  ) => Promise<HMR_ACTION_TYPES> | HMR_ACTION_TYPES | void,\n  onError?: (e: Error) => Promise<HMR_ACTION_TYPES> | HMR_ACTION_TYPES | void\n) => Promise<void>\n\nexport type StopChangeSubscription = (key: EntryKey) => Promise<void>\n\nexport type SendHmr = (id: string, payload: HMR_ACTION_TYPES) => void\n\nexport type StartBuilding = (\n  id: string,\n  requestUrl: string | undefined,\n  forceRebuild: boolean\n) => () => void\n\nexport type ReadyIds = Set<string>\n\nexport type ClientState = {\n  clientIssues: EntryIssuesMap\n  hmrPayloads: Map<string, HMR_ACTION_TYPES>\n  turbopackUpdates: TurbopackUpdate[]\n  subscriptions: Map<string, AsyncIterator<any>>\n}\n\nexport type ClientStateMap = WeakMap<ws, ClientState>\n\n// hooks only used by the dev server.\ntype HandleRouteTypeHooks = {\n  handleWrittenEndpoint: HandleWrittenEndpoint\n  subscribeToChanges: StartChangeSubscription\n}\n\nexport async function handleRouteType({\n  dev,\n  page,\n  pathname,\n  route,\n  currentEntryIssues,\n  entrypoints,\n  manifestLoader,\n  readyIds,\n  devRewrites,\n  productionRewrites,\n  hooks,\n  logErrors,\n}: {\n  dev: boolean\n  page: string\n  pathname: string\n  route: PageRoute | AppRoute\n\n  currentEntryIssues: EntryIssuesMap\n  entrypoints: Entrypoints\n  manifestLoader: TurbopackManifestLoader\n  devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n\n  readyIds?: ReadyIds // dev\n\n  hooks?: HandleRouteTypeHooks // dev\n}) {\n  const shouldCreateWebpackStats = process.env.TURBOPACK_STATS != null\n\n  switch (route.type) {\n    case 'page': {\n      const clientKey = getEntryKey('pages', 'client', page)\n      const serverKey = getEntryKey('pages', 'server', page)\n\n      try {\n        if (entrypoints.global.app) {\n          const key = getEntryKey('pages', 'server', '_app')\n\n          const writtenEndpoint = await entrypoints.global.app.writeToDisk()\n          hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n          processIssues(\n            currentEntryIssues,\n            key,\n            writtenEndpoint,\n            false,\n            logErrors\n          )\n        }\n        await manifestLoader.loadBuildManifest('_app')\n        await manifestLoader.loadPagesManifest('_app')\n\n        if (entrypoints.global.document) {\n          const key = getEntryKey('pages', 'server', '_document')\n\n          const writtenEndpoint =\n            await entrypoints.global.document.writeToDisk()\n          hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n          processIssues(\n            currentEntryIssues,\n            key,\n            writtenEndpoint,\n            false,\n            logErrors\n          )\n        }\n        await manifestLoader.loadPagesManifest('_document')\n\n        const writtenEndpoint = await route.htmlEndpoint.writeToDisk()\n        hooks?.handleWrittenEndpoint(serverKey, writtenEndpoint)\n\n        const type = writtenEndpoint?.type\n\n        await manifestLoader.loadBuildManifest(page)\n        await manifestLoader.loadPagesManifest(page)\n        if (type === 'edge') {\n          await manifestLoader.loadMiddlewareManifest(page, 'pages')\n        } else {\n          manifestLoader.deleteMiddlewareManifest(serverKey)\n        }\n        await manifestLoader.loadFontManifest('/_app', 'pages')\n        await manifestLoader.loadFontManifest(page, 'pages')\n        await manifestLoader.loadLoadableManifest(page, 'pages')\n\n        if (shouldCreateWebpackStats) {\n          await manifestLoader.loadWebpackStats(page, 'pages')\n        }\n\n        await manifestLoader.writeManifests({\n          devRewrites,\n          productionRewrites,\n          entrypoints,\n        })\n\n        processIssues(\n          currentEntryIssues,\n          serverKey,\n          writtenEndpoint,\n          false,\n          logErrors\n        )\n      } finally {\n        if (dev) {\n          // TODO subscriptions should only be caused by the WebSocket connections\n          // otherwise we don't known when to unsubscribe and this leaking\n          hooks?.subscribeToChanges(\n            serverKey,\n            false,\n            route.dataEndpoint,\n            () => {\n              // Report the next compilation again\n              readyIds?.delete(pathname)\n              return {\n                event: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ONLY_CHANGES,\n                pages: [page],\n              }\n            },\n            (e) => {\n              return {\n                action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n                data: `error in ${page} data subscription: ${e}`,\n              }\n            }\n          )\n          hooks?.subscribeToChanges(\n            clientKey,\n            false,\n            route.htmlEndpoint,\n            () => {\n              return {\n                event: HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES,\n              }\n            },\n            (e) => {\n              return {\n                action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n                data: `error in ${page} html subscription: ${e}`,\n              }\n            }\n          )\n          if (entrypoints.global.document) {\n            hooks?.subscribeToChanges(\n              getEntryKey('pages', 'server', '_document'),\n              false,\n              entrypoints.global.document,\n              () => {\n                return {\n                  action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n                  data: '_document has changed (page route)',\n                }\n              },\n              (e) => {\n                return {\n                  action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n                  data: `error in _document subscription (page route): ${e}`,\n                }\n              }\n            )\n          }\n        }\n      }\n\n      break\n    }\n    case 'page-api': {\n      const key = getEntryKey('pages', 'server', page)\n\n      const writtenEndpoint = await route.endpoint.writeToDisk()\n      hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n\n      const type = writtenEndpoint.type\n\n      await manifestLoader.loadPagesManifest(page)\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'pages')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n      await manifestLoader.loadLoadableManifest(page, 'pages')\n\n      await manifestLoader.writeManifests({\n        devRewrites,\n        productionRewrites,\n        entrypoints,\n      })\n\n      processIssues(currentEntryIssues, key, writtenEndpoint, true, logErrors)\n\n      break\n    }\n    case 'app-page': {\n      const key = getEntryKey('app', 'server', page)\n\n      const writtenEndpoint = await route.htmlEndpoint.writeToDisk()\n      hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n\n      if (dev) {\n        // TODO subscriptions should only be caused by the WebSocket connections\n        // otherwise we don't known when to unsubscribe and this leaking\n        hooks?.subscribeToChanges(\n          key,\n          true,\n          route.rscEndpoint,\n          (change) => {\n            if (change.issues.some((issue) => issue.severity === 'error')) {\n              // Ignore any updates that has errors\n              // There will be another update without errors eventually\n              return\n            }\n            // Report the next compilation again\n            readyIds?.delete(pathname)\n            return {\n              action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES,\n            }\n          },\n          () => {\n            return {\n              action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES,\n            }\n          }\n        )\n      }\n\n      const type = writtenEndpoint.type\n\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.loadAppBuildManifest(page)\n      await manifestLoader.loadBuildManifest(page, 'app')\n      await manifestLoader.loadAppPathsManifest(page)\n      await manifestLoader.loadActionManifest(page)\n      await manifestLoader.loadLoadableManifest(page, 'app')\n      await manifestLoader.loadFontManifest(page, 'app')\n\n      if (shouldCreateWebpackStats) {\n        await manifestLoader.loadWebpackStats(page, 'app')\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites,\n        productionRewrites,\n        entrypoints,\n      })\n\n      processIssues(currentEntryIssues, key, writtenEndpoint, dev, logErrors)\n\n      break\n    }\n    case 'app-route': {\n      const key = getEntryKey('app', 'server', page)\n\n      const writtenEndpoint = await route.endpoint.writeToDisk()\n      hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n\n      const type = writtenEndpoint.type\n\n      await manifestLoader.loadAppPathsManifest(page)\n\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites,\n        productionRewrites,\n        entrypoints,\n      })\n      processIssues(currentEntryIssues, key, writtenEndpoint, true, logErrors)\n\n      break\n    }\n    default: {\n      throw new Error(`unknown route type ${(route as any).type} for ${page}`)\n    }\n  }\n}\n\n/**\n * Maintains a mapping between entrypoins and the corresponding client asset paths.\n */\nexport class AssetMapper {\n  private entryMap: Map<EntryKey, Set<string>> = new Map()\n  private assetMap: Map<string, Set<EntryKey>> = new Map()\n\n  /**\n   * Overrides asset paths for a key and updates the mapping from path to key.\n   *\n   * @param key\n   * @param assetPaths asset paths relative to the .next directory\n   */\n  setPathsForKey(key: EntryKey, assetPaths: string[]): void {\n    this.delete(key)\n\n    const newAssetPaths = new Set(assetPaths)\n    this.entryMap.set(key, newAssetPaths)\n\n    for (const assetPath of newAssetPaths) {\n      let assetPathKeys = this.assetMap.get(assetPath)\n      if (!assetPathKeys) {\n        assetPathKeys = new Set()\n        this.assetMap.set(assetPath, assetPathKeys)\n      }\n\n      assetPathKeys!.add(key)\n    }\n  }\n\n  /**\n   * Deletes the key and any asset only referenced by this key.\n   *\n   * @param key\n   */\n  delete(key: EntryKey) {\n    for (const assetPath of this.getAssetPathsByKey(key)) {\n      const assetPathKeys = this.assetMap.get(assetPath)\n\n      assetPathKeys?.delete(key)\n\n      if (!assetPathKeys?.size) {\n        this.assetMap.delete(assetPath)\n      }\n    }\n\n    this.entryMap.delete(key)\n  }\n\n  getAssetPathsByKey(key: EntryKey): string[] {\n    return Array.from(this.entryMap.get(key) ?? [])\n  }\n\n  getKeysByAsset(path: string): EntryKey[] {\n    return Array.from(this.assetMap.get(path) ?? [])\n  }\n\n  keys(): IterableIterator<EntryKey> {\n    return this.entryMap.keys()\n  }\n}\n\nexport function hasEntrypointForKey(\n  entrypoints: Entrypoints,\n  key: EntryKey,\n  assetMapper: AssetMapper | undefined\n): boolean {\n  const { type, page } = splitEntryKey(key)\n\n  switch (type) {\n    case 'app':\n      return entrypoints.app.has(page)\n    case 'pages':\n      switch (page) {\n        case '_app':\n          return entrypoints.global.app != null\n        case '_document':\n          return entrypoints.global.document != null\n        case '_error':\n          return entrypoints.global.error != null\n        default:\n          return entrypoints.page.has(page)\n      }\n    case 'root':\n      switch (page) {\n        case 'middleware':\n          return entrypoints.global.middleware != null\n        case 'instrumentation':\n          return entrypoints.global.instrumentation != null\n        default:\n          return false\n      }\n    case 'assets':\n      if (!assetMapper) {\n        return false\n      }\n\n      return assetMapper\n        .getKeysByAsset(page)\n        .some((pageKey) =>\n          hasEntrypointForKey(entrypoints, pageKey, assetMapper)\n        )\n    default: {\n      // validation that we covered all cases, this should never run.\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      const _: never = type\n      return false\n    }\n  }\n}\n\n// hooks only used by the dev server.\ntype HandleEntrypointsHooks = {\n  handleWrittenEndpoint: HandleWrittenEndpoint\n  propagateServerField: (\n    field: PropagateToWorkersField,\n    args: any\n  ) => Promise<void>\n  sendHmr: SendHmr\n  startBuilding: StartBuilding\n  subscribeToChanges: StartChangeSubscription\n  unsubscribeFromChanges: StopChangeSubscription\n  unsubscribeFromHmrEvents: (client: ws, id: string) => void\n}\n\ntype HandleEntrypointsDevOpts = {\n  assetMapper: AssetMapper\n  changeSubscriptions: ChangeSubscriptions\n  clients: Set<ws>\n  clientStates: ClientStateMap\n  serverFields: ServerFields\n\n  hooks: HandleEntrypointsHooks\n}\n\nexport async function handleEntrypoints({\n  entrypoints,\n\n  currentEntrypoints,\n\n  currentEntryIssues,\n  manifestLoader,\n  devRewrites,\n  productionRewrites,\n  logErrors,\n  dev,\n}: {\n  entrypoints: TurbopackResult<RawEntrypoints>\n\n  currentEntrypoints: Entrypoints\n\n  currentEntryIssues: EntryIssuesMap\n  manifestLoader: TurbopackManifestLoader\n  devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n\n  dev?: HandleEntrypointsDevOpts\n}) {\n  currentEntrypoints.global.app = entrypoints.pagesAppEndpoint\n  currentEntrypoints.global.document = entrypoints.pagesDocumentEndpoint\n  currentEntrypoints.global.error = entrypoints.pagesErrorEndpoint\n\n  currentEntrypoints.global.instrumentation = entrypoints.instrumentation\n\n  currentEntrypoints.page.clear()\n  currentEntrypoints.app.clear()\n\n  for (const [pathname, route] of entrypoints.routes) {\n    switch (route.type) {\n      case 'page':\n      case 'page-api':\n        currentEntrypoints.page.set(pathname, route)\n        break\n      case 'app-page': {\n        route.pages.forEach((page) => {\n          currentEntrypoints.app.set(page.originalName, {\n            type: 'app-page',\n            ...page,\n          })\n        })\n        break\n      }\n      case 'app-route': {\n        currentEntrypoints.app.set(route.originalName, route)\n        break\n      }\n      default:\n        Log.info(`skipping ${pathname} (${route.type})`)\n        break\n    }\n  }\n\n  if (dev) {\n    await handleEntrypointsDevCleanup({\n      currentEntryIssues,\n      currentEntrypoints,\n\n      ...dev,\n    })\n  }\n\n  const { middleware, instrumentation } = entrypoints\n\n  // We check for explicit true/false, since it's initialized to\n  // undefined during the first loop (middlewareChanges event is\n  // unnecessary during the first serve)\n  if (currentEntrypoints.global.middleware && !middleware) {\n    const key = getEntryKey('root', 'server', 'middleware')\n    // Went from middleware to no middleware\n    await dev?.hooks.unsubscribeFromChanges(key)\n    currentEntryIssues.delete(key)\n    dev?.hooks.sendHmr('middleware', {\n      event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES,\n    })\n  } else if (!currentEntrypoints.global.middleware && middleware) {\n    // Went from no middleware to middleware\n    dev?.hooks.sendHmr('middleware', {\n      event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES,\n    })\n  }\n\n  currentEntrypoints.global.middleware = middleware\n\n  if (instrumentation) {\n    const processInstrumentation = async (\n      name: string,\n      prop: 'nodeJs' | 'edge'\n    ) => {\n      const key = getEntryKey('root', 'server', name)\n\n      const writtenEndpoint = await instrumentation[prop].writeToDisk()\n      dev?.hooks.handleWrittenEndpoint(key, writtenEndpoint)\n      processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n    }\n    await processInstrumentation('instrumentation.nodeJs', 'nodeJs')\n    await processInstrumentation('instrumentation.edge', 'edge')\n    await manifestLoader.loadMiddlewareManifest(\n      'instrumentation',\n      'instrumentation'\n    )\n    await manifestLoader.writeManifests({\n      devRewrites,\n      productionRewrites,\n      entrypoints: currentEntrypoints,\n    })\n\n    if (dev) {\n      dev.serverFields.actualInstrumentationHookFile = '/instrumentation'\n      await dev.hooks.propagateServerField(\n        'actualInstrumentationHookFile',\n        dev.serverFields.actualInstrumentationHookFile\n      )\n    }\n  } else {\n    if (dev) {\n      dev.serverFields.actualInstrumentationHookFile = undefined\n      await dev.hooks.propagateServerField(\n        'actualInstrumentationHookFile',\n        dev.serverFields.actualInstrumentationHookFile\n      )\n    }\n  }\n\n  if (middleware) {\n    const key = getEntryKey('root', 'server', 'middleware')\n\n    const endpoint = middleware.endpoint\n\n    async function processMiddleware() {\n      const writtenEndpoint = await endpoint.writeToDisk()\n      dev?.hooks.handleWrittenEndpoint(key, writtenEndpoint)\n      processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n      await manifestLoader.loadMiddlewareManifest('middleware', 'middleware')\n      if (dev) {\n        dev.serverFields.middleware = {\n          match: null as any,\n          page: '/',\n          matchers:\n            manifestLoader.getMiddlewareManifest(key)?.middleware['/'].matchers,\n        }\n      }\n    }\n    await processMiddleware()\n\n    if (dev) {\n      dev?.hooks.subscribeToChanges(\n        key,\n        false,\n        endpoint,\n        async () => {\n          const finishBuilding = dev.hooks.startBuilding(\n            'middleware',\n            undefined,\n            true\n          )\n          await processMiddleware()\n          await dev.hooks.propagateServerField(\n            'actualMiddlewareFile',\n            dev.serverFields.actualMiddlewareFile\n          )\n          await dev.hooks.propagateServerField(\n            'middleware',\n            dev.serverFields.middleware\n          )\n          await manifestLoader.writeManifests({\n            devRewrites,\n            productionRewrites,\n            entrypoints: currentEntrypoints,\n          })\n\n          finishBuilding?.()\n          return { event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES }\n        },\n        () => {\n          return {\n            event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES,\n          }\n        }\n      )\n    }\n  } else {\n    manifestLoader.deleteMiddlewareManifest(\n      getEntryKey('root', 'server', 'middleware')\n    )\n    if (dev) {\n      dev.serverFields.actualMiddlewareFile = undefined\n      dev.serverFields.middleware = undefined\n    }\n  }\n\n  if (dev) {\n    await dev.hooks.propagateServerField(\n      'actualMiddlewareFile',\n      dev.serverFields.actualMiddlewareFile\n    )\n    await dev.hooks.propagateServerField(\n      'middleware',\n      dev.serverFields.middleware\n    )\n  }\n}\n\nasync function handleEntrypointsDevCleanup({\n  currentEntryIssues,\n  currentEntrypoints,\n\n  assetMapper,\n  changeSubscriptions,\n  clients,\n  clientStates,\n\n  hooks,\n}: {\n  currentEntrypoints: Entrypoints\n  currentEntryIssues: EntryIssuesMap\n} & HandleEntrypointsDevOpts) {\n  // this needs to be first as `hasEntrypointForKey` uses the `assetMapper`\n  for (const key of assetMapper.keys()) {\n    if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n      assetMapper.delete(key)\n    }\n  }\n\n  for (const key of changeSubscriptions.keys()) {\n    // middleware is handled separately\n    if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n      await hooks.unsubscribeFromChanges(key)\n    }\n  }\n\n  for (const [key] of currentEntryIssues) {\n    if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n      currentEntryIssues.delete(key)\n    }\n  }\n\n  for (const client of clients) {\n    const state = clientStates.get(client)\n    if (!state) {\n      continue\n    }\n\n    for (const key of state.clientIssues.keys()) {\n      if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n        state.clientIssues.delete(key)\n      }\n    }\n\n    for (const id of state.subscriptions.keys()) {\n      if (\n        !hasEntrypointForKey(\n          currentEntrypoints,\n          getEntryKey('assets', 'client', id),\n          assetMapper\n        )\n      ) {\n        hooks.unsubscribeFromHmrEvents(client, id)\n      }\n    }\n  }\n}\n\nexport async function handlePagesErrorRoute({\n  dev,\n  currentEntryIssues,\n  entrypoints,\n  manifestLoader,\n  devRewrites,\n  productionRewrites,\n  logErrors,\n\n  hooks,\n}: {\n  dev: boolean\n  currentEntryIssues: EntryIssuesMap\n  entrypoints: Entrypoints\n  manifestLoader: TurbopackManifestLoader\n  devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n\n  hooks?: HandleRouteTypeHooks // dev\n}) {\n  if (entrypoints.global.app) {\n    const key = getEntryKey('pages', 'server', '_app')\n\n    const writtenEndpoint = await entrypoints.global.app.writeToDisk()\n    hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n    if (dev) {\n      hooks?.subscribeToChanges(\n        key,\n        false,\n        entrypoints.global.app,\n        () => {\n          // There's a special case for this in `../client/page-bootstrap.ts`.\n          // https://github.com/vercel/next.js/blob/08d7a7e5189a835f5dcb82af026174e587575c0e/packages/next/src/client/page-bootstrap.ts#L69-L71\n          return { event: HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES }\n        },\n        () => {\n          return {\n            action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n            data: '_app has changed (error route)',\n          }\n        }\n      )\n    }\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n  await manifestLoader.loadBuildManifest('_app')\n  await manifestLoader.loadPagesManifest('_app')\n  await manifestLoader.loadFontManifest('_app')\n\n  if (entrypoints.global.document) {\n    const key = getEntryKey('pages', 'server', '_document')\n\n    const writtenEndpoint = await entrypoints.global.document.writeToDisk()\n    hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n    if (dev) {\n      hooks?.subscribeToChanges(\n        key,\n        false,\n        entrypoints.global.document,\n        () => {\n          return {\n            action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n            data: '_document has changed (error route)',\n          }\n        },\n        (e) => {\n          return {\n            action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n            data: `error in _document subscription (error route): ${e}`,\n          }\n        }\n      )\n    }\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n  await manifestLoader.loadPagesManifest('_document')\n\n  if (entrypoints.global.error) {\n    const key = getEntryKey('pages', 'server', '_error')\n\n    const writtenEndpoint = await entrypoints.global.error.writeToDisk()\n    hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n    if (dev) {\n      hooks?.subscribeToChanges(\n        key,\n        false,\n        entrypoints.global.error,\n        () => {\n          // There's a special case for this in `../client/page-bootstrap.ts`.\n          // https://github.com/vercel/next.js/blob/08d7a7e5189a835f5dcb82af026174e587575c0e/packages/next/src/client/page-bootstrap.ts#L69-L71\n          return { event: HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES }\n        },\n        (e) => {\n          return {\n            action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n            data: `error in _error subscription: ${e}`,\n          }\n        }\n      )\n    }\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n  await manifestLoader.loadBuildManifest('_error')\n  await manifestLoader.loadPagesManifest('_error')\n  await manifestLoader.loadFontManifest('_error')\n\n  await manifestLoader.writeManifests({\n    devRewrites,\n    productionRewrites,\n    entrypoints,\n  })\n}\n\nexport function removeRouteSuffix(route: string): string {\n  return route.replace(/\\/route$/, '')\n}\n\nexport function addRouteSuffix(route: string): string {\n  return route + '/route'\n}\n\nexport function addMetadataIdToRoute(route: string): string {\n  return route + '/[__metadata_id__]'\n}\n\n// Since turbopack will create app pages/route entries based on the structure,\n// which means the entry keys are based on file names.\n// But for special metadata conventions we'll change the page/pathname to a different path.\n// So we need this helper to map the new path back to original turbopack entry key.\nexport function normalizedPageToTurbopackStructureRoute(\n  route: string,\n  ext: string | false\n): string {\n  let entrypointKey = route\n  if (isMetadataRoute(entrypointKey)) {\n    entrypointKey = entrypointKey.endsWith('/route')\n      ? entrypointKey.slice(0, -'/route'.length)\n      : entrypointKey\n\n    if (ext) {\n      if (entrypointKey.endsWith('/[__metadata_id__]')) {\n        entrypointKey = entrypointKey.slice(0, -'/[__metadata_id__]'.length)\n      }\n      if (entrypointKey.endsWith('/sitemap.xml') && ext !== '.xml') {\n        // For dynamic sitemap route, remove the extension\n        entrypointKey = entrypointKey.slice(0, -'.xml'.length)\n      }\n    }\n    entrypointKey = entrypointKey + '/route'\n  }\n  return entrypointKey\n}\n\nexport function isPersistentCachingEnabled(\n  config: NextConfigComplete\n): boolean {\n  return config.experimental.turbo?.unstablePersistentCaching || false\n}\n"], "names": ["loadJsConfig", "decodeMagicIdentifier", "MAGIC_IDENTIFIER_REGEX", "bold", "green", "magenta", "red", "HMR_ACTIONS_SENT_TO_BROWSER", "Log", "getEntry<PERSON>ey", "splitEntryKey", "isInternal", "isMetadataRoute", "getTurbopackJsConfig", "dir", "nextConfig", "jsConfig", "compilerOptions", "ModuleBuildError", "Error", "name", "TurbopackInternalError", "constructor", "cause", "message", "stack", "isWellKnownError", "issue", "title", "formattedTitle", "renderStyledStringToErrorAnsi", "includes", "onceErrorSet", "Set", "shouldEmitOnceWarning", "severity", "stage", "value", "has", "add", "printNonFatalIssue", "isRelevantWarning", "warn", "formatIssue", "isNodeModulesIssue", "filePath", "match", "startsWith", "description", "source", "documentationLink", "replace", "formattedFilePath", "replaceAll", "range", "start", "line", "column", "content", "end", "codeFrameColumns", "require", "forceColor", "trim", "getIssueKey", "JSON", "stringify", "processTopLevelIssues", "currentTopLevelIssues", "result", "clear", "issues", "issue<PERSON><PERSON>", "set", "processIssues", "currentEntryIssues", "key", "throwIssue", "logErrors", "newIssues", "Map", "relevantIssues", "formatted", "error", "size", "join", "string", "decodeMagicIdentifiers", "str", "ident", "e", "type", "map", "MILLISECONDS_IN_NANOSECOND", "BigInt", "msToNs", "ms", "Math", "floor", "handleRouteType", "dev", "page", "pathname", "route", "entrypoints", "manifest<PERSON><PERSON>der", "readyIds", "devRewrites", "productionRewrites", "hooks", "shouldCreateWebpackStats", "process", "env", "TURBOPACK_STATS", "client<PERSON>ey", "server<PERSON>ey", "global", "app", "writtenEndpoint", "writeToDisk", "handleWrittenEndpoint", "loadBuildManifest", "loadPagesManifest", "document", "htmlEndpoint", "loadMiddlewareManifest", "deleteMiddlewareManifest", "loadFontManifest", "loadLoadableManifest", "loadWebpackStats", "writeManifests", "subscribeToChanges", "dataEndpoint", "delete", "event", "SERVER_ONLY_CHANGES", "pages", "action", "RELOAD_PAGE", "data", "CLIENT_CHANGES", "endpoint", "rscEndpoint", "change", "some", "SERVER_COMPONENT_CHANGES", "loadAppBuildManifest", "loadAppPathsManifest", "loadActionManifest", "AssetMapper", "setPathsFor<PERSON>ey", "assetPaths", "newAssetPaths", "entryMap", "assetPath", "assetPathKeys", "assetMap", "get", "getAssetPathsByKey", "Array", "from", "getKeysByAsset", "path", "keys", "hasEntrypointForKey", "assetMapper", "middleware", "instrumentation", "page<PERSON><PERSON>", "_", "handleEntrypoints", "currentEntrypoints", "pagesAppEndpoint", "pagesDocumentEndpoint", "pagesErrorEndpoint", "routes", "for<PERSON>ach", "originalName", "info", "handleEntrypointsDevCleanup", "unsubscribeFromChanges", "sendHmr", "MIDDLEWARE_CHANGES", "processInstrumentation", "prop", "serverFields", "actualInstrumentationHookFile", "propagateServerField", "undefined", "processMiddleware", "matchers", "getMiddlewareManifest", "finishBuilding", "startBuilding", "actualMiddlewareFile", "changeSubscriptions", "clients", "clientStates", "client", "state", "clientIssues", "id", "subscriptions", "unsubscribeFromHmrEvents", "handlePagesErrorRoute", "removeRouteSuffix", "addRouteSuffix", "addMetadataIdToRoute", "normalizedPageToTurbopackStructureRoute", "ext", "entrypoint<PERSON><PERSON>", "endsWith", "slice", "length", "isPersistentCachingEnabled", "config", "experimental", "turbo", "unstablePersistentCaching"], "mappings": "AACA,OAAOA,kBAAkB,4BAA2B;AAcpD,SACEC,qBAAqB,EACrBC,sBAAsB,QACjB,oCAAmC;AAC1C,SAASC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,GAAG,QAAQ,uBAAsB;AAChE,SAEEC,2BAA2B,QACtB,uBAAsB;AAC7B,YAAYC,SAAS,yBAAwB;AAI7C,SAEEC,WAAW,EACXC,aAAa,QACR,wBAAuB;AAE9B,OAAOC,gBAAgB,+BAA8B;AACrD,SAASC,eAAe,QAAQ,uCAAsC;AAGtE,OAAO,eAAeC,qBACpBC,GAAW,EACXC,UAA8B;IAE9B,MAAM,EAAEC,QAAQ,EAAE,GAAG,MAAMhB,aAAac,KAAKC;IAC7C,OAAOC,YAAY;QAAEC,iBAAiB,CAAC;IAAE;AAC3C;AAEA,2EAA2E;AAC3E,0CAA0C;AAC1C,OAAO,MAAMC,yBAAyBC;;QAA/B,qBACLC,OAAO;;AACT;AAEA,6EAA6E;AAC7E,6DAA6D;AAC7D,OAAO,MAAMC,+BAA+BF;IAG1CG,YAAYC,KAAY,CAAE;QACxB,KAAK,CAACA,MAAMC,OAAO,QAHrBJ,OAAO;QAIL,IAAI,CAACK,KAAK,GAAGF,MAAME,KAAK;IAC1B;AACF;AAEA;;;CAGC,GACD,OAAO,SAASC,iBAAiBC,KAAY;IAC3C,MAAM,EAAEC,KAAK,EAAE,GAAGD;IAClB,MAAME,iBAAiBC,8BAA8BF;IACrD,mCAAmC;IACnC,IACEC,eAAeE,QAAQ,CAAC,uBACxBF,eAAeE,QAAQ,CAAC,wBACxB;QACA,OAAO;IACT;IAEA,OAAO;AACT;AAEA,MAAMC,eAAe,IAAIC;AACzB;;;;;CAKC,GACD,SAASC,sBAAsBP,KAAY;IACzC,MAAM,EAAEQ,QAAQ,EAAEP,KAAK,EAAEQ,KAAK,EAAE,GAAGT;IACnC,IAAIQ,aAAa,aAAaP,MAAMS,KAAK,KAAK,8BAA8B;QAC1E,IAAIL,aAAaM,GAAG,CAACX,QAAQ;YAC3B,OAAO;QACT;QACAK,aAAaO,GAAG,CAACZ;IACnB;IACA,IACEQ,aAAa,aACbC,UAAU,YACVN,8BAA8BH,MAAMC,KAAK,EAAEG,QAAQ,CAAC,sBACpD;QACA,IAAIC,aAAaM,GAAG,CAACX,QAAQ;YAC3B,OAAO;QACT;QACAK,aAAaO,GAAG,CAACZ;IACnB;IAEA,OAAO;AACT;AAEA,4DAA4D;AAC5D,wDAAwD;AACxD,OAAO,SAASa,mBAAmBb,KAAY;IAC7C,IAAIc,kBAAkBd,UAAUO,sBAAsBP,QAAQ;QAC5DnB,IAAIkC,IAAI,CAACC,YAAYhB;IACvB;AACF;AAEA,SAASiB,mBAAmBjB,KAAY;IACtC,IAAIA,MAAMQ,QAAQ,KAAK,aAAaR,MAAMS,KAAK,KAAK,UAAU;QAC5D,qCAAqC;QACrC,2EAA2E;QAC3E,IACEN,8BAA8BH,MAAMC,KAAK,EAAEG,QAAQ,CAAC,sBACpD;YACA,OAAO;QACT;IACF;IAEA,OACEJ,MAAMQ,QAAQ,KAAK,aAClBR,CAAAA,MAAMkB,QAAQ,CAACC,KAAK,CAAC,8CAA8C,QAClE,0FAA0F;IAC1F,uBAAuB;IACvB,+DAA+D;IAC/DnB,MAAMkB,QAAQ,CAACE,UAAU,CAAC,2BAA0B;AAE1D;AAEA,OAAO,SAASN,kBAAkBd,KAAY;IAC5C,OAAOA,MAAMQ,QAAQ,KAAK,aAAa,CAACS,mBAAmBjB;AAC7D;AAEA,OAAO,SAASgB,YAAYhB,KAAY;IACtC,MAAM,EAAEkB,QAAQ,EAAEjB,KAAK,EAAEoB,WAAW,EAAEC,MAAM,EAAE,GAAGtB;IACjD,IAAI,EAAEuB,iBAAiB,EAAE,GAAGvB;IAC5B,IAAIE,iBAAiBC,8BAA8BF,OAAOuB,OAAO,CAC/D,OACA;IAGF,0CAA0C;IAC1C,+DAA+D;IAC/D,IAAItB,eAAeE,QAAQ,CAAC,qBAAqB;QAC/C,gCAAgC;QAChC,2CAA2C;QAC3CmB,oBAAoB;IACtB;IAEA,IAAIE,oBAAoBP,SACrBM,OAAO,CAAC,cAAc,MACtBE,UAAU,CAAC,OAAO,KAClBF,OAAO,CAAC,WAAW;IAEtB,IAAI3B,UAAU;IAEd,IAAIyB,UAAUA,OAAOK,KAAK,EAAE;QAC1B,MAAM,EAAEC,KAAK,EAAE,GAAGN,OAAOK,KAAK;QAC9B9B,UAAU,GAAG4B,kBAAkB,CAAC,EAAEG,MAAMC,IAAI,GAAG,EAAE,CAAC,EAChDD,MAAME,MAAM,GAAG,EAChB,EAAE,EAAE5B,gBAAgB;IACvB,OAAO,IAAIuB,mBAAmB;QAC5B5B,UAAU,GAAG4B,kBAAkB,EAAE,EAAEvB,gBAAgB;IACrD,OAAO;QACLL,UAAUK;IACZ;IACAL,WAAW;IAEX,IACEyB,CAAAA,0BAAAA,OAAQK,KAAK,KACbL,OAAOA,MAAM,CAACS,OAAO,IACrB,4EAA4E;IAC5E,CAAC/C,WAAWkC,WACZ;QACA,MAAM,EAAEU,KAAK,EAAEI,GAAG,EAAE,GAAGV,OAAOK,KAAK;QACnC,MAAM,EAAEM,gBAAgB,EAAE,GAAGC,QAAQ;QAErCrC,WACEoC,iBACEX,OAAOA,MAAM,CAACS,OAAO,EACrB;YACEH,OAAO;gBACLC,MAAMD,MAAMC,IAAI,GAAG;gBACnBC,QAAQF,MAAME,MAAM,GAAG;YACzB;YACAE,KAAK;gBACHH,MAAMG,IAAIH,IAAI,GAAG;gBACjBC,QAAQE,IAAIF,MAAM,GAAG;YACvB;QACF,GACA;YAAEK,YAAY;QAAK,GACnBC,IAAI,KAAK;IACf;IAEA,IAAIf,aAAa;QACfxB,WAAWM,8BAA8BkB,eAAe;IAC1D;IAEA,yEAAyE;IACzE,gBAAgB;IAChB,8DAA8D;IAC9D,IAAI;IAEJ,wCAAwC;IAExC,IAAIE,mBAAmB;QACrB1B,WAAW0B,oBAAoB;IACjC;IAEA,OAAO1B;AACT;AAOA,SAASwC,YAAYrC,KAAY;IAC/B,OAAO,GAAGA,MAAMQ,QAAQ,CAAC,CAAC,EAAER,MAAMkB,QAAQ,CAAC,CAAC,EAAEoB,KAAKC,SAAS,CAC1DvC,MAAMC,KAAK,EACX,CAAC,EAAEqC,KAAKC,SAAS,CAACvC,MAAMqB,WAAW,GAAG;AAC1C;AAEA,OAAO,SAASmB,sBACdC,qBAAwC,EACxCC,MAAuB;IAEvBD,sBAAsBE,KAAK;IAE3B,KAAK,MAAM3C,SAAS0C,OAAOE,MAAM,CAAE;QACjC,MAAMC,WAAWR,YAAYrC;QAC7ByC,sBAAsBK,GAAG,CAACD,UAAU7C;IACtC;AACF;AAEA,OAAO,SAAS+C,cACdC,kBAAkC,EAClCC,GAAa,EACbP,MAAuB,EACvBQ,UAAmB,EACnBC,SAAkB;IAElB,MAAMC,YAAY,IAAIC;IACtBL,mBAAmBF,GAAG,CAACG,KAAKG;IAE5B,MAAME,iBAAiB,IAAIhD;IAE3B,KAAK,MAAMN,SAAS0C,OAAOE,MAAM,CAAE;QACjC,IACE5C,MAAMQ,QAAQ,KAAK,WACnBR,MAAMQ,QAAQ,KAAK,WACnBR,MAAMQ,QAAQ,KAAK,WAEnB;QAEF,MAAMqC,WAAWR,YAAYrC;QAC7BoD,UAAUN,GAAG,CAACD,UAAU7C;QAExB,IAAIA,MAAMQ,QAAQ,KAAK,WAAW;YAChC,IAAI0C,YAAY;gBACd,MAAMK,YAAYvC,YAAYhB;gBAC9BsD,eAAe1C,GAAG,CAAC2C;YACrB,OAEK,IAAIJ,aAAapD,iBAAiBC,QAAQ;gBAC7C,MAAMuD,YAAYvC,YAAYhB;gBAC9BnB,IAAI2E,KAAK,CAACD;YACZ;QACF;IACF;IAEA,IAAID,eAAeG,IAAI,IAAIP,YAAY;QACrC,MAAM,IAAI3D,iBAAiB;eAAI+D;SAAe,CAACI,IAAI,CAAC;IACtD;AACF;AAEA,OAAO,SAASvD,8BAA8BwD,MAAoB;IAChE,SAASC,uBAAuBC,GAAW;QACzC,OAAOA,IAAInC,UAAU,CAACnD,wBAAwB,CAACuF;YAC7C,IAAI;gBACF,OAAOpF,QAAQ,CAAC,CAAC,EAAEJ,sBAAsBwF,OAAO,CAAC,CAAC;YACpD,EAAE,OAAOC,GAAG;gBACV,OAAOrF,QAAQ,CAAC,CAAC,EAAEoF,MAAM,mBAAmB,EAAEC,EAAE,EAAE,CAAC;YACrD;QACF;IACF;IAEA,OAAQJ,OAAOK,IAAI;QACjB,KAAK;YACH,OAAOJ,uBAAuBD,OAAOjD,KAAK;QAC5C,KAAK;YACH,OAAOlC,KAAKG,IAAIiF,uBAAuBD,OAAOjD,KAAK;QACrD,KAAK;YACH,OAAOjC,MAAMmF,uBAAuBD,OAAOjD,KAAK;QAClD,KAAK;YACH,OAAOiD,OAAOjD,KAAK,CAACuD,GAAG,CAAC9D,+BAA+BuD,IAAI,CAAC;QAC9D,KAAK;YACH,OAAOC,OAAOjD,KAAK,CAACuD,GAAG,CAAC9D,+BAA+BuD,IAAI,CAAC;QAC9D;YACE,MAAM,IAAIlE,MAAM,6BAA6BmE;IACjD;AACF;AAEA,MAAMO,6BAA6BC,OAAO;AAE1C,OAAO,SAASC,OAAOC,EAAU;IAC/B,OAAOF,OAAOG,KAAKC,KAAK,CAACF,OAAOH;AAClC;AAiDA,OAAO,eAAeM,gBAAgB,EACpCC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACL5B,kBAAkB,EAClB6B,WAAW,EACXC,cAAc,EACdC,QAAQ,EACRC,WAAW,EACXC,kBAAkB,EAClBC,KAAK,EACL/B,SAAS,EAiBV;IACC,MAAMgC,2BAA2BC,QAAQC,GAAG,CAACC,eAAe,IAAI;IAEhE,OAAQV,MAAMZ,IAAI;QAChB,KAAK;YAAQ;gBACX,MAAMuB,YAAYzG,YAAY,SAAS,UAAU4F;gBACjD,MAAMc,YAAY1G,YAAY,SAAS,UAAU4F;gBAEjD,IAAI;oBACF,IAAIG,YAAYY,MAAM,CAACC,GAAG,EAAE;wBAC1B,MAAMzC,MAAMnE,YAAY,SAAS,UAAU;wBAE3C,MAAM6G,kBAAkB,MAAMd,YAAYY,MAAM,CAACC,GAAG,CAACE,WAAW;wBAChEV,yBAAAA,MAAOW,qBAAqB,CAAC5C,KAAK0C;wBAClC5C,cACEC,oBACAC,KACA0C,iBACA,OACAxC;oBAEJ;oBACA,MAAM2B,eAAegB,iBAAiB,CAAC;oBACvC,MAAMhB,eAAeiB,iBAAiB,CAAC;oBAEvC,IAAIlB,YAAYY,MAAM,CAACO,QAAQ,EAAE;wBAC/B,MAAM/C,MAAMnE,YAAY,SAAS,UAAU;wBAE3C,MAAM6G,kBACJ,MAAMd,YAAYY,MAAM,CAACO,QAAQ,CAACJ,WAAW;wBAC/CV,yBAAAA,MAAOW,qBAAqB,CAAC5C,KAAK0C;wBAClC5C,cACEC,oBACAC,KACA0C,iBACA,OACAxC;oBAEJ;oBACA,MAAM2B,eAAeiB,iBAAiB,CAAC;oBAEvC,MAAMJ,kBAAkB,MAAMf,MAAMqB,YAAY,CAACL,WAAW;oBAC5DV,yBAAAA,MAAOW,qBAAqB,CAACL,WAAWG;oBAExC,MAAM3B,OAAO2B,mCAAAA,gBAAiB3B,IAAI;oBAElC,MAAMc,eAAegB,iBAAiB,CAACpB;oBACvC,MAAMI,eAAeiB,iBAAiB,CAACrB;oBACvC,IAAIV,SAAS,QAAQ;wBACnB,MAAMc,eAAeoB,sBAAsB,CAACxB,MAAM;oBACpD,OAAO;wBACLI,eAAeqB,wBAAwB,CAACX;oBAC1C;oBACA,MAAMV,eAAesB,gBAAgB,CAAC,SAAS;oBAC/C,MAAMtB,eAAesB,gBAAgB,CAAC1B,MAAM;oBAC5C,MAAMI,eAAeuB,oBAAoB,CAAC3B,MAAM;oBAEhD,IAAIS,0BAA0B;wBAC5B,MAAML,eAAewB,gBAAgB,CAAC5B,MAAM;oBAC9C;oBAEA,MAAMI,eAAeyB,cAAc,CAAC;wBAClCvB;wBACAC;wBACAJ;oBACF;oBAEA9B,cACEC,oBACAwC,WACAG,iBACA,OACAxC;gBAEJ,SAAU;oBACR,IAAIsB,KAAK;wBACP,wEAAwE;wBACxE,gEAAgE;wBAChES,yBAAAA,MAAOsB,kBAAkB,CACvBhB,WACA,OACAZ,MAAM6B,YAAY,EAClB;4BACE,oCAAoC;4BACpC1B,4BAAAA,SAAU2B,MAAM,CAAC/B;4BACjB,OAAO;gCACLgC,OAAO/H,4BAA4BgI,mBAAmB;gCACtDC,OAAO;oCAACnC;iCAAK;4BACf;wBACF,GACA,CAACX;4BACC,OAAO;gCACL+C,QAAQlI,4BAA4BmI,WAAW;gCAC/CC,MAAM,CAAC,SAAS,EAAEtC,KAAK,oBAAoB,EAAEX,GAAG;4BAClD;wBACF;wBAEFmB,yBAAAA,MAAOsB,kBAAkB,CACvBjB,WACA,OACAX,MAAMqB,YAAY,EAClB;4BACE,OAAO;gCACLU,OAAO/H,4BAA4BqI,cAAc;4BACnD;wBACF,GACA,CAAClD;4BACC,OAAO;gCACL+C,QAAQlI,4BAA4BmI,WAAW;gCAC/CC,MAAM,CAAC,SAAS,EAAEtC,KAAK,oBAAoB,EAAEX,GAAG;4BAClD;wBACF;wBAEF,IAAIc,YAAYY,MAAM,CAACO,QAAQ,EAAE;4BAC/Bd,yBAAAA,MAAOsB,kBAAkB,CACvB1H,YAAY,SAAS,UAAU,cAC/B,OACA+F,YAAYY,MAAM,CAACO,QAAQ,EAC3B;gCACE,OAAO;oCACLc,QAAQlI,4BAA4BmI,WAAW;oCAC/CC,MAAM;gCACR;4BACF,GACA,CAACjD;gCACC,OAAO;oCACL+C,QAAQlI,4BAA4BmI,WAAW;oCAC/CC,MAAM,CAAC,8CAA8C,EAAEjD,GAAG;gCAC5D;4BACF;wBAEJ;oBACF;gBACF;gBAEA;YACF;QACA,KAAK;YAAY;gBACf,MAAMd,MAAMnE,YAAY,SAAS,UAAU4F;gBAE3C,MAAMiB,kBAAkB,MAAMf,MAAMsC,QAAQ,CAACtB,WAAW;gBACxDV,yBAAAA,MAAOW,qBAAqB,CAAC5C,KAAK0C;gBAElC,MAAM3B,OAAO2B,gBAAgB3B,IAAI;gBAEjC,MAAMc,eAAeiB,iBAAiB,CAACrB;gBACvC,IAAIV,SAAS,QAAQ;oBACnB,MAAMc,eAAeoB,sBAAsB,CAACxB,MAAM;gBACpD,OAAO;oBACLI,eAAeqB,wBAAwB,CAAClD;gBAC1C;gBACA,MAAM6B,eAAeuB,oBAAoB,CAAC3B,MAAM;gBAEhD,MAAMI,eAAeyB,cAAc,CAAC;oBAClCvB;oBACAC;oBACAJ;gBACF;gBAEA9B,cAAcC,oBAAoBC,KAAK0C,iBAAiB,MAAMxC;gBAE9D;YACF;QACA,KAAK;YAAY;gBACf,MAAMF,MAAMnE,YAAY,OAAO,UAAU4F;gBAEzC,MAAMiB,kBAAkB,MAAMf,MAAMqB,YAAY,CAACL,WAAW;gBAC5DV,yBAAAA,MAAOW,qBAAqB,CAAC5C,KAAK0C;gBAElC,IAAIlB,KAAK;oBACP,wEAAwE;oBACxE,gEAAgE;oBAChES,yBAAAA,MAAOsB,kBAAkB,CACvBvD,KACA,MACA2B,MAAMuC,WAAW,EACjB,CAACC;wBACC,IAAIA,OAAOxE,MAAM,CAACyE,IAAI,CAAC,CAACrH,QAAUA,MAAMQ,QAAQ,KAAK,UAAU;4BAC7D,qCAAqC;4BACrC,yDAAyD;4BACzD;wBACF;wBACA,oCAAoC;wBACpCuE,4BAAAA,SAAU2B,MAAM,CAAC/B;wBACjB,OAAO;4BACLmC,QAAQlI,4BAA4B0I,wBAAwB;wBAC9D;oBACF,GACA;wBACE,OAAO;4BACLR,QAAQlI,4BAA4B0I,wBAAwB;wBAC9D;oBACF;gBAEJ;gBAEA,MAAMtD,OAAO2B,gBAAgB3B,IAAI;gBAEjC,IAAIA,SAAS,QAAQ;oBACnB,MAAMc,eAAeoB,sBAAsB,CAACxB,MAAM;gBACpD,OAAO;oBACLI,eAAeqB,wBAAwB,CAAClD;gBAC1C;gBAEA,MAAM6B,eAAeyC,oBAAoB,CAAC7C;gBAC1C,MAAMI,eAAegB,iBAAiB,CAACpB,MAAM;gBAC7C,MAAMI,eAAe0C,oBAAoB,CAAC9C;gBAC1C,MAAMI,eAAe2C,kBAAkB,CAAC/C;gBACxC,MAAMI,eAAeuB,oBAAoB,CAAC3B,MAAM;gBAChD,MAAMI,eAAesB,gBAAgB,CAAC1B,MAAM;gBAE5C,IAAIS,0BAA0B;oBAC5B,MAAML,eAAewB,gBAAgB,CAAC5B,MAAM;gBAC9C;gBAEA,MAAMI,eAAeyB,cAAc,CAAC;oBAClCvB;oBACAC;oBACAJ;gBACF;gBAEA9B,cAAcC,oBAAoBC,KAAK0C,iBAAiBlB,KAAKtB;gBAE7D;YACF;QACA,KAAK;YAAa;gBAChB,MAAMF,MAAMnE,YAAY,OAAO,UAAU4F;gBAEzC,MAAMiB,kBAAkB,MAAMf,MAAMsC,QAAQ,CAACtB,WAAW;gBACxDV,yBAAAA,MAAOW,qBAAqB,CAAC5C,KAAK0C;gBAElC,MAAM3B,OAAO2B,gBAAgB3B,IAAI;gBAEjC,MAAMc,eAAe0C,oBAAoB,CAAC9C;gBAE1C,IAAIV,SAAS,QAAQ;oBACnB,MAAMc,eAAeoB,sBAAsB,CAACxB,MAAM;gBACpD,OAAO;oBACLI,eAAeqB,wBAAwB,CAAClD;gBAC1C;gBAEA,MAAM6B,eAAeyB,cAAc,CAAC;oBAClCvB;oBACAC;oBACAJ;gBACF;gBACA9B,cAAcC,oBAAoBC,KAAK0C,iBAAiB,MAAMxC;gBAE9D;YACF;QACA;YAAS;gBACP,MAAM,IAAI3D,MAAM,CAAC,mBAAmB,EAAE,AAACoF,MAAcZ,IAAI,CAAC,KAAK,EAAEU,MAAM;YACzE;IACF;AACF;AAEA;;CAEC,GACD,OAAO,MAAMgD;IAIX;;;;;GAKC,GACDC,eAAe1E,GAAa,EAAE2E,UAAoB,EAAQ;QACxD,IAAI,CAAClB,MAAM,CAACzD;QAEZ,MAAM4E,gBAAgB,IAAIvH,IAAIsH;QAC9B,IAAI,CAACE,QAAQ,CAAChF,GAAG,CAACG,KAAK4E;QAEvB,KAAK,MAAME,aAAaF,cAAe;YACrC,IAAIG,gBAAgB,IAAI,CAACC,QAAQ,CAACC,GAAG,CAACH;YACtC,IAAI,CAACC,eAAe;gBAClBA,gBAAgB,IAAI1H;gBACpB,IAAI,CAAC2H,QAAQ,CAACnF,GAAG,CAACiF,WAAWC;YAC/B;YAEAA,cAAepH,GAAG,CAACqC;QACrB;IACF;IAEA;;;;GAIC,GACDyD,OAAOzD,GAAa,EAAE;QACpB,KAAK,MAAM8E,aAAa,IAAI,CAACI,kBAAkB,CAAClF,KAAM;YACpD,MAAM+E,gBAAgB,IAAI,CAACC,QAAQ,CAACC,GAAG,CAACH;YAExCC,iCAAAA,cAAetB,MAAM,CAACzD;YAEtB,IAAI,EAAC+E,iCAAAA,cAAevE,IAAI,GAAE;gBACxB,IAAI,CAACwE,QAAQ,CAACvB,MAAM,CAACqB;YACvB;QACF;QAEA,IAAI,CAACD,QAAQ,CAACpB,MAAM,CAACzD;IACvB;IAEAkF,mBAAmBlF,GAAa,EAAY;QAC1C,OAAOmF,MAAMC,IAAI,CAAC,IAAI,CAACP,QAAQ,CAACI,GAAG,CAACjF,QAAQ,EAAE;IAChD;IAEAqF,eAAeC,IAAY,EAAc;QACvC,OAAOH,MAAMC,IAAI,CAAC,IAAI,CAACJ,QAAQ,CAACC,GAAG,CAACK,SAAS,EAAE;IACjD;IAEAC,OAAmC;QACjC,OAAO,IAAI,CAACV,QAAQ,CAACU,IAAI;IAC3B;;aAvDQV,WAAuC,IAAIzE;aAC3C4E,WAAuC,IAAI5E;;AAuDrD;AAEA,OAAO,SAASoF,oBACd5D,WAAwB,EACxB5B,GAAa,EACbyF,WAAoC;IAEpC,MAAM,EAAE1E,IAAI,EAAEU,IAAI,EAAE,GAAG3F,cAAckE;IAErC,OAAQe;QACN,KAAK;YACH,OAAOa,YAAYa,GAAG,CAAC/E,GAAG,CAAC+D;QAC7B,KAAK;YACH,OAAQA;gBACN,KAAK;oBACH,OAAOG,YAAYY,MAAM,CAACC,GAAG,IAAI;gBACnC,KAAK;oBACH,OAAOb,YAAYY,MAAM,CAACO,QAAQ,IAAI;gBACxC,KAAK;oBACH,OAAOnB,YAAYY,MAAM,CAACjC,KAAK,IAAI;gBACrC;oBACE,OAAOqB,YAAYH,IAAI,CAAC/D,GAAG,CAAC+D;YAChC;QACF,KAAK;YACH,OAAQA;gBACN,KAAK;oBACH,OAAOG,YAAYY,MAAM,CAACkD,UAAU,IAAI;gBAC1C,KAAK;oBACH,OAAO9D,YAAYY,MAAM,CAACmD,eAAe,IAAI;gBAC/C;oBACE,OAAO;YACX;QACF,KAAK;YACH,IAAI,CAACF,aAAa;gBAChB,OAAO;YACT;YAEA,OAAOA,YACJJ,cAAc,CAAC5D,MACf2C,IAAI,CAAC,CAACwB,UACLJ,oBAAoB5D,aAAagE,SAASH;QAEhD;YAAS;gBACP,+DAA+D;gBAC/D,6DAA6D;gBAC7D,MAAMI,IAAW9E;gBACjB,OAAO;YACT;IACF;AACF;AA0BA,OAAO,eAAe+E,kBAAkB,EACtClE,WAAW,EAEXmE,kBAAkB,EAElBhG,kBAAkB,EAClB8B,cAAc,EACdE,WAAW,EACXC,kBAAkB,EAClB9B,SAAS,EACTsB,GAAG,EAaJ;IACCuE,mBAAmBvD,MAAM,CAACC,GAAG,GAAGb,YAAYoE,gBAAgB;IAC5DD,mBAAmBvD,MAAM,CAACO,QAAQ,GAAGnB,YAAYqE,qBAAqB;IACtEF,mBAAmBvD,MAAM,CAACjC,KAAK,GAAGqB,YAAYsE,kBAAkB;IAEhEH,mBAAmBvD,MAAM,CAACmD,eAAe,GAAG/D,YAAY+D,eAAe;IAEvEI,mBAAmBtE,IAAI,CAAC/B,KAAK;IAC7BqG,mBAAmBtD,GAAG,CAAC/C,KAAK;IAE5B,KAAK,MAAM,CAACgC,UAAUC,MAAM,IAAIC,YAAYuE,MAAM,CAAE;QAClD,OAAQxE,MAAMZ,IAAI;YAChB,KAAK;YACL,KAAK;gBACHgF,mBAAmBtE,IAAI,CAAC5B,GAAG,CAAC6B,UAAUC;gBACtC;YACF,KAAK;gBAAY;oBACfA,MAAMiC,KAAK,CAACwC,OAAO,CAAC,CAAC3E;wBACnBsE,mBAAmBtD,GAAG,CAAC5C,GAAG,CAAC4B,KAAK4E,YAAY,EAAE;4BAC5CtF,MAAM;4BACN,GAAGU,IAAI;wBACT;oBACF;oBACA;gBACF;YACA,KAAK;gBAAa;oBAChBsE,mBAAmBtD,GAAG,CAAC5C,GAAG,CAAC8B,MAAM0E,YAAY,EAAE1E;oBAC/C;gBACF;YACA;gBACE/F,IAAI0K,IAAI,CAAC,CAAC,SAAS,EAAE5E,SAAS,EAAE,EAAEC,MAAMZ,IAAI,CAAC,CAAC,CAAC;gBAC/C;QACJ;IACF;IAEA,IAAIS,KAAK;QACP,MAAM+E,4BAA4B;YAChCxG;YACAgG;YAEA,GAAGvE,GAAG;QACR;IACF;IAEA,MAAM,EAAEkE,UAAU,EAAEC,eAAe,EAAE,GAAG/D;IAExC,8DAA8D;IAC9D,8DAA8D;IAC9D,sCAAsC;IACtC,IAAImE,mBAAmBvD,MAAM,CAACkD,UAAU,IAAI,CAACA,YAAY;QACvD,MAAM1F,MAAMnE,YAAY,QAAQ,UAAU;QAC1C,wCAAwC;QACxC,OAAM2F,uBAAAA,IAAKS,KAAK,CAACuE,sBAAsB,CAACxG;QACxCD,mBAAmB0D,MAAM,CAACzD;QAC1BwB,uBAAAA,IAAKS,KAAK,CAACwE,OAAO,CAAC,cAAc;YAC/B/C,OAAO/H,4BAA4B+K,kBAAkB;QACvD;IACF,OAAO,IAAI,CAACX,mBAAmBvD,MAAM,CAACkD,UAAU,IAAIA,YAAY;QAC9D,wCAAwC;QACxClE,uBAAAA,IAAKS,KAAK,CAACwE,OAAO,CAAC,cAAc;YAC/B/C,OAAO/H,4BAA4B+K,kBAAkB;QACvD;IACF;IAEAX,mBAAmBvD,MAAM,CAACkD,UAAU,GAAGA;IAEvC,IAAIC,iBAAiB;QACnB,MAAMgB,yBAAyB,OAC7BnK,MACAoK;YAEA,MAAM5G,MAAMnE,YAAY,QAAQ,UAAUW;YAE1C,MAAMkG,kBAAkB,MAAMiD,eAAe,CAACiB,KAAK,CAACjE,WAAW;YAC/DnB,uBAAAA,IAAKS,KAAK,CAACW,qBAAqB,CAAC5C,KAAK0C;YACtC5C,cAAcC,oBAAoBC,KAAK0C,iBAAiB,OAAOxC;QACjE;QACA,MAAMyG,uBAAuB,0BAA0B;QACvD,MAAMA,uBAAuB,wBAAwB;QACrD,MAAM9E,eAAeoB,sBAAsB,CACzC,mBACA;QAEF,MAAMpB,eAAeyB,cAAc,CAAC;YAClCvB;YACAC;YACAJ,aAAamE;QACf;QAEA,IAAIvE,KAAK;YACPA,IAAIqF,YAAY,CAACC,6BAA6B,GAAG;YACjD,MAAMtF,IAAIS,KAAK,CAAC8E,oBAAoB,CAClC,iCACAvF,IAAIqF,YAAY,CAACC,6BAA6B;QAElD;IACF,OAAO;QACL,IAAItF,KAAK;YACPA,IAAIqF,YAAY,CAACC,6BAA6B,GAAGE;YACjD,MAAMxF,IAAIS,KAAK,CAAC8E,oBAAoB,CAClC,iCACAvF,IAAIqF,YAAY,CAACC,6BAA6B;QAElD;IACF;IAEA,IAAIpB,YAAY;QACd,MAAM1F,MAAMnE,YAAY,QAAQ,UAAU;QAE1C,MAAMoI,WAAWyB,WAAWzB,QAAQ;QAEpC,eAAegD;YACb,MAAMvE,kBAAkB,MAAMuB,SAAStB,WAAW;YAClDnB,uBAAAA,IAAKS,KAAK,CAACW,qBAAqB,CAAC5C,KAAK0C;YACtC5C,cAAcC,oBAAoBC,KAAK0C,iBAAiB,OAAOxC;YAC/D,MAAM2B,eAAeoB,sBAAsB,CAAC,cAAc;YAC1D,IAAIzB,KAAK;oBAKHK;gBAJJL,IAAIqF,YAAY,CAACnB,UAAU,GAAG;oBAC5BxH,OAAO;oBACPuD,MAAM;oBACNyF,QAAQ,GACNrF,wCAAAA,eAAesF,qBAAqB,CAACnH,yBAArC6B,sCAA2C6D,UAAU,CAAC,IAAI,CAACwB,QAAQ;gBACvE;YACF;QACF;QACA,MAAMD;QAEN,IAAIzF,KAAK;YACPA,uBAAAA,IAAKS,KAAK,CAACsB,kBAAkB,CAC3BvD,KACA,OACAiE,UACA;gBACE,MAAMmD,iBAAiB5F,IAAIS,KAAK,CAACoF,aAAa,CAC5C,cACAL,WACA;gBAEF,MAAMC;gBACN,MAAMzF,IAAIS,KAAK,CAAC8E,oBAAoB,CAClC,wBACAvF,IAAIqF,YAAY,CAACS,oBAAoB;gBAEvC,MAAM9F,IAAIS,KAAK,CAAC8E,oBAAoB,CAClC,cACAvF,IAAIqF,YAAY,CAACnB,UAAU;gBAE7B,MAAM7D,eAAeyB,cAAc,CAAC;oBAClCvB;oBACAC;oBACAJ,aAAamE;gBACf;gBAEAqB,kCAAAA;gBACA,OAAO;oBAAE1D,OAAO/H,4BAA4B+K,kBAAkB;gBAAC;YACjE,GACA;gBACE,OAAO;oBACLhD,OAAO/H,4BAA4B+K,kBAAkB;gBACvD;YACF;QAEJ;IACF,OAAO;QACL7E,eAAeqB,wBAAwB,CACrCrH,YAAY,QAAQ,UAAU;QAEhC,IAAI2F,KAAK;YACPA,IAAIqF,YAAY,CAACS,oBAAoB,GAAGN;YACxCxF,IAAIqF,YAAY,CAACnB,UAAU,GAAGsB;QAChC;IACF;IAEA,IAAIxF,KAAK;QACP,MAAMA,IAAIS,KAAK,CAAC8E,oBAAoB,CAClC,wBACAvF,IAAIqF,YAAY,CAACS,oBAAoB;QAEvC,MAAM9F,IAAIS,KAAK,CAAC8E,oBAAoB,CAClC,cACAvF,IAAIqF,YAAY,CAACnB,UAAU;IAE/B;AACF;AAEA,eAAea,4BAA4B,EACzCxG,kBAAkB,EAClBgG,kBAAkB,EAElBN,WAAW,EACX8B,mBAAmB,EACnBC,OAAO,EACPC,YAAY,EAEZxF,KAAK,EAIqB;IAC1B,yEAAyE;IACzE,KAAK,MAAMjC,OAAOyF,YAAYF,IAAI,GAAI;QACpC,IAAI,CAACC,oBAAoBO,oBAAoB/F,KAAKyF,cAAc;YAC9DA,YAAYhC,MAAM,CAACzD;QACrB;IACF;IAEA,KAAK,MAAMA,OAAOuH,oBAAoBhC,IAAI,GAAI;QAC5C,mCAAmC;QACnC,IAAI,CAACC,oBAAoBO,oBAAoB/F,KAAKyF,cAAc;YAC9D,MAAMxD,MAAMuE,sBAAsB,CAACxG;QACrC;IACF;IAEA,KAAK,MAAM,CAACA,IAAI,IAAID,mBAAoB;QACtC,IAAI,CAACyF,oBAAoBO,oBAAoB/F,KAAKyF,cAAc;YAC9D1F,mBAAmB0D,MAAM,CAACzD;QAC5B;IACF;IAEA,KAAK,MAAM0H,UAAUF,QAAS;QAC5B,MAAMG,QAAQF,aAAaxC,GAAG,CAACyC;QAC/B,IAAI,CAACC,OAAO;YACV;QACF;QAEA,KAAK,MAAM3H,OAAO2H,MAAMC,YAAY,CAACrC,IAAI,GAAI;YAC3C,IAAI,CAACC,oBAAoBO,oBAAoB/F,KAAKyF,cAAc;gBAC9DkC,MAAMC,YAAY,CAACnE,MAAM,CAACzD;YAC5B;QACF;QAEA,KAAK,MAAM6H,MAAMF,MAAMG,aAAa,CAACvC,IAAI,GAAI;YAC3C,IACE,CAACC,oBACCO,oBACAlK,YAAY,UAAU,UAAUgM,KAChCpC,cAEF;gBACAxD,MAAM8F,wBAAwB,CAACL,QAAQG;YACzC;QACF;IACF;AACF;AAEA,OAAO,eAAeG,sBAAsB,EAC1CxG,GAAG,EACHzB,kBAAkB,EAClB6B,WAAW,EACXC,cAAc,EACdE,WAAW,EACXC,kBAAkB,EAClB9B,SAAS,EAET+B,KAAK,EAWN;IACC,IAAIL,YAAYY,MAAM,CAACC,GAAG,EAAE;QAC1B,MAAMzC,MAAMnE,YAAY,SAAS,UAAU;QAE3C,MAAM6G,kBAAkB,MAAMd,YAAYY,MAAM,CAACC,GAAG,CAACE,WAAW;QAChEV,yBAAAA,MAAOW,qBAAqB,CAAC5C,KAAK0C;QAClC,IAAIlB,KAAK;YACPS,yBAAAA,MAAOsB,kBAAkB,CACvBvD,KACA,OACA4B,YAAYY,MAAM,CAACC,GAAG,EACtB;gBACE,oEAAoE;gBACpE,qIAAqI;gBACrI,OAAO;oBAAEiB,OAAO/H,4BAA4BqI,cAAc;gBAAC;YAC7D,GACA;gBACE,OAAO;oBACLH,QAAQlI,4BAA4BmI,WAAW;oBAC/CC,MAAM;gBACR;YACF;QAEJ;QACAjE,cAAcC,oBAAoBC,KAAK0C,iBAAiB,OAAOxC;IACjE;IACA,MAAM2B,eAAegB,iBAAiB,CAAC;IACvC,MAAMhB,eAAeiB,iBAAiB,CAAC;IACvC,MAAMjB,eAAesB,gBAAgB,CAAC;IAEtC,IAAIvB,YAAYY,MAAM,CAACO,QAAQ,EAAE;QAC/B,MAAM/C,MAAMnE,YAAY,SAAS,UAAU;QAE3C,MAAM6G,kBAAkB,MAAMd,YAAYY,MAAM,CAACO,QAAQ,CAACJ,WAAW;QACrEV,yBAAAA,MAAOW,qBAAqB,CAAC5C,KAAK0C;QAClC,IAAIlB,KAAK;YACPS,yBAAAA,MAAOsB,kBAAkB,CACvBvD,KACA,OACA4B,YAAYY,MAAM,CAACO,QAAQ,EAC3B;gBACE,OAAO;oBACLc,QAAQlI,4BAA4BmI,WAAW;oBAC/CC,MAAM;gBACR;YACF,GACA,CAACjD;gBACC,OAAO;oBACL+C,QAAQlI,4BAA4BmI,WAAW;oBAC/CC,MAAM,CAAC,+CAA+C,EAAEjD,GAAG;gBAC7D;YACF;QAEJ;QACAhB,cAAcC,oBAAoBC,KAAK0C,iBAAiB,OAAOxC;IACjE;IACA,MAAM2B,eAAeiB,iBAAiB,CAAC;IAEvC,IAAIlB,YAAYY,MAAM,CAACjC,KAAK,EAAE;QAC5B,MAAMP,MAAMnE,YAAY,SAAS,UAAU;QAE3C,MAAM6G,kBAAkB,MAAMd,YAAYY,MAAM,CAACjC,KAAK,CAACoC,WAAW;QAClEV,yBAAAA,MAAOW,qBAAqB,CAAC5C,KAAK0C;QAClC,IAAIlB,KAAK;YACPS,yBAAAA,MAAOsB,kBAAkB,CACvBvD,KACA,OACA4B,YAAYY,MAAM,CAACjC,KAAK,EACxB;gBACE,oEAAoE;gBACpE,qIAAqI;gBACrI,OAAO;oBAAEmD,OAAO/H,4BAA4BqI,cAAc;gBAAC;YAC7D,GACA,CAAClD;gBACC,OAAO;oBACL+C,QAAQlI,4BAA4BmI,WAAW;oBAC/CC,MAAM,CAAC,8BAA8B,EAAEjD,GAAG;gBAC5C;YACF;QAEJ;QACAhB,cAAcC,oBAAoBC,KAAK0C,iBAAiB,OAAOxC;IACjE;IACA,MAAM2B,eAAegB,iBAAiB,CAAC;IACvC,MAAMhB,eAAeiB,iBAAiB,CAAC;IACvC,MAAMjB,eAAesB,gBAAgB,CAAC;IAEtC,MAAMtB,eAAeyB,cAAc,CAAC;QAClCvB;QACAC;QACAJ;IACF;AACF;AAEA,OAAO,SAASqG,kBAAkBtG,KAAa;IAC7C,OAAOA,MAAMpD,OAAO,CAAC,YAAY;AACnC;AAEA,OAAO,SAAS2J,eAAevG,KAAa;IAC1C,OAAOA,QAAQ;AACjB;AAEA,OAAO,SAASwG,qBAAqBxG,KAAa;IAChD,OAAOA,QAAQ;AACjB;AAEA,8EAA8E;AAC9E,sDAAsD;AACtD,2FAA2F;AAC3F,mFAAmF;AACnF,OAAO,SAASyG,wCACdzG,KAAa,EACb0G,GAAmB;IAEnB,IAAIC,gBAAgB3G;IACpB,IAAI3F,gBAAgBsM,gBAAgB;QAClCA,gBAAgBA,cAAcC,QAAQ,CAAC,YACnCD,cAAcE,KAAK,CAAC,GAAG,CAAC,SAASC,MAAM,IACvCH;QAEJ,IAAID,KAAK;YACP,IAAIC,cAAcC,QAAQ,CAAC,uBAAuB;gBAChDD,gBAAgBA,cAAcE,KAAK,CAAC,GAAG,CAAC,qBAAqBC,MAAM;YACrE;YACA,IAAIH,cAAcC,QAAQ,CAAC,mBAAmBF,QAAQ,QAAQ;gBAC5D,kDAAkD;gBAClDC,gBAAgBA,cAAcE,KAAK,CAAC,GAAG,CAAC,OAAOC,MAAM;YACvD;QACF;QACAH,gBAAgBA,gBAAgB;IAClC;IACA,OAAOA;AACT;AAEA,OAAO,SAASI,2BACdC,MAA0B;QAEnBA;IAAP,OAAOA,EAAAA,6BAAAA,OAAOC,YAAY,CAACC,KAAK,qBAAzBF,2BAA2BG,yBAAyB,KAAI;AACjE"}