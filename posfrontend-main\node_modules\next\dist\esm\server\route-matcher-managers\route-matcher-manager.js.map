{"version": 3, "sources": ["../../../src/server/route-matcher-managers/route-matcher-manager.ts"], "sourcesContent": ["import type { RouteMatch } from '../route-matches/route-match'\nimport type { RouteMatcherProvider } from '../route-matcher-providers/route-matcher-provider'\nimport type { LocaleAnalysisResult } from '../lib/i18n-provider'\n\nexport type MatchOptions = {\n  skipDynamic?: boolean\n\n  /**\n   * If defined, this indicates to the matcher that the request should be\n   * treated as locale-aware. If this is undefined, it means that this\n   * application was not configured for additional locales.\n   */\n  i18n?: LocaleAnalysisResult | undefined\n}\n\nexport interface RouteMatcherManager {\n  /**\n   * Returns a promise that resolves when the matcher manager has finished\n   * reloading.\n   */\n  waitTillReady(): Promise<void>\n\n  /**\n   * Pushes in a new matcher for this manager to manage. After all the\n   * providers have been pushed, the manager must be reloaded.\n   *\n   * @param provider the provider for this manager to also manage\n   */\n  push(provider: RouteMatcherProvider): void\n\n  /**\n   * Reloads the matchers from the providers. This should be done after all the\n   * providers have been added or the underlying providers should be refreshed.\n   */\n  reload(): Promise<void>\n\n  /**\n   * Tests the underlying matchers to find a match. It does not return the\n   * match.\n   *\n   * @param pathname the pathname to test for matches\n   * @param options the options for the testing\n   */\n  test(pathname: string, options: MatchOptions): Promise<boolean>\n\n  /**\n   * Returns the first match for a given request.\n   *\n   * @param pathname the pathname to match against\n   * @param options the options for the matching\n   */\n  match(pathname: string, options: MatchOptions): Promise<RouteMatch | null>\n\n  /**\n   * Returns a generator for each match for a given request. This should be\n   * consumed in a `for await (...)` loop, when finished, breaking or returning\n   * from the loop will terminate the matching operation.\n   *\n   * @param pathname the pathname to match against\n   * @param options the options for the matching\n   */\n  matchAll(\n    pathname: string,\n    options: MatchOptions\n  ): AsyncGenerator<RouteMatch, null, undefined>\n}\n"], "names": [], "mappings": "AAeA,WAkDC"}