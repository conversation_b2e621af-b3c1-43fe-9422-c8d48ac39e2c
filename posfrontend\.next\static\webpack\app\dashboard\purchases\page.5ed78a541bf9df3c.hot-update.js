"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/purchases/page",{

/***/ "(app-pages-browser)/./src/components/Purchases/PurchaseFormPanel.tsx":
/*!********************************************************!*\
  !*** ./src/components/Purchases/PurchaseFormPanel.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,InputNumber,Select!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,InputNumber,Select!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,InputNumber,Select!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,InputNumber,Select!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _reduxRTK_services_supplierApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/reduxRTK/services/supplierApi */ \"(app-pages-browser)/./src/reduxRTK/services/supplierApi.ts\");\n/* harmony import */ var _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/reduxRTK/services/productApi */ \"(app-pages-browser)/./src/reduxRTK/services/productApi.ts\");\n/* harmony import */ var _components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/SlidingPanel */ \"(app-pages-browser)/./src/components/ui/SlidingPanel.tsx\");\n/* harmony import */ var _hooks_purchases_usePurchaseCreate__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/purchases/usePurchaseCreate */ \"(app-pages-browser)/./src/hooks/purchases/usePurchaseCreate.ts\");\n/* harmony import */ var _hooks_purchases_usePurchaseUpdate__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/purchases/usePurchaseUpdate */ \"(app-pages-browser)/./src/hooks/purchases/usePurchaseUpdate.ts\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_NumberOutlined_ShoppingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,NumberOutlined,ShoppingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_NumberOutlined_ShoppingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,NumberOutlined,ShoppingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_NumberOutlined_ShoppingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,NumberOutlined,ShoppingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/NumberOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DollarOutlined_NumberOutlined_ShoppingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DollarOutlined,NumberOutlined,ShoppingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _purchase_panels_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./purchase-panels.css */ \"(app-pages-browser)/./src/components/Purchases/purchase-panels.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst { Option } = _barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\nconst PurchaseFormPanel = (param)=>{\n    let { isOpen, onClose, onSuccess, purchase, currentUser } = param;\n    var _suppliersResponse_data, _productsResponse_data;\n    _s();\n    const [form] = _barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm();\n    const isEditMode = !!purchase;\n    const [totalCost, setTotalCost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    // Hooks for creating and updating purchases\n    const { createPurchase, isSubmitting: isCreating } = (0,_hooks_purchases_usePurchaseCreate__WEBPACK_IMPORTED_MODULE_5__.usePurchaseCreate)(onSuccess);\n    const { updatePurchase, isUpdating } = (0,_hooks_purchases_usePurchaseUpdate__WEBPACK_IMPORTED_MODULE_6__.usePurchaseUpdate)(onSuccess);\n    // Fetch suppliers for dropdown - Always fetch when component mounts\n    const { data: suppliersResponse, refetch: refetchSuppliers } = (0,_reduxRTK_services_supplierApi__WEBPACK_IMPORTED_MODULE_2__.useGetAllSuppliersQuery)({}, {\n        refetchOnMountOrArgChange: true,\n        refetchOnFocus: false,\n        refetchOnReconnect: true\n    });\n    const suppliers = (suppliersResponse === null || suppliersResponse === void 0 ? void 0 : (_suppliersResponse_data = suppliersResponse.data) === null || _suppliersResponse_data === void 0 ? void 0 : _suppliersResponse_data.suppliers) || [];\n    // Fetch products for dropdown - Always fetch when component mounts\n    const { data: productsResponse, refetch: refetchProducts } = (0,_reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery)({\n        page: 1,\n        limit: 1000,\n        search: ''\n    }, {\n        refetchOnMountOrArgChange: true,\n        refetchOnFocus: false,\n        refetchOnReconnect: true\n    });\n    const products = (productsResponse === null || productsResponse === void 0 ? void 0 : (_productsResponse_data = productsResponse.data) === null || _productsResponse_data === void 0 ? void 0 : _productsResponse_data.products) || [];\n    // Calculate total cost when quantity or cost price changes\n    const calculateTotalCost = ()=>{\n        const quantity = form.getFieldValue('quantity') || 0;\n        const costPrice = form.getFieldValue('costPrice') || 0;\n        // Calculate total and ensure it's a string with 2 decimal places\n        const total = (quantity * costPrice).toFixed(2);\n        setTotalCost(total);\n        // Set the form value as a string to match the expected type\n        form.setFieldsValue({\n            totalCost: total\n        });\n    };\n    // Handle panel open/close and data fetching\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PurchaseFormPanel.useEffect\": ()=>{\n            if (isOpen) {\n                // When panel opens, ensure we have fresh data\n                console.log('🛒 Purchase panel opened - fetching fresh data');\n                refetchProducts();\n                refetchSuppliers();\n                form.resetFields();\n                if (!purchase) {\n                    setTotalCost(\"0.00\");\n                }\n            }\n        }\n    }[\"PurchaseFormPanel.useEffect\"], [\n        form,\n        isOpen,\n        refetchProducts,\n        refetchSuppliers\n    ]);\n    // Separate effect to populate form when purchase data and dropdown data are available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PurchaseFormPanel.useEffect\": ()=>{\n            if (isOpen && purchase && products.length > 0 && suppliers.length > 0) {\n                // For edit mode, we need to find the product and supplier IDs by name\n                // since the backend returns names, not IDs\n                const selectedProduct = products.find({\n                    \"PurchaseFormPanel.useEffect.selectedProduct\": (p)=>p.name === purchase.product\n                }[\"PurchaseFormPanel.useEffect.selectedProduct\"]);\n                const selectedSupplier = suppliers.find({\n                    \"PurchaseFormPanel.useEffect.selectedSupplier\": (s)=>s.name === purchase.supplier\n                }[\"PurchaseFormPanel.useEffect.selectedSupplier\"]);\n                form.setFieldsValue({\n                    productId: selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.id,\n                    supplierId: selectedSupplier === null || selectedSupplier === void 0 ? void 0 : selectedSupplier.id,\n                    quantity: purchase.quantity,\n                    costPrice: purchase.costPrice,\n                    totalCost: purchase.totalCost\n                });\n                setTotalCost(purchase.totalCost);\n            }\n        }\n    }[\"PurchaseFormPanel.useEffect\"], [\n        isOpen,\n        purchase,\n        products,\n        suppliers,\n        form\n    ]);\n    // Handle form submission\n    const handleSubmit = async (values)=>{\n        try {\n            var _values_costPrice, _values_totalCost;\n            // Convert numeric values to strings as required by the API\n            const formattedValues = {\n                ...values,\n                // Ensure costPrice is a string (backend expects string)\n                costPrice: ((_values_costPrice = values.costPrice) === null || _values_costPrice === void 0 ? void 0 : _values_costPrice.toString()) || \"0\",\n                // Ensure totalCost is a string (backend expects string)\n                totalCost: ((_values_totalCost = values.totalCost) === null || _values_totalCost === void 0 ? void 0 : _values_totalCost.toString()) || \"0\"\n            };\n            console.log(\"Submitting purchase with formatted values:\", formattedValues);\n            if (isEditMode && purchase) {\n                // Update existing purchase\n                await updatePurchase(purchase.id, formattedValues);\n            } else {\n                // Create new purchase\n                await createPurchase(formattedValues);\n            }\n        } catch (error) {\n            console.error(\"Failed to save purchase:\", error);\n        }\n    };\n    // Panel title\n    const panelTitle = isEditMode ? \"Edit Purchase\" : \"Add Purchase\";\n    // Panel footer with action buttons\n    const panelFooter = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-end space-x-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                onClick: onClose,\n                disabled: isCreating || isUpdating,\n                className: \"text-gray-700 hover:text-gray-900\",\n                style: {\n                    borderColor: '#d9d9d9',\n                    background: '#f5f5f5'\n                },\n                children: \"Cancel\"\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                type: \"primary\",\n                loading: isCreating || isUpdating,\n                onClick: ()=>form.submit(),\n                children: isEditMode ? \"Update\" : \"Save\"\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        isOpen: isOpen,\n        onClose: onClose,\n        title: panelTitle,\n        width: \"500px\",\n        footer: panelFooter,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 border-b border-gray-200 pb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-gray-800 flex items-center\",\n                            children: isEditMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_NumberOutlined_ShoppingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"Edit Purchase\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_NumberOutlined_ShoppingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"Add New Purchase\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: isEditMode ? \"Update purchase information\" : \"Fill in the details to add a new purchase\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 text-sm text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-500 mr-1\",\n                            children: \"*\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, undefined),\n                        \" indicates required fields\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    form: form,\n                    layout: \"vertical\",\n                    onFinish: handleSubmit,\n                    className: \"purchase-form\",\n                    requiredMark: true,\n                    onValuesChange: (_, values)=>{\n                        if ('quantity' in values || 'costPrice' in values) {\n                            calculateTotalCost();\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                            name: \"productId\",\n                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_NumberOutlined_ShoppingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 56\n                                    }, void 0),\n                                    \" Product\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 20\n                            }, void 0),\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"Please select a product\"\n                                }\n                            ],\n                            tooltip: \"Select the product you are purchasing\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                placeholder: \"Select a product\",\n                                showSearch: true,\n                                optionFilterProp: \"children\",\n                                children: products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: product.id,\n                                        children: product.name\n                                    }, product.id, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                            name: \"supplierId\",\n                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_NumberOutlined_ShoppingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 56\n                                    }, void 0),\n                                    \" Supplier\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 20\n                            }, void 0),\n                            tooltip: \"Select the supplier (optional)\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                placeholder: \"Select a supplier (optional)\",\n                                allowClear: true,\n                                showSearch: true,\n                                optionFilterProp: \"children\",\n                                children: suppliers.map((supplier)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: supplier.id,\n                                        children: supplier.name\n                                    }, supplier.id, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                            name: \"quantity\",\n                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_NumberOutlined_ShoppingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 56\n                                    }, void 0),\n                                    \" Quantity\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 20\n                            }, void 0),\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"Please enter quantity\"\n                                },\n                                {\n                                    type: 'number',\n                                    min: 1,\n                                    message: \"Quantity must be at least 1\"\n                                }\n                            ],\n                            tooltip: \"The quantity of products purchased\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                min: 1,\n                                style: {\n                                    width: '100%'\n                                },\n                                placeholder: \"Enter quantity\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                            name: \"costPrice\",\n                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_NumberOutlined_ShoppingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 56\n                                    }, void 0),\n                                    \" Cost Price (GHS)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 20\n                            }, void 0),\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"Please enter cost price\"\n                                },\n                                {\n                                    type: 'number',\n                                    min: 0.01,\n                                    message: \"Cost price must be greater than 0\"\n                                }\n                            ],\n                            tooltip: \"The cost price per unit\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                min: 0.01,\n                                step: 0.01,\n                                style: {\n                                    width: '100%'\n                                },\n                                placeholder: \"Enter cost price\",\n                                formatter: (value)=>\"GHS \".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ','),\n                                parser: (value)=>parseFloat(value.replace(/GHS\\s?|(,*)/g, ''))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                            name: \"totalCost\",\n                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarOutlined_NumberOutlined_ShoppingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 56\n                                    }, void 0),\n                                    \" Total Cost (GHS)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 20\n                            }, void 0),\n                            tooltip: \"The total cost (calculated automatically)\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                disabled: true,\n                                style: {\n                                    width: '100%'\n                                },\n                                value: totalCost,\n                                formatter: (value)=>\"GHS \".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ','),\n                                parser: (value)=>value.replace(/GHS\\s?|(,*)/g, '')\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Purchases\\\\PurchaseFormPanel.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PurchaseFormPanel, \"cR9Tk6KQd53Ets2lQK7j4KJmk1M=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Form_InputNumber_Select_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm,\n        _hooks_purchases_usePurchaseCreate__WEBPACK_IMPORTED_MODULE_5__.usePurchaseCreate,\n        _hooks_purchases_usePurchaseUpdate__WEBPACK_IMPORTED_MODULE_6__.usePurchaseUpdate,\n        _reduxRTK_services_supplierApi__WEBPACK_IMPORTED_MODULE_2__.useGetAllSuppliersQuery,\n        _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery\n    ];\n});\n_c = PurchaseFormPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PurchaseFormPanel);\nvar _c;\n$RefreshReg$(_c, \"PurchaseFormPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1B1cmNoYXNlcy9QdXJjaGFzZUZvcm1QYW5lbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFbUQ7QUFDYTtBQUVvQjtBQUNIO0FBQ3pCO0FBQ2dCO0FBQ0E7QUFFMkI7QUFDcEU7QUFFL0IsTUFBTSxFQUFFZ0IsTUFBTSxFQUFFLEdBQUdWLGtHQUFNQTtBQVV6QixNQUFNVyxvQkFBc0Q7UUFBQyxFQUMzREMsTUFBTSxFQUNOQyxPQUFPLEVBQ1BDLFNBQVMsRUFDVEMsUUFBUSxFQUNSQyxXQUFXLEVBQ1o7UUFlK0JDLHlCQVlGQzs7SUExQjVCLE1BQU0sQ0FBQ0MsS0FBSyxHQUFHdEIsa0dBQUlBLENBQUN1QixPQUFPO0lBQzNCLE1BQU1DLGFBQWEsQ0FBQyxDQUFDTjtJQUNyQixNQUFNLENBQUNPLFdBQVdDLGFBQWEsR0FBRzNCLCtDQUFRQSxDQUFTO0lBRW5ELDRDQUE0QztJQUM1QyxNQUFNLEVBQUU0QixjQUFjLEVBQUVDLGNBQWNDLFVBQVUsRUFBRSxHQUFHdEIscUZBQWlCQSxDQUFDVTtJQUN2RSxNQUFNLEVBQUVhLGNBQWMsRUFBRUMsVUFBVSxFQUFFLEdBQUd2QixxRkFBaUJBLENBQUNTO0lBRXpELG9FQUFvRTtJQUNwRSxNQUFNLEVBQUVlLE1BQU1aLGlCQUFpQixFQUFFYSxTQUFTQyxnQkFBZ0IsRUFBRSxHQUFHOUIsdUZBQXVCQSxDQUFDLENBQUMsR0FBRztRQUN6RitCLDJCQUEyQjtRQUMzQkMsZ0JBQWdCO1FBQ2hCQyxvQkFBb0I7SUFDdEI7SUFDQSxNQUFNQyxZQUF3QmxCLENBQUFBLDhCQUFBQSx5Q0FBQUEsMEJBQUFBLGtCQUFtQlksSUFBSSxjQUF2QlosOENBQUFBLHdCQUF5QmtCLFNBQVMsS0FBSSxFQUFFO0lBRXRFLG1FQUFtRTtJQUNuRSxNQUFNLEVBQUVOLE1BQU1YLGdCQUFnQixFQUFFWSxTQUFTTSxlQUFlLEVBQUUsR0FBR2xDLHFGQUFzQkEsQ0FBQztRQUNsRm1DLE1BQU07UUFDTkMsT0FBTztRQUNQQyxRQUFRO0lBQ1YsR0FBRztRQUNEUCwyQkFBMkI7UUFDM0JDLGdCQUFnQjtRQUNoQkMsb0JBQW9CO0lBQ3RCO0lBQ0EsTUFBTU0sV0FBc0J0QixDQUFBQSw2QkFBQUEsd0NBQUFBLHlCQUFBQSxpQkFBa0JXLElBQUksY0FBdEJYLDZDQUFBQSx1QkFBd0JzQixRQUFRLEtBQUksRUFBRTtJQUVsRSwyREFBMkQ7SUFDM0QsTUFBTUMscUJBQXFCO1FBQ3pCLE1BQU1DLFdBQVd2QixLQUFLd0IsYUFBYSxDQUFDLGVBQWU7UUFDbkQsTUFBTUMsWUFBWXpCLEtBQUt3QixhQUFhLENBQUMsZ0JBQWdCO1FBQ3JELGlFQUFpRTtRQUNqRSxNQUFNRSxRQUFRLENBQUNILFdBQVdFLFNBQVEsRUFBR0UsT0FBTyxDQUFDO1FBQzdDdkIsYUFBYXNCO1FBQ2IsNERBQTREO1FBQzVEMUIsS0FBSzRCLGNBQWMsQ0FBQztZQUFFekIsV0FBV3VCO1FBQU07SUFDekM7SUFFQSw0Q0FBNEM7SUFDNUNsRCxnREFBU0E7dUNBQUM7WUFDUixJQUFJaUIsUUFBUTtnQkFDViw4Q0FBOEM7Z0JBQzlDb0MsUUFBUUMsR0FBRyxDQUFDO2dCQUNaYjtnQkFDQUw7Z0JBQ0FaLEtBQUsrQixXQUFXO2dCQUVoQixJQUFJLENBQUNuQyxVQUFVO29CQUNiUSxhQUFhO2dCQUNmO1lBQ0Y7UUFDRjtzQ0FBRztRQUFDSjtRQUFNUDtRQUFRd0I7UUFBaUJMO0tBQWlCO0lBRXBELHNGQUFzRjtJQUN0RnBDLGdEQUFTQTt1Q0FBQztZQUNSLElBQUlpQixVQUFVRyxZQUFZeUIsU0FBU1csTUFBTSxHQUFHLEtBQUtoQixVQUFVZ0IsTUFBTSxHQUFHLEdBQUc7Z0JBQ3JFLHNFQUFzRTtnQkFDdEUsMkNBQTJDO2dCQUMzQyxNQUFNQyxrQkFBa0JaLFNBQVNhLElBQUk7bUVBQUNDLENBQUFBLElBQUtBLEVBQUVDLElBQUksS0FBS3hDLFNBQVN5QyxPQUFPOztnQkFDdEUsTUFBTUMsbUJBQW1CdEIsVUFBVWtCLElBQUk7b0VBQUNLLENBQUFBLElBQUtBLEVBQUVILElBQUksS0FBS3hDLFNBQVM0QyxRQUFROztnQkFFekV4QyxLQUFLNEIsY0FBYyxDQUFDO29CQUNsQmEsU0FBUyxFQUFFUiw0QkFBQUEsc0NBQUFBLGdCQUFpQlMsRUFBRTtvQkFDOUJDLFVBQVUsRUFBRUwsNkJBQUFBLHVDQUFBQSxpQkFBa0JJLEVBQUU7b0JBQ2hDbkIsVUFBVTNCLFNBQVMyQixRQUFRO29CQUMzQkUsV0FBVzdCLFNBQVM2QixTQUFTO29CQUM3QnRCLFdBQVdQLFNBQVNPLFNBQVM7Z0JBQy9CO2dCQUNBQyxhQUFhUixTQUFTTyxTQUFTO1lBQ2pDO1FBQ0Y7c0NBQUc7UUFBQ1Y7UUFBUUc7UUFBVXlCO1FBQVVMO1FBQVdoQjtLQUFLO0lBRWhELHlCQUF5QjtJQUN6QixNQUFNNEMsZUFBZSxPQUFPQztRQUMxQixJQUFJO2dCQUtXQSxtQkFFQUE7WUFOYiwyREFBMkQ7WUFDM0QsTUFBTUMsa0JBQWtCO2dCQUN0QixHQUFHRCxNQUFNO2dCQUNULHdEQUF3RDtnQkFDeERwQixXQUFXb0IsRUFBQUEsb0JBQUFBLE9BQU9wQixTQUFTLGNBQWhCb0Isd0NBQUFBLGtCQUFrQkUsUUFBUSxPQUFNO2dCQUMzQyx3REFBd0Q7Z0JBQ3hENUMsV0FBVzBDLEVBQUFBLG9CQUFBQSxPQUFPMUMsU0FBUyxjQUFoQjBDLHdDQUFBQSxrQkFBa0JFLFFBQVEsT0FBTTtZQUM3QztZQUVBbEIsUUFBUUMsR0FBRyxDQUFDLDhDQUE4Q2dCO1lBRTFELElBQUk1QyxjQUFjTixVQUFVO2dCQUMxQiwyQkFBMkI7Z0JBQzNCLE1BQU1ZLGVBQWVaLFNBQVM4QyxFQUFFLEVBQUVJO1lBQ3BDLE9BQU87Z0JBQ0wsc0JBQXNCO2dCQUN0QixNQUFNekMsZUFBZXlDO1lBQ3ZCO1FBQ0YsRUFBRSxPQUFPRSxPQUFPO1lBQ2RuQixRQUFRbUIsS0FBSyxDQUFDLDRCQUE0QkE7UUFDNUM7SUFDRjtJQUVBLGNBQWM7SUFDZCxNQUFNQyxhQUFhL0MsYUFBYSxrQkFBa0I7SUFFbEQsbUNBQW1DO0lBQ25DLE1BQU1nRCw0QkFDSiw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUN6RSxtR0FBTUE7Z0JBQ0wwRSxTQUFTM0Q7Z0JBQ1Q0RCxVQUFVL0MsY0FBY0U7Z0JBQ3hCMkMsV0FBVTtnQkFDVkcsT0FBTztvQkFBRUMsYUFBYTtvQkFBV0MsWUFBWTtnQkFBVTswQkFDeEQ7Ozs7OzswQkFHRCw4REFBQzlFLG1HQUFNQTtnQkFDTCtFLE1BQUs7Z0JBQ0xDLFNBQVNwRCxjQUFjRTtnQkFDdkI0QyxTQUFTLElBQU1yRCxLQUFLNEQsTUFBTTswQkFFekIxRCxhQUFhLFdBQVc7Ozs7Ozs7Ozs7OztJQUsvQixxQkFDRSw4REFBQ2xCLG1FQUFZQTtRQUNYUyxRQUFRQTtRQUNSQyxTQUFTQTtRQUNUbUUsT0FBT1o7UUFDUGEsT0FBTTtRQUNOQyxRQUFRYjtrQkFFUiw0RUFBQ0M7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ1k7NEJBQUdaLFdBQVU7c0NBQ1hsRCwyQkFDQzs7a0RBQ0UsOERBQUNmLDRJQUFnQkE7d0NBQUNpRSxXQUFVOzs7Ozs7b0NBQVM7OzZEQUl2Qzs7a0RBQ0UsOERBQUNqRSw0SUFBZ0JBO3dDQUFDaUUsV0FBVTs7Ozs7O29DQUFTOzs7Ozs7OztzQ0FLM0MsOERBQUNqQjs0QkFBRWlCLFdBQVU7c0NBQ1ZsRCxhQUNHLGdDQUNBOzs7Ozs7Ozs7Ozs7OEJBS1IsOERBQUNpRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNhOzRCQUFLYixXQUFVO3NDQUFvQjs7Ozs7O3dCQUFROzs7Ozs7OzhCQUc5Qyw4REFBQzFFLGtHQUFJQTtvQkFDSHNCLE1BQU1BO29CQUNOa0UsUUFBTztvQkFDUEMsVUFBVXZCO29CQUNWUSxXQUFVO29CQUNWZ0IsY0FBYztvQkFDZEMsZ0JBQWdCLENBQUNDLEdBQUd6Qjt3QkFDbEIsSUFBSSxjQUFjQSxVQUFVLGVBQWVBLFFBQVE7NEJBQ2pEdkI7d0JBQ0Y7b0JBQ0Y7O3NDQUVBLDhEQUFDNUMsa0dBQUlBLENBQUM2RixJQUFJOzRCQUNSbkMsTUFBSzs0QkFDTG9DLHFCQUFPLDhEQUFDUDtnQ0FBS2IsV0FBVTs7a0RBQW9CLDhEQUFDakUsNElBQWdCQTt3Q0FBQ2lFLFdBQVU7Ozs7OztvQ0FBUzs7Ozs7Ozs0QkFDaEZxQixPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNQyxTQUFTO2dDQUEwQjs2QkFBRTs0QkFDL0RDLFNBQVE7c0NBRVIsNEVBQUMvRixrR0FBTUE7Z0NBQ0xnRyxhQUFZO2dDQUNaQyxVQUFVO2dDQUNWQyxrQkFBaUI7MENBRWhCMUQsU0FBUzJELEdBQUcsQ0FBQzNDLENBQUFBLHdCQUNaLDhEQUFDOUM7d0NBQXdCMEYsT0FBTzVDLFFBQVFLLEVBQUU7a0RBQUdMLFFBQVFELElBQUk7dUNBQTVDQyxRQUFRSyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7c0NBSzdCLDhEQUFDaEUsa0dBQUlBLENBQUM2RixJQUFJOzRCQUNSbkMsTUFBSzs0QkFDTG9DLHFCQUFPLDhEQUFDUDtnQ0FBS2IsV0FBVTs7a0RBQW9CLDhEQUFDL0QsNElBQVlBO3dDQUFDK0QsV0FBVTs7Ozs7O29DQUFTOzs7Ozs7OzRCQUM1RXdCLFNBQVE7c0NBRVIsNEVBQUMvRixrR0FBTUE7Z0NBQ0xnRyxhQUFZO2dDQUNaSyxVQUFVO2dDQUNWSixVQUFVO2dDQUNWQyxrQkFBaUI7MENBRWhCL0QsVUFBVWdFLEdBQUcsQ0FBQ3hDLENBQUFBLHlCQUNiLDhEQUFDakQ7d0NBQXlCMEYsT0FBT3pDLFNBQVNFLEVBQUU7a0RBQUdGLFNBQVNKLElBQUk7dUNBQS9DSSxTQUFTRSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7c0NBSzlCLDhEQUFDaEUsa0dBQUlBLENBQUM2RixJQUFJOzRCQUNSbkMsTUFBSzs0QkFDTG9DLHFCQUFPLDhEQUFDUDtnQ0FBS2IsV0FBVTs7a0RBQW9CLDhEQUFDOUQsNElBQWNBO3dDQUFDOEQsV0FBVTs7Ozs7O29DQUFTOzs7Ozs7OzRCQUM5RXFCLE9BQU87Z0NBQ0w7b0NBQUVDLFVBQVU7b0NBQU1DLFNBQVM7Z0NBQXdCO2dDQUNuRDtvQ0FBRWpCLE1BQU07b0NBQVV5QixLQUFLO29DQUFHUixTQUFTO2dDQUE4Qjs2QkFDbEU7NEJBQ0RDLFNBQVE7c0NBRVIsNEVBQUNoRyxtR0FBV0E7Z0NBQ1Z1RyxLQUFLO2dDQUNMNUIsT0FBTztvQ0FBRU8sT0FBTztnQ0FBTztnQ0FDdkJlLGFBQVk7Ozs7Ozs7Ozs7O3NDQUloQiw4REFBQ25HLGtHQUFJQSxDQUFDNkYsSUFBSTs0QkFDUm5DLE1BQUs7NEJBQ0xvQyxxQkFBTyw4REFBQ1A7Z0NBQUtiLFdBQVU7O2tEQUFvQiw4REFBQ2hFLDRJQUFjQTt3Q0FBQ2dFLFdBQVU7Ozs7OztvQ0FBUzs7Ozs7Ozs0QkFDOUVxQixPQUFPO2dDQUNMO29DQUFFQyxVQUFVO29DQUFNQyxTQUFTO2dDQUEwQjtnQ0FDckQ7b0NBQUVqQixNQUFNO29DQUFVeUIsS0FBSztvQ0FBTVIsU0FBUztnQ0FBb0M7NkJBQzNFOzRCQUNEQyxTQUFRO3NDQUVSLDRFQUFDaEcsbUdBQVdBO2dDQUNWdUcsS0FBSztnQ0FDTEMsTUFBTTtnQ0FDTjdCLE9BQU87b0NBQUVPLE9BQU87Z0NBQU87Z0NBQ3ZCZSxhQUFZO2dDQUNaUSxXQUFXSixDQUFBQSxRQUFTLE9BQWEsT0FBTkEsT0FBUUssT0FBTyxDQUFDLHlCQUF5QjtnQ0FDcEVDLFFBQVFOLENBQUFBLFFBQVNPLFdBQVdQLE1BQU9LLE9BQU8sQ0FBQyxnQkFBZ0I7Ozs7Ozs7Ozs7O3NDQUkvRCw4REFBQzVHLGtHQUFJQSxDQUFDNkYsSUFBSTs0QkFDUm5DLE1BQUs7NEJBQ0xvQyxxQkFBTyw4REFBQ1A7Z0NBQUtiLFdBQVU7O2tEQUFvQiw4REFBQ2hFLDRJQUFjQTt3Q0FBQ2dFLFdBQVU7Ozs7OztvQ0FBUzs7Ozs7Ozs0QkFDOUV3QixTQUFRO3NDQUVSLDRFQUFDaEcsbUdBQVdBO2dDQUNWMEUsUUFBUTtnQ0FDUkMsT0FBTztvQ0FBRU8sT0FBTztnQ0FBTztnQ0FDdkJtQixPQUFPOUU7Z0NBQ1BrRixXQUFXSixDQUFBQSxRQUFTLE9BQWEsT0FBTkEsT0FBUUssT0FBTyxDQUFDLHlCQUF5QjtnQ0FDcEVDLFFBQVFOLENBQUFBLFFBQVNBLE1BQU9LLE9BQU8sQ0FBQyxnQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPOUQ7R0F4UU05Rjs7UUFPV2Qsa0dBQUlBLENBQUN1QjtRQUtpQ2hCLGlGQUFpQkE7UUFDL0JDLGlGQUFpQkE7UUFHT0osbUZBQXVCQTtRQVF6QkMsaUZBQXNCQTs7O0tBeEIvRVM7QUEwUU4saUVBQWVBLGlCQUFpQkEsRUFBQyIsInNvdXJjZXMiOlsiRTpcXFBST0pFQ1RTXFxwb3NcXHBvc2Zyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXFB1cmNoYXNlc1xcUHVyY2hhc2VGb3JtUGFuZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgRm9ybSwgSW5wdXQsIEJ1dHRvbiwgSW5wdXROdW1iZXIsIFNlbGVjdCB9IGZyb20gXCJhbnRkXCI7XG5pbXBvcnQgeyBQdXJjaGFzZSwgQ3JlYXRlUHVyY2hhc2VEdG8sIFVwZGF0ZVB1cmNoYXNlRHRvIH0gZnJvbSBcIkAvcmVkdXhSVEsvc2VydmljZXMvcHVyY2hhc2VBcGlcIjtcbmltcG9ydCB7IHVzZUdldEFsbFN1cHBsaWVyc1F1ZXJ5LCBTdXBwbGllciB9IGZyb20gXCJAL3JlZHV4UlRLL3NlcnZpY2VzL3N1cHBsaWVyQXBpXCI7XG5pbXBvcnQgeyB1c2VHZXRBbGxQcm9kdWN0c1F1ZXJ5LCBQcm9kdWN0IH0gZnJvbSBcIkAvcmVkdXhSVEsvc2VydmljZXMvcHJvZHVjdEFwaVwiO1xuaW1wb3J0IFNsaWRpbmdQYW5lbCBmcm9tIFwiQC9jb21wb25lbnRzL3VpL1NsaWRpbmdQYW5lbFwiO1xuaW1wb3J0IHsgdXNlUHVyY2hhc2VDcmVhdGUgfSBmcm9tIFwiQC9ob29rcy9wdXJjaGFzZXMvdXNlUHVyY2hhc2VDcmVhdGVcIjtcbmltcG9ydCB7IHVzZVB1cmNoYXNlVXBkYXRlIH0gZnJvbSBcIkAvaG9va3MvcHVyY2hhc2VzL3VzZVB1cmNoYXNlVXBkYXRlXCI7XG5pbXBvcnQgeyBVc2VyIH0gZnJvbSBcIkAvdHlwZXMvdXNlclwiO1xuaW1wb3J0IHsgU2hvcHBpbmdPdXRsaW5lZCwgRG9sbGFyT3V0bGluZWQsIFVzZXJPdXRsaW5lZCwgTnVtYmVyT3V0bGluZWQgfSBmcm9tIFwiQGFudC1kZXNpZ24vaWNvbnNcIjtcbmltcG9ydCBcIi4vcHVyY2hhc2UtcGFuZWxzLmNzc1wiO1xuXG5jb25zdCB7IE9wdGlvbiB9ID0gU2VsZWN0O1xuXG5pbnRlcmZhY2UgUHVyY2hhc2VGb3JtUGFuZWxQcm9wcyB7XG4gIGlzT3BlbjogYm9vbGVhbjtcbiAgb25DbG9zZTogKCkgPT4gdm9pZDtcbiAgb25TdWNjZXNzOiAoKSA9PiB2b2lkO1xuICBwdXJjaGFzZT86IFB1cmNoYXNlIHwgbnVsbDtcbiAgY3VycmVudFVzZXI/OiBVc2VyIHwgbnVsbDtcbn1cblxuY29uc3QgUHVyY2hhc2VGb3JtUGFuZWw6IFJlYWN0LkZDPFB1cmNoYXNlRm9ybVBhbmVsUHJvcHM+ID0gKHtcbiAgaXNPcGVuLFxuICBvbkNsb3NlLFxuICBvblN1Y2Nlc3MsXG4gIHB1cmNoYXNlLFxuICBjdXJyZW50VXNlcixcbn0pID0+IHtcbiAgY29uc3QgW2Zvcm1dID0gRm9ybS51c2VGb3JtKCk7XG4gIGNvbnN0IGlzRWRpdE1vZGUgPSAhIXB1cmNoYXNlO1xuICBjb25zdCBbdG90YWxDb3N0LCBzZXRUb3RhbENvc3RdID0gdXNlU3RhdGU8c3RyaW5nPihcIjBcIik7XG5cbiAgLy8gSG9va3MgZm9yIGNyZWF0aW5nIGFuZCB1cGRhdGluZyBwdXJjaGFzZXNcbiAgY29uc3QgeyBjcmVhdGVQdXJjaGFzZSwgaXNTdWJtaXR0aW5nOiBpc0NyZWF0aW5nIH0gPSB1c2VQdXJjaGFzZUNyZWF0ZShvblN1Y2Nlc3MpO1xuICBjb25zdCB7IHVwZGF0ZVB1cmNoYXNlLCBpc1VwZGF0aW5nIH0gPSB1c2VQdXJjaGFzZVVwZGF0ZShvblN1Y2Nlc3MpO1xuXG4gIC8vIEZldGNoIHN1cHBsaWVycyBmb3IgZHJvcGRvd24gLSBBbHdheXMgZmV0Y2ggd2hlbiBjb21wb25lbnQgbW91bnRzXG4gIGNvbnN0IHsgZGF0YTogc3VwcGxpZXJzUmVzcG9uc2UsIHJlZmV0Y2g6IHJlZmV0Y2hTdXBwbGllcnMgfSA9IHVzZUdldEFsbFN1cHBsaWVyc1F1ZXJ5KHt9LCB7XG4gICAgcmVmZXRjaE9uTW91bnRPckFyZ0NoYW5nZTogdHJ1ZSxcbiAgICByZWZldGNoT25Gb2N1czogZmFsc2UsXG4gICAgcmVmZXRjaE9uUmVjb25uZWN0OiB0cnVlLFxuICB9KTtcbiAgY29uc3Qgc3VwcGxpZXJzOiBTdXBwbGllcltdID0gc3VwcGxpZXJzUmVzcG9uc2U/LmRhdGE/LnN1cHBsaWVycyB8fCBbXTtcblxuICAvLyBGZXRjaCBwcm9kdWN0cyBmb3IgZHJvcGRvd24gLSBBbHdheXMgZmV0Y2ggd2hlbiBjb21wb25lbnQgbW91bnRzXG4gIGNvbnN0IHsgZGF0YTogcHJvZHVjdHNSZXNwb25zZSwgcmVmZXRjaDogcmVmZXRjaFByb2R1Y3RzIH0gPSB1c2VHZXRBbGxQcm9kdWN0c1F1ZXJ5KHtcbiAgICBwYWdlOiAxLFxuICAgIGxpbWl0OiAxMDAwLFxuICAgIHNlYXJjaDogJydcbiAgfSwge1xuICAgIHJlZmV0Y2hPbk1vdW50T3JBcmdDaGFuZ2U6IHRydWUsXG4gICAgcmVmZXRjaE9uRm9jdXM6IGZhbHNlLFxuICAgIHJlZmV0Y2hPblJlY29ubmVjdDogdHJ1ZSxcbiAgfSk7XG4gIGNvbnN0IHByb2R1Y3RzOiBQcm9kdWN0W10gPSBwcm9kdWN0c1Jlc3BvbnNlPy5kYXRhPy5wcm9kdWN0cyB8fCBbXTtcblxuICAvLyBDYWxjdWxhdGUgdG90YWwgY29zdCB3aGVuIHF1YW50aXR5IG9yIGNvc3QgcHJpY2UgY2hhbmdlc1xuICBjb25zdCBjYWxjdWxhdGVUb3RhbENvc3QgPSAoKSA9PiB7XG4gICAgY29uc3QgcXVhbnRpdHkgPSBmb3JtLmdldEZpZWxkVmFsdWUoJ3F1YW50aXR5JykgfHwgMDtcbiAgICBjb25zdCBjb3N0UHJpY2UgPSBmb3JtLmdldEZpZWxkVmFsdWUoJ2Nvc3RQcmljZScpIHx8IDA7XG4gICAgLy8gQ2FsY3VsYXRlIHRvdGFsIGFuZCBlbnN1cmUgaXQncyBhIHN0cmluZyB3aXRoIDIgZGVjaW1hbCBwbGFjZXNcbiAgICBjb25zdCB0b3RhbCA9IChxdWFudGl0eSAqIGNvc3RQcmljZSkudG9GaXhlZCgyKTtcbiAgICBzZXRUb3RhbENvc3QodG90YWwpO1xuICAgIC8vIFNldCB0aGUgZm9ybSB2YWx1ZSBhcyBhIHN0cmluZyB0byBtYXRjaCB0aGUgZXhwZWN0ZWQgdHlwZVxuICAgIGZvcm0uc2V0RmllbGRzVmFsdWUoeyB0b3RhbENvc3Q6IHRvdGFsIH0pO1xuICB9O1xuXG4gIC8vIEhhbmRsZSBwYW5lbCBvcGVuL2Nsb3NlIGFuZCBkYXRhIGZldGNoaW5nXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGlzT3Blbikge1xuICAgICAgLy8gV2hlbiBwYW5lbCBvcGVucywgZW5zdXJlIHdlIGhhdmUgZnJlc2ggZGF0YVxuICAgICAgY29uc29sZS5sb2coJ/Cfm5IgUHVyY2hhc2UgcGFuZWwgb3BlbmVkIC0gZmV0Y2hpbmcgZnJlc2ggZGF0YScpO1xuICAgICAgcmVmZXRjaFByb2R1Y3RzKCk7XG4gICAgICByZWZldGNoU3VwcGxpZXJzKCk7XG4gICAgICBmb3JtLnJlc2V0RmllbGRzKCk7XG5cbiAgICAgIGlmICghcHVyY2hhc2UpIHtcbiAgICAgICAgc2V0VG90YWxDb3N0KFwiMC4wMFwiKTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFtmb3JtLCBpc09wZW4sIHJlZmV0Y2hQcm9kdWN0cywgcmVmZXRjaFN1cHBsaWVyc10pO1xuXG4gIC8vIFNlcGFyYXRlIGVmZmVjdCB0byBwb3B1bGF0ZSBmb3JtIHdoZW4gcHVyY2hhc2UgZGF0YSBhbmQgZHJvcGRvd24gZGF0YSBhcmUgYXZhaWxhYmxlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGlzT3BlbiAmJiBwdXJjaGFzZSAmJiBwcm9kdWN0cy5sZW5ndGggPiAwICYmIHN1cHBsaWVycy5sZW5ndGggPiAwKSB7XG4gICAgICAvLyBGb3IgZWRpdCBtb2RlLCB3ZSBuZWVkIHRvIGZpbmQgdGhlIHByb2R1Y3QgYW5kIHN1cHBsaWVyIElEcyBieSBuYW1lXG4gICAgICAvLyBzaW5jZSB0aGUgYmFja2VuZCByZXR1cm5zIG5hbWVzLCBub3QgSURzXG4gICAgICBjb25zdCBzZWxlY3RlZFByb2R1Y3QgPSBwcm9kdWN0cy5maW5kKHAgPT4gcC5uYW1lID09PSBwdXJjaGFzZS5wcm9kdWN0KTtcbiAgICAgIGNvbnN0IHNlbGVjdGVkU3VwcGxpZXIgPSBzdXBwbGllcnMuZmluZChzID0+IHMubmFtZSA9PT0gcHVyY2hhc2Uuc3VwcGxpZXIpO1xuXG4gICAgICBmb3JtLnNldEZpZWxkc1ZhbHVlKHtcbiAgICAgICAgcHJvZHVjdElkOiBzZWxlY3RlZFByb2R1Y3Q/LmlkLFxuICAgICAgICBzdXBwbGllcklkOiBzZWxlY3RlZFN1cHBsaWVyPy5pZCxcbiAgICAgICAgcXVhbnRpdHk6IHB1cmNoYXNlLnF1YW50aXR5LFxuICAgICAgICBjb3N0UHJpY2U6IHB1cmNoYXNlLmNvc3RQcmljZSxcbiAgICAgICAgdG90YWxDb3N0OiBwdXJjaGFzZS50b3RhbENvc3QsXG4gICAgICB9KTtcbiAgICAgIHNldFRvdGFsQ29zdChwdXJjaGFzZS50b3RhbENvc3QpO1xuICAgIH1cbiAgfSwgW2lzT3BlbiwgcHVyY2hhc2UsIHByb2R1Y3RzLCBzdXBwbGllcnMsIGZvcm1dKTtcblxuICAvLyBIYW5kbGUgZm9ybSBzdWJtaXNzaW9uXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IGFzeW5jICh2YWx1ZXM6IGFueSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBDb252ZXJ0IG51bWVyaWMgdmFsdWVzIHRvIHN0cmluZ3MgYXMgcmVxdWlyZWQgYnkgdGhlIEFQSVxuICAgICAgY29uc3QgZm9ybWF0dGVkVmFsdWVzID0ge1xuICAgICAgICAuLi52YWx1ZXMsXG4gICAgICAgIC8vIEVuc3VyZSBjb3N0UHJpY2UgaXMgYSBzdHJpbmcgKGJhY2tlbmQgZXhwZWN0cyBzdHJpbmcpXG4gICAgICAgIGNvc3RQcmljZTogdmFsdWVzLmNvc3RQcmljZT8udG9TdHJpbmcoKSB8fCBcIjBcIixcbiAgICAgICAgLy8gRW5zdXJlIHRvdGFsQ29zdCBpcyBhIHN0cmluZyAoYmFja2VuZCBleHBlY3RzIHN0cmluZylcbiAgICAgICAgdG90YWxDb3N0OiB2YWx1ZXMudG90YWxDb3N0Py50b1N0cmluZygpIHx8IFwiMFwiXG4gICAgICB9O1xuXG4gICAgICBjb25zb2xlLmxvZyhcIlN1Ym1pdHRpbmcgcHVyY2hhc2Ugd2l0aCBmb3JtYXR0ZWQgdmFsdWVzOlwiLCBmb3JtYXR0ZWRWYWx1ZXMpO1xuXG4gICAgICBpZiAoaXNFZGl0TW9kZSAmJiBwdXJjaGFzZSkge1xuICAgICAgICAvLyBVcGRhdGUgZXhpc3RpbmcgcHVyY2hhc2VcbiAgICAgICAgYXdhaXQgdXBkYXRlUHVyY2hhc2UocHVyY2hhc2UuaWQsIGZvcm1hdHRlZFZhbHVlcyBhcyBVcGRhdGVQdXJjaGFzZUR0byk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBDcmVhdGUgbmV3IHB1cmNoYXNlXG4gICAgICAgIGF3YWl0IGNyZWF0ZVB1cmNoYXNlKGZvcm1hdHRlZFZhbHVlcyBhcyBDcmVhdGVQdXJjaGFzZUR0byk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJGYWlsZWQgdG8gc2F2ZSBwdXJjaGFzZTpcIiwgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyBQYW5lbCB0aXRsZVxuICBjb25zdCBwYW5lbFRpdGxlID0gaXNFZGl0TW9kZSA/IFwiRWRpdCBQdXJjaGFzZVwiIDogXCJBZGQgUHVyY2hhc2VcIjtcblxuICAvLyBQYW5lbCBmb290ZXIgd2l0aCBhY3Rpb24gYnV0dG9uc1xuICBjb25zdCBwYW5lbEZvb3RlciA9IChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgc3BhY2UteC0yXCI+XG4gICAgICA8QnV0dG9uXG4gICAgICAgIG9uQ2xpY2s9e29uQ2xvc2V9XG4gICAgICAgIGRpc2FibGVkPXtpc0NyZWF0aW5nIHx8IGlzVXBkYXRpbmd9XG4gICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgaG92ZXI6dGV4dC1ncmF5LTkwMFwiXG4gICAgICAgIHN0eWxlPXt7IGJvcmRlckNvbG9yOiAnI2Q5ZDlkOScsIGJhY2tncm91bmQ6ICcjZjVmNWY1JyB9fVxuICAgICAgPlxuICAgICAgICBDYW5jZWxcbiAgICAgIDwvQnV0dG9uPlxuICAgICAgPEJ1dHRvblxuICAgICAgICB0eXBlPVwicHJpbWFyeVwiXG4gICAgICAgIGxvYWRpbmc9e2lzQ3JlYXRpbmcgfHwgaXNVcGRhdGluZ31cbiAgICAgICAgb25DbGljaz17KCkgPT4gZm9ybS5zdWJtaXQoKX1cbiAgICAgID5cbiAgICAgICAge2lzRWRpdE1vZGUgPyBcIlVwZGF0ZVwiIDogXCJTYXZlXCJ9XG4gICAgICA8L0J1dHRvbj5cbiAgICA8L2Rpdj5cbiAgKTtcblxuICByZXR1cm4gKFxuICAgIDxTbGlkaW5nUGFuZWxcbiAgICAgIGlzT3Blbj17aXNPcGVufVxuICAgICAgb25DbG9zZT17b25DbG9zZX1cbiAgICAgIHRpdGxlPXtwYW5lbFRpdGxlfVxuICAgICAgd2lkdGg9XCI1MDBweFwiXG4gICAgICBmb290ZXI9e3BhbmVsRm9vdGVyfVxuICAgID5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgIHsvKiBGb3JtIGhlYWRpbmcgd2l0aCBpY29uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIHBiLTRcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmF5LTgwMCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAge2lzRWRpdE1vZGUgPyAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgPFNob3BwaW5nT3V0bGluZWQgY2xhc3NOYW1lPVwibXItMlwiIC8+XG4gICAgICAgICAgICAgICAgRWRpdCBQdXJjaGFzZVxuICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgPFNob3BwaW5nT3V0bGluZWQgY2xhc3NOYW1lPVwibXItMlwiIC8+XG4gICAgICAgICAgICAgICAgQWRkIE5ldyBQdXJjaGFzZVxuICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9oMj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG10LTFcIj5cbiAgICAgICAgICAgIHtpc0VkaXRNb2RlXG4gICAgICAgICAgICAgID8gXCJVcGRhdGUgcHVyY2hhc2UgaW5mb3JtYXRpb25cIlxuICAgICAgICAgICAgICA6IFwiRmlsbCBpbiB0aGUgZGV0YWlscyB0byBhZGQgYSBuZXcgcHVyY2hhc2VcIn1cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBSZXF1aXJlZCBmaWVsZHMgaW5kaWNhdG9yICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTQgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwIG1yLTFcIj4qPC9zcGFuPiBpbmRpY2F0ZXMgcmVxdWlyZWQgZmllbGRzXG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxGb3JtXG4gICAgICAgICAgZm9ybT17Zm9ybX1cbiAgICAgICAgICBsYXlvdXQ9XCJ2ZXJ0aWNhbFwiXG4gICAgICAgICAgb25GaW5pc2g9e2hhbmRsZVN1Ym1pdH1cbiAgICAgICAgICBjbGFzc05hbWU9XCJwdXJjaGFzZS1mb3JtXCJcbiAgICAgICAgICByZXF1aXJlZE1hcms9e3RydWV9XG4gICAgICAgICAgb25WYWx1ZXNDaGFuZ2U9eyhfLCB2YWx1ZXMpID0+IHtcbiAgICAgICAgICAgIGlmICgncXVhbnRpdHknIGluIHZhbHVlcyB8fCAnY29zdFByaWNlJyBpbiB2YWx1ZXMpIHtcbiAgICAgICAgICAgICAgY2FsY3VsYXRlVG90YWxDb3N0KCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfX1cbiAgICAgICAgPlxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJwcm9kdWN0SWRcIlxuICAgICAgICAgICAgbGFiZWw9ezxzcGFuIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+PFNob3BwaW5nT3V0bGluZWQgY2xhc3NOYW1lPVwibXItMVwiIC8+IFByb2R1Y3Q8L3NwYW4+fVxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiBcIlBsZWFzZSBzZWxlY3QgYSBwcm9kdWN0XCIgfV19XG4gICAgICAgICAgICB0b29sdGlwPVwiU2VsZWN0IHRoZSBwcm9kdWN0IHlvdSBhcmUgcHVyY2hhc2luZ1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFNlbGVjdFxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlbGVjdCBhIHByb2R1Y3RcIlxuICAgICAgICAgICAgICBzaG93U2VhcmNoXG4gICAgICAgICAgICAgIG9wdGlvbkZpbHRlclByb3A9XCJjaGlsZHJlblwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtwcm9kdWN0cy5tYXAocHJvZHVjdCA9PiAoXG4gICAgICAgICAgICAgICAgPE9wdGlvbiBrZXk9e3Byb2R1Y3QuaWR9IHZhbHVlPXtwcm9kdWN0LmlkfT57cHJvZHVjdC5uYW1lfTwvT3B0aW9uPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cInN1cHBsaWVySWRcIlxuICAgICAgICAgICAgbGFiZWw9ezxzcGFuIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+PFVzZXJPdXRsaW5lZCBjbGFzc05hbWU9XCJtci0xXCIgLz4gU3VwcGxpZXI8L3NwYW4+fVxuICAgICAgICAgICAgdG9vbHRpcD1cIlNlbGVjdCB0aGUgc3VwcGxpZXIgKG9wdGlvbmFsKVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFNlbGVjdFxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlbGVjdCBhIHN1cHBsaWVyIChvcHRpb25hbClcIlxuICAgICAgICAgICAgICBhbGxvd0NsZWFyXG4gICAgICAgICAgICAgIHNob3dTZWFyY2hcbiAgICAgICAgICAgICAgb3B0aW9uRmlsdGVyUHJvcD1cImNoaWxkcmVuXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge3N1cHBsaWVycy5tYXAoc3VwcGxpZXIgPT4gKFxuICAgICAgICAgICAgICAgIDxPcHRpb24ga2V5PXtzdXBwbGllci5pZH0gdmFsdWU9e3N1cHBsaWVyLmlkfT57c3VwcGxpZXIubmFtZX08L09wdGlvbj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJxdWFudGl0eVwiXG4gICAgICAgICAgICBsYWJlbD17PHNwYW4gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj48TnVtYmVyT3V0bGluZWQgY2xhc3NOYW1lPVwibXItMVwiIC8+IFF1YW50aXR5PC9zcGFuPn1cbiAgICAgICAgICAgIHJ1bGVzPXtbXG4gICAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6IFwiUGxlYXNlIGVudGVyIHF1YW50aXR5XCIgfSxcbiAgICAgICAgICAgICAgeyB0eXBlOiAnbnVtYmVyJywgbWluOiAxLCBtZXNzYWdlOiBcIlF1YW50aXR5IG11c3QgYmUgYXQgbGVhc3QgMVwiIH1cbiAgICAgICAgICAgIF19XG4gICAgICAgICAgICB0b29sdGlwPVwiVGhlIHF1YW50aXR5IG9mIHByb2R1Y3RzIHB1cmNoYXNlZFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPElucHV0TnVtYmVyXG4gICAgICAgICAgICAgIG1pbj17MX1cbiAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6ICcxMDAlJyB9fVxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHF1YW50aXR5XCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwiY29zdFByaWNlXCJcbiAgICAgICAgICAgIGxhYmVsPXs8c3BhbiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPjxEb2xsYXJPdXRsaW5lZCBjbGFzc05hbWU9XCJtci0xXCIgLz4gQ29zdCBQcmljZSAoR0hTKTwvc3Bhbj59XG4gICAgICAgICAgICBydWxlcz17W1xuICAgICAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiBcIlBsZWFzZSBlbnRlciBjb3N0IHByaWNlXCIgfSxcbiAgICAgICAgICAgICAgeyB0eXBlOiAnbnVtYmVyJywgbWluOiAwLjAxLCBtZXNzYWdlOiBcIkNvc3QgcHJpY2UgbXVzdCBiZSBncmVhdGVyIHRoYW4gMFwiIH1cbiAgICAgICAgICAgIF19XG4gICAgICAgICAgICB0b29sdGlwPVwiVGhlIGNvc3QgcHJpY2UgcGVyIHVuaXRcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxJbnB1dE51bWJlclxuICAgICAgICAgICAgICBtaW49ezAuMDF9XG4gICAgICAgICAgICAgIHN0ZXA9ezAuMDF9XG4gICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiAnMTAwJScgfX1cbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBjb3N0IHByaWNlXCJcbiAgICAgICAgICAgICAgZm9ybWF0dGVyPXt2YWx1ZSA9PiBgR0hTICR7dmFsdWV9YC5yZXBsYWNlKC9cXEIoPz0oXFxkezN9KSsoPyFcXGQpKS9nLCAnLCcpfVxuICAgICAgICAgICAgICBwYXJzZXI9e3ZhbHVlID0+IHBhcnNlRmxvYXQodmFsdWUhLnJlcGxhY2UoL0dIU1xccz98KCwqKS9nLCAnJykpIGFzIGFueX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwidG90YWxDb3N0XCJcbiAgICAgICAgICAgIGxhYmVsPXs8c3BhbiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPjxEb2xsYXJPdXRsaW5lZCBjbGFzc05hbWU9XCJtci0xXCIgLz4gVG90YWwgQ29zdCAoR0hTKTwvc3Bhbj59XG4gICAgICAgICAgICB0b29sdGlwPVwiVGhlIHRvdGFsIGNvc3QgKGNhbGN1bGF0ZWQgYXV0b21hdGljYWxseSlcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxJbnB1dE51bWJlclxuICAgICAgICAgICAgICBkaXNhYmxlZFxuICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogJzEwMCUnIH19XG4gICAgICAgICAgICAgIHZhbHVlPXt0b3RhbENvc3R9XG4gICAgICAgICAgICAgIGZvcm1hdHRlcj17dmFsdWUgPT4gYEdIUyAke3ZhbHVlfWAucmVwbGFjZSgvXFxCKD89KFxcZHszfSkrKD8hXFxkKSkvZywgJywnKX1cbiAgICAgICAgICAgICAgcGFyc2VyPXt2YWx1ZSA9PiB2YWx1ZSEucmVwbGFjZSgvR0hTXFxzP3woLCopL2csICcnKX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG4gICAgICAgIDwvRm9ybT5cbiAgICAgIDwvZGl2PlxuICAgIDwvU2xpZGluZ1BhbmVsPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgUHVyY2hhc2VGb3JtUGFuZWw7XG5cblxuXG5cblxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJGb3JtIiwiQnV0dG9uIiwiSW5wdXROdW1iZXIiLCJTZWxlY3QiLCJ1c2VHZXRBbGxTdXBwbGllcnNRdWVyeSIsInVzZUdldEFsbFByb2R1Y3RzUXVlcnkiLCJTbGlkaW5nUGFuZWwiLCJ1c2VQdXJjaGFzZUNyZWF0ZSIsInVzZVB1cmNoYXNlVXBkYXRlIiwiU2hvcHBpbmdPdXRsaW5lZCIsIkRvbGxhck91dGxpbmVkIiwiVXNlck91dGxpbmVkIiwiTnVtYmVyT3V0bGluZWQiLCJPcHRpb24iLCJQdXJjaGFzZUZvcm1QYW5lbCIsImlzT3BlbiIsIm9uQ2xvc2UiLCJvblN1Y2Nlc3MiLCJwdXJjaGFzZSIsImN1cnJlbnRVc2VyIiwic3VwcGxpZXJzUmVzcG9uc2UiLCJwcm9kdWN0c1Jlc3BvbnNlIiwiZm9ybSIsInVzZUZvcm0iLCJpc0VkaXRNb2RlIiwidG90YWxDb3N0Iiwic2V0VG90YWxDb3N0IiwiY3JlYXRlUHVyY2hhc2UiLCJpc1N1Ym1pdHRpbmciLCJpc0NyZWF0aW5nIiwidXBkYXRlUHVyY2hhc2UiLCJpc1VwZGF0aW5nIiwiZGF0YSIsInJlZmV0Y2giLCJyZWZldGNoU3VwcGxpZXJzIiwicmVmZXRjaE9uTW91bnRPckFyZ0NoYW5nZSIsInJlZmV0Y2hPbkZvY3VzIiwicmVmZXRjaE9uUmVjb25uZWN0Iiwic3VwcGxpZXJzIiwicmVmZXRjaFByb2R1Y3RzIiwicGFnZSIsImxpbWl0Iiwic2VhcmNoIiwicHJvZHVjdHMiLCJjYWxjdWxhdGVUb3RhbENvc3QiLCJxdWFudGl0eSIsImdldEZpZWxkVmFsdWUiLCJjb3N0UHJpY2UiLCJ0b3RhbCIsInRvRml4ZWQiLCJzZXRGaWVsZHNWYWx1ZSIsImNvbnNvbGUiLCJsb2ciLCJyZXNldEZpZWxkcyIsImxlbmd0aCIsInNlbGVjdGVkUHJvZHVjdCIsImZpbmQiLCJwIiwibmFtZSIsInByb2R1Y3QiLCJzZWxlY3RlZFN1cHBsaWVyIiwicyIsInN1cHBsaWVyIiwicHJvZHVjdElkIiwiaWQiLCJzdXBwbGllcklkIiwiaGFuZGxlU3VibWl0IiwidmFsdWVzIiwiZm9ybWF0dGVkVmFsdWVzIiwidG9TdHJpbmciLCJlcnJvciIsInBhbmVsVGl0bGUiLCJwYW5lbEZvb3RlciIsImRpdiIsImNsYXNzTmFtZSIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsInN0eWxlIiwiYm9yZGVyQ29sb3IiLCJiYWNrZ3JvdW5kIiwidHlwZSIsImxvYWRpbmciLCJzdWJtaXQiLCJ0aXRsZSIsIndpZHRoIiwiZm9vdGVyIiwiaDIiLCJzcGFuIiwibGF5b3V0Iiwib25GaW5pc2giLCJyZXF1aXJlZE1hcmsiLCJvblZhbHVlc0NoYW5nZSIsIl8iLCJJdGVtIiwibGFiZWwiLCJydWxlcyIsInJlcXVpcmVkIiwibWVzc2FnZSIsInRvb2x0aXAiLCJwbGFjZWhvbGRlciIsInNob3dTZWFyY2giLCJvcHRpb25GaWx0ZXJQcm9wIiwibWFwIiwidmFsdWUiLCJhbGxvd0NsZWFyIiwibWluIiwic3RlcCIsImZvcm1hdHRlciIsInJlcGxhY2UiLCJwYXJzZXIiLCJwYXJzZUZsb2F0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Purchases/PurchaseFormPanel.tsx\n"));

/***/ })

});