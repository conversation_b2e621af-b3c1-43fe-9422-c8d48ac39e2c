{"version": 3, "sources": ["../../../src/server/typescript/utils.ts"], "sourcesContent": ["import path from 'path'\n\nimport type tsModule from 'typescript/lib/tsserverlibrary'\ntype TypeScript = typeof import('typescript/lib/tsserverlibrary')\n\nlet ts: TypeScript\nlet info: tsModule.server.PluginCreateInfo\nlet appDirRegExp: RegExp\n\nexport function log(message: string) {\n  info.project.projectService.logger.info(message)\n}\n\n// This function has to be called initially.\nexport function init(opts: {\n  ts: TypeScript\n  info: tsModule.server.PluginCreateInfo\n}) {\n  ts = opts.ts\n  info = opts.info\n  const projectDir = info.project.getCurrentDirectory()\n  appDirRegExp = new RegExp(\n    '^' + (projectDir + '(/src)?/app').replace(/[\\\\/]/g, '[\\\\/]')\n  )\n  log('Starting Next.js TypeScript plugin: ' + projectDir)\n}\n\nexport function getTs() {\n  return ts\n}\n\nexport function getInfo() {\n  return info\n}\n\nexport function getTypeChecker() {\n  return info.languageService.getProgram()?.getType<PERSON>hecker()\n}\n\nexport function getSource(fileName: string) {\n  return info.languageService.getProgram()?.getSourceFile(fileName)\n}\n\nexport function removeStringQuotes(str: string): string {\n  return str.replace(/^['\"`]|['\"`]$/g, '')\n}\n\nexport const isPositionInsideNode = (position: number, node: tsModule.Node) => {\n  const start = node.getFullStart()\n  return start <= position && position <= node.getFullWidth() + start\n}\n\nexport const isDefaultFunctionExport = (\n  node: tsModule.Node\n): node is tsModule.FunctionDeclaration => {\n  if (ts.isFunctionDeclaration(node)) {\n    let hasExportKeyword = false\n    let hasDefaultKeyword = false\n\n    if (node.modifiers) {\n      for (const modifier of node.modifiers) {\n        if (modifier.kind === ts.SyntaxKind.ExportKeyword) {\n          hasExportKeyword = true\n        } else if (modifier.kind === ts.SyntaxKind.DefaultKeyword) {\n          hasDefaultKeyword = true\n        }\n      }\n    }\n\n    // `export default function`\n    if (hasExportKeyword && hasDefaultKeyword) {\n      return true\n    }\n  }\n  return false\n}\n\nexport const isInsideApp = (filePath: string) => {\n  return appDirRegExp.test(filePath)\n}\nexport const isAppEntryFile = (filePath: string) => {\n  return (\n    appDirRegExp.test(filePath) &&\n    /^(page|layout)\\.(mjs|js|jsx|ts|tsx)$/.test(path.basename(filePath))\n  )\n}\nexport const isPageFile = (filePath: string) => {\n  return (\n    appDirRegExp.test(filePath) &&\n    /^page\\.(mjs|js|jsx|ts|tsx)$/.test(path.basename(filePath))\n  )\n}\n\n// Check if a module is a client entry.\nexport function getEntryInfo(\n  fileName: string,\n  throwOnInvalidDirective?: boolean\n) {\n  const source = getSource(fileName)\n  if (source) {\n    let isDirective = true\n    let isClientEntry = false\n    let isServerEntry = false\n\n    ts.forEachChild(source!, (node) => {\n      if (\n        ts.isExpressionStatement(node) &&\n        ts.isStringLiteral(node.expression)\n      ) {\n        if (node.expression.text === 'use client') {\n          if (isDirective) {\n            isClientEntry = true\n          } else {\n            if (throwOnInvalidDirective) {\n              const e = {\n                messageText:\n                  'The `\"use client\"` directive must be put at the top of the file.',\n                start: node.expression.getStart(),\n                length: node.expression.getWidth(),\n              }\n              throw e\n            }\n          }\n        } else if (node.expression.text === 'use server') {\n          if (isDirective) {\n            isServerEntry = true\n          } else {\n            if (throwOnInvalidDirective) {\n              const e = {\n                messageText:\n                  'The `\"use server\"` directive must be put at the top of the file.',\n                start: node.expression.getStart(),\n                length: node.expression.getWidth(),\n              }\n              throw e\n            }\n          }\n        }\n\n        if (isClientEntry && isServerEntry) {\n          const e = {\n            messageText:\n              'Cannot use both \"use client\" and \"use server\" directives in the same file.',\n            start: node.expression.getStart(),\n            length: node.expression.getWidth(),\n          }\n          throw e\n        }\n      } else {\n        isDirective = false\n      }\n    })\n\n    return { client: isClientEntry, server: isServerEntry }\n  }\n\n  return { client: false, server: false }\n}\n"], "names": ["path", "ts", "info", "appDirRegExp", "log", "message", "project", "projectService", "logger", "init", "opts", "projectDir", "getCurrentDirectory", "RegExp", "replace", "getTs", "getInfo", "getType<PERSON><PERSON>cker", "languageService", "getProgram", "getSource", "fileName", "getSourceFile", "removeStringQuotes", "str", "isPositionInsideNode", "position", "node", "start", "getFullStart", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefaultFunctionExport", "isFunctionDeclaration", "hasExportKeyword", "hasDefaultKeyword", "modifiers", "modifier", "kind", "SyntaxKind", "ExportKeyword", "DefaultKeyword", "isInsideApp", "filePath", "test", "isAppEntryFile", "basename", "isPageFile", "getEntryInfo", "throwOnInvalidDirective", "source", "isDirective", "isClientEntry", "isServerEntry", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "isExpressionStatement", "isStringLiteral", "expression", "text", "e", "messageText", "getStart", "length", "getWidth", "client", "server"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AAKvB,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AAEJ,OAAO,SAASC,IAAIC,OAAe;IACjCH,KAAKI,OAAO,CAACC,cAAc,CAACC,MAAM,CAACN,IAAI,CAACG;AAC1C;AAEA,4CAA4C;AAC5C,OAAO,SAASI,KAAKC,IAGpB;IACCT,KAAKS,KAAKT,EAAE;IACZC,OAAOQ,KAAKR,IAAI;IAChB,MAAMS,aAAaT,KAAKI,OAAO,CAACM,mBAAmB;IACnDT,eAAe,IAAIU,OACjB,MAAM,AAACF,CAAAA,aAAa,aAAY,EAAGG,OAAO,CAAC,UAAU;IAEvDV,IAAI,yCAAyCO;AAC/C;AAEA,OAAO,SAASI;IACd,OAAOd;AACT;AAEA,OAAO,SAASe;IACd,OAAOd;AACT;AAEA,OAAO,SAASe;QACPf;IAAP,QAAOA,mCAAAA,KAAKgB,eAAe,CAACC,UAAU,uBAA/BjB,iCAAmCe,cAAc;AAC1D;AAEA,OAAO,SAASG,UAAUC,QAAgB;QACjCnB;IAAP,QAAOA,mCAAAA,KAAKgB,eAAe,CAACC,UAAU,uBAA/BjB,iCAAmCoB,aAAa,CAACD;AAC1D;AAEA,OAAO,SAASE,mBAAmBC,GAAW;IAC5C,OAAOA,IAAIV,OAAO,CAAC,kBAAkB;AACvC;AAEA,OAAO,MAAMW,uBAAuB,CAACC,UAAkBC;IACrD,MAAMC,QAAQD,KAAKE,YAAY;IAC/B,OAAOD,SAASF,YAAYA,YAAYC,KAAKG,YAAY,KAAKF;AAChE,EAAC;AAED,OAAO,MAAMG,0BAA0B,CACrCJ;IAEA,IAAI1B,GAAG+B,qBAAqB,CAACL,OAAO;QAClC,IAAIM,mBAAmB;QACvB,IAAIC,oBAAoB;QAExB,IAAIP,KAAKQ,SAAS,EAAE;YAClB,KAAK,MAAMC,YAAYT,KAAKQ,SAAS,CAAE;gBACrC,IAAIC,SAASC,IAAI,KAAKpC,GAAGqC,UAAU,CAACC,aAAa,EAAE;oBACjDN,mBAAmB;gBACrB,OAAO,IAAIG,SAASC,IAAI,KAAKpC,GAAGqC,UAAU,CAACE,cAAc,EAAE;oBACzDN,oBAAoB;gBACtB;YACF;QACF;QAEA,4BAA4B;QAC5B,IAAID,oBAAoBC,mBAAmB;YACzC,OAAO;QACT;IACF;IACA,OAAO;AACT,EAAC;AAED,OAAO,MAAMO,cAAc,CAACC;IAC1B,OAAOvC,aAAawC,IAAI,CAACD;AAC3B,EAAC;AACD,OAAO,MAAME,iBAAiB,CAACF;IAC7B,OACEvC,aAAawC,IAAI,CAACD,aAClB,uCAAuCC,IAAI,CAAC3C,KAAK6C,QAAQ,CAACH;AAE9D,EAAC;AACD,OAAO,MAAMI,aAAa,CAACJ;IACzB,OACEvC,aAAawC,IAAI,CAACD,aAClB,8BAA8BC,IAAI,CAAC3C,KAAK6C,QAAQ,CAACH;AAErD,EAAC;AAED,uCAAuC;AACvC,OAAO,SAASK,aACd1B,QAAgB,EAChB2B,uBAAiC;IAEjC,MAAMC,SAAS7B,UAAUC;IACzB,IAAI4B,QAAQ;QACV,IAAIC,cAAc;QAClB,IAAIC,gBAAgB;QACpB,IAAIC,gBAAgB;QAEpBnD,GAAGoD,YAAY,CAACJ,QAAS,CAACtB;YACxB,IACE1B,GAAGqD,qBAAqB,CAAC3B,SACzB1B,GAAGsD,eAAe,CAAC5B,KAAK6B,UAAU,GAClC;gBACA,IAAI7B,KAAK6B,UAAU,CAACC,IAAI,KAAK,cAAc;oBACzC,IAAIP,aAAa;wBACfC,gBAAgB;oBAClB,OAAO;wBACL,IAAIH,yBAAyB;4BAC3B,MAAMU,IAAI;gCACRC,aACE;gCACF/B,OAAOD,KAAK6B,UAAU,CAACI,QAAQ;gCAC/BC,QAAQlC,KAAK6B,UAAU,CAACM,QAAQ;4BAClC;4BACA,MAAMJ;wBACR;oBACF;gBACF,OAAO,IAAI/B,KAAK6B,UAAU,CAACC,IAAI,KAAK,cAAc;oBAChD,IAAIP,aAAa;wBACfE,gBAAgB;oBAClB,OAAO;wBACL,IAAIJ,yBAAyB;4BAC3B,MAAMU,IAAI;gCACRC,aACE;gCACF/B,OAAOD,KAAK6B,UAAU,CAACI,QAAQ;gCAC/BC,QAAQlC,KAAK6B,UAAU,CAACM,QAAQ;4BAClC;4BACA,MAAMJ;wBACR;oBACF;gBACF;gBAEA,IAAIP,iBAAiBC,eAAe;oBAClC,MAAMM,IAAI;wBACRC,aACE;wBACF/B,OAAOD,KAAK6B,UAAU,CAACI,QAAQ;wBAC/BC,QAAQlC,KAAK6B,UAAU,CAACM,QAAQ;oBAClC;oBACA,MAAMJ;gBACR;YACF,OAAO;gBACLR,cAAc;YAChB;QACF;QAEA,OAAO;YAAEa,QAAQZ;YAAea,QAAQZ;QAAc;IACxD;IAEA,OAAO;QAAEW,QAAQ;QAAOC,QAAQ;IAAM;AACxC"}