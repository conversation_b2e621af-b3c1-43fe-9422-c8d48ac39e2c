"use client";

import React, { useEffect, useState } from "react";
import { Form, Input, Button, InputNumber, Select } from "antd";
import { Purchase, CreatePurchaseDto, UpdatePurchaseDto } from "@/reduxRTK/services/purchaseApi";
import { useGetAllSuppliersQuery, Supplier } from "@/reduxRTK/services/supplierApi";
import { useGetAllProductsQuery, Product } from "@/reduxRTK/services/productApi";
import SlidingPanel from "@/components/ui/SlidingPanel";
import { usePurchaseCreate } from "@/hooks/purchases/usePurchaseCreate";
import { usePurchaseUpdate } from "@/hooks/purchases/usePurchaseUpdate";
import { User } from "@/types/user";
import { ShoppingOutlined, DollarOutlined, UserOutlined, NumberOutlined } from "@ant-design/icons";
import "./purchase-panels.css";

const { Option } = Select;

interface PurchaseFormPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  purchase?: Purchase | null;
  currentUser?: User | null;
}

const PurchaseFormPanel: React.FC<PurchaseFormPanelProps> = ({
  isOpen,
  onClose,
  onSuccess,
  purchase,
  currentUser,
}) => {
  const [form] = Form.useForm();
  const isEditMode = !!purchase;
  const [totalCost, setTotalCost] = useState<string>("0");

  // Hooks for creating and updating purchases
  const { createPurchase, isSubmitting: isCreating } = usePurchaseCreate(onSuccess);
  const { updatePurchase, isUpdating } = usePurchaseUpdate(onSuccess);

  // Fetch suppliers for dropdown - Always fetch when component mounts
  const { data: suppliersResponse, refetch: refetchSuppliers } = useGetAllSuppliersQuery({}, {
    refetchOnMountOrArgChange: true,
    refetchOnFocus: false,
    refetchOnReconnect: true,
  });
  const suppliers: Supplier[] = suppliersResponse?.data?.suppliers || [];

  // Fetch products for dropdown - Always fetch when component mounts
  const { data: productsResponse, refetch: refetchProducts } = useGetAllProductsQuery({
    page: 1,
    limit: 1000,
    search: ''
  }, {
    refetchOnMountOrArgChange: true,
    refetchOnFocus: false,
    refetchOnReconnect: true,
  });
  const products: Product[] = productsResponse?.data?.products || [];

  // Calculate total cost when quantity or cost price changes
  const calculateTotalCost = () => {
    const quantity = form.getFieldValue('quantity') || 0;
    const costPrice = form.getFieldValue('costPrice') || 0;
    // Calculate total and ensure it's a string with 2 decimal places
    const total = (quantity * costPrice).toFixed(2);
    setTotalCost(total);
    // Set the form value as a string to match the expected type
    form.setFieldsValue({ totalCost: total });
  };

  // Handle panel open/close and data fetching
  useEffect(() => {
    if (isOpen) {
      // When panel opens, ensure we have fresh data
      console.log('🛒 Purchase panel opened - fetching fresh data');
      refetchProducts();
      refetchSuppliers();
      form.resetFields();

      if (!purchase) {
        setTotalCost("0.00");
      }
    }
  }, [form, isOpen, refetchProducts, refetchSuppliers]);

  // Separate effect to populate form when purchase data and dropdown data are available
  useEffect(() => {
    if (isOpen && purchase && products.length > 0 && suppliers.length > 0) {
      // For edit mode, we need to find the product and supplier IDs by name
      // since the backend returns names, not IDs
      const selectedProduct = products.find(p => p.name === purchase.product);
      const selectedSupplier = suppliers.find(s => s.name === purchase.supplier);

      form.setFieldsValue({
        productId: selectedProduct?.id,
        supplierId: selectedSupplier?.id,
        quantity: purchase.quantity,
        costPrice: purchase.costPrice,
        totalCost: purchase.totalCost,
      });
      setTotalCost(purchase.totalCost);
    }
  }, [isOpen, purchase, products, suppliers, form]);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    try {
      // Convert numeric values to strings as required by the API
      const formattedValues = {
        ...values,
        // Ensure costPrice is a string (backend expects string)
        costPrice: values.costPrice?.toString() || "0",
        // Ensure totalCost is a string (backend expects string)
        totalCost: values.totalCost?.toString() || "0"
      };

      console.log("Submitting purchase with formatted values:", formattedValues);

      if (isEditMode && purchase) {
        // Update existing purchase
        await updatePurchase(purchase.id, formattedValues as UpdatePurchaseDto);
      } else {
        // Create new purchase
        await createPurchase(formattedValues as CreatePurchaseDto);
      }
    } catch (error) {
      console.error("Failed to save purchase:", error);
    }
  };

  // Panel title
  const panelTitle = isEditMode ? "Edit Purchase" : "Add Purchase";

  // Panel footer with action buttons
  const panelFooter = (
    <div className="flex justify-end space-x-2">
      <Button
        onClick={onClose}
        disabled={isCreating || isUpdating}
        className="text-gray-700 hover:text-gray-900"
        style={{ borderColor: '#d9d9d9', background: '#f5f5f5' }}
      >
        Cancel
      </Button>
      <Button
        type="primary"
        loading={isCreating || isUpdating}
        onClick={() => form.submit()}
      >
        {isEditMode ? "Update" : "Save"}
      </Button>
    </div>
  );

  return (
    <SlidingPanel
      isOpen={isOpen}
      onClose={onClose}
      title={panelTitle}
      width="500px"
      footer={panelFooter}
    >
      <div className="p-4">
        {/* Form heading with icon */}
        <div className="mb-6 border-b border-gray-200 pb-4">
          <h2 className="text-xl font-bold text-gray-800 flex items-center">
            {isEditMode ? (
              <>
                <ShoppingOutlined className="mr-2" />
                Edit Purchase
              </>
            ) : (
              <>
                <ShoppingOutlined className="mr-2" />
                Add New Purchase
              </>
            )}
          </h2>
          <p className="text-gray-600 mt-1">
            {isEditMode
              ? "Update purchase information"
              : "Fill in the details to add a new purchase"}
          </p>
        </div>

        {/* Required fields indicator */}
        <div className="mb-4 text-sm text-gray-600">
          <span className="text-red-500 mr-1">*</span> indicates required fields
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="purchase-form"
          requiredMark={true}
          onValuesChange={(_, values) => {
            if ('quantity' in values || 'costPrice' in values) {
              calculateTotalCost();
            }
          }}
        >
          <Form.Item
            name="productId"
            label={<span className="flex items-center"><ShoppingOutlined className="mr-1" /> Product</span>}
            rules={[{ required: true, message: "Please select a product" }]}
            tooltip="Select the product you are purchasing"
          >
            <Select
              placeholder="Select a product"
              showSearch
              optionFilterProp="children"
            >
              {products.map(product => (
                <Option key={product.id} value={product.id}>{product.name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="supplierId"
            label={<span className="flex items-center"><UserOutlined className="mr-1" /> Supplier</span>}
            tooltip="Select the supplier (optional)"
          >
            <Select
              placeholder="Select a supplier (optional)"
              allowClear
              showSearch
              optionFilterProp="children"
            >
              {suppliers.map(supplier => (
                <Option key={supplier.id} value={supplier.id}>{supplier.name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="quantity"
            label={<span className="flex items-center"><NumberOutlined className="mr-1" /> Quantity</span>}
            rules={[
              { required: true, message: "Please enter quantity" },
              { type: 'number', min: 1, message: "Quantity must be at least 1" }
            ]}
            tooltip="The quantity of products purchased"
          >
            <InputNumber
              min={1}
              style={{ width: '100%' }}
              placeholder="Enter quantity"
            />
          </Form.Item>

          <Form.Item
            name="costPrice"
            label={<span className="flex items-center"><DollarOutlined className="mr-1" /> Cost Price (GHS)</span>}
            rules={[
              { required: true, message: "Please enter cost price" },
              { type: 'number', min: 0.01, message: "Cost price must be greater than 0" }
            ]}
            tooltip="The cost price per unit"
          >
            <InputNumber
              min={0.01}
              step={0.01}
              style={{ width: '100%' }}
              placeholder="Enter cost price"
              formatter={value => `GHS ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => parseFloat(value!.replace(/GHS\s?|(,*)/g, '')) as any}
            />
          </Form.Item>

          <Form.Item
            name="totalCost"
            label={<span className="flex items-center"><DollarOutlined className="mr-1" /> Total Cost (GHS)</span>}
            tooltip="The total cost (calculated automatically)"
          >
            <InputNumber
              disabled
              style={{ width: '100%' }}
              value={totalCost}
              formatter={value => `GHS ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value!.replace(/GHS\s?|(,*)/g, '')}
            />
          </Form.Item>
        </Form>
      </div>
    </SlidingPanel>
  );
};

export default PurchaseFormPanel;





