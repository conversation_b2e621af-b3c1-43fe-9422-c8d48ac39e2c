{"version": 3, "sources": ["../../../../src/server/dev/turbopack/types.ts"], "sourcesContent": ["import type {\n  Endpoint,\n  Instrumentation,\n  Middleware,\n} from '../../../build/swc/types'\n\nexport interface GlobalEntrypoints {\n  app: Endpoint | undefined\n  document: Endpoint | undefined\n  error: Endpoint | undefined\n\n  middleware: Middleware | undefined\n  instrumentation: Instrumentation | undefined\n}\n\nexport type PageRoute =\n  | {\n      type: 'page'\n      htmlEndpoint: Endpoint\n      dataEndpoint: Endpoint\n    }\n  | {\n      type: 'page-api'\n      endpoint: Endpoint\n    }\n\nexport type AppRoute =\n  | {\n      type: 'app-page'\n      htmlEndpoint: Endpoint\n      rscEndpoint: Endpoint\n    }\n  | {\n      type: 'app-route'\n      endpoint: Endpoint\n    }\n\n// pathname -> route\nexport type PageEntrypoints = Map<string, PageRoute>\n\n// originalName / page -> route\nexport type AppEntrypoints = Map<string, AppRoute>\n\nexport type Entrypoints = {\n  global: GlobalEntrypoints\n\n  page: PageEntrypoints\n  app: AppEntrypoints\n}\n"], "names": [], "mappings": "AA2CA,WAKC"}