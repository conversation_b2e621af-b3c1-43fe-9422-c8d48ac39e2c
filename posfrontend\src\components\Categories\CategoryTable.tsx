"use client";

import React, { useState } from "react";
import { <PERSON>ton, Tooltip, Tag, Checkbox, notification } from "antd";
import { EditOutlined, EyeOutlined, DeleteOutlined, DeleteFilled } from "@ant-design/icons";
import { ResponsiveTableGrid, TableHeader, TableCell, TableRow } from "@/components/ui/ResponsiveTable";
import { Category } from "@/reduxRTK/services/categoryApi";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { UserRole } from "@/types/user";

interface CategoryTableProps {
  categories: Category[];
  loading: boolean;
  onView: (categoryId: number) => void;
  onEdit: (category: Category) => void;
  onDelete: (categoryId: number) => void;
  onBulkDelete?: (categoryIds: number[]) => void;
  isMobile?: boolean;
}

const CategoryTable: React.FC<CategoryTableProps> = ({
  categories,
  loading,
  onView,
  onEdit,
  onDelete,
  onBulkDelete,
  isMobile = false,
}) => {
  const user = useSelector((state: RootState) => state.auth.user);
  const userRole = user?.role as UserRole;

  // State for selected categories
  const [selectedCategories, setSelectedCategories] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Handle select all checkbox change
  const handleSelectAllChange = (e: any) => {
    const checked = e.target.checked;
    setSelectAll(checked);

    if (checked) {
      // Select all categories that the user can delete
      const selectableCategoryIds = categories
        .filter(category => canEditDelete(category))
        .map(category => category.id);
      setSelectedCategories(selectableCategoryIds);
    } else {
      // Deselect all categories
      setSelectedCategories([]);
    }
  };

  // Handle individual checkbox change
  const handleCheckboxChange = (categoryId: number, checked: boolean) => {
    if (checked) {
      setSelectedCategories(prev => [...prev, categoryId]);
    } else {
      setSelectedCategories(prev => prev.filter(id => id !== categoryId));
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedCategories.length > 0 && onBulkDelete) {
      onBulkDelete(selectedCategories);
      setSelectedCategories([]);
      setSelectAll(false);
    } else {
      notification.warning({
        message: 'No categories selected',
        description: 'Please select at least one category to delete.',
      });
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return dayjs(dateString).format("MMM D, YYYY");
  };

  // Check if user can edit/delete (superadmin can edit all, others only their own)
  const canEditDelete = (category: Category) => {
    if (userRole === "superadmin") return true;
    if (userRole === "admin" && user?.id === category.createdBy) return true;
    return false;
  };

  return (
    <div className="overflow-hidden bg-white">
      {/* Bulk Delete Button - Show only when categories are selected */}
      {selectedCategories.length > 0 && (
        <div className="p-2 bg-gray-100 border-b flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            {selectedCategories.length} {selectedCategories.length === 1 ? 'category' : 'categories'} selected
          </span>
          <Button
            type="primary"
            danger
            icon={<DeleteFilled />}
            onClick={handleBulkDelete}
            className="ml-2"
          >
            Delete Selected
          </Button>
        </div>
      )}

      <ResponsiveTableGrid
        columns={isMobile ? "50px 200px 120px 150px" : "50px 200px 200px 120px 150px"}
        minWidth={isMobile ? "700px" : "900px"}
      >
        {/* Table Headers */}
        <TableHeader className="text-center">
          <Checkbox
            checked={selectAll}
            onChange={handleSelectAllChange}
            disabled={categories.filter(category => canEditDelete(category)).length === 0}
          />
        </TableHeader>
        <TableHeader sticky={isMobile ? undefined : "left"}>
          Name
        </TableHeader>
        {!isMobile && (
          <TableHeader>
            Description
          </TableHeader>
        )}
        <TableHeader>
          Created At
        </TableHeader>
        <TableHeader sticky={isMobile ? undefined : "right"} className="text-right">
          Actions
        </TableHeader>
        {/* Table Rows */}
        {categories.map((category) => (
          <TableRow
            key={category.id}
            selected={selectedCategories.includes(category.id)}
          >
            {/* Checkbox Column */}
            <TableCell className="text-center">
              {canEditDelete(category) && (
                <Checkbox
                  checked={selectedCategories.includes(category.id)}
                  onChange={(e) => handleCheckboxChange(category.id, e.target.checked)}
                />
              )}
            </TableCell>

            {/* Name Column - Always visible */}
            <TableCell sticky={isMobile ? undefined : "left"}>
              <div className="max-w-[180px] overflow-hidden text-ellipsis font-medium">
                {category.name}
              </div>
            </TableCell>

            {/* Show description only on desktop */}
            {!isMobile && (
              <TableCell>
                <div className="max-w-[180px] overflow-hidden text-ellipsis text-gray-600">
                  {category.description || "No description"}
                </div>
              </TableCell>
            )}

            {/* Created At Column */}
            <TableCell>
              <div className="max-w-[110px] overflow-hidden text-ellipsis">
                {formatDate(category.createdAt)}
              </div>
            </TableCell>

            {/* Actions Column - Always visible */}
            <TableCell sticky={isMobile ? undefined : "right"} className="text-right">
              <div className="flex justify-end space-x-1">
                <Tooltip title="View">
                  <Button
                    icon={<EyeOutlined />}
                    onClick={() => onView(category.id)}
                    type="text"
                    className="view-button text-green-500 hover:text-green-400"
                    size={isMobile ? "small" : "middle"}
                  />
                </Tooltip>

                {canEditDelete(category) && (
                  <>
                    <Tooltip title="Edit">
                      <Button
                        icon={<EditOutlined />}
                        onClick={() => onEdit(category)}
                        type="text"
                        className="edit-button text-blue-500 hover:text-blue-400"
                        size={isMobile ? "small" : "middle"}
                      />
                    </Tooltip>
                    <Tooltip title="Delete">
                      <Button
                        icon={<DeleteOutlined />}
                        onClick={() => onDelete(category.id)}
                        type="text"
                        className="delete-button text-red-500 hover:text-red-400"
                        size={isMobile ? "small" : "middle"}
                      />
                    </Tooltip>
                  </>
                )}
              </div>
            </TableCell>
          </TableRow>
        ))}
      </ResponsiveTableGrid>
    </div>
  );
};

export default CategoryTable;
