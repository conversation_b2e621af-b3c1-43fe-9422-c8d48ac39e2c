/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/components/Categories/category-panels.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/* Custom styles for category panels */

/* Light theme for descriptions */
.category-detail-light {
  color: #333;
  background-color: #ffffff;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

.category-detail-light .ant-descriptions-item-label {
  background-color: #f5f5f5 !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
  word-break: break-word;
}

.category-detail-light .ant-descriptions-item-content {
  background-color: #ffffff !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
  word-break: break-word;
}

/* Light theme for form items */
.ant-form-item-label > label {
  color: rgba(0, 0, 0, 0.85) !important;
}

/* Light theme for select */
.ant-select-selector {
  background-color: #ffffff !important;
  color: rgba(0, 0, 0, 0.85) !important;
  border-color: #d9d9d9 !important;
}

.ant-select-selection-item {
  color: rgba(0, 0, 0, 0.85) !important;
}

.ant-select-arrow {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Light theme for input */
.ant-input, .ant-input-password {
  background-color: #ffffff !important;
  color: rgba(0, 0, 0, 0.85) !important;
  border-color: #d9d9d9 !important;
}

.ant-input-password-icon {
  color: rgba(0, 0, 0, 0.45) !important;
}

/* Dark theme for tags */
.ant-tag {
  border-color: transparent !important;
}

/* Fix for loading spinner */
.ant-spin-text,
.ant-spin-dot + div,
.ant-spin + div {
  display: none !important;
}

/* Center all spinners */
.ant-spin-container {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  min-height: 200px !important;
}

/* Fix for z-index */
.ant-form-item-explain-error {
  color: #ff4d4f !important;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  .ant-form-item {
    margin-bottom: 12px !important;
  }

  .ant-form-item-label {
    padding-bottom: 4px !important;
  }

  .ant-input, .ant-input-password, .ant-select {
    font-size: 16px !important; /* Prevents iOS zoom on focus */
  }

  .ant-descriptions-item-label,
  .ant-descriptions-item-content {
    padding: 8px !important;
    font-size: 14px !important;
  }

  /* Ensure form inputs don't overflow */
  .ant-form-item-control-input {
    width: 100% !important;
    max-width: 100% !important;
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/search.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/* Custom styles for search inputs */

/* Light background for input and its wrapper */
.ant-input-affix-wrapper {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
}

/* Ensure hover and focus states maintain light background */
.ant-input-affix-wrapper:hover,
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
  background-color: #ffffff !important;
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* Style for the input inside the wrapper */
.ant-input-affix-wrapper .ant-input {
  background-color: transparent !important;
  color: rgba(0, 0, 0, 0.85) !important;
}

/* Style for the clear icon */
.ant-input-affix-wrapper .ant-input-clear-icon {
  color: rgba(0, 0, 0, 0.25) !important;
}

.ant-input-affix-wrapper .ant-input-clear-icon:hover {
  color: rgba(0, 0, 0, 0.45) !important;
}

/* Style for the placeholder */
.ant-input::-moz-placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}
.ant-input::placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Style for the search icon */
.ant-input-prefix .anticon {
  color: rgba(0, 0, 0, 0.25) !important;
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/dashboard/categories/categories.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/* Custom styles for the categories table */
.category-table .ant-table-container {
  overflow-x: auto;
  background-color: #ffffff;
}

.category-table .ant-table-body {
  overflow-x: auto !important;
}

/* Ensure all table cells have light background */
.category-table .ant-table {
  background-color: #ffffff;
}

.category-table .ant-table-thead > tr > th,
.category-table .ant-table-tbody > tr > td {
  background-color: #ffffff;
  color: #333333;
}

/* Ensure fixed columns work properly */
.category-table .ant-table-cell.ant-table-cell-fix-left,
.category-table .ant-table-cell.ant-table-cell-fix-right {
  z-index: 2;
  background-color: #ffffff !important;
}

/* Fix for sticky header */
.category-table .ant-table-header {
  background-color: #f5f5f5;
}

/* Fix for sticky columns */
.category-table .ant-table-cell-fix-left-last::after,
.category-table .ant-table-cell-fix-right-first::after {
  background-color: #ffffff !important;
}

/* Improve tag display on mobile */
@media (max-width: 640px) {
  .category-table .ant-tag {
    margin-right: 0;
    padding: 0 4px;
    font-size: 11px;
  }

  .category-table .ant-table-thead > tr > th,
  .category-table .ant-table-tbody > tr > td {
    padding: 8px 4px;
    background-color: #ffffff !important;
    color: #333333;
  }

  .category-table .ant-table-filter-trigger {
    margin-right: -4px;
  }

  /* Ensure action buttons are properly spaced */
  .category-table .ant-space-item {
    margin-right: 0 !important;
  }

  .category-table .ant-btn {
    padding: 0 4px;
  }

  /* Additional fixes for mobile */
  .category-table .ant-table-container::before,
  .category-table .ant-table-container::after {
    background-color: #ffffff !important;
  }

  .category-table .ant-table-cell-scrollbar {
    box-shadow: none;
    background-color: #ffffff !important;
  }

  /* Fix for row hover effect */
  .category-table .ant-table-tbody > tr.ant-table-row:hover > td {
    background-color: #f5f5f5 !important;
  }
}

/* Ensure the table doesn't affect the layout */
.overflow-x-auto {
  -webkit-overflow-scrolling: touch;
  background-color: #ffffff;
}

/* Fix for any remaining transparent areas */
.category-table .ant-table-wrapper,
.category-table .ant-spin-nested-loading,
.category-table .ant-spin-container,
.category-table .ant-table,
.category-table .ant-table-content,
.category-table .ant-table-scroll,
.category-table .ant-table-ping-left:not(.ant-table-has-fix-left) .ant-table-cell-fix-left-first,
.category-table .ant-table-ping-right:not(.ant-table-has-fix-right) .ant-table-cell-fix-right-last {
  background-color: #ffffff !important;
}

/* Fix for striped rows if enabled */
.category-table .ant-table-tbody > tr.ant-table-row:nth-child(odd) > td {
  background-color: #ffffff !important;
}

.category-table .ant-table-tbody > tr.ant-table-row:nth-child(even) > td {
  background-color: #f9f9f9 !important;
}

/* Fix for sticky header and scrollbar */
.category-table .ant-table-sticky-scroll {
  background-color: #ffffff !important;
}

.category-table .ant-table-sticky-scroll-bar {
  background-color: #e6e6e6 !important;
}

/* Fix for any shadow effects */
.category-table .ant-table-cell-fix-left-last::after,
.category-table .ant-table-cell-fix-right-first::after {
  box-shadow: none !important;
  background-color: #ffffff !important;
}

/* Fix for any remaining transparent areas */
.category-table .ant-table-container::before,
.category-table .ant-table-container::after,
.category-table .ant-table-header::before,
.category-table .ant-table-header::after,
.category-table .ant-table-body::before,
.category-table .ant-table-body::after {
  display: none !important;
  background-color: #ffffff !important;
}

/* Custom styles for action buttons */
.view-button .anticon-eye {
  color: #3b82f6 !important; /* Blue color for view icon */
  font-size: 18px !important;
}

.edit-button .anticon-edit {
  color: #10b981 !important; /* Green color for edit icon */
  font-size: 18px !important;
}

.delete-button .anticon-delete {
  color: #ef4444 !important; /* Red color for delete icon */
  font-size: 18px !important;
}

/* Hover effects for action buttons */
.view-button:hover {
  background-color: rgba(59, 130, 246, 0.2) !important; /* Blue hover */
}

.edit-button:hover {
  background-color: rgba(16, 185, 129, 0.2) !important; /* Green hover */
}

.delete-button:hover {
  background-color: rgba(239, 68, 68, 0.2) !important; /* Red hover */
}

/* Button background */
.view-button, .edit-button, .delete-button {
  background-color: #f5f5f5 !important;
  border: none !important;
}

/* Pagination styles */
.bg-white {
  background-color: #ffffff !important;
}

.border-gray-200, .border-gray-300 {
  border-color: #e8e8e8 !important;
}

.text-gray-500, .text-gray-700 {
  color: #595959 !important;
}

.bg-blue-50 {
  background-color: #e6f7ff !important;
}

.border-blue-500 {
  border-color: #1890ff !important;
}

.text-blue-600 {
  color: #1890ff !important;
}

.hover\:bg-gray-50:hover {
  background-color: #f5f5f5 !important;
}

