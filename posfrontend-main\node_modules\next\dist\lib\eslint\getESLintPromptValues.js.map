{"version": 3, "sources": ["../../../src/lib/eslint/getESLintPromptValues.ts"], "sourcesContent": ["import findUp from 'next/dist/compiled/find-up'\n\nexport const getESLintStrictValue = async (cwd: string) => {\n  const tsConfigLocation = await findUp('tsconfig.json', { cwd })\n  const hasTSConfig = tsConfigLocation !== undefined\n\n  return {\n    title: 'Strict',\n    recommended: true,\n    config: {\n      extends: hasTSConfig\n        ? ['next/core-web-vitals', 'next/typescript']\n        : 'next/core-web-vitals',\n    },\n  }\n}\n\nexport const getESLintPromptValues = async (cwd: string) => {\n  return [\n    await getESLintStrictValue(cwd),\n    {\n      title: 'Base',\n      config: {\n        extends: 'next',\n      },\n    },\n    {\n      title: 'Cancel',\n      config: null,\n    },\n  ]\n}\n"], "names": ["getESLintPromptValues", "getESLintStrictValue", "cwd", "tsConfigLocation", "findUp", "hasTSConfig", "undefined", "title", "recommended", "config", "extends"], "mappings": ";;;;;;;;;;;;;;;IAiBaA,qBAAqB;eAArBA;;IAfAC,oBAAoB;eAApBA;;;+DAFM;;;;;;AAEZ,MAAMA,uBAAuB,OAAOC;IACzC,MAAMC,mBAAmB,MAAMC,IAAAA,eAAM,EAAC,iBAAiB;QAAEF;IAAI;IAC7D,MAAMG,cAAcF,qBAAqBG;IAEzC,OAAO;QACLC,OAAO;QACPC,aAAa;QACbC,QAAQ;YACNC,SAASL,cACL;gBAAC;gBAAwB;aAAkB,GAC3C;QACN;IACF;AACF;AAEO,MAAML,wBAAwB,OAAOE;IAC1C,OAAO;QACL,MAAMD,qBAAqBC;QAC3B;YACEK,OAAO;YACPE,QAAQ;gBACNC,SAAS;YACX;QACF;QACA;YACEH,OAAO;YACPE,QAAQ;QACV;KACD;AACH"}