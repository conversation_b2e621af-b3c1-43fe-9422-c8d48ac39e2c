"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/products/page",{

/***/ "(app-pages-browser)/./src/components/Products/ProductTable.tsx":
/*!**************************************************!*\
  !*** ./src/components/Products/ProductTable.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/notification/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/checkbox/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteFilled.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CalendarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarcodeOutlined.js\");\n/* harmony import */ var _components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ResponsiveTable */ \"(app-pages-browser)/./src/components/ui/ResponsiveTable.tsx\");\n/* harmony import */ var _hooks_useResponsiveTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useResponsiveTable */ \"(app-pages-browser)/./src/hooks/useResponsiveTable.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ProductTable = (param)=>{\n    let { products, loading, onView, onEdit, onDelete, onBulkDelete, onAdjustStock, isMobile: propIsMobile = false } = param;\n    _s();\n    // Use hook for responsive detection, fallback to prop\n    const hookIsMobile = (0,_hooks_useResponsiveTable__WEBPACK_IMPORTED_MODULE_3__.useResponsiveTable)();\n    const isMobile = propIsMobile || hookIsMobile;\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__.useSelector)({\n        \"ProductTable.useSelector[user]\": (state)=>state.auth.user\n    }[\"ProductTable.useSelector[user]\"]);\n    const userRole = user === null || user === void 0 ? void 0 : user.role;\n    // State for selected products\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectAll, setSelectAll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle select all checkbox change\n    const handleSelectAllChange = (e)=>{\n        const checked = e.target.checked;\n        setSelectAll(checked);\n        if (checked) {\n            // Select all products that the user can delete\n            const selectableProductIds = products.filter((product)=>canEditDelete(product)).map((product)=>product.id);\n            setSelectedProducts(selectableProductIds);\n        } else {\n            // Deselect all products\n            setSelectedProducts([]);\n        }\n    };\n    // Handle individual checkbox change\n    const handleCheckboxChange = (productId, checked)=>{\n        if (checked) {\n            setSelectedProducts((prev)=>[\n                    ...prev,\n                    productId\n                ]);\n        } else {\n            setSelectedProducts((prev)=>prev.filter((id)=>id !== productId));\n        }\n    };\n    // Handle bulk delete\n    const handleBulkDelete = ()=>{\n        if (selectedProducts.length > 0 && onBulkDelete) {\n            onBulkDelete(selectedProducts);\n            setSelectedProducts([]);\n            setSelectAll(false);\n        } else {\n            _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].warning({\n                message: 'No products selected',\n                description: 'Please select at least one product to delete.'\n            });\n        }\n    };\n    // Format date for display\n    const formatDate = (dateString)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateString).format(\"MMM D, YYYY\");\n    };\n    // Format currency for display\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-GH', {\n            style: 'currency',\n            currency: 'GHS'\n        }).format(parseFloat(amount));\n    };\n    // Check if user can edit/delete (superadmin can edit all, others only their own)\n    const canEditDelete = (product)=>{\n        if (userRole === \"superadmin\") return true;\n        if (userRole === \"admin\" && (user === null || user === void 0 ? void 0 : user.id) === product.createdBy) return true;\n        return false;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-hidden bg-white\",\n        children: [\n            selectedProducts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 bg-gray-100 border-b flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: [\n                            selectedProducts.length,\n                            \" \",\n                            selectedProducts.length === 1 ? 'product' : 'products',\n                            \" selected\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        type: \"primary\",\n                        danger: true,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: handleBulkDelete,\n                        className: \"ml-2\",\n                        children: \"Delete Selected\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                lineNumber: 121,\n                columnNumber: 9\n            }, undefined),\n            isMobile ? // Mobile: Use CSS Grid\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.ResponsiveTableGrid, {\n                columns: \"50px 200px 100px 80px 120px 150px\",\n                minWidth: \"800px\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            checked: selectAll,\n                            onChange: handleSelectAllChange,\n                            disabled: products.filter((product)=>canEditDelete(product)).length === 0\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Name\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Price\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Stock\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Created At\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        className: \"text-right\",\n                        children: \"Actions\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, undefined),\n                    products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableRow, {\n                            selected: selectedProducts.includes(product.id),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    className: \"text-center\",\n                                    children: canEditDelete(product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        checked: selectedProducts.includes(product.id),\n                                        onChange: (e)=>handleCheckboxChange(product.id, e.target.checked)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-[180px] overflow-hidden text-ellipsis font-medium\",\n                                        children: product.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-green-600\",\n                                        children: formatCurrency(product.price)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full \".concat(product.stockQuantity <= 0 ? 'bg-red-500 text-white' : product.stockQuantity <= (product.minStockLevel || 5) ? 'bg-yellow-500 text-white' : 'bg-green-500 text-white'),\n                                        children: product.stockQuantity\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: formatDate(product.createdAt)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    className: \"text-right\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                title: \"View\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    onClick: ()=>onView(product.id),\n                                                    type: \"text\",\n                                                    className: \"view-button text-green-500 hover:text-green-400\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            userRole === \"admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                title: \"Adjust Stock\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 31\n                                                    }, void 0),\n                                                    onClick: ()=>onAdjustStock(product),\n                                                    type: \"text\",\n                                                    className: \"adjust-stock-button text-purple-500 hover:text-purple-400\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            canEditDelete(product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        title: \"Edit\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 33\n                                                            }, void 0),\n                                                            onClick: ()=>onEdit(product),\n                                                            type: \"text\",\n                                                            className: \"edit-button text-blue-500 hover:text-blue-400\",\n                                                            size: \"small\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        title: \"Delete\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 33\n                                                            }, void 0),\n                                                            onClick: ()=>onDelete(product.id),\n                                                            type: \"text\",\n                                                            className: \"delete-button text-red-500 hover:text-red-400\",\n                                                            size: \"small\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, product.id, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, undefined) : // Desktop: Use traditional HTML table\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"w-10 px-3 py-3 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            checked: selectAll,\n                                            onChange: handleSelectAllChange,\n                                            disabled: products.filter((product)=>canEditDelete(product)).length === 0\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Name\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Price\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Stock\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"SKU\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Created At\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: selectedProducts.includes(product.id) ? \"bg-blue-50\" : \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-4 whitespace-nowrap text-center\",\n                                            children: canEditDelete(product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                checked: selectedProducts.includes(product.id),\n                                                onChange: (e)=>handleCheckboxChange(product.id, e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-w-[120px] overflow-hidden text-ellipsis\",\n                                                children: product.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-4 whitespace-nowrap text-gray-800\",\n                                            children: formatCurrency(product.price)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full \".concat(product.stockQuantity <= 0 ? 'bg-red-500 text-white' : product.stockQuantity <= (product.minStockLevel || 5) ? 'bg-yellow-500 text-white' : 'bg-green-500 text-white'),\n                                                children: product.stockQuantity\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-4 whitespace-nowrap text-gray-800\",\n                                            children: product.sku || 'N/A'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-3 py-4 whitespace-nowrap text-gray-800\",\n                                            children: formatDate(product.createdAt)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        title: \"View\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 33\n                                                            }, void 0),\n                                                            onClick: ()=>onView(product.id),\n                                                            type: \"text\",\n                                                            className: \"view-button text-green-500 hover:text-green-400\",\n                                                            size: \"middle\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    userRole === \"admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        title: \"Adjust Stock\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 35\n                                                            }, void 0),\n                                                            onClick: ()=>onAdjustStock(product),\n                                                            type: \"text\",\n                                                            className: \"adjust-stock-button text-purple-500 hover:text-purple-400\",\n                                                            size: \"middle\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    canEditDelete(product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                title: \"Edit\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                                        lineNumber: 405,\n                                                                        columnNumber: 37\n                                                                    }, void 0),\n                                                                    onClick: ()=>onEdit(product),\n                                                                    type: \"text\",\n                                                                    className: \"edit-button text-blue-500 hover:text-blue-400\",\n                                                                    size: \"middle\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                title: \"Delete\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 37\n                                                                    }, void 0),\n                                                                    onClick: ()=>onDelete(product.id),\n                                                                    type: \"text\",\n                                                                    className: \"delete-button text-red-500 hover:text-red-400\",\n                                                                    size: \"middle\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, product.id, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                lineNumber: 268,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductTable, \"VEcgbTfsjalFpb05mI9/e+GKOfw=\", false, function() {\n    return [\n        _hooks_useResponsiveTable__WEBPACK_IMPORTED_MODULE_3__.useResponsiveTable,\n        react_redux__WEBPACK_IMPORTED_MODULE_5__.useSelector\n    ];\n});\n_c = ProductTable;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductTable);\nvar _c;\n$RefreshReg$(_c, \"ProductTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Products/ProductTable.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useResponsiveTable.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useResponsiveTable.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useResponsiveTable: () => (/* binding */ useResponsiveTable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useResponsiveTable auto */ \n/**\n * Hook to determine if we should use mobile table layout\n * Returns true for mobile devices (width < 768px)\n */ const useResponsiveTable = ()=>{\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useResponsiveTable.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"useResponsiveTable.useEffect.checkScreenSize\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"useResponsiveTable.useEffect.checkScreenSize\"];\n            // Check on mount\n            checkScreenSize();\n            // Add event listener for window resize\n            window.addEventListener('resize', checkScreenSize);\n            // Cleanup\n            return ({\n                \"useResponsiveTable.useEffect\": ()=>window.removeEventListener('resize', checkScreenSize)\n            })[\"useResponsiveTable.useEffect\"];\n        }\n    }[\"useResponsiveTable.useEffect\"], []);\n    return isMobile;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VSZXNwb25zaXZlVGFibGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O3dFQUU0QztBQUU1Qzs7O0NBR0MsR0FDTSxNQUFNRSxxQkFBcUI7SUFDaEMsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdKLCtDQUFRQSxDQUFDO0lBRXpDQyxnREFBU0E7d0NBQUM7WUFDUixNQUFNSTtnRUFBa0I7b0JBQ3RCRCxZQUFZRSxPQUFPQyxVQUFVLEdBQUc7Z0JBQ2xDOztZQUVBLGlCQUFpQjtZQUNqQkY7WUFFQSx1Q0FBdUM7WUFDdkNDLE9BQU9FLGdCQUFnQixDQUFDLFVBQVVIO1lBRWxDLFVBQVU7WUFDVjtnREFBTyxJQUFNQyxPQUFPRyxtQkFBbUIsQ0FBQyxVQUFVSjs7UUFDcEQ7dUNBQUcsRUFBRTtJQUVMLE9BQU9GO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsiRTpcXFBST0pFQ1RTXFxwb3NcXHBvc2Zyb250ZW5kXFxzcmNcXGhvb2tzXFx1c2VSZXNwb25zaXZlVGFibGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5cbi8qKlxuICogSG9vayB0byBkZXRlcm1pbmUgaWYgd2Ugc2hvdWxkIHVzZSBtb2JpbGUgdGFibGUgbGF5b3V0XG4gKiBSZXR1cm5zIHRydWUgZm9yIG1vYmlsZSBkZXZpY2VzICh3aWR0aCA8IDc2OHB4KVxuICovXG5leHBvcnQgY29uc3QgdXNlUmVzcG9uc2l2ZVRhYmxlID0gKCkgPT4ge1xuICBjb25zdCBbaXNNb2JpbGUsIHNldElzTW9iaWxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGNoZWNrU2NyZWVuU2l6ZSA9ICgpID0+IHtcbiAgICAgIHNldElzTW9iaWxlKHdpbmRvdy5pbm5lcldpZHRoIDwgNzY4KTtcbiAgICB9O1xuXG4gICAgLy8gQ2hlY2sgb24gbW91bnRcbiAgICBjaGVja1NjcmVlblNpemUoKTtcblxuICAgIC8vIEFkZCBldmVudCBsaXN0ZW5lciBmb3Igd2luZG93IHJlc2l6ZVxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCBjaGVja1NjcmVlblNpemUpO1xuXG4gICAgLy8gQ2xlYW51cFxuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgY2hlY2tTY3JlZW5TaXplKTtcbiAgfSwgW10pO1xuXG4gIHJldHVybiBpc01vYmlsZTtcbn07XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSZXNwb25zaXZlVGFibGUiLCJpc01vYmlsZSIsInNldElzTW9iaWxlIiwiY2hlY2tTY3JlZW5TaXplIiwid2luZG93IiwiaW5uZXJXaWR0aCIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useResponsiveTable.ts\n"));

/***/ })

});