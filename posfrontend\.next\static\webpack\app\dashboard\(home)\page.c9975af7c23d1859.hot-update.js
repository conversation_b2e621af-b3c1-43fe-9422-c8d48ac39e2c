"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/(home)/page",{

/***/ "(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx":
/*!*************************************************!*\
  !*** ./src/components/Sales/SalesFormPanel.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,Input,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,Input,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,Input,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,Input,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,Input,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,Input,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,Input,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,Input,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/image/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ScanOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PrinterOutlined.js\");\n/* harmony import */ var _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/reduxRTK/services/salesApi */ \"(app-pages-browser)/./src/reduxRTK/services/salesApi.ts\");\n/* harmony import */ var _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/reduxRTK/services/productApi */ \"(app-pages-browser)/./src/reduxRTK/services/productApi.ts\");\n/* harmony import */ var _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/reduxRTK/services/userStoreApi */ \"(app-pages-browser)/./src/reduxRTK/services/userStoreApi.ts\");\n/* harmony import */ var _components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/SlidingPanel */ \"(app-pages-browser)/./src/components/ui/SlidingPanel.tsx\");\n/* harmony import */ var _components_BarcodeScanner_BarcodeScanner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/BarcodeScanner/BarcodeScanner */ \"(app-pages-browser)/./src/components/BarcodeScanner/BarcodeScanner.tsx\");\n/* harmony import */ var _hooks_useBarcodeScanner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useBarcodeScanner */ \"(app-pages-browser)/./src/hooks/useBarcodeScanner.ts\");\n/* harmony import */ var _utils_showMessage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/showMessage */ \"(app-pages-browser)/./src/utils/showMessage.ts\");\n/* harmony import */ var _utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/cloudinaryUtils */ \"(app-pages-browser)/./src/utils/cloudinaryUtils.ts\");\n/* harmony import */ var _sales_panels_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./sales-panels.css */ \"(app-pages-browser)/./src/components/Sales/sales-panels.css\");\n/* harmony import */ var _modern_sales_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./modern-sales.css */ \"(app-pages-browser)/./src/components/Sales/modern-sales.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst SalesFormPanel = (param)=>{\n    let { isOpen, onClose, onSuccess } = param;\n    var _productsData_data_products, _productsData_data;\n    _s();\n    const [form] = _barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].useForm();\n    const [productForm] = _barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].useForm();\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalAmount, setTotalAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingReceipt, setIsGeneratingReceipt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptPreviewVisible, setReceiptPreviewVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptUrl, setReceiptUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPrinted, setHasPrinted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [barcodeInput, setBarcodeInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Barcode scanner functionality\n    const { isOpen: isScannerOpen, openScanner, closeScanner, handleBarcodeScanned, isLoading: isScannerLoading } = (0,_hooks_useBarcodeScanner__WEBPACK_IMPORTED_MODULE_7__.useBarcodeScanner)({\n        onProductFound: {\n            \"SalesFormPanel.useBarcodeScanner\": (product)=>{\n                setSelectedProduct(product);\n                setQuantity(1);\n                if (productForm) {\n                    productForm.setFieldsValue({\n                        productId: product.id,\n                        quantity: 1\n                    });\n                }\n                // Auto-add to cart after scanning\n                setTimeout({\n                    \"SalesFormPanel.useBarcodeScanner\": ()=>{\n                        handleAddItem();\n                    }\n                }[\"SalesFormPanel.useBarcodeScanner\"], 100);\n            }\n        }[\"SalesFormPanel.useBarcodeScanner\"],\n        onProductNotFound: {\n            \"SalesFormPanel.useBarcodeScanner\": (barcode)=>{\n                console.log('Product not found for barcode:', barcode);\n            }\n        }[\"SalesFormPanel.useBarcodeScanner\"]\n    });\n    // Handle hardware barcode scanner input\n    const handleBarcodeInputChange = async (value)=>{\n        setBarcodeInput(value);\n        // If input looks like a barcode (typically 8+ characters), try to find product\n        if (value && value.length >= 8) {\n            try {\n                var _productsData_data;\n                console.log('Searching for product with barcode:', value);\n                // Use the existing products data to search for barcode\n                const products = (productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products) || [];\n                // Look for exact barcode match first\n                let foundProduct = products.find((product)=>product.barcode === value.trim());\n                // If no exact barcode match, try SKU\n                if (!foundProduct) {\n                    foundProduct = products.find((product)=>product.sku === value.trim());\n                }\n                if (foundProduct) {\n                    console.log('Product found via hardware scanner:', foundProduct);\n                    setSelectedProduct(foundProduct);\n                    setQuantity(1);\n                    if (productForm) {\n                        productForm.setFieldsValue({\n                            productId: foundProduct.id,\n                            quantity: 1\n                        });\n                    }\n                    // Clear the barcode input\n                    setBarcodeInput('');\n                    // Auto-add to cart\n                    setTimeout(()=>{\n                        handleAddItem();\n                    }, 100);\n                    (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)('success', \"Product found: \".concat(foundProduct.name));\n                } else {\n                    console.log('No product found for barcode:', value);\n                    (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)('warning', \"No product found for barcode: \".concat(value));\n                }\n            } catch (error) {\n                console.error('Error searching for product:', error);\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)('error', 'Error searching for product');\n            }\n        }\n    };\n    // Debug state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            console.log(\"Items state changed:\", items);\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items\n    ]);\n    const { data: productsData, isLoading: isLoadingProducts, refetch: refetchProducts } = (0,_reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery)({\n        page: 1,\n        limit: 1000,\n        search: searchTerm\n    }, {\n        // Force refetch when component mounts and when panel opens\n        refetchOnMountOrArgChange: true,\n        refetchOnFocus: false,\n        refetchOnReconnect: true\n    });\n    // Debug products data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (productsData) {\n                var _productsData_data, _productsData_data_products, _productsData_data1;\n                console.log(\"🛒 Products loaded:\", {\n                    total: ((_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.total) || 0,\n                    productsCount: ((_productsData_data1 = productsData.data) === null || _productsData_data1 === void 0 ? void 0 : (_productsData_data_products = _productsData_data1.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.length) || 0,\n                    isLoading: isLoadingProducts\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        productsData,\n        isLoadingProducts\n    ]);\n    // Get current user ID from auth state\n    const getCurrentUserId = ()=>{\n        if (true) {\n            var _state_auth_user, _state_auth;\n            // @ts-ignore - Redux state is exposed for debugging\n            const state = window.__REDUX_STATE;\n            return (state === null || state === void 0 ? void 0 : (_state_auth = state.auth) === null || _state_auth === void 0 ? void 0 : (_state_auth_user = _state_auth.user) === null || _state_auth_user === void 0 ? void 0 : _state_auth_user.id) || 0;\n        }\n        return 0;\n    };\n    // Fetch user stores\n    const { data: userStoresData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery)(getCurrentUserId());\n    // Fetch default store\n    const { data: defaultStoreData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery)(getCurrentUserId());\n    // Set default store when data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (defaultStoreData === null || defaultStoreData === void 0 ? void 0 : defaultStoreData.data) {\n                setSelectedStore(defaultStoreData.data);\n                form.setFieldsValue({\n                    storeId: defaultStoreData.data.id\n                });\n            } else if ((userStoresData === null || userStoresData === void 0 ? void 0 : userStoresData.data) && userStoresData.data.length > 0) {\n                setSelectedStore(userStoresData.data[0]);\n                form.setFieldsValue({\n                    storeId: userStoresData.data[0].id\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        defaultStoreData,\n        userStoresData,\n        form\n    ]);\n    // Create sale mutation\n    const [createSale, { isLoading: isSubmitting }] = (0,_reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation)();\n    // Calculate total amount whenever items change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (items && items.length > 0) {\n                const total = items.reduce({\n                    \"SalesFormPanel.useEffect.total\": (sum, item)=>sum + item.price * item.quantity\n                }[\"SalesFormPanel.useEffect.total\"], 0);\n                setTotalAmount(total);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: total\n                    });\n                }\n                // Debug log to check items state\n                console.log(\"Current items in useEffect:\", items);\n            } else {\n                setTotalAmount(0);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: 0\n                    });\n                }\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items,\n        form\n    ]);\n    // Handle panel open/close\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (isOpen) {\n                // When panel opens, ensure we have fresh product data\n                console.log(\"🛒 Sales panel opened - fetching fresh product data\");\n                refetchProducts();\n            } else {\n                // Reset forms when panel is closed\n                if (form) {\n                    form.resetFields();\n                }\n                if (productForm) {\n                    productForm.resetFields();\n                }\n                setItems([]);\n                setSelectedProduct(null);\n                setQuantity(1);\n                setTotalAmount(0);\n                setReceiptUrl(null);\n                setReceiptPreviewVisible(false);\n                setHasPrinted(false);\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        isOpen,\n        form,\n        refetchProducts\n    ]);\n    // Handle adding an item to the sale\n    const handleAddItem = ()=>{\n        if (!selectedProduct) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"Please select a product\");\n            return;\n        }\n        if (quantity <= 0) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"Quantity must be greater than 0\");\n            return;\n        }\n        if (selectedProduct.stockQuantity < quantity) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"Only \".concat(selectedProduct.stockQuantity, \" units available in stock\"));\n            return;\n        }\n        console.log(\"Adding item with product:\", selectedProduct);\n        // Check if product already exists in items\n        const existingItemIndex = items.findIndex((item)=>item.productId === selectedProduct.id);\n        if (existingItemIndex >= 0) {\n            // Update existing item\n            const updatedItems = [\n                ...items\n            ];\n            const newQuantity = updatedItems[existingItemIndex].quantity + quantity;\n            if (newQuantity > selectedProduct.stockQuantity) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"Cannot add more than \".concat(selectedProduct.stockQuantity, \" units of this product\"));\n                return;\n            }\n            updatedItems[existingItemIndex].quantity = newQuantity;\n            // Make sure the product name is set\n            if (!updatedItems[existingItemIndex].productName) {\n                updatedItems[existingItemIndex].productName = selectedProduct.name;\n            }\n            // Update the state with the new array\n            console.log(\"Updating existing item. New items array:\", updatedItems);\n            setItems([\n                ...updatedItems\n            ]); // Create a new array reference to force re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"success\", \"Updated quantity of \".concat(selectedProduct.name));\n        } else {\n            // Add new item\n            const newItem = {\n                productId: selectedProduct.id,\n                productName: selectedProduct.name,\n                quantity,\n                price: typeof selectedProduct.price === \"string\" ? parseFloat(selectedProduct.price) : selectedProduct.price\n            };\n            // Create a new array with the new item\n            const newItems = [\n                ...items,\n                newItem\n            ];\n            // Update the state with the new array\n            console.log(\"Adding new item. New items array:\", newItems);\n            setItems(newItems); // This should trigger a re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"success\", \"Added \".concat(quantity, \" \").concat(selectedProduct.name, \" to sale\"));\n        }\n        // Reset selection\n        setSelectedProduct(null);\n        setQuantity(1);\n        if (productForm) {\n            productForm.setFieldsValue({\n                productId: undefined,\n                quantity: 1\n            });\n        }\n    };\n    // Handle removing an item from the sale\n    const handleRemoveItem = (index)=>{\n        const updatedItems = [\n            ...items\n        ];\n        updatedItems.splice(index, 1);\n        setItems(updatedItems);\n        (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"success\", \"Item removed from sale\");\n    };\n    // Handle updating quantity of an item in the cart\n    const handleUpdateItemQuantity = (index, newQuantity)=>{\n        var _productsData_data;\n        if (newQuantity <= 0) {\n            handleRemoveItem(index);\n            return;\n        }\n        const item = items[index];\n        const product = productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products.find((p)=>p.id === item.productId);\n        if (product && newQuantity > product.stockQuantity) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)('error', \"Only \".concat(product.stockQuantity, \" units available in stock\"));\n            return;\n        }\n        const updatedItems = [\n            ...items\n        ];\n        updatedItems[index] = {\n            ...item,\n            quantity: newQuantity\n        };\n        setItems(updatedItems);\n        (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)('success', 'Quantity updated');\n    };\n    // Handle printing receipt - directly trigger print dialog\n    const handlePrintReceipt = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n            if (!receiptUrl || hasPrinted) {\n                console.log(\"Skipping print: \", !receiptUrl ? \"No receipt URL\" : \"Already printed\");\n                return;\n            }\n            console.log(\"Printing receipt:\", receiptUrl);\n            // Mark as printed immediately to prevent multiple print dialogs\n            setHasPrinted(true);\n            // Create a hidden iframe to load the image\n            const iframe = document.createElement(\"iframe\");\n            iframe.style.display = \"none\";\n            document.body.appendChild(iframe);\n            // Set up the iframe content with the image and print CSS\n            iframe.onload = ({\n                \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                    if (iframe.contentWindow) {\n                        // Write the HTML content to the iframe\n                        iframe.contentWindow.document.write('\\n          <!DOCTYPE html>\\n          <html>\\n            <head>\\n              <title>Print Receipt</title>\\n              <style>\\n                body {\\n                  margin: 0;\\n                  padding: 0;\\n                  display: flex;\\n                  justify-content: center;\\n                  align-items: center;\\n                  height: 100vh;\\n                }\\n                img {\\n                  max-width: 100%;\\n                  max-height: 100vh;\\n                }\\n                @media print {\\n                  body {\\n                    margin: 0;\\n                    padding: 0;\\n                  }\\n                  img {\\n                    width: 100%;\\n                    height: auto;\\n                  }\\n                }\\n              </style>\\n            </head>\\n            <body>\\n              <img src=\"'.concat(receiptUrl, '\" alt=\"Receipt\" />\\n            </body>\\n          </html>\\n        '));\n                        // Close the document\n                        iframe.contentWindow.document.close();\n                        // Use a single print trigger with a delay to ensure the image is loaded\n                        setTimeout({\n                            \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                if (iframe.contentWindow) {\n                                    try {\n                                        // Print the iframe content\n                                        iframe.contentWindow.focus();\n                                        iframe.contentWindow.print();\n                                    } catch (e) {\n                                        console.error(\"Error printing receipt:\", e);\n                                    }\n                                    // Remove the iframe after printing\n                                    setTimeout({\n                                        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                            document.body.removeChild(iframe);\n                                        }\n                                    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 1000);\n                                }\n                            }\n                        }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 500);\n                    }\n                }\n            })[\"SalesFormPanel.useCallback[handlePrintReceipt]\"];\n            // Set the iframe source to trigger the onload event\n            iframe.src = \"about:blank\";\n        }\n    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], [\n        receiptUrl,\n        hasPrinted\n    ]);\n    // Effect to automatically print receipt when modal is shown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (receiptPreviewVisible && receiptUrl && !hasPrinted) {\n                // Add a small delay to ensure the receipt image is loaded\n                const timer = setTimeout({\n                    \"SalesFormPanel.useEffect.timer\": ()=>{\n                        handlePrintReceipt(); // This now handles the hasPrinted state internally\n                    }\n                }[\"SalesFormPanel.useEffect.timer\"], 800);\n                return ({\n                    \"SalesFormPanel.useEffect\": ()=>clearTimeout(timer)\n                })[\"SalesFormPanel.useEffect\"];\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        receiptPreviewVisible,\n        receiptUrl,\n        hasPrinted,\n        handlePrintReceipt\n    ]);\n    // Handle form submission\n    const handleSubmit = async ()=>{\n        try {\n            var _userStoresData_data;\n            if (items.length === 0) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"Please add at least one item to the sale\");\n                return;\n            }\n            // Validate form fields\n            const values = await form.validateFields();\n            // Check if store is selected\n            if (!selectedStore) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"No store information available. Please set up your store in your profile settings.\");\n                return;\n            }\n            // Set loading state for receipt generation\n            setIsGeneratingReceipt(true);\n            // Get store information for receipt\n            const storeInfo = selectedStore || (userStoresData === null || userStoresData === void 0 ? void 0 : (_userStoresData_data = userStoresData.data) === null || _userStoresData_data === void 0 ? void 0 : _userStoresData_data.find((store)=>store.id === values.storeId)) || {\n                name: \"POS System\"\n            };\n            // Generate receipt HTML\n            const receiptHTML = (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_9__.generateReceiptHTML)({\n                id: Date.now(),\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                transactionDate: new Date().toISOString(),\n                items: items.map((item)=>({\n                        productName: item.productName,\n                        quantity: item.quantity,\n                        price: item.price\n                    }))\n            }, storeInfo);\n            // Generate receipt image and get URL\n            let receiptUrl = \"https://receipt.example.com/placeholder\";\n            try {\n                receiptUrl = await (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_9__.generateReceiptImage)(receiptHTML);\n            } catch (error) {\n                console.error(\"Failed to generate receipt image:\", error);\n            // Continue with placeholder URL if image generation fails\n            }\n            const saleData = {\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                items: items.map((item)=>({\n                        productId: item.productId,\n                        quantity: item.quantity,\n                        price: item.price\n                    })),\n                receiptUrl,\n                storeId: selectedStore === null || selectedStore === void 0 ? void 0 : selectedStore.id\n            };\n            const response = await createSale(saleData).unwrap();\n            if (response.success) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"success\", \"Sale created successfully\");\n                // Store the receipt URL for preview\n                setReceiptUrl(receiptUrl);\n                // Show receipt preview modal and offer print option\n                setReceiptPreviewVisible(true);\n                // Refresh product data to get updated stock quantities\n                refetchProducts();\n                // Trigger the success callback to refresh the list WITHOUT closing the panel\n                setTimeout(()=>{\n                    if (onSuccess) {\n                        // Call refetch directly instead of closing the panel\n                        refetchProducts();\n                    }\n                }, 300);\n            // Keep the panel open until the user explicitly closes it\n            // This ensures the receipt modal stays visible\n            } else {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", response.message || \"Failed to create sale\");\n            }\n        } catch (error) {\n            var _error_data;\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", ((_error_data = error.data) === null || _error_data === void 0 ? void 0 : _error_data.message) || \"An error occurred while creating the sale\");\n        } finally{\n            setIsGeneratingReceipt(false);\n        }\n    };\n    // Debug log to check items state when rendering\n    console.log(\"Rendering with items:\", items);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        title: \"Point of Sale\",\n        isOpen: isOpen,\n        onClose: onClose,\n        width: \"95%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sales-form min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between border-b border-slate-200 bg-white/80 backdrop-blur-sm px-6 py-4 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"text-xl text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"m-0 text-xl font-bold text-slate-800\",\n                                                children: \"New Transaction\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-slate-500 m-0\",\n                                                children: \"Point of Sale System\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-slate-500 m-0\",\n                                        children: \"Total Amount\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 616,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent\",\n                                        children: [\n                                            \"GHS \",\n                                            totalAmount.toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 603,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-6 lg:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 rounded-2xl border border-slate-200 bg-white/70 backdrop-blur-sm p-6 shadow-xl shadow-slate-200/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg mr-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"text-sm text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 631,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 630,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-bold text-slate-800 m-0\",\n                                                                            children: \"Product Selection\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 634,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-slate-500 m-0\",\n                                                                            children: \"Choose products for this transaction\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 637,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 633,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-sm text-slate-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-1 text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 641,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \" Required fields\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    form: productForm,\n                                                    layout: \"vertical\",\n                                                    initialValues: {\n                                                        quantity: 1\n                                                    },\n                                                    className: \"product-form\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Item, {\n                                                                label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-700 font-medium\",\n                                                                    children: \"\\uD83D\\uDCF7 Hardware Barcode Scanner\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                className: \"mb-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    placeholder: \"Focus here and scan with barcode scanner device...\",\n                                                                    value: barcodeInput,\n                                                                    onChange: (e)=>handleBarcodeInputChange(e.target.value),\n                                                                    className: \"modern-select\",\n                                                                    size: \"large\",\n                                                                    autoFocus: true,\n                                                                    style: {\n                                                                        borderColor: '#10b981',\n                                                                        backgroundColor: '#f0fdf4'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 663,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 gap-6 md:grid-cols-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"md:col-span-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Item, {\n                                                                        name: \"productId\",\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-slate-700 font-medium\",\n                                                                            children: [\n                                                                                \"Product \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-500\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 684,\n                                                                                    columnNumber: 37\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 683,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        className: \"mb-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            showSearch: true,\n                                                                            placeholder: isLoadingProducts ? \"Loading products...\" : \"🔍 Search and select a product...\",\n                                                                            optionFilterProp: \"children\",\n                                                                            loading: isLoadingProducts,\n                                                                            disabled: isLoadingProducts,\n                                                                            onChange: (value)=>{\n                                                                                var _productsData_data;\n                                                                                const product = productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products.find((p)=>p.id === value);\n                                                                                console.log(\"Selected product:\", product);\n                                                                                if (product) {\n                                                                                    // Make a deep copy to avoid reference issues\n                                                                                    setSelectedProduct({\n                                                                                        ...product,\n                                                                                        // Ensure price is properly formatted\n                                                                                        price: typeof product.price === \"string\" ? product.price : String(product.price)\n                                                                                    });\n                                                                                } else {\n                                                                                    setSelectedProduct(null);\n                                                                                }\n                                                                            },\n                                                                            onSearch: setSearchTerm,\n                                                                            filterOption: false,\n                                                                            className: \"modern-select\",\n                                                                            size: \"large\",\n                                                                            suffixIcon: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                spin: true,\n                                                                                className: \"text-blue-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 724,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-center bg-gradient-to-r mr-3 mt-4\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"text-slate-400 \"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 727,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 726,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            notFoundContent: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-center py-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        size: \"small\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 734,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"ml-2 text-slate-500\",\n                                                                                        children: \"Loading products...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 735,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 733,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center py-4 text-slate-500\",\n                                                                                children: \"No products found\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 738,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            children: productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : (_productsData_data_products = _productsData_data.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Option, {\n                                                                                    value: product.id,\n                                                                                    disabled: product.stockQuantity <= 0,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"font-medium text-slate-800\",\n                                                                                                        children: product.name\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 752,\n                                                                                                        columnNumber: 35\n                                                                                                    }, undefined),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-sm text-slate-500\",\n                                                                                                        children: [\n                                                                                                            \"GHS \",\n                                                                                                            Number(product.price).toFixed(2)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 753,\n                                                                                                        columnNumber: 35\n                                                                                                    }, undefined)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                lineNumber: 751,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-sm px-2 py-1 rounded-full \".concat(product.stockQuantity <= 0 ? 'bg-red-100 text-red-600' : product.stockQuantity <= 5 ? 'bg-yellow-100 text-yellow-600' : 'bg-green-100 text-green-600'),\n                                                                                                children: product.stockQuantity <= 0 ? \"Out of Stock\" : \"Stock: \".concat(product.stockQuantity)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                lineNumber: 757,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 750,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, product.id, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 745,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 689,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 680,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Item, {\n                                                                        name: \"quantity\",\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-slate-700 font-medium\",\n                                                                            children: [\n                                                                                \"Quantity \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-500\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 780,\n                                                                                    columnNumber: 38\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 779,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        className: \"mb-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            min: 1,\n                                                                            value: quantity,\n                                                                            onChange: (value)=>setQuantity(value || 1),\n                                                                            style: {\n                                                                                width: \"100%\"\n                                                                            },\n                                                                            className: \"modern-input\",\n                                                                            size: \"large\",\n                                                                            placeholder: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 785,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 776,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 775,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 678,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4 space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    type: \"primary\",\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 801,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    onClick: handleAddItem,\n                                                                    className: \"h-14 w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 border-0 rounded-xl font-semibold text-base shadow-lg shadow-blue-200 transition-all duration-200 hover:shadow-xl hover:shadow-blue-300 hover:-translate-y-0.5\",\n                                                                    disabled: !selectedProduct,\n                                                                    size: \"large\",\n                                                                    children: \"Add to Cart\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 799,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 811,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    onClick: openScanner,\n                                                                    loading: isScannerLoading,\n                                                                    className: \"h-12 w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 border-0 rounded-xl font-semibold text-base shadow-lg shadow-purple-200 transition-all duration-200 hover:shadow-xl hover:shadow-purple-300 hover:-translate-y-0.5 text-white\",\n                                                                    size: \"large\",\n                                                                    children: isScannerLoading ? 'Searching...' : 'Scan Barcode'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 810,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 798,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 rounded-2xl border border-slate-200 bg-white/70 backdrop-blur-sm p-6 shadow-xl shadow-slate-200/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"text-sm text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 826,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-bold text-slate-800 m-0\",\n                                                                    children: \"Cart Items\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 830,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-500 m-0\",\n                                                                    children: \"Items added to this transaction\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 831,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 829,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 825,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-[400px] overflow-x-auto overflow-y-auto\",\n                                                    children: items.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-2xl border border-slate-200 bg-gradient-to-br from-slate-50 to-blue-50 p-12 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center w-16 h-16 bg-gradient-to-r from-slate-400 to-slate-500 rounded-full mx-auto mb-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"text-2xl text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 838,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 837,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-slate-700 mb-2\",\n                                                                children: \"Cart is Empty\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 840,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-slate-500\",\n                                                                children: \"Add products to start building your transaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 836,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-hidden rounded-2xl border border-slate-200 bg-white shadow-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-slate-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-12 gap-4 text-sm font-semibold text-slate-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-5\",\n                                                                            children: \"Product\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 847,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 text-center\",\n                                                                            children: \"Qty\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 848,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 text-right\",\n                                                                            children: \"Price\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 849,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 text-right\",\n                                                                            children: \"Subtotal\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 850,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-1 text-center\",\n                                                                            children: \"Action\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 851,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 845,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"divide-y divide-slate-100\",\n                                                                children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"px-6 py-4 hover:bg-slate-50 transition-colors duration-150\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-12 gap-4 items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-5\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-semibold text-slate-800\",\n                                                                                        children: item.productName || \"Unknown Product\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 862,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 861,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-2 text-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-center space-x-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                                size: \"small\",\n                                                                                                type: \"text\",\n                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-xs\",\n                                                                                                    children: \"−\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 871,\n                                                                                                    columnNumber: 43\n                                                                                                }, void 0),\n                                                                                                onClick: ()=>handleUpdateItemQuantity(index, item.quantity - 1),\n                                                                                                className: \"w-6 h-6 flex items-center justify-center text-slate-500 hover:text-red-600 hover:bg-red-50 rounded\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                lineNumber: 868,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                                min: 1,\n                                                                                                value: item.quantity,\n                                                                                                onChange: (value)=>handleUpdateItemQuantity(index, value || 1),\n                                                                                                className: \"w-16 text-center\",\n                                                                                                size: \"small\",\n                                                                                                controls: false,\n                                                                                                style: {\n                                                                                                    textAlign: 'center'\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                lineNumber: 875,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                                size: \"small\",\n                                                                                                type: \"text\",\n                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-xs\",\n                                                                                                    children: \"+\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 887,\n                                                                                                    columnNumber: 43\n                                                                                                }, void 0),\n                                                                                                onClick: ()=>handleUpdateItemQuantity(index, item.quantity + 1),\n                                                                                                className: \"w-6 h-6 flex items-center justify-center text-slate-500 hover:text-green-600 hover:bg-green-50 rounded\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                lineNumber: 884,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 867,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 866,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-2 text-right text-slate-600 font-medium\",\n                                                                                    children: [\n                                                                                        \"GHS \",\n                                                                                        Number(item.price).toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 893,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-2 text-right font-bold text-slate-800\",\n                                                                                    children: [\n                                                                                        \"GHS \",\n                                                                                        (Number(item.price) * item.quantity).toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 896,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-1 text-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 901,\n                                                                                            columnNumber: 41\n                                                                                        }, void 0),\n                                                                                        onClick: ()=>handleRemoveItem(index),\n                                                                                        type: \"text\",\n                                                                                        danger: true,\n                                                                                        className: \"text-red-500 hover:bg-red-50 hover:text-red-600 rounded-lg\",\n                                                                                        size: \"small\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 900,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 899,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 860,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, \"\".concat(item.productId, \"-\").concat(index), false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 856,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 854,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-emerald-50 to-green-50 px-6 py-4 border-t border-slate-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-12 gap-4 items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-9 text-right\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg font-bold text-slate-800\",\n                                                                                children: \"Total:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 916,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 915,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-3 text-right\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    totalAmount.toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 919,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 918,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 914,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 913,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 844,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 834,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 824,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sticky top-4 rounded-2xl border border-slate-200 bg-white/70 backdrop-blur-sm p-6 shadow-xl shadow-slate-200/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg mr-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"text-sm text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 936,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 935,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-bold text-slate-800 m-0\",\n                                                                children: \"Checkout\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 939,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-slate-500 m-0\",\n                                                                children: \"Complete your transaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 940,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 938,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 934,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                form: form,\n                                                layout: \"vertical\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 rounded-xl border border-slate-200 bg-gradient-to-br from-slate-50 to-blue-50 p-5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-slate-600\",\n                                                                        children: \"Items:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 947,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full text-sm font-bold\",\n                                                                        children: items.length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 948,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 946,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-slate-600\",\n                                                                        children: \"Total Quantity:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 953,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-800 rounded-full text-sm font-bold\",\n                                                                        children: items.reduce((sum, item)=>sum + item.quantity, 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 954,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 952,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-slate-600\",\n                                                                        children: \"Subtotal:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 959,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-semibold text-slate-800\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 960,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 958,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"my-4 border-t border-slate-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 964,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-bold text-slate-800\",\n                                                                        children: \"Total:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 966,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 967,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 965,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 945,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    selectedStore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"mb-3 block text-slate-700 font-medium\",\n                                                                children: \"Store\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 976,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center rounded-xl border border-slate-200 bg-gradient-to-r from-blue-50 to-indigo-50 p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg mr-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"text-sm text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 979,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 978,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-semibold text-slate-800\",\n                                                                        children: selectedStore.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 981,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"hidden\",\n                                                                        name: \"storeId\",\n                                                                        value: selectedStore.id\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 984,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 977,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 975,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 rounded-xl border border-orange-200 bg-gradient-to-r from-orange-50 to-yellow-50 p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-lg mr-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"text-sm text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 995,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 994,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-orange-800 m-0\",\n                                                                            children: \"Store Setup Required\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 998,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-orange-600 m-0\",\n                                                                            children: \"Please set up your store in profile settings.\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 999,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 997,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 993,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 992,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Item, {\n                                                        name: \"paymentMethod\",\n                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-700 font-medium\",\n                                                            children: [\n                                                                \"Payment Method \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1011,\n                                                                    columnNumber: 40\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 1010,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        rules: [\n                                                            {\n                                                                required: true,\n                                                                message: \"Please select a payment method\"\n                                                            }\n                                                        ],\n                                                        initialValue: \"cash\",\n                                                        className: \"mb-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"modern-select\",\n                                                            size: \"large\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Option, {\n                                                                    value: \"cash\",\n                                                                    children: \"\\uD83D\\uDCB5 Cash\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1024,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Option, {\n                                                                    value: \"card\",\n                                                                    children: \"\\uD83D\\uDCB3 Card\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1027,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Option, {\n                                                                    value: \"mobile_money\",\n                                                                    children: \"\\uD83D\\uDCF1 Mobile Money\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1030,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 1023,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 1007,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-8 space-y-4\",\n                                                        children: [\n                                                            receiptPreviewVisible ? // Show \"Done\" button when receipt is visible\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1041,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                onClick: ()=>{\n                                                                    // Close the modal and reset the receipt state\n                                                                    setReceiptPreviewVisible(false);\n                                                                    setReceiptUrl(null);\n                                                                    setHasPrinted(false);\n                                                                    // Reset the forms to start a new sale\n                                                                    form.resetFields();\n                                                                    productForm.resetFields();\n                                                                    setItems([]);\n                                                                    setSelectedProduct(null);\n                                                                    setQuantity(1);\n                                                                    setTotalAmount(0);\n                                                                },\n                                                                className: \"h-14 w-full bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 border-0 rounded-xl font-semibold text-base shadow-lg shadow-emerald-200 transition-all duration-200 hover:shadow-xl hover:shadow-emerald-300 hover:-translate-y-0.5\",\n                                                                size: \"large\",\n                                                                children: \"Start New Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1039,\n                                                                columnNumber: 23\n                                                            }, undefined) : // Show \"Complete Sale\" button when creating a sale\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1065,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                onClick: handleSubmit,\n                                                                loading: isSubmitting || isGeneratingReceipt,\n                                                                disabled: items.length === 0,\n                                                                className: \"h-14 w-full bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 border-0 rounded-xl font-semibold text-base shadow-lg shadow-emerald-200 transition-all duration-200 hover:shadow-xl hover:shadow-emerald-300 hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:transform-none disabled:hover:shadow-lg\",\n                                                                size: \"large\",\n                                                                children: isGeneratingReceipt ? \"Generating Receipt\" : \"Complete Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1063,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                onClick: onClose,\n                                                                className: \"h-12 w-full border-slate-300 bg-slate-100 text-slate-700 hover:bg-slate-200 rounded-xl font-medium transition-all duration-200 hover:shadow-md\",\n                                                                size: \"large\",\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 1078,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 1036,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 944,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 933,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 932,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 624,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 623,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 601,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-gray-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                            className: \"mr-2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1097,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Receipt Preview\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1098,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 1096,\n                    columnNumber: 11\n                }, void 0),\n                open: receiptPreviewVisible,\n                onCancel: ()=>{\n                    // Close the modal and reset the receipt state\n                    setReceiptPreviewVisible(false);\n                    setReceiptUrl(null);\n                    setHasPrinted(false);\n                    // Reset the forms to start a new sale\n                    form.resetFields();\n                    productForm.resetFields();\n                    setItems([]);\n                    setSelectedProduct(null);\n                    setQuantity(1);\n                    setTotalAmount(0);\n                },\n                width: 500,\n                centered: true,\n                className: \"receipt-preview-modal\",\n                footer: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        onClick: ()=>{\n                            // Close the modal and reset the receipt state\n                            setReceiptPreviewVisible(false);\n                            setReceiptUrl(null);\n                            setHasPrinted(false);\n                            // Reset the forms to start a new sale\n                            form.resetFields();\n                            productForm.resetFields();\n                            setItems([]);\n                            setSelectedProduct(null);\n                            setQuantity(1);\n                            setTotalAmount(0);\n                        },\n                        className: \"border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200\",\n                        children: \"Close & New Sale\"\n                    }, \"close\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1120,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        type: \"primary\",\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1143,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: ()=>{\n                            // If already printed once, reset the flag to allow printing again\n                            if (hasPrinted) {\n                                setHasPrinted(false);\n                            }\n                            handlePrintReceipt();\n                        },\n                        className: \"bg-blue-600 hover:bg-blue-700\",\n                        children: hasPrinted ? \"Print Again\" : \"Print Receipt\"\n                    }, \"print\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1140,\n                        columnNumber: 11\n                    }, void 0)\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: receiptUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"receipt-image-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                            src: receiptUrl,\n                            alt: \"Receipt\",\n                            className: \"receipt-image\",\n                            style: {\n                                maxWidth: \"100%\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1160,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1159,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-64 items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            size: \"large\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1169,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1168,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 1157,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 1094,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BarcodeScanner_BarcodeScanner__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isScannerOpen,\n                onClose: closeScanner,\n                onScan: handleBarcodeScanned,\n                title: \"Scan Product Barcode\"\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 1176,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n        lineNumber: 595,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SalesFormPanel, \"cSW9e09S5a5AlxqCiHM5E8H+s9k=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Form_Image_Input_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].useForm,\n        _hooks_useBarcodeScanner__WEBPACK_IMPORTED_MODULE_7__.useBarcodeScanner,\n        _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery,\n        _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation\n    ];\n});\n_c = SalesFormPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SalesFormPanel);\nvar _c;\n$RefreshReg$(_c, \"SalesFormPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx\n"));

/***/ })

});