"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/sales/page",{

/***/ "(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx":
/*!*************************************************!*\
  !*** ./src/components/Sales/SalesFormPanel.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/image/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PrinterOutlined.js\");\n/* harmony import */ var _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/reduxRTK/services/salesApi */ \"(app-pages-browser)/./src/reduxRTK/services/salesApi.ts\");\n/* harmony import */ var _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/reduxRTK/services/productApi */ \"(app-pages-browser)/./src/reduxRTK/services/productApi.ts\");\n/* harmony import */ var _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/reduxRTK/services/userStoreApi */ \"(app-pages-browser)/./src/reduxRTK/services/userStoreApi.ts\");\n/* harmony import */ var _components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/SlidingPanel */ \"(app-pages-browser)/./src/components/ui/SlidingPanel.tsx\");\n/* harmony import */ var _utils_showMessage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/showMessage */ \"(app-pages-browser)/./src/utils/showMessage.ts\");\n/* harmony import */ var _utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/cloudinaryUtils */ \"(app-pages-browser)/./src/utils/cloudinaryUtils.ts\");\n/* harmony import */ var _sales_panels_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./sales-panels.css */ \"(app-pages-browser)/./src/components/Sales/sales-panels.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst SalesFormPanel = (param)=>{\n    let { isOpen, onClose, onSuccess } = param;\n    var _productsData_data_products, _productsData_data;\n    _s();\n    const [form] = _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm();\n    const [productForm] = _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm();\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalAmount, setTotalAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingReceipt, setIsGeneratingReceipt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptPreviewVisible, setReceiptPreviewVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptUrl, setReceiptUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPrinted, setHasPrinted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            console.log(\"Items state changed:\", items);\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items\n    ]);\n    const { data: productsData, isLoading: isLoadingProducts, refetch: refetchProducts } = (0,_reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery)({\n        page: 1,\n        limit: 1000,\n        search: searchTerm\n    }, {\n        // Force refetch when component mounts and when panel opens\n        refetchOnMountOrArgChange: true,\n        refetchOnFocus: false,\n        refetchOnReconnect: true\n    });\n    // Debug products data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (productsData) {\n                var _productsData_data, _productsData_data_products, _productsData_data1;\n                console.log(\"🛒 Products loaded:\", {\n                    total: ((_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.total) || 0,\n                    productsCount: ((_productsData_data1 = productsData.data) === null || _productsData_data1 === void 0 ? void 0 : (_productsData_data_products = _productsData_data1.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.length) || 0,\n                    isLoading: isLoadingProducts\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        productsData,\n        isLoadingProducts\n    ]);\n    // Get current user ID from auth state\n    const getCurrentUserId = ()=>{\n        if (true) {\n            var _state_auth_user, _state_auth;\n            // @ts-ignore - Redux state is exposed for debugging\n            const state = window.__REDUX_STATE;\n            return (state === null || state === void 0 ? void 0 : (_state_auth = state.auth) === null || _state_auth === void 0 ? void 0 : (_state_auth_user = _state_auth.user) === null || _state_auth_user === void 0 ? void 0 : _state_auth_user.id) || 0;\n        }\n        return 0;\n    };\n    // Fetch user stores\n    const { data: userStoresData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery)(getCurrentUserId());\n    // Fetch default store\n    const { data: defaultStoreData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery)(getCurrentUserId());\n    // Set default store when data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (defaultStoreData === null || defaultStoreData === void 0 ? void 0 : defaultStoreData.data) {\n                setSelectedStore(defaultStoreData.data);\n                form.setFieldsValue({\n                    storeId: defaultStoreData.data.id\n                });\n            } else if ((userStoresData === null || userStoresData === void 0 ? void 0 : userStoresData.data) && userStoresData.data.length > 0) {\n                setSelectedStore(userStoresData.data[0]);\n                form.setFieldsValue({\n                    storeId: userStoresData.data[0].id\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        defaultStoreData,\n        userStoresData,\n        form\n    ]);\n    // Create sale mutation\n    const [createSale, { isLoading: isSubmitting }] = (0,_reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation)();\n    // Calculate total amount whenever items change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (items && items.length > 0) {\n                const total = items.reduce({\n                    \"SalesFormPanel.useEffect.total\": (sum, item)=>sum + item.price * item.quantity\n                }[\"SalesFormPanel.useEffect.total\"], 0);\n                setTotalAmount(total);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: total\n                    });\n                }\n                // Debug log to check items state\n                console.log(\"Current items in useEffect:\", items);\n            } else {\n                setTotalAmount(0);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: 0\n                    });\n                }\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items,\n        form\n    ]);\n    // Handle panel open/close\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (isOpen) {\n                // When panel opens, ensure we have fresh product data\n                console.log(\"🛒 Sales panel opened - fetching fresh product data\");\n                refetchProducts();\n            } else {\n                // Reset forms when panel is closed\n                if (form) {\n                    form.resetFields();\n                }\n                if (productForm) {\n                    productForm.resetFields();\n                }\n                setItems([]);\n                setSelectedProduct(null);\n                setQuantity(1);\n                setTotalAmount(0);\n                setReceiptUrl(null);\n                setReceiptPreviewVisible(false);\n                setHasPrinted(false);\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        isOpen,\n        form,\n        refetchProducts\n    ]);\n    // Handle adding an item to the sale\n    const handleAddItem = ()=>{\n        if (!selectedProduct) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Please select a product\");\n            return;\n        }\n        if (quantity <= 0) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Quantity must be greater than 0\");\n            return;\n        }\n        if (selectedProduct.stockQuantity < quantity) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Only \".concat(selectedProduct.stockQuantity, \" units available in stock\"));\n            return;\n        }\n        console.log(\"Adding item with product:\", selectedProduct);\n        // Check if product already exists in items\n        const existingItemIndex = items.findIndex((item)=>item.productId === selectedProduct.id);\n        if (existingItemIndex >= 0) {\n            // Update existing item\n            const updatedItems = [\n                ...items\n            ];\n            const newQuantity = updatedItems[existingItemIndex].quantity + quantity;\n            if (newQuantity > selectedProduct.stockQuantity) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Cannot add more than \".concat(selectedProduct.stockQuantity, \" units of this product\"));\n                return;\n            }\n            updatedItems[existingItemIndex].quantity = newQuantity;\n            // Make sure the product name is set\n            if (!updatedItems[existingItemIndex].productName) {\n                updatedItems[existingItemIndex].productName = selectedProduct.name;\n            }\n            // Update the state with the new array\n            console.log(\"Updating existing item. New items array:\", updatedItems);\n            setItems([\n                ...updatedItems\n            ]); // Create a new array reference to force re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Updated quantity of \".concat(selectedProduct.name));\n        } else {\n            // Add new item\n            const newItem = {\n                productId: selectedProduct.id,\n                productName: selectedProduct.name,\n                quantity,\n                price: typeof selectedProduct.price === \"string\" ? parseFloat(selectedProduct.price) : selectedProduct.price\n            };\n            // Create a new array with the new item\n            const newItems = [\n                ...items,\n                newItem\n            ];\n            // Update the state with the new array\n            console.log(\"Adding new item. New items array:\", newItems);\n            setItems(newItems); // This should trigger a re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Added \".concat(quantity, \" \").concat(selectedProduct.name, \" to sale\"));\n        }\n        // Reset selection\n        setSelectedProduct(null);\n        setQuantity(1);\n        if (productForm) {\n            productForm.setFieldsValue({\n                productId: undefined,\n                quantity: 1\n            });\n        }\n    };\n    // Handle removing an item from the sale\n    const handleRemoveItem = (index)=>{\n        const updatedItems = [\n            ...items\n        ];\n        updatedItems.splice(index, 1);\n        setItems(updatedItems);\n    };\n    // Handle printing receipt - directly trigger print dialog\n    const handlePrintReceipt = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n            if (!receiptUrl || hasPrinted) {\n                console.log(\"Skipping print: \", !receiptUrl ? \"No receipt URL\" : \"Already printed\");\n                return;\n            }\n            console.log(\"Printing receipt:\", receiptUrl);\n            // Mark as printed immediately to prevent multiple print dialogs\n            setHasPrinted(true);\n            // Create a hidden iframe to load the image\n            const iframe = document.createElement(\"iframe\");\n            iframe.style.display = \"none\";\n            document.body.appendChild(iframe);\n            // Set up the iframe content with the image and print CSS\n            iframe.onload = ({\n                \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                    if (iframe.contentWindow) {\n                        // Write the HTML content to the iframe\n                        iframe.contentWindow.document.write('\\n          <!DOCTYPE html>\\n          <html>\\n            <head>\\n              <title>Print Receipt</title>\\n              <style>\\n                body {\\n                  margin: 0;\\n                  padding: 0;\\n                  display: flex;\\n                  justify-content: center;\\n                  align-items: center;\\n                  height: 100vh;\\n                }\\n                img {\\n                  max-width: 100%;\\n                  max-height: 100vh;\\n                }\\n                @media print {\\n                  body {\\n                    margin: 0;\\n                    padding: 0;\\n                  }\\n                  img {\\n                    width: 100%;\\n                    height: auto;\\n                  }\\n                }\\n              </style>\\n            </head>\\n            <body>\\n              <img src=\"'.concat(receiptUrl, '\" alt=\"Receipt\" />\\n            </body>\\n          </html>\\n        '));\n                        // Close the document\n                        iframe.contentWindow.document.close();\n                        // Use a single print trigger with a delay to ensure the image is loaded\n                        setTimeout({\n                            \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                if (iframe.contentWindow) {\n                                    try {\n                                        // Print the iframe content\n                                        iframe.contentWindow.focus();\n                                        iframe.contentWindow.print();\n                                    } catch (e) {\n                                        console.error(\"Error printing receipt:\", e);\n                                    }\n                                    // Remove the iframe after printing\n                                    setTimeout({\n                                        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                            document.body.removeChild(iframe);\n                                        }\n                                    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 1000);\n                                }\n                            }\n                        }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 500);\n                    }\n                }\n            })[\"SalesFormPanel.useCallback[handlePrintReceipt]\"];\n            // Set the iframe source to trigger the onload event\n            iframe.src = \"about:blank\";\n        }\n    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], [\n        receiptUrl,\n        hasPrinted\n    ]);\n    // Effect to automatically print receipt when modal is shown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (receiptPreviewVisible && receiptUrl && !hasPrinted) {\n                // Add a small delay to ensure the receipt image is loaded\n                const timer = setTimeout({\n                    \"SalesFormPanel.useEffect.timer\": ()=>{\n                        handlePrintReceipt(); // This now handles the hasPrinted state internally\n                    }\n                }[\"SalesFormPanel.useEffect.timer\"], 800);\n                return ({\n                    \"SalesFormPanel.useEffect\": ()=>clearTimeout(timer)\n                })[\"SalesFormPanel.useEffect\"];\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        receiptPreviewVisible,\n        receiptUrl,\n        hasPrinted,\n        handlePrintReceipt\n    ]);\n    // Handle form submission\n    const handleSubmit = async ()=>{\n        try {\n            var _userStoresData_data;\n            if (items.length === 0) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Please add at least one item to the sale\");\n                return;\n            }\n            // Validate form fields\n            const values = await form.validateFields();\n            // Check if store is selected\n            if (!selectedStore) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"No store information available. Please set up your store in your profile settings.\");\n                return;\n            }\n            // Set loading state for receipt generation\n            setIsGeneratingReceipt(true);\n            // Get store information for receipt\n            const storeInfo = selectedStore || (userStoresData === null || userStoresData === void 0 ? void 0 : (_userStoresData_data = userStoresData.data) === null || _userStoresData_data === void 0 ? void 0 : _userStoresData_data.find((store)=>store.id === values.storeId)) || {\n                name: \"POS System\"\n            };\n            // Generate receipt HTML\n            const receiptHTML = (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__.generateReceiptHTML)({\n                id: Date.now(),\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                transactionDate: new Date().toISOString(),\n                items: items.map((item)=>({\n                        productName: item.productName,\n                        quantity: item.quantity,\n                        price: item.price\n                    }))\n            }, storeInfo);\n            // Generate receipt image and get URL\n            let receiptUrl = \"https://receipt.example.com/placeholder\";\n            try {\n                receiptUrl = await (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__.generateReceiptImage)(receiptHTML);\n            } catch (error) {\n                console.error(\"Failed to generate receipt image:\", error);\n            // Continue with placeholder URL if image generation fails\n            }\n            const saleData = {\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                items: items.map((item)=>({\n                        productId: item.productId,\n                        quantity: item.quantity,\n                        price: item.price\n                    })),\n                receiptUrl,\n                storeId: selectedStore === null || selectedStore === void 0 ? void 0 : selectedStore.id\n            };\n            const response = await createSale(saleData).unwrap();\n            if (response.success) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Sale created successfully\");\n                // Store the receipt URL for preview\n                setReceiptUrl(receiptUrl);\n                // Show receipt preview modal and offer print option\n                setReceiptPreviewVisible(true);\n                // Refresh product data to get updated stock quantities\n                refetchProducts();\n                // Trigger the success callback to refresh the list WITHOUT closing the panel\n                setTimeout(()=>{\n                    if (onSuccess) {\n                        // Call refetch directly instead of closing the panel\n                        refetchProducts();\n                    }\n                }, 300);\n            // Keep the panel open until the user explicitly closes it\n            // This ensures the receipt modal stays visible\n            } else {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", response.message || \"Failed to create sale\");\n            }\n        } catch (error) {\n            var _error_data;\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", ((_error_data = error.data) === null || _error_data === void 0 ? void 0 : _error_data.message) || \"An error occurred while creating the sale\");\n        } finally{\n            setIsGeneratingReceipt(false);\n        }\n    };\n    // Debug log to check items state when rendering\n    console.log(\"Rendering with items:\", items);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        title: \"Point of Sale\",\n        isOpen: isOpen,\n        onClose: onClose,\n        width: \"95%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sales-form min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between border-b border-slate-200 bg-white/80 backdrop-blur-sm px-6 py-4 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"text-xl text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"m-0 text-xl font-bold text-slate-800\",\n                                                children: \"New Transaction\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-slate-500 m-0\",\n                                                children: \"Point of Sale System\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-slate-500 m-0\",\n                                        children: \"Total Amount\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent\",\n                                        children: [\n                                            \"GHS \",\n                                            totalAmount.toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 496,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-6 lg:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 rounded-2xl border border-slate-200 bg-white/70 backdrop-blur-sm p-6 shadow-xl shadow-slate-200/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg mr-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"text-sm text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 524,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-bold text-slate-800 m-0\",\n                                                                            children: \"Product Selection\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 527,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-slate-500 m-0\",\n                                                                            children: \"Choose products for this transaction\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 526,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-sm text-slate-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-1 text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \" Required fields\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    form: productForm,\n                                                    layout: \"vertical\",\n                                                    initialValues: {\n                                                        quantity: 1\n                                                    },\n                                                    className: \"product-form\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 gap-6 md:grid-cols-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"md:col-span-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                                        name: \"productId\",\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-slate-700 font-medium\",\n                                                                            children: [\n                                                                                \"Product \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-500\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 552,\n                                                                                    columnNumber: 37\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 551,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        className: \"mb-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            showSearch: true,\n                                                                            placeholder: isLoadingProducts ? \"Loading products...\" : \"🔍 Search and select a product...\",\n                                                                            optionFilterProp: \"children\",\n                                                                            loading: isLoadingProducts,\n                                                                            disabled: isLoadingProducts,\n                                                                            onChange: (value)=>{\n                                                                                var _productsData_data;\n                                                                                const product = productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products.find((p)=>p.id === value);\n                                                                                console.log(\"Selected product:\", product);\n                                                                                if (product) {\n                                                                                    // Make a deep copy to avoid reference issues\n                                                                                    setSelectedProduct({\n                                                                                        ...product,\n                                                                                        // Ensure price is properly formatted\n                                                                                        price: typeof product.price === \"string\" ? product.price : String(product.price)\n                                                                                    });\n                                                                                } else {\n                                                                                    setSelectedProduct(null);\n                                                                                }\n                                                                            },\n                                                                            onSearch: setSearchTerm,\n                                                                            filterOption: false,\n                                                                            className: \"modern-select\",\n                                                                            size: \"large\",\n                                                                            suffixIcon: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                spin: true,\n                                                                                className: \"text-blue-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 592,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"text-slate-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 594,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            notFoundContent: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-center py-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        size: \"small\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 600,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"ml-2 text-slate-500\",\n                                                                                        children: \"Loading products...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 601,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 599,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center py-4 text-slate-500\",\n                                                                                children: \"No products found\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 604,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            children: productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : (_productsData_data_products = _productsData_data.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Option, {\n                                                                                    value: product.id,\n                                                                                    disabled: product.stockQuantity <= 0,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"font-medium text-slate-800\",\n                                                                                                        children: product.name\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 618,\n                                                                                                        columnNumber: 35\n                                                                                                    }, undefined),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-sm text-slate-500\",\n                                                                                                        children: [\n                                                                                                            \"GHS \",\n                                                                                                            Number(product.price).toFixed(2)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 619,\n                                                                                                        columnNumber: 35\n                                                                                                    }, undefined)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                lineNumber: 617,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-sm px-2 py-1 rounded-full \".concat(product.stockQuantity <= 0 ? 'bg-red-100 text-red-600' : product.stockQuantity <= 5 ? 'bg-yellow-100 text-yellow-600' : 'bg-green-100 text-green-600'),\n                                                                                                children: product.stockQuantity <= 0 ? \"Out of Stock\" : \"Stock: \".concat(product.stockQuantity)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                lineNumber: 623,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 616,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, product.id, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 611,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 557,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 548,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                                        name: \"quantity\",\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-slate-700 font-medium\",\n                                                                            children: [\n                                                                                \"Quantity \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-500\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 646,\n                                                                                    columnNumber: 38\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 645,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        className: \"mb-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            min: 1,\n                                                                            value: quantity,\n                                                                            onChange: (value)=>setQuantity(value || 1),\n                                                                            style: {\n                                                                                width: \"100%\"\n                                                                            },\n                                                                            className: \"modern-input\",\n                                                                            size: \"large\",\n                                                                            placeholder: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 651,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 642,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 641,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            type: \"primary\",\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 666,\n                                                                columnNumber: 27\n                                                            }, void 0),\n                                                            onClick: handleAddItem,\n                                                            className: \"mt-4 h-14 w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 border-0 rounded-xl font-semibold text-base shadow-lg shadow-blue-200 transition-all duration-200 hover:shadow-xl hover:shadow-blue-300 hover:-translate-y-0.5\",\n                                                            disabled: !selectedProduct,\n                                                            size: \"large\",\n                                                            children: \"Add to Cart\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 rounded-2xl border border-slate-200 bg-white/70 backdrop-blur-sm p-6 shadow-xl shadow-slate-200/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"text-sm text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 681,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 680,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-bold text-slate-800 m-0\",\n                                                                    children: \"Cart Items\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-500 m-0\",\n                                                                    children: \"Items added to this transaction\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 683,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-[400px] overflow-x-auto overflow-y-auto\",\n                                                    children: items.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-2xl border border-slate-200 bg-gradient-to-br from-slate-50 to-blue-50 p-12 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center w-16 h-16 bg-gradient-to-r from-slate-400 to-slate-500 rounded-full mx-auto mb-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"text-2xl text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 691,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-slate-700 mb-2\",\n                                                                children: \"Cart is Empty\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-slate-500\",\n                                                                children: \"Add products to start building your transaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-hidden rounded-2xl border border-slate-200 bg-white shadow-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-slate-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-12 gap-4 text-sm font-semibold text-slate-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-5\",\n                                                                            children: \"Product\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 701,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 text-center\",\n                                                                            children: \"Qty\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 702,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 text-right\",\n                                                                            children: \"Price\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 703,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 text-right\",\n                                                                            children: \"Subtotal\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 704,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-1 text-center\",\n                                                                            children: \"Action\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 705,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 700,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 699,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"divide-y divide-slate-100\",\n                                                                children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"px-6 py-4 hover:bg-slate-50 transition-colors duration-150\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-12 gap-4 items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-5\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-semibold text-slate-800\",\n                                                                                        children: item.productName || \"Unknown Product\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 716,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 715,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-2 text-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"inline-flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full text-sm font-medium\",\n                                                                                        children: item.quantity\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 721,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 720,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-2 text-right text-slate-600 font-medium\",\n                                                                                    children: [\n                                                                                        \"GHS \",\n                                                                                        Number(item.price).toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 725,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-2 text-right font-bold text-slate-800\",\n                                                                                    children: [\n                                                                                        \"GHS \",\n                                                                                        (Number(item.price) * item.quantity).toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 728,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-1 text-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 733,\n                                                                                            columnNumber: 41\n                                                                                        }, void 0),\n                                                                                        onClick: ()=>handleRemoveItem(index),\n                                                                                        type: \"text\",\n                                                                                        danger: true,\n                                                                                        className: \"text-red-500 hover:bg-red-50 hover:text-red-600 rounded-lg\",\n                                                                                        size: \"small\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 732,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 731,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 714,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, \"\".concat(item.productId, \"-\").concat(index), false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 710,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 708,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-emerald-50 to-green-50 px-6 py-4 border-t border-slate-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-12 gap-4 items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-9 text-right\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg font-bold text-slate-800\",\n                                                                                children: \"Total:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 748,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 747,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-3 text-right\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    totalAmount.toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 751,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 750,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 746,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 745,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 688,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sticky top-4 rounded-2xl border border-slate-200 bg-white/70 backdrop-blur-sm p-6 shadow-xl shadow-slate-200/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg mr-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"text-sm text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-bold text-slate-800 m-0\",\n                                                                children: \"Checkout\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-slate-500 m-0\",\n                                                                children: \"Complete your transaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 772,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                form: form,\n                                                layout: \"vertical\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 rounded-lg border border-gray-200 bg-gray-50 p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"Items:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 779,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-gray-800\",\n                                                                        children: items.length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 780,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 778,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"Total Quantity:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 785,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-gray-800\",\n                                                                        children: items.reduce((sum, item)=>sum + item.quantity, 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 786,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 784,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between text-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"Subtotal:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 791,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-gray-800\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 792,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 790,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"my-4 border-t border-gray-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 796,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-800\",\n                                                                        children: \"Total:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 798,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-green-600\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 799,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 797,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 777,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    selectedStore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"mb-2 block text-gray-700\",\n                                                                children: \"Store\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 808,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center rounded-lg border border-gray-200 bg-gray-50 p-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"mr-2 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 810,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-800\",\n                                                                        children: selectedStore.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 811,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"hidden\",\n                                                                        name: \"storeId\",\n                                                                        value: selectedStore.id\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 814,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 809,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 807,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4 rounded-lg border border-gray-200 bg-gray-50 p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"No store information available. Please set up your store in your profile settings.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 823,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                        name: \"paymentMethod\",\n                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-700\",\n                                                            children: [\n                                                                \"Payment Method \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 834,\n                                                                    columnNumber: 40\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 833,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        rules: [\n                                                            {\n                                                                required: true,\n                                                                message: \"Please select a payment method\"\n                                                            }\n                                                        ],\n                                                        initialValue: \"cash\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"text-gray-800\",\n                                                            size: \"large\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Option, {\n                                                                    value: \"cash\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mr-2\",\n                                                                                children: \"\\uD83D\\uDCB5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 848,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \" Cash\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 847,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Option, {\n                                                                    value: \"card\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mr-2\",\n                                                                                children: \"\\uD83D\\uDCB3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 853,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \" Card\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 852,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 851,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Option, {\n                                                                    value: \"mobile_money\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mr-2\",\n                                                                                children: \"\\uD83D\\uDCF1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 858,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \" Mobile Money\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 857,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 856,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 845,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 830,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-8 space-y-4\",\n                                                        children: [\n                                                            receiptPreviewVisible ? // Show \"Done\" button when receipt is visible\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 869,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                onClick: ()=>{\n                                                                    // Close the modal and reset the receipt state\n                                                                    setReceiptPreviewVisible(false);\n                                                                    setReceiptUrl(null);\n                                                                    setHasPrinted(false);\n                                                                    // Reset the forms to start a new sale\n                                                                    form.resetFields();\n                                                                    productForm.resetFields();\n                                                                    setItems([]);\n                                                                    setSelectedProduct(null);\n                                                                    setQuantity(1);\n                                                                    setTotalAmount(0);\n                                                                },\n                                                                className: \"text-md font-small h-14 w-full bg-green-600 hover:bg-green-700\",\n                                                                size: \"large\",\n                                                                children: \"Start New Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 867,\n                                                                columnNumber: 23\n                                                            }, undefined) : // Show \"Complete Sale\" button when creating a sale\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 893,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                onClick: handleSubmit,\n                                                                loading: isSubmitting || isGeneratingReceipt,\n                                                                disabled: items.length === 0,\n                                                                className: \"text-md font-small h-14 w-full bg-green-600 hover:bg-green-700\",\n                                                                size: \"large\",\n                                                                children: isGeneratingReceipt ? \"Generating Receipt\" : \"Complete Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 891,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                onClick: onClose,\n                                                                className: \"h-12 w-full border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200\",\n                                                                size: \"large\",\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 906,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 864,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 776,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 764,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 494,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-gray-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"mr-2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 925,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Receipt Preview\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 926,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 924,\n                    columnNumber: 11\n                }, void 0),\n                open: receiptPreviewVisible,\n                onCancel: ()=>{\n                    // Close the modal and reset the receipt state\n                    setReceiptPreviewVisible(false);\n                    setReceiptUrl(null);\n                    setHasPrinted(false);\n                    // Reset the forms to start a new sale\n                    form.resetFields();\n                    productForm.resetFields();\n                    setItems([]);\n                    setSelectedProduct(null);\n                    setQuantity(1);\n                    setTotalAmount(0);\n                },\n                width: 500,\n                centered: true,\n                className: \"receipt-preview-modal\",\n                footer: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        onClick: ()=>{\n                            // Close the modal and reset the receipt state\n                            setReceiptPreviewVisible(false);\n                            setReceiptUrl(null);\n                            setHasPrinted(false);\n                            // Reset the forms to start a new sale\n                            form.resetFields();\n                            productForm.resetFields();\n                            setItems([]);\n                            setSelectedProduct(null);\n                            setQuantity(1);\n                            setTotalAmount(0);\n                        },\n                        className: \"border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200\",\n                        children: \"Close & New Sale\"\n                    }, \"close\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 948,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        type: \"primary\",\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 971,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: ()=>{\n                            // If already printed once, reset the flag to allow printing again\n                            if (hasPrinted) {\n                                setHasPrinted(false);\n                            }\n                            handlePrintReceipt();\n                        },\n                        className: \"bg-blue-600 hover:bg-blue-700\",\n                        children: hasPrinted ? \"Print Again\" : \"Print Receipt\"\n                    }, \"print\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 968,\n                        columnNumber: 11\n                    }, void 0)\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: receiptUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"receipt-image-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            src: receiptUrl,\n                            alt: \"Receipt\",\n                            className: \"receipt-image\",\n                            style: {\n                                maxWidth: \"100%\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 988,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 987,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-64 items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            size: \"large\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 997,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 996,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 985,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 922,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n        lineNumber: 488,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SalesFormPanel, \"ahv2kziGtLLfOAMc+sJghDBg4RM=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm,\n        _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery,\n        _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation\n    ];\n});\n_c = SalesFormPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SalesFormPanel);\nvar _c;\n$RefreshReg$(_c, \"SalesFormPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx\n"));

/***/ })

});