"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/expense-categories/page",{

/***/ "(app-pages-browser)/./src/components/Expenses/ExpenseCategoryTable.tsx":
/*!**********************************************************!*\
  !*** ./src/components/Expenses/ExpenseCategoryTable.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/checkbox/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CrownOutlined,DeleteFilled,DeleteOutlined,EditOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteFilled.js\");\n/* harmony import */ var _barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CrownOutlined,DeleteFilled,DeleteOutlined,EditOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CrownOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CrownOutlined,DeleteFilled,DeleteOutlined,EditOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CrownOutlined,DeleteFilled,DeleteOutlined,EditOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst ExpenseCategoryTable = (param)=>{\n    let { categories, loading = false, onEdit, onDelete, onBulkDelete, selectedCategories = [], onSelectionChange, isMobile = false } = param;\n    _s();\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_2__.useSelector)({\n        \"ExpenseCategoryTable.useSelector[user]\": (state)=>state.auth.user\n    }[\"ExpenseCategoryTable.useSelector[user]\"]);\n    const userRole = user === null || user === void 0 ? void 0 : user.role;\n    // Check permissions\n    const canEdit = userRole === \"admin\" || userRole === \"superadmin\";\n    const canDelete = userRole === \"admin\" || userRole === \"superadmin\";\n    // Handle individual checkbox change\n    const handleCheckboxChange = (categoryId, checked)=>{\n        if (!onSelectionChange) return;\n        const newSelection = checked ? [\n            ...selectedCategories,\n            categoryId\n        ] : selectedCategories.filter((id)=>id !== categoryId);\n        onSelectionChange(newSelection);\n    };\n    // Handle select all checkbox\n    const handleSelectAll = (e)=>{\n        if (!onSelectionChange) return;\n        if (e.target.checked) {\n            // Only select non-default categories for bulk operations\n            const selectableIds = categories.filter((category)=>!category.isDefault).map((category)=>category.id);\n            onSelectionChange(selectableIds);\n        } else {\n            onSelectionChange([]);\n        }\n    };\n    // Check if all selectable categories are selected\n    const selectableCategories = categories.filter((category)=>!category.isDefault);\n    const isAllSelected = selectableCategories.length > 0 && selectedCategories.length === selectableCategories.length;\n    const isIndeterminate = selectedCategories.length > 0 && selectedCategories.length < selectableCategories.length;\n    if (isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                selectedCategories.length > 0 && canDelete && onBulkDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-700 text-sm\",\n                                children: [\n                                    selectedCategories.length,\n                                    \" category(s) selected\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                type: \"primary\",\n                                danger: true,\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 23\n                                }, void 0),\n                                onClick: ()=>onBulkDelete(selectedCategories),\n                                children: \"Delete Selected\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, undefined),\n                categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg p-4 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            onSelectionChange && !category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                checked: selectedCategories.includes(category.id),\n                                                onChange: (e)=>handleCheckboxChange(category.id, e.target.checked),\n                                                className: \"mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 rounded-full mr-2\",\n                                                        style: {\n                                                            backgroundColor: category.color\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900 text-sm flex items-center\",\n                                                            children: [\n                                                                category.name,\n                                                                category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"ml-2 text-yellow-500\",\n                                                                    title: \"System Default\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                                    lineNumber: 129,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1\",\n                                        children: [\n                                            canEdit && onEdit && !category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                title: \"Edit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    type: \"text\",\n                                                    size: \"small\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    onClick: ()=>onEdit(category)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            canDelete && onDelete && !category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                title: \"Delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    type: \"text\",\n                                                    size: \"small\",\n                                                    danger: true,\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    onClick: ()=>onDelete(category.id)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    category.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: category.description\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                color: category.color,\n                                                className: \"text-xs\",\n                                                children: category.color\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                color: \"gold\",\n                                                className: \"text-xs\",\n                                                children: \"System Default\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, category.id, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Desktop Table View\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-x-auto\",\n        children: [\n            selectedCategories.length > 0 && canDelete && onBulkDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-700\",\n                            children: [\n                                selectedCategories.length,\n                                \" category(s) selected\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            type: \"primary\",\n                            danger: true,\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 21\n                            }, void 0),\n                            onClick: ()=>onBulkDelete(selectedCategories),\n                            children: \"Delete Selected\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                lineNumber: 189,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        className: \"bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: [\n                                onSelectionChange && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-left\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        indeterminate: isIndeterminate,\n                                        checked: isAllSelected,\n                                        onChange: handleSelectAll\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: \"Category\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: \"Color\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: \"Type\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        className: \"bg-white divide-y divide-gray-200\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-gray-50\",\n                                children: [\n                                    onSelectionChange && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 whitespace-nowrap\",\n                                        children: !category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            checked: selectedCategories.includes(category.id),\n                                            onChange: (e)=>handleCheckboxChange(category.id, e.target.checked)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded-full mr-3\",\n                                                    style: {\n                                                        backgroundColor: category.color\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-900 flex items-center\",\n                                                        children: [\n                                                            category.name,\n                                                            category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"ml-2 text-yellow-500\",\n                                                                title: \"System Default\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-900 max-w-xs truncate\",\n                                            children: category.description || '-'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 whitespace-nowrap\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            color: category.color,\n                                            className: \"text-xs\",\n                                            children: category.color\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 whitespace-nowrap\",\n                                        children: category.isDefault ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            color: \"gold\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 43\n                                            }, void 0),\n                                            children: \"System Default\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            color: \"blue\",\n                                            children: \"Custom\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-end space-x-2\",\n                                            children: [\n                                                canEdit && onEdit && !category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    title: \"Edit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        type: \"text\",\n                                                        size: \"small\",\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        onClick: ()=>onEdit(category)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                canDelete && onDelete && !category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    title: \"Delete\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        type: \"text\",\n                                                        size: \"small\",\n                                                        danger: true,\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        onClick: ()=>onDelete(category.id)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, category.id, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ExpenseCategoryTable, \"ODc3cjw/vvoWaLFqHWcEGODyOfo=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_2__.useSelector\n    ];\n});\n_c = ExpenseCategoryTable;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ExpenseCategoryTable);\nvar _c;\n$RefreshReg$(_c, \"ExpenseCategoryTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Expenses/ExpenseCategoryTable.tsx\n"));

/***/ })

});