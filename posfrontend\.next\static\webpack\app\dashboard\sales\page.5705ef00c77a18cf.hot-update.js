"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/sales/page",{

/***/ "(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx":
/*!*************************************************!*\
  !*** ./src/components/Sales/SalesFormPanel.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/empty/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/image/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PrinterOutlined.js\");\n/* harmony import */ var _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/reduxRTK/services/salesApi */ \"(app-pages-browser)/./src/reduxRTK/services/salesApi.ts\");\n/* harmony import */ var _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/reduxRTK/services/productApi */ \"(app-pages-browser)/./src/reduxRTK/services/productApi.ts\");\n/* harmony import */ var _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/reduxRTK/services/userStoreApi */ \"(app-pages-browser)/./src/reduxRTK/services/userStoreApi.ts\");\n/* harmony import */ var _components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/SlidingPanel */ \"(app-pages-browser)/./src/components/ui/SlidingPanel.tsx\");\n/* harmony import */ var _utils_showMessage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/showMessage */ \"(app-pages-browser)/./src/utils/showMessage.ts\");\n/* harmony import */ var _utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/cloudinaryUtils */ \"(app-pages-browser)/./src/utils/cloudinaryUtils.ts\");\n/* harmony import */ var _sales_panels_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./sales-panels.css */ \"(app-pages-browser)/./src/components/Sales/sales-panels.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst SalesFormPanel = (param)=>{\n    let { isOpen, onClose, onSuccess } = param;\n    var _productsData_data_products, _productsData_data;\n    _s();\n    const [form] = _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm();\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalAmount, setTotalAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingReceipt, setIsGeneratingReceipt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptPreviewVisible, setReceiptPreviewVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptUrl, setReceiptUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPrinted, setHasPrinted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            console.log(\"Items state changed:\", items);\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items\n    ]);\n    const { data: productsData, isLoading: isLoadingProducts, refetch: refetchProducts } = (0,_reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery)({\n        page: 1,\n        limit: 1000,\n        search: searchTerm\n    }, {\n        // Force refetch when component mounts and when panel opens\n        refetchOnMountOrArgChange: true,\n        refetchOnFocus: false,\n        refetchOnReconnect: true\n    });\n    // Debug products data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (productsData) {\n                var _productsData_data, _productsData_data_products, _productsData_data1;\n                console.log(\"🛒 Products loaded:\", {\n                    total: ((_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.total) || 0,\n                    productsCount: ((_productsData_data1 = productsData.data) === null || _productsData_data1 === void 0 ? void 0 : (_productsData_data_products = _productsData_data1.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.length) || 0,\n                    isLoading: isLoadingProducts\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        productsData,\n        isLoadingProducts\n    ]);\n    // Get current user ID from auth state\n    const getCurrentUserId = ()=>{\n        if (true) {\n            var _state_auth_user, _state_auth;\n            // @ts-ignore - Redux state is exposed for debugging\n            const state = window.__REDUX_STATE;\n            return (state === null || state === void 0 ? void 0 : (_state_auth = state.auth) === null || _state_auth === void 0 ? void 0 : (_state_auth_user = _state_auth.user) === null || _state_auth_user === void 0 ? void 0 : _state_auth_user.id) || 0;\n        }\n        return 0;\n    };\n    // Fetch user stores\n    const { data: userStoresData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery)(getCurrentUserId());\n    // Fetch default store\n    const { data: defaultStoreData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery)(getCurrentUserId());\n    // Set default store when data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (defaultStoreData === null || defaultStoreData === void 0 ? void 0 : defaultStoreData.data) {\n                setSelectedStore(defaultStoreData.data);\n                form.setFieldsValue({\n                    storeId: defaultStoreData.data.id\n                });\n            } else if ((userStoresData === null || userStoresData === void 0 ? void 0 : userStoresData.data) && userStoresData.data.length > 0) {\n                setSelectedStore(userStoresData.data[0]);\n                form.setFieldsValue({\n                    storeId: userStoresData.data[0].id\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        defaultStoreData,\n        userStoresData,\n        form\n    ]);\n    // Create sale mutation\n    const [createSale, { isLoading: isSubmitting }] = (0,_reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation)();\n    // Calculate total amount whenever items change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (items && items.length > 0) {\n                const total = items.reduce({\n                    \"SalesFormPanel.useEffect.total\": (sum, item)=>sum + item.price * item.quantity\n                }[\"SalesFormPanel.useEffect.total\"], 0);\n                setTotalAmount(total);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: total\n                    });\n                }\n                // Debug log to check items state\n                console.log(\"Current items in useEffect:\", items);\n            } else {\n                setTotalAmount(0);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: 0\n                    });\n                }\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items,\n        form\n    ]);\n    // Handle panel open/close\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (isOpen) {\n                // When panel opens, ensure we have fresh product data\n                console.log(\"🛒 Sales panel opened - fetching fresh product data\");\n                refetchProducts();\n            } else {\n                // Reset form when panel is closed\n                if (form) {\n                    form.resetFields();\n                }\n                setItems([]);\n                setSelectedProduct(null);\n                setQuantity(1);\n                setTotalAmount(0);\n                setReceiptUrl(null);\n                setReceiptPreviewVisible(false);\n                setHasPrinted(false);\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        isOpen,\n        form,\n        refetchProducts\n    ]);\n    // Handle adding an item to the sale\n    const handleAddItem = ()=>{\n        if (!selectedProduct) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Please select a product\");\n            return;\n        }\n        if (quantity <= 0) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Quantity must be greater than 0\");\n            return;\n        }\n        if (selectedProduct.stockQuantity < quantity) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Only \".concat(selectedProduct.stockQuantity, \" units available in stock\"));\n            return;\n        }\n        console.log(\"Adding item with product:\", selectedProduct);\n        // Check if product already exists in items\n        const existingItemIndex = items.findIndex((item)=>item.productId === selectedProduct.id);\n        if (existingItemIndex >= 0) {\n            // Update existing item\n            const updatedItems = [\n                ...items\n            ];\n            const newQuantity = updatedItems[existingItemIndex].quantity + quantity;\n            if (newQuantity > selectedProduct.stockQuantity) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Cannot add more than \".concat(selectedProduct.stockQuantity, \" units of this product\"));\n                return;\n            }\n            updatedItems[existingItemIndex].quantity = newQuantity;\n            // Make sure the product name is set\n            if (!updatedItems[existingItemIndex].productName) {\n                updatedItems[existingItemIndex].productName = selectedProduct.name;\n            }\n            // Update the state with the new array\n            console.log(\"Updating existing item. New items array:\", updatedItems);\n            setItems([\n                ...updatedItems\n            ]); // Create a new array reference to force re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Updated quantity of \".concat(selectedProduct.name));\n        } else {\n            // Add new item\n            const newItem = {\n                productId: selectedProduct.id,\n                productName: selectedProduct.name,\n                quantity,\n                price: typeof selectedProduct.price === \"string\" ? parseFloat(selectedProduct.price) : selectedProduct.price\n            };\n            // Create a new array with the new item\n            const newItems = [\n                ...items,\n                newItem\n            ];\n            // Update the state with the new array\n            console.log(\"Adding new item. New items array:\", newItems);\n            setItems(newItems); // This should trigger a re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Added \".concat(quantity, \" \").concat(selectedProduct.name, \" to sale\"));\n        }\n        // Reset selection\n        setSelectedProduct(null);\n        setQuantity(1);\n        if (form) {\n            form.setFieldsValue({\n                productId: undefined,\n                quantity: 1\n            });\n        }\n    };\n    // Handle removing an item from the sale\n    const handleRemoveItem = (index)=>{\n        const updatedItems = [\n            ...items\n        ];\n        updatedItems.splice(index, 1);\n        setItems(updatedItems);\n    };\n    // Handle printing receipt - directly trigger print dialog\n    const handlePrintReceipt = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n            if (!receiptUrl || hasPrinted) {\n                console.log(\"Skipping print: \", !receiptUrl ? \"No receipt URL\" : \"Already printed\");\n                return;\n            }\n            console.log(\"Printing receipt:\", receiptUrl);\n            // Mark as printed immediately to prevent multiple print dialogs\n            setHasPrinted(true);\n            // Create a hidden iframe to load the image\n            const iframe = document.createElement(\"iframe\");\n            iframe.style.display = \"none\";\n            document.body.appendChild(iframe);\n            // Set up the iframe content with the image and print CSS\n            iframe.onload = ({\n                \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                    if (iframe.contentWindow) {\n                        // Write the HTML content to the iframe\n                        iframe.contentWindow.document.write('\\n          <!DOCTYPE html>\\n          <html>\\n            <head>\\n              <title>Print Receipt</title>\\n              <style>\\n                body {\\n                  margin: 0;\\n                  padding: 0;\\n                  display: flex;\\n                  justify-content: center;\\n                  align-items: center;\\n                  height: 100vh;\\n                }\\n                img {\\n                  max-width: 100%;\\n                  max-height: 100vh;\\n                }\\n                @media print {\\n                  body {\\n                    margin: 0;\\n                    padding: 0;\\n                  }\\n                  img {\\n                    width: 100%;\\n                    height: auto;\\n                  }\\n                }\\n              </style>\\n            </head>\\n            <body>\\n              <img src=\"'.concat(receiptUrl, '\" alt=\"Receipt\" />\\n            </body>\\n          </html>\\n        '));\n                        // Close the document\n                        iframe.contentWindow.document.close();\n                        // Use a single print trigger with a delay to ensure the image is loaded\n                        setTimeout({\n                            \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                if (iframe.contentWindow) {\n                                    try {\n                                        // Print the iframe content\n                                        iframe.contentWindow.focus();\n                                        iframe.contentWindow.print();\n                                    } catch (e) {\n                                        console.error(\"Error printing receipt:\", e);\n                                    }\n                                    // Remove the iframe after printing\n                                    setTimeout({\n                                        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                            document.body.removeChild(iframe);\n                                        }\n                                    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 1000);\n                                }\n                            }\n                        }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 500);\n                    }\n                }\n            })[\"SalesFormPanel.useCallback[handlePrintReceipt]\"];\n            // Set the iframe source to trigger the onload event\n            iframe.src = \"about:blank\";\n        }\n    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], [\n        receiptUrl,\n        hasPrinted\n    ]);\n    // Effect to automatically print receipt when modal is shown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (receiptPreviewVisible && receiptUrl && !hasPrinted) {\n                // Add a small delay to ensure the receipt image is loaded\n                const timer = setTimeout({\n                    \"SalesFormPanel.useEffect.timer\": ()=>{\n                        handlePrintReceipt(); // This now handles the hasPrinted state internally\n                    }\n                }[\"SalesFormPanel.useEffect.timer\"], 800);\n                return ({\n                    \"SalesFormPanel.useEffect\": ()=>clearTimeout(timer)\n                })[\"SalesFormPanel.useEffect\"];\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        receiptPreviewVisible,\n        receiptUrl,\n        hasPrinted,\n        handlePrintReceipt\n    ]);\n    // Handle form submission\n    const handleSubmit = async ()=>{\n        try {\n            var _userStoresData_data;\n            if (items.length === 0) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Please add at least one item to the sale\");\n                return;\n            }\n            // Validate form fields\n            const values = await form.validateFields();\n            // Check if store is selected\n            if (!selectedStore) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"No store information available. Please set up your store in your profile settings.\");\n                return;\n            }\n            // Set loading state for receipt generation\n            setIsGeneratingReceipt(true);\n            // Get store information for receipt\n            const storeInfo = selectedStore || (userStoresData === null || userStoresData === void 0 ? void 0 : (_userStoresData_data = userStoresData.data) === null || _userStoresData_data === void 0 ? void 0 : _userStoresData_data.find((store)=>store.id === values.storeId)) || {\n                name: \"POS System\"\n            };\n            // Generate receipt HTML\n            const receiptHTML = (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__.generateReceiptHTML)({\n                id: Date.now(),\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                transactionDate: new Date().toISOString(),\n                items: items.map((item)=>({\n                        productName: item.productName,\n                        quantity: item.quantity,\n                        price: item.price\n                    }))\n            }, storeInfo);\n            // Generate receipt image and get URL\n            let receiptUrl = \"https://receipt.example.com/placeholder\";\n            try {\n                receiptUrl = await (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__.generateReceiptImage)(receiptHTML);\n            } catch (error) {\n                console.error(\"Failed to generate receipt image:\", error);\n            // Continue with placeholder URL if image generation fails\n            }\n            const saleData = {\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                items: items.map((item)=>({\n                        productId: item.productId,\n                        quantity: item.quantity,\n                        price: item.price\n                    })),\n                receiptUrl,\n                storeId: selectedStore === null || selectedStore === void 0 ? void 0 : selectedStore.id\n            };\n            const response = await createSale(saleData).unwrap();\n            if (response.success) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Sale created successfully\");\n                // Store the receipt URL for preview\n                setReceiptUrl(receiptUrl);\n                // Show receipt preview modal and offer print option\n                setReceiptPreviewVisible(true);\n                // Refresh product data to get updated stock quantities\n                refetchProducts();\n                // Trigger the success callback to refresh the list WITHOUT closing the panel\n                setTimeout(()=>{\n                    if (onSuccess) {\n                        // Call refetch directly instead of closing the panel\n                        refetchProducts();\n                    }\n                }, 300);\n            // Keep the panel open until the user explicitly closes it\n            // This ensures the receipt modal stays visible\n            } else {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", response.message || \"Failed to create sale\");\n            }\n        } catch (error) {\n            var _error_data;\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", ((_error_data = error.data) === null || _error_data === void 0 ? void 0 : _error_data.message) || \"An error occurred while creating the sale\");\n        } finally{\n            setIsGeneratingReceipt(false);\n        }\n    };\n    // Debug log to check items state when rendering\n    console.log(\"Rendering with items:\", items);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        title: \"Point of Sale\",\n        isOpen: isOpen,\n        onClose: onClose,\n        width: \"95%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sales-form mt-10 min-h-screen bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between border-b border-gray-200 bg-gray-50 px-6 py-3 shadow-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"mr-3 text-2xl text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"m-0 text-xl font-bold text-gray-800\",\n                                        children: \"New Transaction\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg font-bold text-gray-800\",\n                                children: [\n                                    \"Total:\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600\",\n                                        children: [\n                                            \"GHS \",\n                                            totalAmount.toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-4 lg:grid-cols-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4 flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-800\",\n                                                            children: \"Product Selection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-1 text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 515,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \" Required fields\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 gap-4 md:grid-cols-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"md:col-span-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"mb-2 block text-gray-800\",\n                                                                        children: [\n                                                                            \"Product \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 524,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 523,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        showSearch: true,\n                                                                        placeholder: isLoadingProducts ? \"Loading products...\" : \"Search products...\",\n                                                                        optionFilterProp: \"children\",\n                                                                        loading: isLoadingProducts,\n                                                                        disabled: isLoadingProducts,\n                                                                        onChange: (value)=>{\n                                                                            var _productsData_data;\n                                                                            const product = productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products.find((p)=>p.id === value);\n                                                                            console.log(\"Selected product:\", product);\n                                                                            if (product) {\n                                                                                setSelectedProduct({\n                                                                                    ...product,\n                                                                                    price: typeof product.price === \"string\" ? product.price : String(product.price)\n                                                                                });\n                                                                            } else {\n                                                                                setSelectedProduct(null);\n                                                                            }\n                                                                        },\n                                                                        onSearch: setSearchTerm,\n                                                                        filterOption: false,\n                                                                        className: \"text-gray-800\",\n                                                                        size: \"large\",\n                                                                        suffixIcon: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            spin: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 29\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 561,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        notFoundContent: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            size: \"small\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 566,\n                                                                            columnNumber: 29\n                                                                        }, void 0) : \"No products found\",\n                                                                        children: productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : (_productsData_data_products = _productsData_data.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"].Option, {\n                                                                                value: product.id,\n                                                                                disabled: product.stockQuantity <= 0,\n                                                                                children: [\n                                                                                    product.name,\n                                                                                    \" - GHS \",\n                                                                                    Number(product.price).toFixed(2),\n                                                                                    \" \",\n                                                                                    product.stockQuantity <= 0 ? \"(Out of Stock)\" : \"(Stock: \".concat(product.stockQuantity, \")\")\n                                                                                ]\n                                                                            }, product.id, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 573,\n                                                                                columnNumber: 27\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 526,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"mb-2 block text-gray-800\",\n                                                                        children: [\n                                                                            \"Qty \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 591,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 590,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        min: 1,\n                                                                        value: quantity,\n                                                                        onChange: (value)=>setQuantity(value || 1),\n                                                                        style: {\n                                                                            width: \"100%\"\n                                                                        },\n                                                                        className: \"text-gray-800\",\n                                                                        size: \"large\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 593,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    type: \"primary\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    onClick: handleAddItem,\n                                                    className: \"mt-2 h-12 w-full bg-blue-600 text-base font-medium hover:bg-blue-700\",\n                                                    disabled: !selectedProduct,\n                                                    size: \"large\",\n                                                    children: \"Add to Cart\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"mb-4 flex items-center text-lg font-semibold text-gray-800\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \" Cart Items\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-[350px] overflow-x-auto overflow-y-auto\",\n                                                    children: items.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-lg border border-gray-200 bg-gray-50 p-8 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500\",\n                                                                children: \"No items in cart\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 27\n                                                            }, void 0),\n                                                            image: _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"].PRESENTED_IMAGE_SIMPLE\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 625,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                        className: \"min-w-full overflow-hidden rounded-lg border border-gray-200 bg-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                className: \"sticky top-0 z-10 bg-gray-50 text-gray-700\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"px-4 py-3 text-left font-medium\",\n                                                                            children: \"Product\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 638,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"w-20 px-4 py-3 text-center font-medium\",\n                                                                            children: \"Qty\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 641,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"w-28 px-4 py-3 text-right font-medium\",\n                                                                            children: \"Price\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 644,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"w-28 px-4 py-3 text-right font-medium\",\n                                                                            children: \"Subtotal\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 647,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"w-16 px-4 py-3 text-center font-medium\",\n                                                                            children: \"Action\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 650,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 637,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"border-b border-gray-200 hover:bg-gray-50\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 font-medium text-gray-800\",\n                                                                                children: item.productName || \"Unknown Product\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 661,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-center text-gray-800\",\n                                                                                children: item.quantity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 664,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-right text-gray-800\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    Number(item.price).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 667,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-right font-medium text-gray-800\",\n                                                                                children: [\n                                                                                    \"GHS\",\n                                                                                    \" \",\n                                                                                    (Number(item.price) * item.quantity).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 670,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 676,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    onClick: ()=>handleRemoveItem(index),\n                                                                                    type: \"text\",\n                                                                                    danger: true,\n                                                                                    className: \"text-red-500 hover:bg-gray-100 hover:text-red-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 675,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 674,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, \"\".concat(item.productId, \"-\").concat(index), true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 657,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tfoot\", {\n                                                                className: \"sticky bottom-0 z-10 bg-gray-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            colSpan: 3,\n                                                                            className: \"px-4 py-3 text-right font-bold text-gray-800\",\n                                                                            children: \"Total\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            colSpan: 2,\n                                                                            className: \"px-4 py-3 text-right font-bold text-green-600\",\n                                                                            children: [\n                                                                                \"GHS \",\n                                                                                totalAmount.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 694,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 686,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sticky top-4 rounded-lg border border-gray-200 bg-white p-4 shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"mb-4 flex items-center text-lg font-semibold text-gray-800\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" Checkout\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 711,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                form: form,\n                                                layout: \"vertical\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 rounded-lg border border-gray-200 bg-gray-50 p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"Items:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 718,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-gray-800\",\n                                                                        children: items.length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"Total Quantity:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 724,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-gray-800\",\n                                                                        children: items.reduce((sum, item)=>sum + item.quantity, 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 725,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 723,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between text-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"Subtotal:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 730,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-gray-800\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 731,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"my-4 border-t border-gray-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-800\",\n                                                                        children: \"Total:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 737,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-green-600\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 738,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 736,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    selectedStore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"mb-2 block text-gray-700\",\n                                                                children: \"Store\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 747,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center rounded-lg border border-gray-200 bg-gray-50 p-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"mr-2 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 749,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-800\",\n                                                                        children: selectedStore.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 750,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"hidden\",\n                                                                        name: \"storeId\",\n                                                                        value: selectedStore.id\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 748,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4 rounded-lg border border-gray-200 bg-gray-50 p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"No store information available. Please set up your store in your profile settings.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 761,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                        name: \"paymentMethod\",\n                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-700\",\n                                                            children: [\n                                                                \"Payment Method \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 773,\n                                                                    columnNumber: 40\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 772,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        rules: [\n                                                            {\n                                                                required: true,\n                                                                message: \"Please select a payment method\"\n                                                            }\n                                                        ],\n                                                        initialValue: \"cash\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"text-gray-800\",\n                                                            size: \"large\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"].Option, {\n                                                                    value: \"cash\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mr-2\",\n                                                                                children: \"\\uD83D\\uDCB5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 787,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \" Cash\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 786,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 785,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"].Option, {\n                                                                    value: \"card\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mr-2\",\n                                                                                children: \"\\uD83D\\uDCB3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 792,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \" Card\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 791,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"].Option, {\n                                                                    value: \"mobile_money\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mr-2\",\n                                                                                children: \"\\uD83D\\uDCF1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 797,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \" Mobile Money\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 796,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 795,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 784,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-8 space-y-4\",\n                                                        children: [\n                                                            receiptPreviewVisible ? // Show \"Done\" button when receipt is visible\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 808,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                onClick: ()=>{\n                                                                    // Close the modal and reset the receipt state\n                                                                    setReceiptPreviewVisible(false);\n                                                                    setReceiptUrl(null);\n                                                                    setHasPrinted(false);\n                                                                    // Reset the form to start a new sale\n                                                                    form.resetFields();\n                                                                    setItems([]);\n                                                                    setSelectedProduct(null);\n                                                                    setQuantity(1);\n                                                                    setTotalAmount(0);\n                                                                },\n                                                                className: \"text-md font-small h-14 w-full bg-green-600 hover:bg-green-700\",\n                                                                size: \"large\",\n                                                                children: \"Start New Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 23\n                                                            }, undefined) : // Show \"Complete Sale\" button when creating a sale\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 831,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                onClick: handleSubmit,\n                                                                loading: isSubmitting || isGeneratingReceipt,\n                                                                disabled: items.length === 0,\n                                                                className: \"text-md font-small h-14 w-full bg-green-600 hover:bg-green-700\",\n                                                                size: \"large\",\n                                                                children: isGeneratingReceipt ? \"Generating Receipt\" : \"Complete Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 829,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                onClick: onClose,\n                                                                className: \"h-12 w-full border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200\",\n                                                                size: \"large\",\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 844,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 803,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 710,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 490,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-gray-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            className: \"mr-2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 863,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Receipt Preview\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 864,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 862,\n                    columnNumber: 11\n                }, void 0),\n                open: receiptPreviewVisible,\n                onCancel: ()=>{\n                    // Close the modal and reset the receipt state\n                    setReceiptPreviewVisible(false);\n                    setReceiptUrl(null);\n                    setHasPrinted(false);\n                    // Reset the form to start a new sale\n                    form.resetFields();\n                    setItems([]);\n                    setSelectedProduct(null);\n                    setQuantity(1);\n                    setTotalAmount(0);\n                },\n                width: 500,\n                centered: true,\n                className: \"receipt-preview-modal\",\n                footer: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        onClick: ()=>{\n                            // Close the modal and reset the receipt state\n                            setReceiptPreviewVisible(false);\n                            setReceiptUrl(null);\n                            setHasPrinted(false);\n                            // Reset the form to start a new sale\n                            form.resetFields();\n                            setItems([]);\n                            setSelectedProduct(null);\n                            setQuantity(1);\n                            setTotalAmount(0);\n                        },\n                        className: \"border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200\",\n                        children: \"Close & New Sale\"\n                    }, \"close\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 885,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        type: \"primary\",\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 907,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: ()=>{\n                            // If already printed once, reset the flag to allow printing again\n                            if (hasPrinted) {\n                                setHasPrinted(false);\n                            }\n                            handlePrintReceipt();\n                        },\n                        className: \"bg-blue-600 hover:bg-blue-700\",\n                        children: hasPrinted ? \"Print Again\" : \"Print Receipt\"\n                    }, \"print\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 904,\n                        columnNumber: 11\n                    }, void 0)\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: receiptUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"receipt-image-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            src: receiptUrl,\n                            alt: \"Receipt\",\n                            className: \"receipt-image\",\n                            style: {\n                                maxWidth: \"100%\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 924,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 923,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-64 items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            size: \"large\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 933,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 932,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 921,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 860,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n        lineNumber: 484,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SalesFormPanel, \"1/38FKgYVc7r3Srw9LN2gx/dVME=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm,\n        _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery,\n        _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation\n    ];\n});\n_c = SalesFormPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SalesFormPanel);\nvar _c;\n$RefreshReg$(_c, \"SalesFormPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1NhbGVzL1NhbGVzRm9ybVBhbmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWdFO0FBVWxEO0FBVWE7QUFLVztBQUlFO0FBSUU7QUFDYztBQUNOO0FBSWpCO0FBRUw7QUFRNUIsTUFBTTJCLGlCQUFnRDtRQUFDLEVBQ3JEQyxNQUFNLEVBQ05DLE9BQU8sRUFDUEMsU0FBUyxFQUNWO1FBb2dCd0JDLDZCQUFBQTs7SUFuZ0J2QixNQUFNLENBQUNDLEtBQUssR0FBRzVCLHlIQUFJQSxDQUFDNkIsT0FBTztJQUMzQixNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBR2xDLCtDQUFRQSxDQUVoQyxFQUFFO0lBQ0osTUFBTSxDQUFDbUMsaUJBQWlCQyxtQkFBbUIsR0FBR3BDLCtDQUFRQSxDQUFpQjtJQUN2RSxNQUFNLENBQUNxQyxVQUFVQyxZQUFZLEdBQUd0QywrQ0FBUUEsQ0FBUztJQUNqRCxNQUFNLENBQUN1QyxhQUFhQyxlQUFlLEdBQUd4QywrQ0FBUUEsQ0FBUztJQUN2RCxNQUFNLENBQUN5QyxZQUFZQyxjQUFjLEdBQUcxQywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUMyQyxlQUFlQyxpQkFBaUIsR0FBRzVDLCtDQUFRQSxDQUFlO0lBQ2pFLE1BQU0sQ0FBQzZDLHFCQUFxQkMsdUJBQXVCLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUMrQyx1QkFBdUJDLHlCQUF5QixHQUFHaEQsK0NBQVFBLENBQUM7SUFDbkUsTUFBTSxDQUFDaUQsWUFBWUMsY0FBYyxHQUFHbEQsK0NBQVFBLENBQWdCO0lBQzVELE1BQU0sQ0FBQ21ELFlBQVlDLGNBQWMsR0FBR3BELCtDQUFRQSxDQUFDO0lBRTdDLHNCQUFzQjtJQUN0QkMsZ0RBQVNBO29DQUFDO1lBQ1JvRCxRQUFRQyxHQUFHLENBQUMsd0JBQXdCckI7UUFDdEM7bUNBQUc7UUFBQ0E7S0FBTTtJQUdWLE1BQU0sRUFDSnNCLE1BQU16QixZQUFZLEVBQ2xCMEIsV0FBV0MsaUJBQWlCLEVBQzVCQyxTQUFTQyxlQUFlLEVBQ3pCLEdBQUd4QyxxRkFBc0JBLENBQ3hCO1FBQ0V5QyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsUUFBUXJCO0lBQ1YsR0FDQTtRQUNFLDJEQUEyRDtRQUMzRHNCLDJCQUEyQjtRQUMzQkMsZ0JBQWdCO1FBQ2hCQyxvQkFBb0I7SUFDdEI7SUFHRixzQkFBc0I7SUFDdEJoRSxnREFBU0E7b0NBQUM7WUFDUixJQUFJNkIsY0FBYztvQkFFUEEsb0JBQ1FBLDZCQUFBQTtnQkFGakJ1QixRQUFRQyxHQUFHLENBQUMsdUJBQXVCO29CQUNqQ1ksT0FBT3BDLEVBQUFBLHFCQUFBQSxhQUFheUIsSUFBSSxjQUFqQnpCLHlDQUFBQSxtQkFBbUJvQyxLQUFLLEtBQUk7b0JBQ25DQyxlQUFlckMsRUFBQUEsc0JBQUFBLGFBQWF5QixJQUFJLGNBQWpCekIsMkNBQUFBLDhCQUFBQSxvQkFBbUJzQyxRQUFRLGNBQTNCdEMsa0RBQUFBLDRCQUE2QnVDLE1BQU0sS0FBSTtvQkFDdERiLFdBQVdDO2dCQUNiO1lBQ0Y7UUFDRjttQ0FBRztRQUFDM0I7UUFBYzJCO0tBQWtCO0lBRXBDLHNDQUFzQztJQUN0QyxNQUFNYSxtQkFBbUI7UUFDdkIsSUFBSSxJQUE2QixFQUFFO2dCQUcxQkMsa0JBQUFBO1lBRlAsb0RBQW9EO1lBQ3BELE1BQU1BLFFBQVFDLE9BQU9DLGFBQWE7WUFDbEMsT0FBT0YsQ0FBQUEsa0JBQUFBLDZCQUFBQSxjQUFBQSxNQUFPRyxJQUFJLGNBQVhILG1DQUFBQSxtQkFBQUEsWUFBYUksSUFBSSxjQUFqQkosdUNBQUFBLGlCQUFtQkssRUFBRSxLQUFJO1FBQ2xDO1FBQ0EsT0FBTztJQUNUO0lBRUEsb0JBQW9CO0lBQ3BCLE1BQU0sRUFBRXJCLE1BQU1zQixjQUFjLEVBQUUsR0FBR3pELHNGQUFxQkEsQ0FBQ2tEO0lBRXZELHNCQUFzQjtJQUN0QixNQUFNLEVBQUVmLE1BQU11QixnQkFBZ0IsRUFBRSxHQUM5QnpELDRGQUEyQkEsQ0FBQ2lEO0lBRTlCLHdDQUF3QztJQUN4Q3JFLGdEQUFTQTtvQ0FBQztZQUNSLElBQUk2RSw2QkFBQUEsdUNBQUFBLGlCQUFrQnZCLElBQUksRUFBRTtnQkFDMUJYLGlCQUFpQmtDLGlCQUFpQnZCLElBQUk7Z0JBQ3RDeEIsS0FBS2dELGNBQWMsQ0FBQztvQkFBRUMsU0FBU0YsaUJBQWlCdkIsSUFBSSxDQUFDcUIsRUFBRTtnQkFBQztZQUMxRCxPQUFPLElBQUlDLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0J0QixJQUFJLEtBQUlzQixlQUFldEIsSUFBSSxDQUFDYyxNQUFNLEdBQUcsR0FBRztnQkFDakV6QixpQkFBaUJpQyxlQUFldEIsSUFBSSxDQUFDLEVBQUU7Z0JBQ3ZDeEIsS0FBS2dELGNBQWMsQ0FBQztvQkFBRUMsU0FBU0gsZUFBZXRCLElBQUksQ0FBQyxFQUFFLENBQUNxQixFQUFFO2dCQUFDO1lBQzNEO1FBQ0Y7bUNBQUc7UUFBQ0U7UUFBa0JEO1FBQWdCOUM7S0FBSztJQUUzQyx1QkFBdUI7SUFDdkIsTUFBTSxDQUFDa0QsWUFBWSxFQUFFekIsV0FBVzBCLFlBQVksRUFBRSxDQUFDLEdBQUdoRSxrRkFBcUJBO0lBRXZFLCtDQUErQztJQUMvQ2pCLGdEQUFTQTtvQ0FBQztZQUNSLElBQUlnQyxTQUFTQSxNQUFNb0MsTUFBTSxHQUFHLEdBQUc7Z0JBQzdCLE1BQU1ILFFBQVFqQyxNQUFNa0QsTUFBTTtzREFDeEIsQ0FBQ0MsS0FBS0MsT0FBU0QsTUFBTUMsS0FBS0MsS0FBSyxHQUFHRCxLQUFLaEQsUUFBUTtxREFDL0M7Z0JBRUZHLGVBQWUwQjtnQkFDZixJQUFJbkMsTUFBTTtvQkFDUkEsS0FBS2dELGNBQWMsQ0FBQzt3QkFBRXhDLGFBQWEyQjtvQkFBTTtnQkFDM0M7Z0JBRUEsaUNBQWlDO2dCQUNqQ2IsUUFBUUMsR0FBRyxDQUFDLCtCQUErQnJCO1lBQzdDLE9BQU87Z0JBQ0xPLGVBQWU7Z0JBQ2YsSUFBSVQsTUFBTTtvQkFDUkEsS0FBS2dELGNBQWMsQ0FBQzt3QkFBRXhDLGFBQWE7b0JBQUU7Z0JBQ3ZDO1lBQ0Y7UUFDRjttQ0FBRztRQUFDTjtRQUFPRjtLQUFLO0lBRWhCLDBCQUEwQjtJQUMxQjlCLGdEQUFTQTtvQ0FBQztZQUNSLElBQUkwQixRQUFRO2dCQUNWLHNEQUFzRDtnQkFDdEQwQixRQUFRQyxHQUFHLENBQUM7Z0JBQ1pLO1lBQ0YsT0FBTztnQkFDTCxrQ0FBa0M7Z0JBQ2xDLElBQUk1QixNQUFNO29CQUNSQSxLQUFLd0QsV0FBVztnQkFDbEI7Z0JBQ0FyRCxTQUFTLEVBQUU7Z0JBQ1hFLG1CQUFtQjtnQkFDbkJFLFlBQVk7Z0JBQ1pFLGVBQWU7Z0JBQ2ZVLGNBQWM7Z0JBQ2RGLHlCQUF5QjtnQkFDekJJLGNBQWM7WUFDaEI7UUFDRjttQ0FBRztRQUFDekI7UUFBUUk7UUFBTTRCO0tBQWdCO0lBRWxDLG9DQUFvQztJQUNwQyxNQUFNNkIsZ0JBQWdCO1FBQ3BCLElBQUksQ0FBQ3JELGlCQUFpQjtZQUNwQlosK0RBQVdBLENBQUMsU0FBUztZQUNyQjtRQUNGO1FBRUEsSUFBSWMsWUFBWSxHQUFHO1lBQ2pCZCwrREFBV0EsQ0FBQyxTQUFTO1lBQ3JCO1FBQ0Y7UUFFQSxJQUFJWSxnQkFBZ0JzRCxhQUFhLEdBQUdwRCxVQUFVO1lBQzVDZCwrREFBV0EsQ0FDVCxTQUNBLFFBQXNDLE9BQTlCWSxnQkFBZ0JzRCxhQUFhLEVBQUM7WUFFeEM7UUFDRjtRQUVBcEMsUUFBUUMsR0FBRyxDQUFDLDZCQUE2Qm5CO1FBRXpDLDJDQUEyQztRQUMzQyxNQUFNdUQsb0JBQW9CekQsTUFBTTBELFNBQVMsQ0FDdkMsQ0FBQ04sT0FBU0EsS0FBS08sU0FBUyxLQUFLekQsZ0JBQWdCeUMsRUFBRTtRQUdqRCxJQUFJYyxxQkFBcUIsR0FBRztZQUMxQix1QkFBdUI7WUFDdkIsTUFBTUcsZUFBZTttQkFBSTVEO2FBQU07WUFDL0IsTUFBTTZELGNBQWNELFlBQVksQ0FBQ0gsa0JBQWtCLENBQUNyRCxRQUFRLEdBQUdBO1lBRS9ELElBQUl5RCxjQUFjM0QsZ0JBQWdCc0QsYUFBYSxFQUFFO2dCQUMvQ2xFLCtEQUFXQSxDQUNULFNBQ0Esd0JBQXNELE9BQTlCWSxnQkFBZ0JzRCxhQUFhLEVBQUM7Z0JBRXhEO1lBQ0Y7WUFFQUksWUFBWSxDQUFDSCxrQkFBa0IsQ0FBQ3JELFFBQVEsR0FBR3lEO1lBRTNDLG9DQUFvQztZQUNwQyxJQUFJLENBQUNELFlBQVksQ0FBQ0gsa0JBQWtCLENBQUNLLFdBQVcsRUFBRTtnQkFDaERGLFlBQVksQ0FBQ0gsa0JBQWtCLENBQUNLLFdBQVcsR0FBRzVELGdCQUFnQjZELElBQUk7WUFDcEU7WUFFQSxzQ0FBc0M7WUFDdEMzQyxRQUFRQyxHQUFHLENBQUMsNENBQTRDdUM7WUFDeEQzRCxTQUFTO21CQUFJMkQ7YUFBYSxHQUFHLGtEQUFrRDtZQUUvRSx1QkFBdUI7WUFDdkJ0RSwrREFBV0EsQ0FBQyxXQUFXLHVCQUE0QyxPQUFyQlksZ0JBQWdCNkQsSUFBSTtRQUNwRSxPQUFPO1lBQ0wsZUFBZTtZQUNmLE1BQU1DLFVBQVU7Z0JBQ2RMLFdBQVd6RCxnQkFBZ0J5QyxFQUFFO2dCQUM3Qm1CLGFBQWE1RCxnQkFBZ0I2RCxJQUFJO2dCQUNqQzNEO2dCQUNBaUQsT0FDRSxPQUFPbkQsZ0JBQWdCbUQsS0FBSyxLQUFLLFdBQzdCWSxXQUFXL0QsZ0JBQWdCbUQsS0FBSyxJQUNoQ25ELGdCQUFnQm1ELEtBQUs7WUFDN0I7WUFFQSx1Q0FBdUM7WUFDdkMsTUFBTWEsV0FBVzttQkFBSWxFO2dCQUFPZ0U7YUFBUTtZQUVwQyxzQ0FBc0M7WUFDdEM1QyxRQUFRQyxHQUFHLENBQUMscUNBQXFDNkM7WUFDakRqRSxTQUFTaUUsV0FBVyxrQ0FBa0M7WUFFdEQsdUJBQXVCO1lBQ3ZCNUUsK0RBQVdBLENBQ1QsV0FDQSxTQUFxQlksT0FBWkUsVUFBUyxLQUF3QixPQUFyQkYsZ0JBQWdCNkQsSUFBSSxFQUFDO1FBRTlDO1FBRUEsa0JBQWtCO1FBQ2xCNUQsbUJBQW1CO1FBQ25CRSxZQUFZO1FBQ1osSUFBSVAsTUFBTTtZQUNSQSxLQUFLZ0QsY0FBYyxDQUFDO2dCQUFFYSxXQUFXUTtnQkFBVy9ELFVBQVU7WUFBRTtRQUMxRDtJQUNGO0lBRUEsd0NBQXdDO0lBQ3hDLE1BQU1nRSxtQkFBbUIsQ0FBQ0M7UUFDeEIsTUFBTVQsZUFBZTtlQUFJNUQ7U0FBTTtRQUMvQjRELGFBQWFVLE1BQU0sQ0FBQ0QsT0FBTztRQUMzQnBFLFNBQVMyRDtJQUNYO0lBRUEsMERBQTBEO0lBQzFELE1BQU1XLHFCQUFxQnRHLGtEQUFXQTswREFBQztZQUVyQyxJQUFJLENBQUMrQyxjQUFjRSxZQUFZO2dCQUM3QkUsUUFBUUMsR0FBRyxDQUNULG9CQUNBLENBQUNMLGFBQWEsbUJBQW1CO2dCQUVuQztZQUNGO1lBRUFJLFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUJMO1lBRWpDLGdFQUFnRTtZQUNoRUcsY0FBYztZQUVkLDJDQUEyQztZQUMzQyxNQUFNcUQsU0FBU0MsU0FBU0MsYUFBYSxDQUFDO1lBQ3RDRixPQUFPRyxLQUFLLENBQUNDLE9BQU8sR0FBRztZQUN2QkgsU0FBU0ksSUFBSSxDQUFDQyxXQUFXLENBQUNOO1lBRTFCLHlEQUF5RDtZQUN6REEsT0FBT08sTUFBTTtrRUFBRztvQkFDZCxJQUFJUCxPQUFPUSxhQUFhLEVBQUU7d0JBQ3hCLHVDQUF1Qzt3QkFDdkNSLE9BQU9RLGFBQWEsQ0FBQ1AsUUFBUSxDQUFDUSxLQUFLLENBQUMsNDJCQStCUCxPQUFYakUsWUFBVzt3QkFLN0IscUJBQXFCO3dCQUNyQndELE9BQU9RLGFBQWEsQ0FBQ1AsUUFBUSxDQUFDUyxLQUFLO3dCQUVuQyx3RUFBd0U7d0JBQ3hFQzs4RUFBVztnQ0FDVCxJQUFJWCxPQUFPUSxhQUFhLEVBQUU7b0NBQ3hCLElBQUk7d0NBQ0YsMkJBQTJCO3dDQUMzQlIsT0FBT1EsYUFBYSxDQUFDSSxLQUFLO3dDQUMxQlosT0FBT1EsYUFBYSxDQUFDSyxLQUFLO29DQUM1QixFQUFFLE9BQU9DLEdBQUc7d0NBQ1ZsRSxRQUFRbUUsS0FBSyxDQUFDLDJCQUEyQkQ7b0NBQzNDO29DQUVBLG1DQUFtQztvQ0FDbkNIOzBGQUFXOzRDQUNUVixTQUFTSSxJQUFJLENBQUNXLFdBQVcsQ0FBQ2hCO3dDQUM1Qjt5RkFBRztnQ0FDTDs0QkFDRjs2RUFBRztvQkFDTDtnQkFDRjs7WUFFQSxvREFBb0Q7WUFDcERBLE9BQU9pQixHQUFHLEdBQUc7UUFDZjt5REFBRztRQUFDekU7UUFBWUU7S0FBVztJQUUzQiw0REFBNEQ7SUFDNURsRCxnREFBU0E7b0NBQUM7WUFDUixJQUFJOEMseUJBQXlCRSxjQUFjLENBQUNFLFlBQVk7Z0JBQ3RELDBEQUEwRDtnQkFDMUQsTUFBTXdFLFFBQVFQO3NEQUFXO3dCQUN2Qlosc0JBQXNCLG1EQUFtRDtvQkFDM0U7cURBQUc7Z0JBRUg7Z0RBQU8sSUFBTW9CLGFBQWFEOztZQUM1QjtRQUNGO21DQUFHO1FBQUM1RTtRQUF1QkU7UUFBWUU7UUFBWXFEO0tBQW1CO0lBRXRFLHlCQUF5QjtJQUN6QixNQUFNcUIsZUFBZTtRQUNuQixJQUFJO2dCQXVCQWhEO1lBdEJGLElBQUk1QyxNQUFNb0MsTUFBTSxLQUFLLEdBQUc7Z0JBQ3RCOUMsK0RBQVdBLENBQUMsU0FBUztnQkFDckI7WUFDRjtZQUVBLHVCQUF1QjtZQUN2QixNQUFNdUcsU0FBUyxNQUFNL0YsS0FBS2dHLGNBQWM7WUFFeEMsNkJBQTZCO1lBQzdCLElBQUksQ0FBQ3BGLGVBQWU7Z0JBQ2xCcEIsK0RBQVdBLENBQ1QsU0FDQTtnQkFFRjtZQUNGO1lBRUEsMkNBQTJDO1lBQzNDdUIsdUJBQXVCO1lBRXZCLG9DQUFvQztZQUNwQyxNQUFNa0YsWUFBWXJGLGtCQUNoQmtDLDJCQUFBQSxzQ0FBQUEsdUJBQUFBLGVBQWdCdEIsSUFBSSxjQUFwQnNCLDJDQUFBQSxxQkFBc0JvRCxJQUFJLENBQUMsQ0FBQ0MsUUFBVUEsTUFBTXRELEVBQUUsS0FBS2tELE9BQU85QyxPQUFPLE1BQUs7Z0JBQ3BFZ0IsTUFBTTtZQUNSO1lBRUYsd0JBQXdCO1lBQ3hCLE1BQU1tQyxjQUFjM0csMkVBQW1CQSxDQUNyQztnQkFDRW9ELElBQUl3RCxLQUFLQyxHQUFHO2dCQUNaOUY7Z0JBQ0ErRixlQUFlUixPQUFPUSxhQUFhO2dCQUNuQ0MsaUJBQWlCLElBQUlILE9BQU9JLFdBQVc7Z0JBQ3ZDdkcsT0FBT0EsTUFBTXdHLEdBQUcsQ0FBQyxDQUFDcEQsT0FBVTt3QkFDMUJVLGFBQWFWLEtBQUtVLFdBQVc7d0JBQzdCMUQsVUFBVWdELEtBQUtoRCxRQUFRO3dCQUN2QmlELE9BQU9ELEtBQUtDLEtBQUs7b0JBQ25CO1lBQ0YsR0FDQTBDO1lBR0YscUNBQXFDO1lBQ3JDLElBQUkvRSxhQUFhO1lBQ2pCLElBQUk7Z0JBQ0ZBLGFBQWEsTUFBTXhCLDRFQUFvQkEsQ0FBQzBHO1lBQzFDLEVBQUUsT0FBT1gsT0FBTztnQkFDZG5FLFFBQVFtRSxLQUFLLENBQUMscUNBQXFDQTtZQUNuRCwwREFBMEQ7WUFDNUQ7WUFFQSxNQUFNa0IsV0FBMEI7Z0JBQzlCbkc7Z0JBQ0ErRixlQUFlUixPQUFPUSxhQUFhO2dCQUNuQ3JHLE9BQU9BLE1BQU13RyxHQUFHLENBQUMsQ0FBQ3BELE9BQVU7d0JBQzFCTyxXQUFXUCxLQUFLTyxTQUFTO3dCQUN6QnZELFVBQVVnRCxLQUFLaEQsUUFBUTt3QkFDdkJpRCxPQUFPRCxLQUFLQyxLQUFLO29CQUNuQjtnQkFDQXJDO2dCQUNBK0IsT0FBTyxFQUFFckMsMEJBQUFBLG9DQUFBQSxjQUFlaUMsRUFBRTtZQUM1QjtZQUVBLE1BQU0rRCxXQUFXLE1BQU0xRCxXQUFXeUQsVUFBVUUsTUFBTTtZQUVsRCxJQUFJRCxTQUFTRSxPQUFPLEVBQUU7Z0JBQ3BCdEgsK0RBQVdBLENBQUMsV0FBVztnQkFFdkIsb0NBQW9DO2dCQUNwQzJCLGNBQWNEO2dCQUVkLG9EQUFvRDtnQkFDcERELHlCQUF5QjtnQkFJekIsdURBQXVEO2dCQUN2RFc7Z0JBRUEsNkVBQTZFO2dCQUU3RXlELFdBQVc7b0JBQ1QsSUFBSXZGLFdBQVc7d0JBQ2IscURBQXFEO3dCQUNyRDhCO29CQUNGO2dCQUNGLEdBQUc7WUFFSCwwREFBMEQ7WUFDMUQsK0NBQStDO1lBQ2pELE9BQU87Z0JBQ0xwQywrREFBV0EsQ0FBQyxTQUFTb0gsU0FBU0csT0FBTyxJQUFJO1lBQzNDO1FBQ0YsRUFBRSxPQUFPdEIsT0FBWTtnQkFHakJBO1lBRkZqRywrREFBV0EsQ0FDVCxTQUNBaUcsRUFBQUEsY0FBQUEsTUFBTWpFLElBQUksY0FBVmlFLGtDQUFBQSxZQUFZc0IsT0FBTyxLQUFJO1FBRTNCLFNBQVU7WUFDUmhHLHVCQUF1QjtRQUN6QjtJQUNGO0lBRUEsZ0RBQWdEO0lBQ2hETyxRQUFRQyxHQUFHLENBQUMseUJBQXlCckI7SUFFckMscUJBQ0UsOERBQUNYLG1FQUFZQTtRQUNYeUgsT0FBTTtRQUNOcEgsUUFBUUE7UUFDUkMsU0FBU0E7UUFDVG9ILE9BQU07OzBCQUVOLDhEQUFDQztnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDckksNkxBQW9CQTt3Q0FBQ3FJLFdBQVU7Ozs7OztrREFDaEMsOERBQUNDO3dDQUFHRCxXQUFVO2tEQUFzQzs7Ozs7Ozs7Ozs7OzBDQUl0RCw4REFBQ0Q7Z0NBQUlDLFdBQVU7O29DQUFrQztvQ0FDeEM7a0RBQ1AsOERBQUNFO3dDQUFLRixXQUFVOzs0Q0FBaUI7NENBQUszRyxZQUFZOEcsT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUk5RCw4REFBQ0o7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0k7NERBQUdKLFdBQVU7c0VBQXNDOzs7Ozs7c0VBR3BELDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNFO29FQUFLRixXQUFVOzhFQUFvQjs7Ozs7O2dFQUFROzs7Ozs7Ozs7Ozs7OzhEQUtoRCw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTtzRUFDYiw0RUFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDSzt3RUFBTUwsV0FBVTs7NEVBQTJCOzBGQUNsQyw4REFBQ0U7Z0ZBQUtGLFdBQVU7MEZBQWU7Ozs7Ozs7Ozs7OztrRkFFekMsOERBQUM3SSwwSEFBTUE7d0VBQ0xtSixVQUFVO3dFQUNWQyxhQUNFaEcsb0JBQ0ksd0JBQ0E7d0VBRU5pRyxrQkFBaUI7d0VBQ2pCQyxTQUFTbEc7d0VBQ1RtRyxVQUFVbkc7d0VBQ1ZvRyxVQUFVLENBQUNDO2dGQUNPaEk7NEVBQWhCLE1BQU1pSSxVQUFVakkseUJBQUFBLG9DQUFBQSxxQkFBQUEsYUFBY3lCLElBQUksY0FBbEJ6Qix5Q0FBQUEsbUJBQW9Cc0MsUUFBUSxDQUFDNkQsSUFBSSxDQUMvQyxDQUFDK0IsSUFBTUEsRUFBRXBGLEVBQUUsS0FBS2tGOzRFQUVsQnpHLFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUJ5Rzs0RUFDakMsSUFBSUEsU0FBUztnRkFDWDNILG1CQUFtQjtvRkFDakIsR0FBRzJILE9BQU87b0ZBQ1Z6RSxPQUNFLE9BQU95RSxRQUFRekUsS0FBSyxLQUFLLFdBQ3JCeUUsUUFBUXpFLEtBQUssR0FDYjJFLE9BQU9GLFFBQVF6RSxLQUFLO2dGQUM1Qjs0RUFDRixPQUFPO2dGQUNMbEQsbUJBQW1COzRFQUNyQjt3RUFDRjt3RUFDQThILFVBQVV4SDt3RUFDVnlILGNBQWM7d0VBQ2RqQixXQUFVO3dFQUNWa0IsTUFBSzt3RUFDTEMsWUFDRTVHLGtDQUNFLDhEQUFDMUMsNkxBQWVBOzRFQUFDdUosSUFBSTs7Ozs7bUdBRXJCLDhEQUFDeEosNkxBQWNBOzs7Ozt3RUFHbkJ5SixpQkFDRTlHLGtDQUNFLDhEQUFDakQsMEhBQUlBOzRFQUFDNEosTUFBSzs7Ozs7cUZBRVg7a0ZBSUh0SSx5QkFBQUEsb0NBQUFBLHFCQUFBQSxhQUFjeUIsSUFBSSxjQUFsQnpCLDBDQUFBQSw4QkFBQUEsbUJBQW9Cc0MsUUFBUSxjQUE1QnRDLGtEQUFBQSw0QkFBOEIyRyxHQUFHLENBQUMsQ0FBQ3NCLHdCQUNsQyw4REFBQzFKLDBIQUFNQSxDQUFDbUssTUFBTTtnRkFFWlYsT0FBT0MsUUFBUW5GLEVBQUU7Z0ZBQ2pCZ0YsVUFBVUcsUUFBUXRFLGFBQWEsSUFBSTs7b0ZBRWxDc0UsUUFBUS9ELElBQUk7b0ZBQUM7b0ZBQVF5RSxPQUFPVixRQUFRekUsS0FBSyxFQUFFK0QsT0FBTyxDQUFDO29GQUFJO29GQUN2RFUsUUFBUXRFLGFBQWEsSUFBSSxJQUN0QixtQkFDQSxXQUFpQyxPQUF0QnNFLFFBQVF0RSxhQUFhLEVBQUM7OytFQVBoQ3NFLFFBQVFuRixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBY3pCLDhEQUFDcUU7c0VBQ0MsNEVBQUNBO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0s7d0VBQU1MLFdBQVU7OzRFQUEyQjswRkFDdEMsOERBQUNFO2dGQUFLRixXQUFVOzBGQUFlOzs7Ozs7Ozs7Ozs7a0ZBRXJDLDhEQUFDNUksMEhBQVdBO3dFQUNWb0ssS0FBSzt3RUFDTFosT0FBT3pIO3dFQUNQd0gsVUFBVSxDQUFDQyxRQUFVeEgsWUFBWXdILFNBQVM7d0VBQzFDbEQsT0FBTzs0RUFBRW9DLE9BQU87d0VBQU87d0VBQ3ZCRSxXQUFVO3dFQUNWa0IsTUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBTWIsOERBQUNoSywwSEFBTUE7b0RBQ0x1SyxNQUFLO29EQUNMQyxvQkFBTSw4REFBQ2hLLDZMQUFZQTs7Ozs7b0RBQ25CaUssU0FBU3JGO29EQUNUMEQsV0FBVTtvREFDVlUsVUFBVSxDQUFDekg7b0RBQ1hpSSxNQUFLOzhEQUNOOzs7Ozs7Ozs7Ozs7c0RBTUgsOERBQUNuQjs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNJO29EQUFHSixXQUFVOztzRUFDWiw4REFBQ3JJLDZMQUFvQkE7NERBQUNxSSxXQUFVOzs7Ozs7d0RBQVM7Ozs7Ozs7OERBRTNDLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDWmpILE1BQU1vQyxNQUFNLEtBQUssa0JBQ2hCLDhEQUFDNEU7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUMzSSwwSEFBS0E7NERBQ0p1SywyQkFDRSw4REFBQzFCO2dFQUFLRixXQUFVOzBFQUFnQjs7Ozs7OzREQUlsQzZCLE9BQU94SywwSEFBS0EsQ0FBQ3lLLHNCQUFzQjs7Ozs7Ozs7OztrRkFJdkMsOERBQUNDO3dEQUFNL0IsV0FBVTs7MEVBQ2YsOERBQUNnQztnRUFBTWhDLFdBQVU7MEVBQ2YsNEVBQUNpQzs7c0ZBQ0MsOERBQUNDOzRFQUFHbEMsV0FBVTtzRkFBa0M7Ozs7OztzRkFHaEQsOERBQUNrQzs0RUFBR2xDLFdBQVU7c0ZBQXlDOzs7Ozs7c0ZBR3ZELDhEQUFDa0M7NEVBQUdsQyxXQUFVO3NGQUF3Qzs7Ozs7O3NGQUd0RCw4REFBQ2tDOzRFQUFHbEMsV0FBVTtzRkFBd0M7Ozs7OztzRkFHdEQsOERBQUNrQzs0RUFBR2xDLFdBQVU7c0ZBQXlDOzs7Ozs7Ozs7Ozs7Ozs7OzswRUFLM0QsOERBQUNtQzswRUFDRXBKLE1BQU13RyxHQUFHLENBQUMsQ0FBQ3BELE1BQU1pQixzQkFDaEIsOERBQUM2RTt3RUFFQ2pDLFdBQVU7OzBGQUVWLDhEQUFDb0M7Z0ZBQUdwQyxXQUFVOzBGQUNYN0QsS0FBS1UsV0FBVyxJQUFJOzs7Ozs7MEZBRXZCLDhEQUFDdUY7Z0ZBQUdwQyxXQUFVOzBGQUNYN0QsS0FBS2hELFFBQVE7Ozs7OzswRkFFaEIsOERBQUNpSjtnRkFBR3BDLFdBQVU7O29GQUFxQztvRkFDNUN1QixPQUFPcEYsS0FBS0MsS0FBSyxFQUFFK0QsT0FBTyxDQUFDOzs7Ozs7OzBGQUVsQyw4REFBQ2lDO2dGQUFHcEMsV0FBVTs7b0ZBQWlEO29GQUN6RDtvRkFDRnVCLENBQUFBLE9BQU9wRixLQUFLQyxLQUFLLElBQUlELEtBQUtoRCxRQUFRLEVBQUVnSCxPQUFPLENBQUM7Ozs7Ozs7MEZBRWhELDhEQUFDaUM7Z0ZBQUdwQyxXQUFVOzBGQUNaLDRFQUFDOUksMEhBQU1BO29GQUNMd0ssb0JBQU0sOERBQUNqSyw2TEFBY0E7Ozs7O29GQUNyQmtLLFNBQVMsSUFBTXhFLGlCQUFpQkM7b0ZBQ2hDcUUsTUFBSztvRkFDTFksTUFBTTtvRkFDTnJDLFdBQVU7Ozs7Ozs7Ozs7Ozt1RUF0QlQsR0FBcUI1QyxPQUFsQmpCLEtBQUtPLFNBQVMsRUFBQyxLQUFTLE9BQU5VOzs7Ozs7Ozs7OzBFQTRCaEMsOERBQUNrRjtnRUFBTXRDLFdBQVU7MEVBQ2YsNEVBQUNpQzs7c0ZBQ0MsOERBQUNHOzRFQUNDRyxTQUFTOzRFQUNUdkMsV0FBVTtzRkFDWDs7Ozs7O3NGQUdELDhEQUFDb0M7NEVBQ0NHLFNBQVM7NEVBQ1R2QyxXQUFVOztnRkFDWDtnRkFDTTNHLFlBQVk4RyxPQUFPLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQVd6Qyw4REFBQ0o7OENBQ0MsNEVBQUNBO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0k7Z0RBQUdKLFdBQVU7O2tFQUNaLDhEQUFDckksNkxBQW9CQTt3REFBQ3FJLFdBQVU7Ozs7OztvREFBUzs7Ozs7OzswREFHM0MsOERBQUMvSSx5SEFBSUE7Z0RBQUM0QixNQUFNQTtnREFBTTJKLFFBQU87O2tFQUN2Qiw4REFBQ3pDO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRTt3RUFBS0YsV0FBVTtrRkFBZ0I7Ozs7OztrRkFDaEMsOERBQUNFO3dFQUFLRixXQUFVO2tGQUNiakgsTUFBTW9DLE1BQU07Ozs7Ozs7Ozs7OzswRUFHakIsOERBQUM0RTtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNFO3dFQUFLRixXQUFVO2tGQUFnQjs7Ozs7O2tGQUNoQyw4REFBQ0U7d0VBQUtGLFdBQVU7a0ZBQ2JqSCxNQUFNa0QsTUFBTSxDQUFDLENBQUNDLEtBQUtDLE9BQVNELE1BQU1DLEtBQUtoRCxRQUFRLEVBQUU7Ozs7Ozs7Ozs7OzswRUFHdEQsOERBQUM0RztnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNFO3dFQUFLRixXQUFVO2tGQUFnQjs7Ozs7O2tGQUNoQyw4REFBQ0U7d0VBQUtGLFdBQVU7OzRFQUEwQjs0RUFDbkMzRyxZQUFZOEcsT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7OzBFQUc3Qiw4REFBQ0o7Z0VBQUlDLFdBQVU7Ozs7OzswRUFDZiw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRTt3RUFBS0YsV0FBVTtrRkFBZ0I7Ozs7OztrRkFDaEMsOERBQUNFO3dFQUFLRixXQUFVOzs0RUFBMkI7NEVBQ3BDM0csWUFBWThHLE9BQU8sQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvREFNOUIxRyw4QkFDQyw4REFBQ3NHO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0s7Z0VBQU1MLFdBQVU7MEVBQTJCOzs7Ozs7MEVBQzVDLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNsSSw2TEFBWUE7d0VBQUNrSSxXQUFVOzs7Ozs7a0ZBQ3hCLDhEQUFDRTt3RUFBS0YsV0FBVTtrRkFDYnZHLGNBQWNxRCxJQUFJOzs7Ozs7a0ZBRXJCLDhEQUFDMkY7d0VBQ0NoQixNQUFLO3dFQUNMM0UsTUFBSzt3RUFDTDhELE9BQU9uSCxjQUFjaUMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0ZBSzdCLDhEQUFDcUU7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUNjOzREQUFFZCxXQUFVO3NFQUFnQjs7Ozs7Ozs7Ozs7a0VBT2pDLDhEQUFDL0kseUhBQUlBLENBQUN5TCxJQUFJO3dEQUNSNUYsTUFBSzt3REFDTHVELHFCQUNFLDhEQUFDSDs0REFBS0YsV0FBVTs7Z0VBQWdCOzhFQUNmLDhEQUFDRTtvRUFBS0YsV0FBVTs4RUFBZTs7Ozs7Ozs7Ozs7O3dEQUdsRDJDLE9BQU87NERBQ0w7Z0VBQ0VDLFVBQVU7Z0VBQ1ZoRCxTQUFTOzREQUNYO3lEQUNEO3dEQUNEaUQsY0FBYTtrRUFFYiw0RUFBQzFMLDBIQUFNQTs0REFBQzZJLFdBQVU7NERBQWdCa0IsTUFBSzs7OEVBQ3JDLDhEQUFDL0osMEhBQU1BLENBQUNtSyxNQUFNO29FQUFDVixPQUFNOzhFQUNuQiw0RUFBQ2I7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDRTtnRkFBS0YsV0FBVTswRkFBTzs7Ozs7OzRFQUFTOzs7Ozs7Ozs7Ozs7OEVBR3BDLDhEQUFDN0ksMEhBQU1BLENBQUNtSyxNQUFNO29FQUFDVixPQUFNOzhFQUNuQiw0RUFBQ2I7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDRTtnRkFBS0YsV0FBVTswRkFBTzs7Ozs7OzRFQUFTOzs7Ozs7Ozs7Ozs7OEVBR3BDLDhEQUFDN0ksMEhBQU1BLENBQUNtSyxNQUFNO29FQUFDVixPQUFNOzhFQUNuQiw0RUFBQ2I7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDRTtnRkFBS0YsV0FBVTswRkFBTzs7Ozs7OzRFQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFNeEMsOERBQUNEO3dEQUFJQyxXQUFVOzs0REFDWm5HLHdCQUNDLDZDQUE2QzswRUFDN0MsOERBQUMzQywwSEFBTUE7Z0VBQ0x1SyxNQUFLO2dFQUNMQyxvQkFBTSw4REFBQy9KLDZMQUFvQkE7Ozs7O2dFQUMzQmdLLFNBQVM7b0VBQ1AsOENBQThDO29FQUM5QzdILHlCQUF5QjtvRUFDekJFLGNBQWM7b0VBQ2RFLGNBQWM7b0VBRWQscUNBQXFDO29FQUNyQ3JCLEtBQUt3RCxXQUFXO29FQUNoQnJELFNBQVMsRUFBRTtvRUFDWEUsbUJBQW1CO29FQUNuQkUsWUFBWTtvRUFDWkUsZUFBZTtnRUFDakI7Z0VBQ0EwRyxXQUFVO2dFQUNWa0IsTUFBSzswRUFDTjs7Ozs7NEVBSUQsbURBQW1EOzBFQUNuRCw4REFBQ2hLLDBIQUFNQTtnRUFDTHVLLE1BQUs7Z0VBQ0xDLG9CQUFNLDhEQUFDL0osNkxBQW9CQTs7Ozs7Z0VBQzNCZ0ssU0FBU2hEO2dFQUNUOEIsU0FBU3pFLGdCQUFnQnJDO2dFQUN6QitHLFVBQVUzSCxNQUFNb0MsTUFBTSxLQUFLO2dFQUMzQjZFLFdBQVU7Z0VBQ1ZrQixNQUFLOzBFQUVKdkgsc0JBQ0csdUJBQ0E7Ozs7OzswRUFJUiw4REFBQ3pDLDBIQUFNQTtnRUFDTHlLLFNBQVNqSjtnRUFDVHNILFdBQVU7Z0VBQ1ZrQixNQUFLOzBFQUNOOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVlmLDhEQUFDM0osMEhBQUtBO2dCQUNKc0kscUJBQ0UsOERBQUNFO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ2pJLDZMQUFlQTs0QkFBQ2lJLFdBQVU7Ozs7OztzQ0FDM0IsOERBQUNFO3NDQUFLOzs7Ozs7Ozs7Ozs7Z0JBR1Y0QyxNQUFNako7Z0JBQ05rSixVQUFVO29CQUNSLDhDQUE4QztvQkFDOUNqSix5QkFBeUI7b0JBQ3pCRSxjQUFjO29CQUNkRSxjQUFjO29CQUVkLHFDQUFxQztvQkFDckNyQixLQUFLd0QsV0FBVztvQkFDaEJyRCxTQUFTLEVBQUU7b0JBQ1hFLG1CQUFtQjtvQkFDbkJFLFlBQVk7b0JBQ1pFLGVBQWU7Z0JBQ2pCO2dCQUNBd0csT0FBTztnQkFDUGtELFFBQVE7Z0JBQ1JoRCxXQUFVO2dCQUNWaUQsUUFBUTtrQ0FDTiw4REFBQy9MLDBIQUFNQTt3QkFFTHlLLFNBQVM7NEJBQ1AsOENBQThDOzRCQUM5QzdILHlCQUF5Qjs0QkFDekJFLGNBQWM7NEJBQ2RFLGNBQWM7NEJBRWQscUNBQXFDOzRCQUNyQ3JCLEtBQUt3RCxXQUFXOzRCQUNoQnJELFNBQVMsRUFBRTs0QkFDWEUsbUJBQW1COzRCQUNuQkUsWUFBWTs0QkFDWkUsZUFBZTt3QkFDakI7d0JBQ0EwRyxXQUFVO2tDQUNYO3VCQWZLOzs7OztrQ0FrQk4sOERBQUM5SSwwSEFBTUE7d0JBRUx1SyxNQUFLO3dCQUNMQyxvQkFBTSw4REFBQzNKLDZMQUFlQTs7Ozs7d0JBQ3RCNEosU0FBUzs0QkFDUCxrRUFBa0U7NEJBQ2xFLElBQUkxSCxZQUFZO2dDQUNkQyxjQUFjOzRCQUNoQjs0QkFDQW9EO3dCQUNGO3dCQUNBMEMsV0FBVTtrQ0FFVC9GLGFBQWEsZ0JBQWdCO3VCQVoxQjs7Ozs7aUJBY1A7MEJBRUQsNEVBQUM4RjtvQkFBSUMsV0FBVTs4QkFDWmpHLDJCQUNDLDhEQUFDZ0c7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUN4SSwwSEFBS0E7NEJBQ0pnSCxLQUFLekU7NEJBQ0xtSixLQUFJOzRCQUNKbEQsV0FBVTs0QkFDVnRDLE9BQU87Z0NBQUV5RixVQUFVOzRCQUFPOzs7Ozs7Ozs7O2tEQUk5Qiw4REFBQ3BEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDMUksMEhBQUlBOzRCQUFDNEosTUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT3pCO0dBeDNCTTFJOztRQUtXdkIseUhBQUlBLENBQUM2QjtRQXdCaEJiLGlGQUFzQkE7UUFvQ09DLGtGQUFxQkE7UUFJcERDLHdGQUEyQkE7UUFjcUJILDhFQUFxQkE7OztLQW5GbkVRO0FBMDNCTixpRUFBZUEsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsiRTpcXFBST0pFQ1RTXFxwb3NcXHBvc2Zyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXFNhbGVzXFxTYWxlc0Zvcm1QYW5lbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjayB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHtcbiAgRm9ybSxcbiAgQnV0dG9uLFxuICBTZWxlY3QsXG4gIElucHV0TnVtYmVyLFxuICBFbXB0eSxcbiAgU3BpbixcbiAgTW9kYWwsXG4gIEltYWdlLFxufSBmcm9tIFwiYW50ZFwiO1xuaW1wb3J0IHtcbiAgRGVsZXRlT3V0bGluZWQsXG4gIFBsdXNPdXRsaW5lZCxcbiAgU2hvcHBpbmdDYXJ0T3V0bGluZWQsXG4gIFNlYXJjaE91dGxpbmVkLFxuICBMb2FkaW5nT3V0bGluZWQsXG4gIFNob3BPdXRsaW5lZCxcbiAgUHJpbnRlck91dGxpbmVkLFxuICBFeWVPdXRsaW5lZCxcbn0gZnJvbSBcIkBhbnQtZGVzaWduL2ljb25zXCI7XG5pbXBvcnQge1xuICB1c2VDcmVhdGVTYWxlTXV0YXRpb24sXG4gIENyZWF0ZVNhbGVEdG8sXG4gIENyZWF0ZVNhbGVJdGVtRHRvLFxufSBmcm9tIFwiQC9yZWR1eFJUSy9zZXJ2aWNlcy9zYWxlc0FwaVwiO1xuaW1wb3J0IHtcbiAgdXNlR2V0QWxsUHJvZHVjdHNRdWVyeSxcbiAgUHJvZHVjdCxcbn0gZnJvbSBcIkAvcmVkdXhSVEsvc2VydmljZXMvcHJvZHVjdEFwaVwiO1xuaW1wb3J0IHtcbiAgdXNlR2V0VXNlclN0b3Jlc1F1ZXJ5LFxuICB1c2VHZXRVc2VyRGVmYXVsdFN0b3JlUXVlcnksXG59IGZyb20gXCJAL3JlZHV4UlRLL3NlcnZpY2VzL3VzZXJTdG9yZUFwaVwiO1xuaW1wb3J0IFNsaWRpbmdQYW5lbCBmcm9tIFwiQC9jb21wb25lbnRzL3VpL1NsaWRpbmdQYW5lbFwiO1xuaW1wb3J0IHsgc2hvd01lc3NhZ2UgfSBmcm9tIFwiQC91dGlscy9zaG93TWVzc2FnZVwiO1xuaW1wb3J0IHtcbiAgZ2VuZXJhdGVSZWNlaXB0SFRNTCxcbiAgZ2VuZXJhdGVSZWNlaXB0SW1hZ2UsXG59IGZyb20gXCJAL3V0aWxzL2Nsb3VkaW5hcnlVdGlsc1wiO1xuaW1wb3J0IHsgU3RvcmUgfSBmcm9tIFwiQC90eXBlcy9zdG9yZVwiO1xuaW1wb3J0IFwiLi9zYWxlcy1wYW5lbHMuY3NzXCI7XG5cbmludGVyZmFjZSBTYWxlc0Zvcm1QYW5lbFByb3BzIHtcbiAgaXNPcGVuOiBib29sZWFuO1xuICBvbkNsb3NlOiAoKSA9PiB2b2lkO1xuICBvblN1Y2Nlc3M/OiAoKSA9PiB2b2lkO1xufVxuXG5jb25zdCBTYWxlc0Zvcm1QYW5lbDogUmVhY3QuRkM8U2FsZXNGb3JtUGFuZWxQcm9wcz4gPSAoe1xuICBpc09wZW4sXG4gIG9uQ2xvc2UsXG4gIG9uU3VjY2Vzcyxcbn0pID0+IHtcbiAgY29uc3QgW2Zvcm1dID0gRm9ybS51c2VGb3JtKCk7XG4gIGNvbnN0IFtpdGVtcywgc2V0SXRlbXNdID0gdXNlU3RhdGU8XG4gICAgKENyZWF0ZVNhbGVJdGVtRHRvICYgeyBwcm9kdWN0TmFtZTogc3RyaW5nIH0pW11cbiAgPihbXSk7XG4gIGNvbnN0IFtzZWxlY3RlZFByb2R1Y3QsIHNldFNlbGVjdGVkUHJvZHVjdF0gPSB1c2VTdGF0ZTxQcm9kdWN0IHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtxdWFudGl0eSwgc2V0UXVhbnRpdHldID0gdXNlU3RhdGU8bnVtYmVyPigxKTtcbiAgY29uc3QgW3RvdGFsQW1vdW50LCBzZXRUb3RhbEFtb3VudF0gPSB1c2VTdGF0ZTxudW1iZXI+KDApO1xuICBjb25zdCBbc2VhcmNoVGVybSwgc2V0U2VhcmNoVGVybV0gPSB1c2VTdGF0ZShcIlwiKTtcbiAgY29uc3QgW3NlbGVjdGVkU3RvcmUsIHNldFNlbGVjdGVkU3RvcmVdID0gdXNlU3RhdGU8U3RvcmUgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2lzR2VuZXJhdGluZ1JlY2VpcHQsIHNldElzR2VuZXJhdGluZ1JlY2VpcHRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcmVjZWlwdFByZXZpZXdWaXNpYmxlLCBzZXRSZWNlaXB0UHJldmlld1Zpc2libGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcmVjZWlwdFVybCwgc2V0UmVjZWlwdFVybF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2hhc1ByaW50ZWQsIHNldEhhc1ByaW50ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIERlYnVnIHN0YXRlIGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zb2xlLmxvZyhcIkl0ZW1zIHN0YXRlIGNoYW5nZWQ6XCIsIGl0ZW1zKTtcbiAgfSwgW2l0ZW1zXSk7XG5cblxuICBjb25zdCB7XG4gICAgZGF0YTogcHJvZHVjdHNEYXRhLFxuICAgIGlzTG9hZGluZzogaXNMb2FkaW5nUHJvZHVjdHMsXG4gICAgcmVmZXRjaDogcmVmZXRjaFByb2R1Y3RzLFxuICB9ID0gdXNlR2V0QWxsUHJvZHVjdHNRdWVyeShcbiAgICB7XG4gICAgICBwYWdlOiAxLFxuICAgICAgbGltaXQ6IDEwMDAsIC8vIEluY3JlYXNlZCBsaW1pdCB0byBnZXQgbW9yZSBwcm9kdWN0cyBmb3Igc2FsZXNcbiAgICAgIHNlYXJjaDogc2VhcmNoVGVybSxcbiAgICB9LFxuICAgIHtcbiAgICAgIC8vIEZvcmNlIHJlZmV0Y2ggd2hlbiBjb21wb25lbnQgbW91bnRzIGFuZCB3aGVuIHBhbmVsIG9wZW5zXG4gICAgICByZWZldGNoT25Nb3VudE9yQXJnQ2hhbmdlOiB0cnVlLFxuICAgICAgcmVmZXRjaE9uRm9jdXM6IGZhbHNlLFxuICAgICAgcmVmZXRjaE9uUmVjb25uZWN0OiB0cnVlLCAvLyBSZWZldGNoIHdoZW4gcmVjb25uZWN0aW5nXG4gICAgfSxcbiAgKTtcblxuICAvLyBEZWJ1ZyBwcm9kdWN0cyBkYXRhXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHByb2R1Y3RzRGF0YSkge1xuICAgICAgY29uc29sZS5sb2coXCLwn5uSIFByb2R1Y3RzIGxvYWRlZDpcIiwge1xuICAgICAgICB0b3RhbDogcHJvZHVjdHNEYXRhLmRhdGE/LnRvdGFsIHx8IDAsXG4gICAgICAgIHByb2R1Y3RzQ291bnQ6IHByb2R1Y3RzRGF0YS5kYXRhPy5wcm9kdWN0cz8ubGVuZ3RoIHx8IDAsXG4gICAgICAgIGlzTG9hZGluZzogaXNMb2FkaW5nUHJvZHVjdHMsXG4gICAgICB9KTtcbiAgICB9XG4gIH0sIFtwcm9kdWN0c0RhdGEsIGlzTG9hZGluZ1Byb2R1Y3RzXSk7XG5cbiAgLy8gR2V0IGN1cnJlbnQgdXNlciBJRCBmcm9tIGF1dGggc3RhdGVcbiAgY29uc3QgZ2V0Q3VycmVudFVzZXJJZCA9ICgpID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgLy8gQHRzLWlnbm9yZSAtIFJlZHV4IHN0YXRlIGlzIGV4cG9zZWQgZm9yIGRlYnVnZ2luZ1xuICAgICAgY29uc3Qgc3RhdGUgPSB3aW5kb3cuX19SRURVWF9TVEFURTtcbiAgICAgIHJldHVybiBzdGF0ZT8uYXV0aD8udXNlcj8uaWQgfHwgMDtcbiAgICB9XG4gICAgcmV0dXJuIDA7XG4gIH07XG5cbiAgLy8gRmV0Y2ggdXNlciBzdG9yZXNcbiAgY29uc3QgeyBkYXRhOiB1c2VyU3RvcmVzRGF0YSB9ID0gdXNlR2V0VXNlclN0b3Jlc1F1ZXJ5KGdldEN1cnJlbnRVc2VySWQoKSk7XG5cbiAgLy8gRmV0Y2ggZGVmYXVsdCBzdG9yZVxuICBjb25zdCB7IGRhdGE6IGRlZmF1bHRTdG9yZURhdGEgfSA9XG4gICAgdXNlR2V0VXNlckRlZmF1bHRTdG9yZVF1ZXJ5KGdldEN1cnJlbnRVc2VySWQoKSk7XG5cbiAgLy8gU2V0IGRlZmF1bHQgc3RvcmUgd2hlbiBkYXRhIGlzIGxvYWRlZFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChkZWZhdWx0U3RvcmVEYXRhPy5kYXRhKSB7XG4gICAgICBzZXRTZWxlY3RlZFN0b3JlKGRlZmF1bHRTdG9yZURhdGEuZGF0YSk7XG4gICAgICBmb3JtLnNldEZpZWxkc1ZhbHVlKHsgc3RvcmVJZDogZGVmYXVsdFN0b3JlRGF0YS5kYXRhLmlkIH0pO1xuICAgIH0gZWxzZSBpZiAodXNlclN0b3Jlc0RhdGE/LmRhdGEgJiYgdXNlclN0b3Jlc0RhdGEuZGF0YS5sZW5ndGggPiAwKSB7XG4gICAgICBzZXRTZWxlY3RlZFN0b3JlKHVzZXJTdG9yZXNEYXRhLmRhdGFbMF0pO1xuICAgICAgZm9ybS5zZXRGaWVsZHNWYWx1ZSh7IHN0b3JlSWQ6IHVzZXJTdG9yZXNEYXRhLmRhdGFbMF0uaWQgfSk7XG4gICAgfVxuICB9LCBbZGVmYXVsdFN0b3JlRGF0YSwgdXNlclN0b3Jlc0RhdGEsIGZvcm1dKTtcblxuICAvLyBDcmVhdGUgc2FsZSBtdXRhdGlvblxuICBjb25zdCBbY3JlYXRlU2FsZSwgeyBpc0xvYWRpbmc6IGlzU3VibWl0dGluZyB9XSA9IHVzZUNyZWF0ZVNhbGVNdXRhdGlvbigpO1xuXG4gIC8vIENhbGN1bGF0ZSB0b3RhbCBhbW91bnQgd2hlbmV2ZXIgaXRlbXMgY2hhbmdlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGl0ZW1zICYmIGl0ZW1zLmxlbmd0aCA+IDApIHtcbiAgICAgIGNvbnN0IHRvdGFsID0gaXRlbXMucmVkdWNlKFxuICAgICAgICAoc3VtLCBpdGVtKSA9PiBzdW0gKyBpdGVtLnByaWNlICogaXRlbS5xdWFudGl0eSxcbiAgICAgICAgMCxcbiAgICAgICk7XG4gICAgICBzZXRUb3RhbEFtb3VudCh0b3RhbCk7XG4gICAgICBpZiAoZm9ybSkge1xuICAgICAgICBmb3JtLnNldEZpZWxkc1ZhbHVlKHsgdG90YWxBbW91bnQ6IHRvdGFsIH0pO1xuICAgICAgfVxuXG4gICAgICAvLyBEZWJ1ZyBsb2cgdG8gY2hlY2sgaXRlbXMgc3RhdGVcbiAgICAgIGNvbnNvbGUubG9nKFwiQ3VycmVudCBpdGVtcyBpbiB1c2VFZmZlY3Q6XCIsIGl0ZW1zKTtcbiAgICB9IGVsc2Uge1xuICAgICAgc2V0VG90YWxBbW91bnQoMCk7XG4gICAgICBpZiAoZm9ybSkge1xuICAgICAgICBmb3JtLnNldEZpZWxkc1ZhbHVlKHsgdG90YWxBbW91bnQ6IDAgfSk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbaXRlbXMsIGZvcm1dKTtcblxuICAvLyBIYW5kbGUgcGFuZWwgb3Blbi9jbG9zZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpc09wZW4pIHtcbiAgICAgIC8vIFdoZW4gcGFuZWwgb3BlbnMsIGVuc3VyZSB3ZSBoYXZlIGZyZXNoIHByb2R1Y3QgZGF0YVxuICAgICAgY29uc29sZS5sb2coXCLwn5uSIFNhbGVzIHBhbmVsIG9wZW5lZCAtIGZldGNoaW5nIGZyZXNoIHByb2R1Y3QgZGF0YVwiKTtcbiAgICAgIHJlZmV0Y2hQcm9kdWN0cygpO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBSZXNldCBmb3JtIHdoZW4gcGFuZWwgaXMgY2xvc2VkXG4gICAgICBpZiAoZm9ybSkge1xuICAgICAgICBmb3JtLnJlc2V0RmllbGRzKCk7XG4gICAgICB9XG4gICAgICBzZXRJdGVtcyhbXSk7XG4gICAgICBzZXRTZWxlY3RlZFByb2R1Y3QobnVsbCk7XG4gICAgICBzZXRRdWFudGl0eSgxKTtcbiAgICAgIHNldFRvdGFsQW1vdW50KDApO1xuICAgICAgc2V0UmVjZWlwdFVybChudWxsKTtcbiAgICAgIHNldFJlY2VpcHRQcmV2aWV3VmlzaWJsZShmYWxzZSk7XG4gICAgICBzZXRIYXNQcmludGVkKGZhbHNlKTtcbiAgICB9XG4gIH0sIFtpc09wZW4sIGZvcm0sIHJlZmV0Y2hQcm9kdWN0c10pO1xuXG4gIC8vIEhhbmRsZSBhZGRpbmcgYW4gaXRlbSB0byB0aGUgc2FsZVxuICBjb25zdCBoYW5kbGVBZGRJdGVtID0gKCkgPT4ge1xuICAgIGlmICghc2VsZWN0ZWRQcm9kdWN0KSB7XG4gICAgICBzaG93TWVzc2FnZShcImVycm9yXCIsIFwiUGxlYXNlIHNlbGVjdCBhIHByb2R1Y3RcIik7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgaWYgKHF1YW50aXR5IDw9IDApIHtcbiAgICAgIHNob3dNZXNzYWdlKFwiZXJyb3JcIiwgXCJRdWFudGl0eSBtdXN0IGJlIGdyZWF0ZXIgdGhhbiAwXCIpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGlmIChzZWxlY3RlZFByb2R1Y3Quc3RvY2tRdWFudGl0eSA8IHF1YW50aXR5KSB7XG4gICAgICBzaG93TWVzc2FnZShcbiAgICAgICAgXCJlcnJvclwiLFxuICAgICAgICBgT25seSAke3NlbGVjdGVkUHJvZHVjdC5zdG9ja1F1YW50aXR5fSB1bml0cyBhdmFpbGFibGUgaW4gc3RvY2tgLFxuICAgICAgKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZyhcIkFkZGluZyBpdGVtIHdpdGggcHJvZHVjdDpcIiwgc2VsZWN0ZWRQcm9kdWN0KTtcblxuICAgIC8vIENoZWNrIGlmIHByb2R1Y3QgYWxyZWFkeSBleGlzdHMgaW4gaXRlbXNcbiAgICBjb25zdCBleGlzdGluZ0l0ZW1JbmRleCA9IGl0ZW1zLmZpbmRJbmRleChcbiAgICAgIChpdGVtKSA9PiBpdGVtLnByb2R1Y3RJZCA9PT0gc2VsZWN0ZWRQcm9kdWN0LmlkLFxuICAgICk7XG5cbiAgICBpZiAoZXhpc3RpbmdJdGVtSW5kZXggPj0gMCkge1xuICAgICAgLy8gVXBkYXRlIGV4aXN0aW5nIGl0ZW1cbiAgICAgIGNvbnN0IHVwZGF0ZWRJdGVtcyA9IFsuLi5pdGVtc107XG4gICAgICBjb25zdCBuZXdRdWFudGl0eSA9IHVwZGF0ZWRJdGVtc1tleGlzdGluZ0l0ZW1JbmRleF0ucXVhbnRpdHkgKyBxdWFudGl0eTtcblxuICAgICAgaWYgKG5ld1F1YW50aXR5ID4gc2VsZWN0ZWRQcm9kdWN0LnN0b2NrUXVhbnRpdHkpIHtcbiAgICAgICAgc2hvd01lc3NhZ2UoXG4gICAgICAgICAgXCJlcnJvclwiLFxuICAgICAgICAgIGBDYW5ub3QgYWRkIG1vcmUgdGhhbiAke3NlbGVjdGVkUHJvZHVjdC5zdG9ja1F1YW50aXR5fSB1bml0cyBvZiB0aGlzIHByb2R1Y3RgLFxuICAgICAgICApO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIHVwZGF0ZWRJdGVtc1tleGlzdGluZ0l0ZW1JbmRleF0ucXVhbnRpdHkgPSBuZXdRdWFudGl0eTtcblxuICAgICAgLy8gTWFrZSBzdXJlIHRoZSBwcm9kdWN0IG5hbWUgaXMgc2V0XG4gICAgICBpZiAoIXVwZGF0ZWRJdGVtc1tleGlzdGluZ0l0ZW1JbmRleF0ucHJvZHVjdE5hbWUpIHtcbiAgICAgICAgdXBkYXRlZEl0ZW1zW2V4aXN0aW5nSXRlbUluZGV4XS5wcm9kdWN0TmFtZSA9IHNlbGVjdGVkUHJvZHVjdC5uYW1lO1xuICAgICAgfVxuXG4gICAgICAvLyBVcGRhdGUgdGhlIHN0YXRlIHdpdGggdGhlIG5ldyBhcnJheVxuICAgICAgY29uc29sZS5sb2coXCJVcGRhdGluZyBleGlzdGluZyBpdGVtLiBOZXcgaXRlbXMgYXJyYXk6XCIsIHVwZGF0ZWRJdGVtcyk7XG4gICAgICBzZXRJdGVtcyhbLi4udXBkYXRlZEl0ZW1zXSk7IC8vIENyZWF0ZSBhIG5ldyBhcnJheSByZWZlcmVuY2UgdG8gZm9yY2UgcmUtcmVuZGVyXG5cbiAgICAgIC8vIFNob3cgc3VjY2VzcyBtZXNzYWdlXG4gICAgICBzaG93TWVzc2FnZShcInN1Y2Nlc3NcIiwgYFVwZGF0ZWQgcXVhbnRpdHkgb2YgJHtzZWxlY3RlZFByb2R1Y3QubmFtZX1gKTtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gQWRkIG5ldyBpdGVtXG4gICAgICBjb25zdCBuZXdJdGVtID0ge1xuICAgICAgICBwcm9kdWN0SWQ6IHNlbGVjdGVkUHJvZHVjdC5pZCxcbiAgICAgICAgcHJvZHVjdE5hbWU6IHNlbGVjdGVkUHJvZHVjdC5uYW1lLCAvLyBNYWtlIHN1cmUgdGhpcyBpcyBjb3JyZWN0bHkgc2V0XG4gICAgICAgIHF1YW50aXR5LFxuICAgICAgICBwcmljZTpcbiAgICAgICAgICB0eXBlb2Ygc2VsZWN0ZWRQcm9kdWN0LnByaWNlID09PSBcInN0cmluZ1wiXG4gICAgICAgICAgICA/IHBhcnNlRmxvYXQoc2VsZWN0ZWRQcm9kdWN0LnByaWNlKVxuICAgICAgICAgICAgOiBzZWxlY3RlZFByb2R1Y3QucHJpY2UsXG4gICAgICB9O1xuXG4gICAgICAvLyBDcmVhdGUgYSBuZXcgYXJyYXkgd2l0aCB0aGUgbmV3IGl0ZW1cbiAgICAgIGNvbnN0IG5ld0l0ZW1zID0gWy4uLml0ZW1zLCBuZXdJdGVtXTtcblxuICAgICAgLy8gVXBkYXRlIHRoZSBzdGF0ZSB3aXRoIHRoZSBuZXcgYXJyYXlcbiAgICAgIGNvbnNvbGUubG9nKFwiQWRkaW5nIG5ldyBpdGVtLiBOZXcgaXRlbXMgYXJyYXk6XCIsIG5ld0l0ZW1zKTtcbiAgICAgIHNldEl0ZW1zKG5ld0l0ZW1zKTsgLy8gVGhpcyBzaG91bGQgdHJpZ2dlciBhIHJlLXJlbmRlclxuXG4gICAgICAvLyBTaG93IHN1Y2Nlc3MgbWVzc2FnZVxuICAgICAgc2hvd01lc3NhZ2UoXG4gICAgICAgIFwic3VjY2Vzc1wiLFxuICAgICAgICBgQWRkZWQgJHtxdWFudGl0eX0gJHtzZWxlY3RlZFByb2R1Y3QubmFtZX0gdG8gc2FsZWAsXG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIFJlc2V0IHNlbGVjdGlvblxuICAgIHNldFNlbGVjdGVkUHJvZHVjdChudWxsKTtcbiAgICBzZXRRdWFudGl0eSgxKTtcbiAgICBpZiAoZm9ybSkge1xuICAgICAgZm9ybS5zZXRGaWVsZHNWYWx1ZSh7IHByb2R1Y3RJZDogdW5kZWZpbmVkLCBxdWFudGl0eTogMSB9KTtcbiAgICB9XG4gIH07XG5cbiAgLy8gSGFuZGxlIHJlbW92aW5nIGFuIGl0ZW0gZnJvbSB0aGUgc2FsZVxuICBjb25zdCBoYW5kbGVSZW1vdmVJdGVtID0gKGluZGV4OiBudW1iZXIpID0+IHtcbiAgICBjb25zdCB1cGRhdGVkSXRlbXMgPSBbLi4uaXRlbXNdO1xuICAgIHVwZGF0ZWRJdGVtcy5zcGxpY2UoaW5kZXgsIDEpO1xuICAgIHNldEl0ZW1zKHVwZGF0ZWRJdGVtcyk7XG4gIH07XG5cbiAgLy8gSGFuZGxlIHByaW50aW5nIHJlY2VpcHQgLSBkaXJlY3RseSB0cmlnZ2VyIHByaW50IGRpYWxvZ1xuICBjb25zdCBoYW5kbGVQcmludFJlY2VpcHQgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG5cbiAgICBpZiAoIXJlY2VpcHRVcmwgfHwgaGFzUHJpbnRlZCkge1xuICAgICAgY29uc29sZS5sb2coXG4gICAgICAgIFwiU2tpcHBpbmcgcHJpbnQ6IFwiLFxuICAgICAgICAhcmVjZWlwdFVybCA/IFwiTm8gcmVjZWlwdCBVUkxcIiA6IFwiQWxyZWFkeSBwcmludGVkXCIsXG4gICAgICApO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKFwiUHJpbnRpbmcgcmVjZWlwdDpcIiwgcmVjZWlwdFVybCk7XG5cbiAgICAvLyBNYXJrIGFzIHByaW50ZWQgaW1tZWRpYXRlbHkgdG8gcHJldmVudCBtdWx0aXBsZSBwcmludCBkaWFsb2dzXG4gICAgc2V0SGFzUHJpbnRlZCh0cnVlKTtcblxuICAgIC8vIENyZWF0ZSBhIGhpZGRlbiBpZnJhbWUgdG8gbG9hZCB0aGUgaW1hZ2VcbiAgICBjb25zdCBpZnJhbWUgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KFwiaWZyYW1lXCIpO1xuICAgIGlmcmFtZS5zdHlsZS5kaXNwbGF5ID0gXCJub25lXCI7XG4gICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChpZnJhbWUpO1xuXG4gICAgLy8gU2V0IHVwIHRoZSBpZnJhbWUgY29udGVudCB3aXRoIHRoZSBpbWFnZSBhbmQgcHJpbnQgQ1NTXG4gICAgaWZyYW1lLm9ubG9hZCA9ICgpID0+IHtcbiAgICAgIGlmIChpZnJhbWUuY29udGVudFdpbmRvdykge1xuICAgICAgICAvLyBXcml0ZSB0aGUgSFRNTCBjb250ZW50IHRvIHRoZSBpZnJhbWVcbiAgICAgICAgaWZyYW1lLmNvbnRlbnRXaW5kb3cuZG9jdW1lbnQud3JpdGUoYFxuICAgICAgICAgIDwhRE9DVFlQRSBodG1sPlxuICAgICAgICAgIDxodG1sPlxuICAgICAgICAgICAgPGhlYWQ+XG4gICAgICAgICAgICAgIDx0aXRsZT5QcmludCBSZWNlaXB0PC90aXRsZT5cbiAgICAgICAgICAgICAgPHN0eWxlPlxuICAgICAgICAgICAgICAgIGJvZHkge1xuICAgICAgICAgICAgICAgICAgbWFyZ2luOiAwO1xuICAgICAgICAgICAgICAgICAgcGFkZGluZzogMDtcbiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICAgICAgICBoZWlnaHQ6IDEwMHZoO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpbWcge1xuICAgICAgICAgICAgICAgICAgbWF4LXdpZHRoOiAxMDAlO1xuICAgICAgICAgICAgICAgICAgbWF4LWhlaWdodDogMTAwdmg7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIEBtZWRpYSBwcmludCB7XG4gICAgICAgICAgICAgICAgICBib2R5IHtcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luOiAwO1xuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAwO1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgaW1nIHtcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgICAgICAgICAgICAgIGhlaWdodDogYXV0bztcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIDwvc3R5bGU+XG4gICAgICAgICAgICA8L2hlYWQ+XG4gICAgICAgICAgICA8Ym9keT5cbiAgICAgICAgICAgICAgPGltZyBzcmM9XCIke3JlY2VpcHRVcmx9XCIgYWx0PVwiUmVjZWlwdFwiIC8+XG4gICAgICAgICAgICA8L2JvZHk+XG4gICAgICAgICAgPC9odG1sPlxuICAgICAgICBgKTtcblxuICAgICAgICAvLyBDbG9zZSB0aGUgZG9jdW1lbnRcbiAgICAgICAgaWZyYW1lLmNvbnRlbnRXaW5kb3cuZG9jdW1lbnQuY2xvc2UoKTtcblxuICAgICAgICAvLyBVc2UgYSBzaW5nbGUgcHJpbnQgdHJpZ2dlciB3aXRoIGEgZGVsYXkgdG8gZW5zdXJlIHRoZSBpbWFnZSBpcyBsb2FkZWRcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgaWYgKGlmcmFtZS5jb250ZW50V2luZG93KSB7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAvLyBQcmludCB0aGUgaWZyYW1lIGNvbnRlbnRcbiAgICAgICAgICAgICAgaWZyYW1lLmNvbnRlbnRXaW5kb3cuZm9jdXMoKTtcbiAgICAgICAgICAgICAgaWZyYW1lLmNvbnRlbnRXaW5kb3cucHJpbnQoKTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHByaW50aW5nIHJlY2VpcHQ6XCIsIGUpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBSZW1vdmUgdGhlIGlmcmFtZSBhZnRlciBwcmludGluZ1xuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQoaWZyYW1lKTtcbiAgICAgICAgICAgIH0sIDEwMDApO1xuICAgICAgICAgIH1cbiAgICAgICAgfSwgNTAwKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgLy8gU2V0IHRoZSBpZnJhbWUgc291cmNlIHRvIHRyaWdnZXIgdGhlIG9ubG9hZCBldmVudFxuICAgIGlmcmFtZS5zcmMgPSBcImFib3V0OmJsYW5rXCI7XG4gIH0sIFtyZWNlaXB0VXJsLCBoYXNQcmludGVkXSk7XG5cbiAgLy8gRWZmZWN0IHRvIGF1dG9tYXRpY2FsbHkgcHJpbnQgcmVjZWlwdCB3aGVuIG1vZGFsIGlzIHNob3duXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHJlY2VpcHRQcmV2aWV3VmlzaWJsZSAmJiByZWNlaXB0VXJsICYmICFoYXNQcmludGVkKSB7XG4gICAgICAvLyBBZGQgYSBzbWFsbCBkZWxheSB0byBlbnN1cmUgdGhlIHJlY2VpcHQgaW1hZ2UgaXMgbG9hZGVkXG4gICAgICBjb25zdCB0aW1lciA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBoYW5kbGVQcmludFJlY2VpcHQoKTsgLy8gVGhpcyBub3cgaGFuZGxlcyB0aGUgaGFzUHJpbnRlZCBzdGF0ZSBpbnRlcm5hbGx5XG4gICAgICB9LCA4MDApO1xuXG4gICAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVyKTtcbiAgICB9XG4gIH0sIFtyZWNlaXB0UHJldmlld1Zpc2libGUsIHJlY2VpcHRVcmwsIGhhc1ByaW50ZWQsIGhhbmRsZVByaW50UmVjZWlwdF0pO1xuXG4gIC8vIEhhbmRsZSBmb3JtIHN1Ym1pc3Npb25cbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBpZiAoaXRlbXMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIHNob3dNZXNzYWdlKFwiZXJyb3JcIiwgXCJQbGVhc2UgYWRkIGF0IGxlYXN0IG9uZSBpdGVtIHRvIHRoZSBzYWxlXCIpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIFZhbGlkYXRlIGZvcm0gZmllbGRzXG4gICAgICBjb25zdCB2YWx1ZXMgPSBhd2FpdCBmb3JtLnZhbGlkYXRlRmllbGRzKCk7XG5cbiAgICAgIC8vIENoZWNrIGlmIHN0b3JlIGlzIHNlbGVjdGVkXG4gICAgICBpZiAoIXNlbGVjdGVkU3RvcmUpIHtcbiAgICAgICAgc2hvd01lc3NhZ2UoXG4gICAgICAgICAgXCJlcnJvclwiLFxuICAgICAgICAgIFwiTm8gc3RvcmUgaW5mb3JtYXRpb24gYXZhaWxhYmxlLiBQbGVhc2Ugc2V0IHVwIHlvdXIgc3RvcmUgaW4geW91ciBwcm9maWxlIHNldHRpbmdzLlwiLFxuICAgICAgICApO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIFNldCBsb2FkaW5nIHN0YXRlIGZvciByZWNlaXB0IGdlbmVyYXRpb25cbiAgICAgIHNldElzR2VuZXJhdGluZ1JlY2VpcHQodHJ1ZSk7XG5cbiAgICAgIC8vIEdldCBzdG9yZSBpbmZvcm1hdGlvbiBmb3IgcmVjZWlwdFxuICAgICAgY29uc3Qgc3RvcmVJbmZvID0gc2VsZWN0ZWRTdG9yZSB8fFxuICAgICAgICB1c2VyU3RvcmVzRGF0YT8uZGF0YT8uZmluZCgoc3RvcmUpID0+IHN0b3JlLmlkID09PSB2YWx1ZXMuc3RvcmVJZCkgfHwge1xuICAgICAgICAgIG5hbWU6IFwiUE9TIFN5c3RlbVwiLFxuICAgICAgICB9O1xuXG4gICAgICAvLyBHZW5lcmF0ZSByZWNlaXB0IEhUTUxcbiAgICAgIGNvbnN0IHJlY2VpcHRIVE1MID0gZ2VuZXJhdGVSZWNlaXB0SFRNTChcbiAgICAgICAge1xuICAgICAgICAgIGlkOiBEYXRlLm5vdygpLCAvLyBUZW1wb3JhcnkgSUQgdW50aWwgd2UgZ2V0IHRoZSByZWFsIG9uZVxuICAgICAgICAgIHRvdGFsQW1vdW50LFxuICAgICAgICAgIHBheW1lbnRNZXRob2Q6IHZhbHVlcy5wYXltZW50TWV0aG9kLFxuICAgICAgICAgIHRyYW5zYWN0aW9uRGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIGl0ZW1zOiBpdGVtcy5tYXAoKGl0ZW0pID0+ICh7XG4gICAgICAgICAgICBwcm9kdWN0TmFtZTogaXRlbS5wcm9kdWN0TmFtZSxcbiAgICAgICAgICAgIHF1YW50aXR5OiBpdGVtLnF1YW50aXR5LFxuICAgICAgICAgICAgcHJpY2U6IGl0ZW0ucHJpY2UsXG4gICAgICAgICAgfSkpLFxuICAgICAgICB9LFxuICAgICAgICBzdG9yZUluZm8sXG4gICAgICApO1xuXG4gICAgICAvLyBHZW5lcmF0ZSByZWNlaXB0IGltYWdlIGFuZCBnZXQgVVJMXG4gICAgICBsZXQgcmVjZWlwdFVybCA9IFwiaHR0cHM6Ly9yZWNlaXB0LmV4YW1wbGUuY29tL3BsYWNlaG9sZGVyXCI7XG4gICAgICB0cnkge1xuICAgICAgICByZWNlaXB0VXJsID0gYXdhaXQgZ2VuZXJhdGVSZWNlaXB0SW1hZ2UocmVjZWlwdEhUTUwpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkZhaWxlZCB0byBnZW5lcmF0ZSByZWNlaXB0IGltYWdlOlwiLCBlcnJvcik7XG4gICAgICAgIC8vIENvbnRpbnVlIHdpdGggcGxhY2Vob2xkZXIgVVJMIGlmIGltYWdlIGdlbmVyYXRpb24gZmFpbHNcbiAgICAgIH1cblxuICAgICAgY29uc3Qgc2FsZURhdGE6IENyZWF0ZVNhbGVEdG8gPSB7XG4gICAgICAgIHRvdGFsQW1vdW50LFxuICAgICAgICBwYXltZW50TWV0aG9kOiB2YWx1ZXMucGF5bWVudE1ldGhvZCxcbiAgICAgICAgaXRlbXM6IGl0ZW1zLm1hcCgoaXRlbSkgPT4gKHtcbiAgICAgICAgICBwcm9kdWN0SWQ6IGl0ZW0ucHJvZHVjdElkLFxuICAgICAgICAgIHF1YW50aXR5OiBpdGVtLnF1YW50aXR5LFxuICAgICAgICAgIHByaWNlOiBpdGVtLnByaWNlLFxuICAgICAgICB9KSksXG4gICAgICAgIHJlY2VpcHRVcmwsXG4gICAgICAgIHN0b3JlSWQ6IHNlbGVjdGVkU3RvcmU/LmlkLFxuICAgICAgfTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjcmVhdGVTYWxlKHNhbGVEYXRhKS51bndyYXAoKTtcblxuICAgICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2hvd01lc3NhZ2UoXCJzdWNjZXNzXCIsIFwiU2FsZSBjcmVhdGVkIHN1Y2Nlc3NmdWxseVwiKTtcblxuICAgICAgICAvLyBTdG9yZSB0aGUgcmVjZWlwdCBVUkwgZm9yIHByZXZpZXdcbiAgICAgICAgc2V0UmVjZWlwdFVybChyZWNlaXB0VXJsKTtcblxuICAgICAgICAvLyBTaG93IHJlY2VpcHQgcHJldmlldyBtb2RhbCBhbmQgb2ZmZXIgcHJpbnQgb3B0aW9uXG4gICAgICAgIHNldFJlY2VpcHRQcmV2aWV3VmlzaWJsZSh0cnVlKTtcblxuXG5cbiAgICAgICAgLy8gUmVmcmVzaCBwcm9kdWN0IGRhdGEgdG8gZ2V0IHVwZGF0ZWQgc3RvY2sgcXVhbnRpdGllc1xuICAgICAgICByZWZldGNoUHJvZHVjdHMoKTtcblxuICAgICAgICAvLyBUcmlnZ2VyIHRoZSBzdWNjZXNzIGNhbGxiYWNrIHRvIHJlZnJlc2ggdGhlIGxpc3QgV0lUSE9VVCBjbG9zaW5nIHRoZSBwYW5lbFxuXG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgIGlmIChvblN1Y2Nlc3MpIHtcbiAgICAgICAgICAgIC8vIENhbGwgcmVmZXRjaCBkaXJlY3RseSBpbnN0ZWFkIG9mIGNsb3NpbmcgdGhlIHBhbmVsXG4gICAgICAgICAgICByZWZldGNoUHJvZHVjdHMoKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0sIDMwMCk7XG5cbiAgICAgICAgLy8gS2VlcCB0aGUgcGFuZWwgb3BlbiB1bnRpbCB0aGUgdXNlciBleHBsaWNpdGx5IGNsb3NlcyBpdFxuICAgICAgICAvLyBUaGlzIGVuc3VyZXMgdGhlIHJlY2VpcHQgbW9kYWwgc3RheXMgdmlzaWJsZVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2hvd01lc3NhZ2UoXCJlcnJvclwiLCByZXNwb25zZS5tZXNzYWdlIHx8IFwiRmFpbGVkIHRvIGNyZWF0ZSBzYWxlXCIpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIHNob3dNZXNzYWdlKFxuICAgICAgICBcImVycm9yXCIsXG4gICAgICAgIGVycm9yLmRhdGE/Lm1lc3NhZ2UgfHwgXCJBbiBlcnJvciBvY2N1cnJlZCB3aGlsZSBjcmVhdGluZyB0aGUgc2FsZVwiLFxuICAgICAgKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNHZW5lcmF0aW5nUmVjZWlwdChmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIERlYnVnIGxvZyB0byBjaGVjayBpdGVtcyBzdGF0ZSB3aGVuIHJlbmRlcmluZ1xuICBjb25zb2xlLmxvZyhcIlJlbmRlcmluZyB3aXRoIGl0ZW1zOlwiLCBpdGVtcyk7XG5cbiAgcmV0dXJuIChcbiAgICA8U2xpZGluZ1BhbmVsXG4gICAgICB0aXRsZT1cIlBvaW50IG9mIFNhbGVcIlxuICAgICAgaXNPcGVuPXtpc09wZW59XG4gICAgICBvbkNsb3NlPXtvbkNsb3NlfVxuICAgICAgd2lkdGg9XCI5NSVcIlxuICAgID5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic2FsZXMtZm9ybSBtdC0xMCBtaW4taC1zY3JlZW4gYmctd2hpdGVcIj5cbiAgICAgICAgey8qIE1vZGVybiBQT1MgSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBib3JkZXItYiBib3JkZXItZ3JheS0yMDAgYmctZ3JheS01MCBweC02IHB5LTMgc2hhZG93LW1kXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPFNob3BwaW5nQ2FydE91dGxpbmVkIGNsYXNzTmFtZT1cIm1yLTMgdGV4dC0yeGwgdGV4dC1ibHVlLTUwMFwiIC8+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwibS0wIHRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS04MDBcIj5cbiAgICAgICAgICAgICAgTmV3IFRyYW5zYWN0aW9uXG4gICAgICAgICAgICA8L2gyPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC1ncmF5LTgwMFwiPlxuICAgICAgICAgICAgVG90YWw6e1wiIFwifVxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi02MDBcIj5HSFMge3RvdGFsQW1vdW50LnRvRml4ZWQoMil9PC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBnYXAtNCBsZzpncmlkLWNvbHMtNFwiPlxuICAgICAgICAgICAgey8qIExlZnQgQ29sdW1uIC0gUHJvZHVjdCBTZWxlY3Rpb24gKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTNcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02IHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBiZy13aGl0ZSBwLTQgc2hhZG93LWxnXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwXCI+XG4gICAgICAgICAgICAgICAgICAgIFByb2R1Y3QgU2VsZWN0aW9uXG4gICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMSB0ZXh0LXJlZC01MDBcIj4qPC9zcGFuPiBSZXF1aXJlZCBmaWVsZHNcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIFByb2R1Y3QgU2VsZWN0aW9uIC0gTm8gRm9ybSB3cmFwcGVyIGhlcmUgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGdhcC00IG1kOmdyaWQtY29scy0zXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1kOmNvbC1zcGFuLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cIm1iLTIgYmxvY2sgdGV4dC1ncmF5LTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgUHJvZHVjdCA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDBcIj4qPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdFxuICAgICAgICAgICAgICAgICAgICAgICAgc2hvd1NlYXJjaFxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e1xuICAgICAgICAgICAgICAgICAgICAgICAgICBpc0xvYWRpbmdQcm9kdWN0c1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJMb2FkaW5nIHByb2R1Y3RzLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiU2VhcmNoIHByb2R1Y3RzLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbkZpbHRlclByb3A9XCJjaGlsZHJlblwiXG4gICAgICAgICAgICAgICAgICAgICAgICBsb2FkaW5nPXtpc0xvYWRpbmdQcm9kdWN0c31cbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmdQcm9kdWN0c31cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsodmFsdWUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcHJvZHVjdCA9IHByb2R1Y3RzRGF0YT8uZGF0YT8ucHJvZHVjdHMuZmluZChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAocCkgPT4gcC5pZCA9PT0gdmFsdWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiU2VsZWN0ZWQgcHJvZHVjdDpcIiwgcHJvZHVjdCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChwcm9kdWN0KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRQcm9kdWN0KHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByb2R1Y3QsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcmljZTpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZW9mIHByb2R1Y3QucHJpY2UgPT09IFwic3RyaW5nXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHByb2R1Y3QucHJpY2VcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFN0cmluZyhwcm9kdWN0LnByaWNlKSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZFByb2R1Y3QobnVsbCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICBvblNlYXJjaD17c2V0U2VhcmNoVGVybX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGZpbHRlck9wdGlvbj17ZmFsc2V9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktODAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJsYXJnZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzdWZmaXhJY29uPXtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaXNMb2FkaW5nUHJvZHVjdHMgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExvYWRpbmdPdXRsaW5lZCBzcGluIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlYXJjaE91dGxpbmVkIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIG5vdEZvdW5kQ29udGVudD17XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlzTG9hZGluZ1Byb2R1Y3RzID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTcGluIHNpemU9XCJzbWFsbFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJObyBwcm9kdWN0cyBmb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdHNEYXRhPy5kYXRhPy5wcm9kdWN0cz8ubWFwKChwcm9kdWN0KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3QuT3B0aW9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtwcm9kdWN0LmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtwcm9kdWN0LmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtwcm9kdWN0LnN0b2NrUXVhbnRpdHkgPD0gMH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9kdWN0Lm5hbWV9IC0gR0hTIHtOdW1iZXIocHJvZHVjdC5wcmljZSkudG9GaXhlZCgyKX17XCIgXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb2R1Y3Quc3RvY2tRdWFudGl0eSA8PSAwXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiKE91dCBvZiBTdG9jaylcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBgKFN0b2NrOiAke3Byb2R1Y3Quc3RvY2tRdWFudGl0eX0pYH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3QuT3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJtYi0yIGJsb2NrIHRleHQtZ3JheS04MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIFF0eSA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDBcIj4qPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0TnVtYmVyXG4gICAgICAgICAgICAgICAgICAgICAgICBtaW49ezF9XG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17cXVhbnRpdHl9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiBzZXRRdWFudGl0eSh2YWx1ZSB8fCAxKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiBcIjEwMCVcIiB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTgwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwibGFyZ2VcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB0eXBlPVwicHJpbWFyeVwiXG4gICAgICAgICAgICAgICAgICBpY29uPXs8UGx1c091dGxpbmVkIC8+fVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQWRkSXRlbX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTIgaC0xMiB3LWZ1bGwgYmctYmx1ZS02MDAgdGV4dC1iYXNlIGZvbnQtbWVkaXVtIGhvdmVyOmJnLWJsdWUtNzAwXCJcbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXshc2VsZWN0ZWRQcm9kdWN0fVxuICAgICAgICAgICAgICAgICAgc2l6ZT1cImxhcmdlXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBBZGQgdG8gQ2FydFxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogQ2FydCBJdGVtcyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02IHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBiZy13aGl0ZSBwLTQgc2hhZG93LWxnXCI+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cIm1iLTQgZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDBcIj5cbiAgICAgICAgICAgICAgICAgIDxTaG9wcGluZ0NhcnRPdXRsaW5lZCBjbGFzc05hbWU9XCJtci0yXCIgLz4gQ2FydCBJdGVtc1xuICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtaC1bMzUwcHhdIG92ZXJmbG93LXgtYXV0byBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgIHtpdGVtcy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIGJnLWdyYXktNTAgcC04IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEVtcHR5XG4gICAgICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbj17XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBObyBpdGVtcyBpbiBjYXJ0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGltYWdlPXtFbXB0eS5QUkVTRU5URURfSU1BR0VfU0lNUExFfVxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPHRhYmxlIGNsYXNzTmFtZT1cIm1pbi13LWZ1bGwgb3ZlcmZsb3ctaGlkZGVuIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBiZy13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDx0aGVhZCBjbGFzc05hbWU9XCJzdGlja3kgdG9wLTAgei0xMCBiZy1ncmF5LTUwIHRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0cj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTQgcHktMyB0ZXh0LWxlZnQgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBQcm9kdWN0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ3LTIwIHB4LTQgcHktMyB0ZXh0LWNlbnRlciBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFF0eVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidy0yOCBweC00IHB5LTMgdGV4dC1yaWdodCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFByaWNlXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ3LTI4IHB4LTQgcHktMyB0ZXh0LXJpZ2h0IGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgU3VidG90YWxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInctMTYgcHgtNCBweS0zIHRleHQtY2VudGVyIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQWN0aW9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHRib2R5PlxuICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtgJHtpdGVtLnByb2R1Y3RJZH0tJHtpbmRleH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBob3ZlcjpiZy1ncmF5LTUwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC00IHB5LTMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0ucHJvZHVjdE5hbWUgfHwgXCJVbmtub3duIFByb2R1Y3RcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC00IHB5LTMgdGV4dC1jZW50ZXIgdGV4dC1ncmF5LTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0ucXVhbnRpdHl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNCBweS0zIHRleHQtcmlnaHQgdGV4dC1ncmF5LTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgR0hTIHtOdW1iZXIoaXRlbS5wcmljZSkudG9GaXhlZCgyKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC00IHB5LTMgdGV4dC1yaWdodCBmb250LW1lZGl1bSB0ZXh0LWdyYXktODAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBHSFN7XCIgXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7KE51bWJlcihpdGVtLnByaWNlKSAqIGl0ZW0ucXVhbnRpdHkpLnRvRml4ZWQoMil9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNCBweS0zIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGljb249ezxEZWxldGVPdXRsaW5lZCAvPn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlUmVtb3ZlSXRlbShpbmRleCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGFuZ2VyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCBob3ZlcjpiZy1ncmF5LTEwMCBob3Zlcjp0ZXh0LXJlZC00MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC90Ym9keT5cbiAgICAgICAgICAgICAgICAgICAgICA8dGZvb3QgY2xhc3NOYW1lPVwic3RpY2t5IGJvdHRvbS0wIHotMTAgYmctZ3JheS0xMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0cj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sU3Bhbj17M31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTMgdGV4dC1yaWdodCBmb250LWJvbGQgdGV4dC1ncmF5LTgwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBUb3RhbFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xTcGFuPXsyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMyB0ZXh0LXJpZ2h0IGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTYwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBHSFMge3RvdGFsQW1vdW50LnRvRml4ZWQoMil9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgICAgIDwvdGZvb3Q+XG4gICAgICAgICAgICAgICAgICAgIDwvdGFibGU+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogUmlnaHQgQ29sdW1uIC0gUGF5bWVudCBJbmZvcm1hdGlvbiAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3RpY2t5IHRvcC00IHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBiZy13aGl0ZSBwLTQgc2hhZG93LWxnXCI+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cIm1iLTQgZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDBcIj5cbiAgICAgICAgICAgICAgICAgIDxTaG9wcGluZ0NhcnRPdXRsaW5lZCBjbGFzc05hbWU9XCJtci0yXCIgLz4gQ2hlY2tvdXRcbiAgICAgICAgICAgICAgICA8L2gzPlxuXG4gICAgICAgICAgICAgICAgPEZvcm0gZm9ybT17Zm9ybX0gbGF5b3V0PVwidmVydGljYWxcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAgYmctZ3JheS01MCBwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5JdGVtczo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW1zLmxlbmd0aH1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlRvdGFsIFF1YW50aXR5Ojwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktODAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7aXRlbXMucmVkdWNlKChzdW0sIGl0ZW0pID0+IHN1bSArIGl0ZW0ucXVhbnRpdHksIDApfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gdGV4dC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5TdWJ0b3RhbDo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1ib2xkIHRleHQtZ3JheS04MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEdIUyB7dG90YWxBbW91bnQudG9GaXhlZCgyKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm15LTQgYm9yZGVyLXQgYm9yZGVyLWdyYXktMzAwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHRleHQteGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktODAwXCI+VG90YWw6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LWdyZWVuLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgR0hTIHt0b3RhbEFtb3VudC50b0ZpeGVkKDIpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIFN0b3JlIGluZm9ybWF0aW9uIGlzIG5vdyBhdXRvbWF0aWNhbGx5IGFzc29jaWF0ZWQgd2l0aCB0aGUgYWRtaW4gdXNlciAqL31cbiAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZFN0b3JlID8gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwibWItMiBibG9jayB0ZXh0LWdyYXktNzAwXCI+U3RvcmU8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIGJnLWdyYXktNTAgcC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U2hvcE91dGxpbmVkIGNsYXNzTmFtZT1cIm1yLTIgdGV4dC1ibHVlLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktODAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZFN0b3JlLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImhpZGRlblwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJzdG9yZUlkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkU3RvcmUuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAgYmctZ3JheS01MCBwLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBObyBzdG9yZSBpbmZvcm1hdGlvbiBhdmFpbGFibGUuIFBsZWFzZSBzZXQgdXAgeW91ciBzdG9yZVxuICAgICAgICAgICAgICAgICAgICAgICAgaW4geW91ciBwcm9maWxlIHNldHRpbmdzLlxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJwYXltZW50TWV0aG9kXCJcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9e1xuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIFBheW1lbnQgTWV0aG9kIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPio8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHJ1bGVzPXtbXG4gICAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsXG4gICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiBcIlBsZWFzZSBzZWxlY3QgYSBwYXltZW50IG1ldGhvZFwiLFxuICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIF19XG4gICAgICAgICAgICAgICAgICAgIGluaXRpYWxWYWx1ZT1cImNhc2hcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0IGNsYXNzTmFtZT1cInRleHQtZ3JheS04MDBcIiBzaXplPVwibGFyZ2VcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0Lk9wdGlvbiB2YWx1ZT1cImNhc2hcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMlwiPvCfkrU8L3NwYW4+IENhc2hcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0Lk9wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0Lk9wdGlvbiB2YWx1ZT1cImNhcmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMlwiPvCfkrM8L3NwYW4+IENhcmRcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0Lk9wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0Lk9wdGlvbiB2YWx1ZT1cIm1vYmlsZV9tb25leVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtci0yXCI+8J+TsTwvc3Bhbj4gTW9iaWxlIE1vbmV5XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdC5PcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtOCBzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAge3JlY2VpcHRQcmV2aWV3VmlzaWJsZSA/IChcbiAgICAgICAgICAgICAgICAgICAgICAvLyBTaG93IFwiRG9uZVwiIGJ1dHRvbiB3aGVuIHJlY2VpcHQgaXMgdmlzaWJsZVxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJwcmltYXJ5XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGljb249ezxTaG9wcGluZ0NhcnRPdXRsaW5lZCAvPn1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gQ2xvc2UgdGhlIG1vZGFsIGFuZCByZXNldCB0aGUgcmVjZWlwdCBzdGF0ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRSZWNlaXB0UHJldmlld1Zpc2libGUoZmFsc2UpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRSZWNlaXB0VXJsKG51bGwpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRIYXNQcmludGVkKGZhbHNlKTtcblxuICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBSZXNldCB0aGUgZm9ybSB0byBzdGFydCBhIG5ldyBzYWxlXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGZvcm0ucmVzZXRGaWVsZHMoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0SXRlbXMoW10pO1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZFByb2R1Y3QobnVsbCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNldFF1YW50aXR5KDEpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRUb3RhbEFtb3VudCgwKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LW1kIGZvbnQtc21hbGwgaC0xNCB3LWZ1bGwgYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwibGFyZ2VcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIFN0YXJ0IE5ldyBTYWxlXG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgLy8gU2hvdyBcIkNvbXBsZXRlIFNhbGVcIiBidXR0b24gd2hlbiBjcmVhdGluZyBhIHNhbGVcbiAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwicHJpbWFyeVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBpY29uPXs8U2hvcHBpbmdDYXJ0T3V0bGluZWQgLz59XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTdWJtaXR9XG4gICAgICAgICAgICAgICAgICAgICAgICBsb2FkaW5nPXtpc1N1Ym1pdHRpbmcgfHwgaXNHZW5lcmF0aW5nUmVjZWlwdH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpdGVtcy5sZW5ndGggPT09IDB9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LW1kIGZvbnQtc21hbGwgaC0xNCB3LWZ1bGwgYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwibGFyZ2VcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtpc0dlbmVyYXRpbmdSZWNlaXB0XG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJHZW5lcmF0aW5nIFJlY2VpcHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiQ29tcGxldGUgU2FsZVwifVxuICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtvbkNsb3NlfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtMTIgdy1mdWxsIGJvcmRlci1ncmF5LTMwMCBiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICBzaXplPVwibGFyZ2VcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgQ2FuY2VsXG4gICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9Gb3JtPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogUmVjZWlwdCBQcmV2aWV3IE1vZGFsICovfVxuICAgICAgPE1vZGFsXG4gICAgICAgIHRpdGxlPXtcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHRleHQtZ3JheS04MDBcIj5cbiAgICAgICAgICAgIDxQcmludGVyT3V0bGluZWQgY2xhc3NOYW1lPVwibXItMlwiIC8+XG4gICAgICAgICAgICA8c3Bhbj5SZWNlaXB0IFByZXZpZXc8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIH1cbiAgICAgICAgb3Blbj17cmVjZWlwdFByZXZpZXdWaXNpYmxlfVxuICAgICAgICBvbkNhbmNlbD17KCkgPT4ge1xuICAgICAgICAgIC8vIENsb3NlIHRoZSBtb2RhbCBhbmQgcmVzZXQgdGhlIHJlY2VpcHQgc3RhdGVcbiAgICAgICAgICBzZXRSZWNlaXB0UHJldmlld1Zpc2libGUoZmFsc2UpO1xuICAgICAgICAgIHNldFJlY2VpcHRVcmwobnVsbCk7XG4gICAgICAgICAgc2V0SGFzUHJpbnRlZChmYWxzZSk7XG5cbiAgICAgICAgICAvLyBSZXNldCB0aGUgZm9ybSB0byBzdGFydCBhIG5ldyBzYWxlXG4gICAgICAgICAgZm9ybS5yZXNldEZpZWxkcygpO1xuICAgICAgICAgIHNldEl0ZW1zKFtdKTtcbiAgICAgICAgICBzZXRTZWxlY3RlZFByb2R1Y3QobnVsbCk7XG4gICAgICAgICAgc2V0UXVhbnRpdHkoMSk7XG4gICAgICAgICAgc2V0VG90YWxBbW91bnQoMCk7XG4gICAgICAgIH19XG4gICAgICAgIHdpZHRoPXs1MDB9XG4gICAgICAgIGNlbnRlcmVkXG4gICAgICAgIGNsYXNzTmFtZT1cInJlY2VpcHQtcHJldmlldy1tb2RhbFwiXG4gICAgICAgIGZvb3Rlcj17W1xuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIGtleT1cImNsb3NlXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgLy8gQ2xvc2UgdGhlIG1vZGFsIGFuZCByZXNldCB0aGUgcmVjZWlwdCBzdGF0ZVxuICAgICAgICAgICAgICBzZXRSZWNlaXB0UHJldmlld1Zpc2libGUoZmFsc2UpO1xuICAgICAgICAgICAgICBzZXRSZWNlaXB0VXJsKG51bGwpO1xuICAgICAgICAgICAgICBzZXRIYXNQcmludGVkKGZhbHNlKTtcblxuICAgICAgICAgICAgICAvLyBSZXNldCB0aGUgZm9ybSB0byBzdGFydCBhIG5ldyBzYWxlXG4gICAgICAgICAgICAgIGZvcm0ucmVzZXRGaWVsZHMoKTtcbiAgICAgICAgICAgICAgc2V0SXRlbXMoW10pO1xuICAgICAgICAgICAgICBzZXRTZWxlY3RlZFByb2R1Y3QobnVsbCk7XG4gICAgICAgICAgICAgIHNldFF1YW50aXR5KDEpO1xuICAgICAgICAgICAgICBzZXRUb3RhbEFtb3VudCgwKTtcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItZ3JheS0zMDAgYmctZ3JheS0xMDAgdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTIwMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgQ2xvc2UgJiBOZXcgU2FsZVxuICAgICAgICAgIDwvQnV0dG9uPixcbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICBrZXk9XCJwcmludFwiXG4gICAgICAgICAgICB0eXBlPVwicHJpbWFyeVwiXG4gICAgICAgICAgICBpY29uPXs8UHJpbnRlck91dGxpbmVkIC8+fVxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAvLyBJZiBhbHJlYWR5IHByaW50ZWQgb25jZSwgcmVzZXQgdGhlIGZsYWcgdG8gYWxsb3cgcHJpbnRpbmcgYWdhaW5cbiAgICAgICAgICAgICAgaWYgKGhhc1ByaW50ZWQpIHtcbiAgICAgICAgICAgICAgICBzZXRIYXNQcmludGVkKGZhbHNlKTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICBoYW5kbGVQcmludFJlY2VpcHQoKTtcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2hhc1ByaW50ZWQgPyBcIlByaW50IEFnYWluXCIgOiBcIlByaW50IFJlY2VpcHRcIn1cbiAgICAgICAgICA8L0J1dHRvbj4sXG4gICAgICAgIF19XG4gICAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICB7cmVjZWlwdFVybCA/IChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVjZWlwdC1pbWFnZS1jb250YWluZXJcIj5cbiAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgc3JjPXtyZWNlaXB0VXJsfVxuICAgICAgICAgICAgICAgIGFsdD1cIlJlY2VpcHRcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlY2VpcHQtaW1hZ2VcIlxuICAgICAgICAgICAgICAgIHN0eWxlPXt7IG1heFdpZHRoOiBcIjEwMCVcIiB9fVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLTY0IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8U3BpbiBzaXplPVwibGFyZ2VcIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L01vZGFsPlxuICAgIDwvU2xpZGluZ1BhbmVsPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgU2FsZXNGb3JtUGFuZWw7Il0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsIkZvcm0iLCJCdXR0b24iLCJTZWxlY3QiLCJJbnB1dE51bWJlciIsIkVtcHR5IiwiU3BpbiIsIk1vZGFsIiwiSW1hZ2UiLCJEZWxldGVPdXRsaW5lZCIsIlBsdXNPdXRsaW5lZCIsIlNob3BwaW5nQ2FydE91dGxpbmVkIiwiU2VhcmNoT3V0bGluZWQiLCJMb2FkaW5nT3V0bGluZWQiLCJTaG9wT3V0bGluZWQiLCJQcmludGVyT3V0bGluZWQiLCJ1c2VDcmVhdGVTYWxlTXV0YXRpb24iLCJ1c2VHZXRBbGxQcm9kdWN0c1F1ZXJ5IiwidXNlR2V0VXNlclN0b3Jlc1F1ZXJ5IiwidXNlR2V0VXNlckRlZmF1bHRTdG9yZVF1ZXJ5IiwiU2xpZGluZ1BhbmVsIiwic2hvd01lc3NhZ2UiLCJnZW5lcmF0ZVJlY2VpcHRIVE1MIiwiZ2VuZXJhdGVSZWNlaXB0SW1hZ2UiLCJTYWxlc0Zvcm1QYW5lbCIsImlzT3BlbiIsIm9uQ2xvc2UiLCJvblN1Y2Nlc3MiLCJwcm9kdWN0c0RhdGEiLCJmb3JtIiwidXNlRm9ybSIsIml0ZW1zIiwic2V0SXRlbXMiLCJzZWxlY3RlZFByb2R1Y3QiLCJzZXRTZWxlY3RlZFByb2R1Y3QiLCJxdWFudGl0eSIsInNldFF1YW50aXR5IiwidG90YWxBbW91bnQiLCJzZXRUb3RhbEFtb3VudCIsInNlYXJjaFRlcm0iLCJzZXRTZWFyY2hUZXJtIiwic2VsZWN0ZWRTdG9yZSIsInNldFNlbGVjdGVkU3RvcmUiLCJpc0dlbmVyYXRpbmdSZWNlaXB0Iiwic2V0SXNHZW5lcmF0aW5nUmVjZWlwdCIsInJlY2VpcHRQcmV2aWV3VmlzaWJsZSIsInNldFJlY2VpcHRQcmV2aWV3VmlzaWJsZSIsInJlY2VpcHRVcmwiLCJzZXRSZWNlaXB0VXJsIiwiaGFzUHJpbnRlZCIsInNldEhhc1ByaW50ZWQiLCJjb25zb2xlIiwibG9nIiwiZGF0YSIsImlzTG9hZGluZyIsImlzTG9hZGluZ1Byb2R1Y3RzIiwicmVmZXRjaCIsInJlZmV0Y2hQcm9kdWN0cyIsInBhZ2UiLCJsaW1pdCIsInNlYXJjaCIsInJlZmV0Y2hPbk1vdW50T3JBcmdDaGFuZ2UiLCJyZWZldGNoT25Gb2N1cyIsInJlZmV0Y2hPblJlY29ubmVjdCIsInRvdGFsIiwicHJvZHVjdHNDb3VudCIsInByb2R1Y3RzIiwibGVuZ3RoIiwiZ2V0Q3VycmVudFVzZXJJZCIsInN0YXRlIiwid2luZG93IiwiX19SRURVWF9TVEFURSIsImF1dGgiLCJ1c2VyIiwiaWQiLCJ1c2VyU3RvcmVzRGF0YSIsImRlZmF1bHRTdG9yZURhdGEiLCJzZXRGaWVsZHNWYWx1ZSIsInN0b3JlSWQiLCJjcmVhdGVTYWxlIiwiaXNTdWJtaXR0aW5nIiwicmVkdWNlIiwic3VtIiwiaXRlbSIsInByaWNlIiwicmVzZXRGaWVsZHMiLCJoYW5kbGVBZGRJdGVtIiwic3RvY2tRdWFudGl0eSIsImV4aXN0aW5nSXRlbUluZGV4IiwiZmluZEluZGV4IiwicHJvZHVjdElkIiwidXBkYXRlZEl0ZW1zIiwibmV3UXVhbnRpdHkiLCJwcm9kdWN0TmFtZSIsIm5hbWUiLCJuZXdJdGVtIiwicGFyc2VGbG9hdCIsIm5ld0l0ZW1zIiwidW5kZWZpbmVkIiwiaGFuZGxlUmVtb3ZlSXRlbSIsImluZGV4Iiwic3BsaWNlIiwiaGFuZGxlUHJpbnRSZWNlaXB0IiwiaWZyYW1lIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50Iiwic3R5bGUiLCJkaXNwbGF5IiwiYm9keSIsImFwcGVuZENoaWxkIiwib25sb2FkIiwiY29udGVudFdpbmRvdyIsIndyaXRlIiwiY2xvc2UiLCJzZXRUaW1lb3V0IiwiZm9jdXMiLCJwcmludCIsImUiLCJlcnJvciIsInJlbW92ZUNoaWxkIiwic3JjIiwidGltZXIiLCJjbGVhclRpbWVvdXQiLCJoYW5kbGVTdWJtaXQiLCJ2YWx1ZXMiLCJ2YWxpZGF0ZUZpZWxkcyIsInN0b3JlSW5mbyIsImZpbmQiLCJzdG9yZSIsInJlY2VpcHRIVE1MIiwiRGF0ZSIsIm5vdyIsInBheW1lbnRNZXRob2QiLCJ0cmFuc2FjdGlvbkRhdGUiLCJ0b0lTT1N0cmluZyIsIm1hcCIsInNhbGVEYXRhIiwicmVzcG9uc2UiLCJ1bndyYXAiLCJzdWNjZXNzIiwibWVzc2FnZSIsInRpdGxlIiwid2lkdGgiLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsInNwYW4iLCJ0b0ZpeGVkIiwiaDMiLCJsYWJlbCIsInNob3dTZWFyY2giLCJwbGFjZWhvbGRlciIsIm9wdGlvbkZpbHRlclByb3AiLCJsb2FkaW5nIiwiZGlzYWJsZWQiLCJvbkNoYW5nZSIsInZhbHVlIiwicHJvZHVjdCIsInAiLCJTdHJpbmciLCJvblNlYXJjaCIsImZpbHRlck9wdGlvbiIsInNpemUiLCJzdWZmaXhJY29uIiwic3BpbiIsIm5vdEZvdW5kQ29udGVudCIsIk9wdGlvbiIsIk51bWJlciIsIm1pbiIsInR5cGUiLCJpY29uIiwib25DbGljayIsImRlc2NyaXB0aW9uIiwiaW1hZ2UiLCJQUkVTRU5URURfSU1BR0VfU0lNUExFIiwidGFibGUiLCJ0aGVhZCIsInRyIiwidGgiLCJ0Ym9keSIsInRkIiwiZGFuZ2VyIiwidGZvb3QiLCJjb2xTcGFuIiwibGF5b3V0IiwiaW5wdXQiLCJJdGVtIiwicnVsZXMiLCJyZXF1aXJlZCIsImluaXRpYWxWYWx1ZSIsIm9wZW4iLCJvbkNhbmNlbCIsImNlbnRlcmVkIiwiZm9vdGVyIiwiYWx0IiwibWF4V2lkdGgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx\n"));

/***/ })

});