globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/sales/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/nextjs-toploader/dist/index.js":{"*":{"id":"(ssr)/./node_modules/nextjs-toploader/dist/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/providers.tsx":{"*":{"id":"(ssr)/./src/app/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Layouts/sidebar/sidebar-context.tsx":{"*":{"id":"(ssr)/./src/components/Layouts/sidebar/sidebar-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/provider/Provider.tsx":{"*":{"id":"(ssr)/./src/provider/Provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Dashboard/ProtectedDashboardContent.tsx":{"*":{"id":"(ssr)/./src/components/Dashboard/ProtectedDashboardContent.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/sales/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/sales/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/categories/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/categories/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/reports/sales/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/reports/sales/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/(home)/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/(home)/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/products/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/products/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"E:\\PROJECTS\\pos\\posfrontend\\node_modules\\flatpickr\\dist\\flatpickr.min.css":{"id":"(app-pages-browser)/./node_modules/flatpickr/dist/flatpickr.min.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\node_modules\\nextjs-toploader\\dist\\index.js":{"id":"(app-pages-browser)/./node_modules/nextjs-toploader/dist/index.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\node_modules\\react-hot-toast\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\global-spinner.css":{"id":"(app-pages-browser)/./src/app/global-spinner.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\providers.tsx":{"id":"(app-pages-browser)/./src/app/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\sidebar-context.tsx":{"id":"(app-pages-browser)/./src/components/Layouts/sidebar/sidebar-context.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\src\\css\\satoshi.css":{"id":"(app-pages-browser)/./src/css/satoshi.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\src\\css\\style.css":{"id":"(app-pages-browser)/./src/css/style.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\src\\provider\\Provider.tsx":{"id":"(app-pages-browser)/./src/provider/Provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\fix-sidebar.css":{"id":"(app-pages-browser)/./src/app/dashboard/fix-sidebar.css","name":"*","chunks":["app/dashboard/layout","static/chunks/app/dashboard/layout.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\logo-fix.css":{"id":"(app-pages-browser)/./src/app/dashboard/logo-fix.css","name":"*","chunks":["app/dashboard/layout","static/chunks/app/dashboard/layout.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Dashboard\\ProtectedDashboardContent.tsx":{"id":"(app-pages-browser)/./src/components/Dashboard/ProtectedDashboardContent.tsx","name":"*","chunks":["app/dashboard/layout","static/chunks/app/dashboard/layout.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\sales\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/sales/page.tsx","name":"*","chunks":["app/dashboard/sales/page","static/chunks/app/dashboard/sales/page.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\node_modules\\next\\dist\\lib\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\node_modules\\next\\dist\\esm\\lib\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\categories\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/categories/page.tsx","name":"*","chunks":[],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\reports\\sales\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/reports/sales/page.tsx","name":"*","chunks":[],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/(home)/page.tsx","name":"*","chunks":["app/dashboard/(home)/page","static/chunks/app/dashboard/(home)/page.js"],"async":false},"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\products\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/products/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"E:\\PROJECTS\\pos\\posfrontend\\src\\":[],"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\layout":[{"inlined":false,"path":"static/css/app/dashboard/layout.css"}],"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\(home)\\page":[{"inlined":false,"path":"static/css/app/dashboard/(home)/page.css"}],"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\dashboard\\sales\\page":[{"inlined":false,"path":"static/css/app/dashboard/sales/page.css"}]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/flatpickr/dist/flatpickr.min.css":{"*":{"id":"(rsc)/./node_modules/flatpickr/dist/flatpickr.min.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/nextjs-toploader/dist/index.js":{"*":{"id":"(rsc)/./node_modules/nextjs-toploader/dist/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/global-spinner.css":{"*":{"id":"(rsc)/./src/app/global-spinner.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/providers.tsx":{"*":{"id":"(rsc)/./src/app/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Layouts/sidebar/sidebar-context.tsx":{"*":{"id":"(rsc)/./src/components/Layouts/sidebar/sidebar-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/css/satoshi.css":{"*":{"id":"(rsc)/./src/css/satoshi.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/css/style.css":{"*":{"id":"(rsc)/./src/css/style.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/provider/Provider.tsx":{"*":{"id":"(rsc)/./src/provider/Provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/fix-sidebar.css":{"*":{"id":"(rsc)/./src/app/dashboard/fix-sidebar.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/logo-fix.css":{"*":{"id":"(rsc)/./src/app/dashboard/logo-fix.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Dashboard/ProtectedDashboardContent.tsx":{"*":{"id":"(rsc)/./src/components/Dashboard/ProtectedDashboardContent.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/sales/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/sales/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/categories/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/categories/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/reports/sales/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/reports/sales/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/(home)/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/(home)/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/products/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/products/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}