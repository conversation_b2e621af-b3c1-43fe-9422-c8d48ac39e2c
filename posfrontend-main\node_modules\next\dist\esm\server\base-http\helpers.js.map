{"version": 3, "sources": ["../../../src/server/base-http/helpers.ts"], "sourcesContent": ["import type { BaseNextRequest, BaseNextResponse } from './'\nimport type { NodeNextRequest, NodeNextResponse } from './node'\nimport type { WebNextRequest, WebNextResponse } from './web'\n\n/**\n * This file provides some helpers that should be used in conjunction with\n * explicit environment checks. When combined with the environment checks, it\n * will ensure that the correct typings are used as well as enable code\n * elimination.\n */\n\n/**\n * Type guard to determine if a request is a WebNextRequest. This does not\n * actually check the type of the request, but rather the runtime environment.\n * It's expected that when the runtime environment is the edge runtime, that any\n * base request is a WebNextRequest.\n */\nexport const isWebNextRequest = (req: BaseNextRequest): req is WebNextRequest =>\n  process.env.NEXT_RUNTIME === 'edge'\n\n/**\n * Type guard to determine if a response is a WebNextResponse. This does not\n * actually check the type of the response, but rather the runtime environment.\n * It's expected that when the runtime environment is the edge runtime, that any\n * base response is a WebNextResponse.\n */\nexport const isWebNextResponse = (\n  res: BaseNextResponse\n): res is WebNextResponse => process.env.NEXT_RUNTIME === 'edge'\n\n/**\n * Type guard to determine if a request is a NodeNextRequest. This does not\n * actually check the type of the request, but rather the runtime environment.\n * It's expected that when the runtime environment is the node runtime, that any\n * base request is a NodeNextRequest.\n */\nexport const isNodeNextRequest = (\n  req: BaseNextRequest\n): req is NodeNextRequest => process.env.NEXT_RUNTIME !== 'edge'\n\n/**\n * Type guard to determine if a response is a NodeNextResponse. This does not\n * actually check the type of the response, but rather the runtime environment.\n * It's expected that when the runtime environment is the node runtime, that any\n * base response is a NodeNextResponse.\n */\nexport const isNodeNextResponse = (\n  res: BaseNextResponse\n): res is NodeNextResponse => process.env.NEXT_RUNTIME !== 'edge'\n"], "names": ["isWebNextRequest", "req", "process", "env", "NEXT_RUNTIME", "isWebNextResponse", "res", "isNodeNextRequest", "isNodeNextResponse"], "mappings": "AAIA;;;;;CAKC,GAED;;;;;CAKC,GACD,OAAO,MAAMA,mBAAmB,CAACC,MAC/BC,QAAQC,GAAG,CAACC,YAAY,KAAK,OAAM;AAErC;;;;;CAKC,GACD,OAAO,MAAMC,oBAAoB,CAC/BC,MAC2BJ,QAAQC,GAAG,CAACC,YAAY,KAAK,OAAM;AAEhE;;;;;CAKC,GACD,OAAO,MAAMG,oBAAoB,CAC/BN,MAC2BC,QAAQC,GAAG,CAACC,YAAY,KAAK,OAAM;AAEhE;;;;;CAKC,GACD,OAAO,MAAMI,qBAAqB,CAChCF,MAC4BJ,QAAQC,GAAG,CAACC,YAAY,KAAK,OAAM"}