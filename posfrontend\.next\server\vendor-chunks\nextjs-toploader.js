/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/nextjs-toploader";
exports.ids = ["vendor-chunks/nextjs-toploader"];
exports.modules = {

/***/ "(ssr)/./node_modules/nextjs-toploader/dist/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/nextjs-toploader/dist/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar D = Object.create;\nvar y = Object.defineProperty;\nvar G = Object.getOwnPropertyDescriptor;\nvar Q = Object.getOwnPropertyNames;\nvar V = Object.getPrototypeOf, Y = Object.prototype.hasOwnProperty;\nvar i = (o, e)=>y(o, \"name\", {\n        value: e,\n        configurable: !0\n    });\nvar Z = (o, e)=>{\n    for(var p in e)y(o, p, {\n        get: e[p],\n        enumerable: !0\n    });\n}, C = (o, e, p, h)=>{\n    if (e && typeof e == \"object\" || typeof e == \"function\") for (let l of Q(e))!Y.call(o, l) && l !== p && y(o, l, {\n        get: ()=>e[l],\n        enumerable: !(h = G(e, l)) || h.enumerable\n    });\n    return o;\n};\nvar T = (o, e, p)=>(p = o != null ? D(V(o)) : {}, C(e || !o || !o.__esModule ? y(p, \"default\", {\n        value: o,\n        enumerable: !0\n    }) : p, o)), _ = (o)=>C(y({}, \"__esModule\", {\n        value: !0\n    }), o);\nvar re = {};\nZ(re, {\n    default: ()=>ee,\n    useTopLoader: ()=>O\n});\nmodule.exports = _(re);\nvar t = T(__webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\")), L = T(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\")), a = T(__webpack_require__(/*! nprogress */ \"(ssr)/./node_modules/nprogress/nprogress.js\"));\nvar s = T(__webpack_require__(/*! nprogress */ \"(ssr)/./node_modules/nprogress/nprogress.js\"));\nvar O = i(()=>({\n        start: ()=>s.start(),\n        done: (e)=>s.done(e),\n        remove: ()=>s.remove(),\n        setProgress: (e)=>s.set(e),\n        inc: (e)=>s.inc(e),\n        trickle: ()=>s.trickle(),\n        isStarted: ()=>s.isStarted(),\n        isRendered: ()=>s.isRendered(),\n        getPositioningCSS: ()=>s.getPositioningCSS()\n    }), \"useTopLoader\");\nvar z = i(({ color: o, height: e, showSpinner: p, crawl: h, crawlSpeed: l, initialPosition: v, easing: S, speed: k, shadow: N, template: E, zIndex: A = 1600, showAtBottom: H = !1, showForHashAnchor: K = !0 })=>{\n    let W = \"#29d\", u = o != null ? o : W, j = e != null ? e : 3, B = !N && N !== void 0 ? \"\" : N ? `box-shadow:${N}` : `box-shadow:0 0 10px ${u},0 0 5px ${u}`, F = L.createElement(\"style\", null, `#nprogress{pointer-events:none}#nprogress .bar{background:${u};position:fixed;z-index:${A};${H ? \"bottom: 0;\" : \"top: 0;\"}left:0;width:100%;height:${j}px}#nprogress .peg{display:block;position:absolute;right:0;width:100px;height:100%;${B};opacity:1;-webkit-transform:rotate(3deg) translate(0px,-4px);-ms-transform:rotate(3deg) translate(0px,-4px);transform:rotate(3deg) translate(0px,-4px)}#nprogress .spinner{display:block;position:fixed;z-index:${A};${H ? \"bottom: 15px;\" : \"top: 15px;\"}right:15px}#nprogress .spinner-icon{width:18px;height:18px;box-sizing:border-box;border:2px solid transparent;border-top-color:${u};border-left-color:${u};border-radius:50%;-webkit-animation:nprogress-spinner 400ms linear infinite;animation:nprogress-spinner 400ms linear infinite}.nprogress-custom-parent{overflow:hidden;position:relative}.nprogress-custom-parent #nprogress .bar,.nprogress-custom-parent #nprogress .spinner{position:absolute}@-webkit-keyframes nprogress-spinner{0%{-webkit-transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg)}}@keyframes nprogress-spinner{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}`), f = i((m)=>new URL(m, window.location.href).href, \"toAbsoluteURL\"), q = i((m, b)=>{\n        let d = new URL(f(m)), P = new URL(f(b));\n        return d.href.split(\"#\")[0] === P.href.split(\"#\")[0];\n    }, \"isHashAnchor\"), I = i((m, b)=>{\n        let d = new URL(f(m)), P = new URL(f(b));\n        return d.hostname.replace(/^www\\./, \"\") === P.hostname.replace(/^www\\./, \"\");\n    }, \"isSameHostName\");\n    return L.useEffect({\n        \"z.useEffect\": ()=>{\n            a.configure({\n                showSpinner: p != null ? p : !0,\n                trickle: h != null ? h : !0,\n                trickleSpeed: l != null ? l : 200,\n                minimum: v != null ? v : .08,\n                easing: S != null ? S : \"ease\",\n                speed: k != null ? k : 200,\n                template: E != null ? E : '<div class=\"bar\" role=\"bar\"><div class=\"peg\"></div></div><div class=\"spinner\" role=\"spinner\"><div class=\"spinner-icon\"></div></div>'\n            });\n            function m(r, g) {\n                let n = new URL(r), c = new URL(g);\n                if (n.hostname === c.hostname && n.pathname === c.pathname && n.search === c.search) {\n                    let w = n.hash, x = c.hash;\n                    return w !== x && n.href.replace(w, \"\") === c.href.replace(x, \"\");\n                }\n                return !1;\n            }\n            i(m, \"isAnchorOfCurrentUrl\");\n            var b = document.querySelectorAll(\"html\");\n            let d = i({\n                \"z.useEffect.d\": ()=>b.forEach({\n                        \"z.useEffect.d\": (r)=>r.classList.remove(\"nprogress-busy\")\n                    }[\"z.useEffect.d\"])\n            }[\"z.useEffect.d\"], \"removeNProgressClass\");\n            function P(r) {\n                for(; r && r.tagName.toLowerCase() !== \"a\";)r = r.parentElement;\n                return r;\n            }\n            i(P, \"findClosestAnchor\");\n            function R(r) {\n                try {\n                    let g = r.target, n = P(g), c = n == null ? void 0 : n.href;\n                    if (c) {\n                        let w = window.location.href, x = n.target !== \"\", J = [\n                            \"tel:\",\n                            \"mailto:\",\n                            \"sms:\",\n                            \"blob:\",\n                            \"download:\"\n                        ].some({\n                            \"z.useEffect.R.J\": (X)=>c.startsWith(X)\n                        }[\"z.useEffect.R.J\"]);\n                        if (!I(window.location.href, n.href)) return;\n                        let M = m(w, c) || q(window.location.href, n.href);\n                        if (!K && M) return;\n                        c === w || x || J || M || r.ctrlKey || r.metaKey || r.shiftKey || r.altKey || !f(n.href).startsWith(\"http\") ? (a.start(), a.done(), d()) : a.start();\n                    }\n                } catch (g) {\n                    a.start(), a.done();\n                }\n            }\n            i(R, \"handleClick\"), ({\n                \"z.useEffect\": (r)=>{\n                    let g = r.pushState;\n                    r.pushState = ({\n                        \"z.useEffect\": (...n)=>(a.done(), d(), g.apply(r, n))\n                    })[\"z.useEffect\"];\n                }\n            })[\"z.useEffect\"](window.history), ({\n                \"z.useEffect\": (r)=>{\n                    let g = r.replaceState;\n                    r.replaceState = ({\n                        \"z.useEffect\": (...n)=>(a.done(), d(), g.apply(r, n))\n                    })[\"z.useEffect\"];\n                }\n            })[\"z.useEffect\"](window.history);\n            function U() {\n                a.done(), d();\n            }\n            i(U, \"handlePageHide\");\n            function $() {\n                a.done();\n            }\n            return i($, \"handleBackAndForth\"), window.addEventListener(\"popstate\", $), document.addEventListener(\"click\", R), window.addEventListener(\"pagehide\", U), ({\n                \"z.useEffect\": ()=>{\n                    document.removeEventListener(\"click\", R), window.removeEventListener(\"pagehide\", U), window.removeEventListener(\"popstate\", $);\n                }\n            })[\"z.useEffect\"];\n        }\n    }[\"z.useEffect\"], []), F;\n}, \"NextTopLoader\"), ee = z;\nz.propTypes = {\n    color: t.string,\n    height: t.number,\n    showSpinner: t.bool,\n    crawl: t.bool,\n    crawlSpeed: t.number,\n    initialPosition: t.number,\n    easing: t.string,\n    speed: t.number,\n    template: t.string,\n    shadow: t.oneOfType([\n        t.string,\n        t.bool\n    ]),\n    zIndex: t.number,\n    showAtBottom: t.bool\n};\n0 && (0); /**\n *\n * NextTopLoader\n * @license MIT\n * @param {NextTopLoaderProps} props The properties to configure NextTopLoader\n * @returns {React.JSX.Element}\n *\n */  //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/nextjs-toploader/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/nextjs-toploader/dist/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/nextjs-toploader/dist/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const { createProxy } = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js")

module.exports = createProxy("E:\\PROJECTS\\pos\\posfrontend\\node_modules\\nextjs-toploader\\dist\\index.js")


/***/ })

};
;