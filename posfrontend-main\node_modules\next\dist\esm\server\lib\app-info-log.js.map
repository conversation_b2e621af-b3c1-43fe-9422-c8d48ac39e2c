{"version": 3, "sources": ["../../../src/server/lib/app-info-log.ts"], "sourcesContent": ["import { loadEnvConfig } from '@next/env'\nimport * as Log from '../../build/output/log'\nimport { bold, purple } from '../../lib/picocolors'\nimport {\n  PHASE_DEVELOPMENT_SERVER,\n  PHASE_PRODUCTION_BUILD,\n} from '../../shared/lib/constants'\nimport loadConfig, { getEnabledExperimentalFeatures } from '../config'\n\nexport function logStartInfo({\n  networkUrl,\n  appUrl,\n  envInfo,\n  expFeatureInfo,\n  maxExperimentalFeatures = Infinity,\n}: {\n  networkUrl: string | null\n  appUrl: string | null\n  envInfo?: string[]\n  expFeatureInfo?: string[]\n  maxExperimentalFeatures?: number\n}) {\n  Log.bootstrap(\n    `${bold(\n      purple(`${Log.prefixes.ready} Next.js ${process.env.__NEXT_VERSION}`)\n    )}${process.env.TURBOPACK ? ' (Turbopack)' : ''}`\n  )\n  if (appUrl) {\n    Log.bootstrap(`- Local:        ${appUrl}`)\n  }\n  if (networkUrl) {\n    Log.bootstrap(`- Network:      ${networkUrl}`)\n  }\n  if (envInfo?.length) Log.bootstrap(`- Environments: ${envInfo.join(', ')}`)\n\n  if (expFeatureInfo?.length) {\n    Log.bootstrap(`- Experiments (use with caution):`)\n    // only show a maximum number of flags\n    for (const exp of expFeatureInfo.slice(0, maxExperimentalFeatures)) {\n      Log.bootstrap(`  · ${exp}`)\n    }\n    /* indicate if there are more than the maximum shown no. flags */\n    if (expFeatureInfo.length > maxExperimentalFeatures) {\n      Log.bootstrap(`  · ...`)\n    }\n  }\n\n  // New line after the bootstrap info\n  Log.info('')\n}\n\nexport async function getStartServerInfo(\n  dir: string,\n  dev: boolean\n): Promise<{\n  envInfo?: string[]\n  expFeatureInfo?: string[]\n}> {\n  let expFeatureInfo: string[] = []\n  await loadConfig(\n    dev ? PHASE_DEVELOPMENT_SERVER : PHASE_PRODUCTION_BUILD,\n    dir,\n    {\n      onLoadUserConfig(userConfig) {\n        const userNextConfigExperimental = getEnabledExperimentalFeatures(\n          userConfig.experimental\n        )\n        expFeatureInfo = userNextConfigExperimental.sort(\n          (a, b) => a.length - b.length\n        )\n      },\n    }\n  )\n\n  // we need to reset env if we are going to create\n  // the worker process with the esm loader so that the\n  // initial env state is correct\n  let envInfo: string[] = []\n  const { loadedEnvFiles } = loadEnvConfig(dir, true, console, false)\n  if (loadedEnvFiles.length > 0) {\n    envInfo = loadedEnvFiles.map((f) => f.path)\n  }\n\n  return {\n    envInfo,\n    expFeatureInfo,\n  }\n}\n"], "names": ["loadEnvConfig", "Log", "bold", "purple", "PHASE_DEVELOPMENT_SERVER", "PHASE_PRODUCTION_BUILD", "loadConfig", "getEnabledExperimentalFeatures", "logStartInfo", "networkUrl", "appUrl", "envInfo", "expFeatureInfo", "maxExperimentalFeatures", "Infinity", "bootstrap", "prefixes", "ready", "process", "env", "__NEXT_VERSION", "TURBOPACK", "length", "join", "exp", "slice", "info", "getStartServerInfo", "dir", "dev", "onLoadUserConfig", "userConfig", "userNextConfigExperimental", "experimental", "sort", "a", "b", "loadedEnvFiles", "console", "map", "f", "path"], "mappings": "AAAA,SAASA,aAAa,QAAQ,YAAW;AACzC,YAAYC,SAAS,yBAAwB;AAC7C,SAASC,IAAI,EAAEC,MAAM,QAAQ,uBAAsB;AACnD,SACEC,wBAAwB,EACxBC,sBAAsB,QACjB,6BAA4B;AACnC,OAAOC,cAAcC,8BAA8B,QAAQ,YAAW;AAEtE,OAAO,SAASC,aAAa,EAC3BC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,cAAc,EACdC,0BAA0BC,QAAQ,EAOnC;IACCb,IAAIc,SAAS,CACX,GAAGb,KACDC,OAAO,GAAGF,IAAIe,QAAQ,CAACC,KAAK,CAAC,SAAS,EAAEC,QAAQC,GAAG,CAACC,cAAc,EAAE,KAClEF,QAAQC,GAAG,CAACE,SAAS,GAAG,iBAAiB,IAAI;IAEnD,IAAIX,QAAQ;QACVT,IAAIc,SAAS,CAAC,CAAC,gBAAgB,EAAEL,QAAQ;IAC3C;IACA,IAAID,YAAY;QACdR,IAAIc,SAAS,CAAC,CAAC,gBAAgB,EAAEN,YAAY;IAC/C;IACA,IAAIE,2BAAAA,QAASW,MAAM,EAAErB,IAAIc,SAAS,CAAC,CAAC,gBAAgB,EAAEJ,QAAQY,IAAI,CAAC,OAAO;IAE1E,IAAIX,kCAAAA,eAAgBU,MAAM,EAAE;QAC1BrB,IAAIc,SAAS,CAAC,CAAC,iCAAiC,CAAC;QACjD,sCAAsC;QACtC,KAAK,MAAMS,OAAOZ,eAAea,KAAK,CAAC,GAAGZ,yBAA0B;YAClEZ,IAAIc,SAAS,CAAC,CAAC,IAAI,EAAES,KAAK;QAC5B;QACA,+DAA+D,GAC/D,IAAIZ,eAAeU,MAAM,GAAGT,yBAAyB;YACnDZ,IAAIc,SAAS,CAAC,CAAC,OAAO,CAAC;QACzB;IACF;IAEA,oCAAoC;IACpCd,IAAIyB,IAAI,CAAC;AACX;AAEA,OAAO,eAAeC,mBACpBC,GAAW,EACXC,GAAY;IAKZ,IAAIjB,iBAA2B,EAAE;IACjC,MAAMN,WACJuB,MAAMzB,2BAA2BC,wBACjCuB,KACA;QACEE,kBAAiBC,UAAU;YACzB,MAAMC,6BAA6BzB,+BACjCwB,WAAWE,YAAY;YAEzBrB,iBAAiBoB,2BAA2BE,IAAI,CAC9C,CAACC,GAAGC,IAAMD,EAAEb,MAAM,GAAGc,EAAEd,MAAM;QAEjC;IACF;IAGF,iDAAiD;IACjD,qDAAqD;IACrD,+BAA+B;IAC/B,IAAIX,UAAoB,EAAE;IAC1B,MAAM,EAAE0B,cAAc,EAAE,GAAGrC,cAAc4B,KAAK,MAAMU,SAAS;IAC7D,IAAID,eAAef,MAAM,GAAG,GAAG;QAC7BX,UAAU0B,eAAeE,GAAG,CAAC,CAACC,IAAMA,EAAEC,IAAI;IAC5C;IAEA,OAAO;QACL9B;QACAC;IACF;AACF"}