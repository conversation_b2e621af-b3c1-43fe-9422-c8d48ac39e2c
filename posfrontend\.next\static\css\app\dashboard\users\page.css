/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/dashboard/users/users-tailwind.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
/* Custom styles for the users table */

/* Make individual cells scrollable */
.overflow-hidden .max-w-\[120px\],
.overflow-hidden .max-w-\[140px\],
.overflow-hidden .max-w-\[150px\],
.overflow-hidden .max-w-\[200px\],
.overflow-hidden .max-w-\[100px\],
.overflow-hidden .max-w-\[130px\] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s ease;
}

/* Show scrollbar on hover */
.overflow-hidden .max-w-\[120px\]:hover,
.overflow-hidden .max-w-\[140px\]:hover,
.overflow-hidden .max-w-\[150px\]:hover,
.overflow-hidden .max-w-\[200px\]:hover,
.overflow-hidden .max-w-\[100px\]:hover,
.overflow-hidden .max-w-\[130px\]:hover {
  overflow: auto !important;
  text-overflow: clip;
  z-index: 10;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  cursor: text;
}

/* Style for scrollbar */
.overflow-hidden div::-webkit-scrollbar {
  height: 4px;
  width: 4px;
}

.overflow-hidden div::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 4px;
}

.overflow-hidden div::-webkit-scrollbar-track {
  background-color: #f5f5f5;
}

/* Fix for sticky columns */
.sticky.left-0 {
  position: sticky;
  left: 0;
  z-index: 10;
}

.sticky.right-0 {
  position: sticky;
  right: 0;
  z-index: 10;
}

/* Fix for hover state */
tr.hover\:bg-\[#1a2234\]:hover td {
  background-color: #f5f5f5;
}

/* Fix for sticky columns on hover */
tr.hover\:bg-\[#1a2234\]:hover td.sticky {
  background-color: #f5f5f5 !important;
}

/* Mobile specific styles */
@media (max-width: 640px) {
  /* Ensure the table doesn't affect the layout */
  .overflow-hidden {
    -webkit-overflow-scrolling: touch;
  }

  /* Smaller padding for mobile */
  .px-3.py-4 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  /* Ensure sticky columns work on mobile */
  .sticky.left-0,
  .sticky.right-0 {
    background-color: #ffffff;
  }

  /* Fix for hover state on mobile */
  tr.hover\:bg-\[#1a2234\]:hover td.sticky {
    background-color: #f5f5f5 !important;
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/dashboard/users/users.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/* Custom styles for the users table */
.user-table .ant-table-container {
  overflow-x: auto;
  background-color: #ffffff;
}

.user-table .ant-table-body {
  overflow-x: auto !important;
}

/* Ensure all table cells have light background */
.user-table .ant-table {
  background-color: #ffffff;
}

.user-table .ant-table-thead > tr > th,
.user-table .ant-table-tbody > tr > td {
  background-color: #ffffff;
  color: #333333;
}

/* Ensure fixed columns work properly */
.user-table .ant-table-cell.ant-table-cell-fix-left,
.user-table .ant-table-cell.ant-table-cell-fix-right {
  z-index: 2;
  background-color: #ffffff !important;
}

/* Fix for sticky header */
.user-table .ant-table-header {
  background-color: #f5f5f5;
}

/* Fix for sticky columns */
.user-table .ant-table-cell-fix-left-last::after,
.user-table .ant-table-cell-fix-right-first::after {
  background-color: #ffffff !important;
}

/* Improve tag display on mobile */
@media (max-width: 640px) {
  .user-table .ant-tag {
    margin-right: 0;
    padding: 0 4px;
    font-size: 11px;
  }

  .user-table .ant-table-thead > tr > th,
  .user-table .ant-table-tbody > tr > td {
    padding: 8px 4px;
    background-color: #ffffff !important;
    color: #333333;
  }

  .user-table .ant-table-filter-trigger {
    margin-right: -4px;
  }

  /* Ensure action buttons are properly spaced */
  .user-table .ant-space-item {
    margin-right: 0 !important;
  }

  .user-table .ant-btn {
    padding: 0 4px;
  }

  /* Additional fixes for mobile */
  .user-table .ant-table-container::before,
  .user-table .ant-table-container::after {
    background-color: #ffffff !important;
  }

  .user-table .ant-table-cell-scrollbar {
    box-shadow: none;
    background-color: #ffffff !important;
  }

  /* Fix for row hover effect */
  .user-table .ant-table-tbody > tr.ant-table-row:hover > td {
    background-color: #f5f5f5 !important;
  }
}

/* Ensure the table doesn't affect the layout */
.overflow-x-auto {
  -webkit-overflow-scrolling: touch;
  background-color: #ffffff;
}

/* Fix for any remaining transparent areas */
.user-table .ant-table-wrapper,
.user-table .ant-spin-nested-loading,
.user-table .ant-spin-container,
.user-table .ant-table,
.user-table .ant-table-content,
.user-table .ant-table-scroll,
.user-table .ant-table-ping-left:not(.ant-table-has-fix-left) .ant-table-cell-fix-left-first,
.user-table .ant-table-ping-right:not(.ant-table-has-fix-right) .ant-table-cell-fix-right-last {
  background-color: #ffffff !important;
}

/* Fix for striped rows if enabled */
.user-table .ant-table-tbody > tr.ant-table-row:nth-child(odd) > td {
  background-color: #ffffff !important;
}

.user-table .ant-table-tbody > tr.ant-table-row:nth-child(even) > td {
  background-color: #f9f9f9 !important;
}

/* Fix for sticky header and scrollbar */
.user-table .ant-table-sticky-scroll {
  background-color: #ffffff !important;
}

.user-table .ant-table-sticky-scroll-bar {
  background-color: #e6e6e6 !important;
}

/* Fix for any shadow effects */
.user-table .ant-table-cell-fix-left-last::after,
.user-table .ant-table-cell-fix-right-first::after {
  box-shadow: none !important;
  background-color: #ffffff !important;
}

/* Fix for any remaining transparent areas */
.user-table .ant-table-container::before,
.user-table .ant-table-container::after,
.user-table .ant-table-header::before,
.user-table .ant-table-header::after,
.user-table .ant-table-body::before,
.user-table .ant-table-body::after {
  display: none !important;
  background-color: #ffffff !important;
}

/* Fix for tag backgrounds */
.ant-tag {
  background-color: #f0f0f0 !important;
  border-color: #d9d9d9 !important;
}

/* Custom tag colors */
.ant-tag-purple {
  color: #722ed1 !important;
  border-color: #d3adf7 !important;
  background-color: #f9f0ff !important;
}

.ant-tag-blue {
  color: #1890ff !important;
  border-color: #91d5ff !important;
  background-color: #e6f7ff !important;
}

.ant-tag-green {
  color: #52c41a !important;
  border-color: #b7eb8f !important;
  background-color: #f6ffed !important;
}

.ant-tag-success {
  color: #52c41a !important;
  border-color: #b7eb8f !important;
  background-color: #f6ffed !important;
}

.ant-tag-warning {
  color: #faad14 !important;
  border-color: #ffe58f !important;
  background-color: #fffbe6 !important;
}

.ant-tag-error {
  color: #f5222d !important;
  border-color: #ffa39e !important;
  background-color: #fff1f0 !important;
}

.ant-tag-default {
  color: #595959 !important;
  border-color: #d9d9d9 !important;
  background-color: #f5f5f5 !important;
}

/* Custom styles for action buttons */
.view-button .anticon-eye {
  color: #3b82f6 !important; /* Blue color for view icon */
  font-size: 18px !important;
}

.edit-button .anticon-edit {
  color: #10b981 !important; /* Green color for edit icon */
  font-size: 18px !important;
}

.delete-button .anticon-delete {
  color: #ef4444 !important; /* Red color for delete icon */
  font-size: 18px !important;
}

/* Hover effects for action buttons */
.view-button:hover {
  background-color: rgba(59, 130, 246, 0.2) !important; /* Blue hover */
}

.edit-button:hover {
  background-color: rgba(16, 185, 129, 0.2) !important; /* Green hover */
}

.delete-button:hover {
  background-color: rgba(239, 68, 68, 0.2) !important; /* Red hover */
}

/* Button background */
.view-button, .edit-button, .delete-button {
  background-color: #f5f5f5 !important;
  border: none !important;
}

/* Pagination styles */
.bg-white {
  background-color: #ffffff !important;
}

.border-gray-200, .border-gray-300 {
  border-color: #e8e8e8 !important;
}

.text-gray-500, .text-gray-700 {
  color: #595959 !important;
}

.bg-blue-50 {
  background-color: #e6f7ff !important;
}

.border-blue-500 {
  border-color: #1890ff !important;
}

.text-blue-600 {
  color: #1890ff !important;
}

.hover\:bg-gray-50:hover {
  background-color: #f5f5f5 !important;
}

/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/components/Users/<USER>
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/* Custom styles for user panels */

/* Light theme for descriptions */
.user-detail-dark {
  color: #333;
  background-color: #ffffff;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

.user-detail-dark .ant-descriptions-item-label {
  background-color: #f5f5f5 !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
  word-break: break-word;
}

.user-detail-dark .ant-descriptions-item-content {
  background-color: #ffffff !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
  word-break: break-word;
}

/* Light theme for form items */
.ant-form-item-label > label {
  color: rgba(0, 0, 0, 0.85) !important;
}

/* Light theme for select */
.ant-select-selector {
  background-color: #ffffff !important;
  color: rgba(0, 0, 0, 0.85) !important;
  border-color: #d9d9d9 !important;
}

.ant-select-selection-item {
  color: rgba(0, 0, 0, 0.85) !important;
}

.ant-select-arrow {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Light theme for input */
.ant-input, .ant-input-password {
  background-color: #ffffff !important;
  color: rgba(0, 0, 0, 0.85) !important;
  border-color: #d9d9d9 !important;
}

.ant-input-password-icon {
  color: rgba(0, 0, 0, 0.45) !important;
}

/* Dark theme for tags */
.ant-tag {
  border-color: transparent !important;
}

/* Fix for loading spinner */
.ant-spin-text,
.ant-spin-dot + div,
.ant-spin + div {
  display: none !important;
}

/* Center all spinners */
.ant-spin-container {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  min-height: 200px !important;
}

/* Fix for z-index */
.ant-form-item-explain-error {
  color: #ff4d4f !important;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  .ant-form-item {
    margin-bottom: 12px !important;
  }

  .ant-form-item-label {
    padding-bottom: 4px !important;
  }

  .ant-input, .ant-input-password, .ant-select {
    font-size: 16px !important; /* Prevents iOS zoom on focus */
  }

  .ant-descriptions-item-label,
  .ant-descriptions-item-content {
    padding: 8px !important;
    font-size: 14px !important;
  }

  /* Ensure form inputs don't overflow */
  .ant-form-item-control-input {
    width: 100% !important;
    max-width: 100% !important;
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/search.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/* Custom styles for search inputs */

/* Light background for input and its wrapper */
.ant-input-affix-wrapper {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
}

/* Ensure hover and focus states maintain light background */
.ant-input-affix-wrapper:hover,
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
  background-color: #ffffff !important;
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* Style for the input inside the wrapper */
.ant-input-affix-wrapper .ant-input {
  background-color: transparent !important;
  color: rgba(0, 0, 0, 0.85) !important;
}

/* Style for the clear icon */
.ant-input-affix-wrapper .ant-input-clear-icon {
  color: rgba(0, 0, 0, 0.25) !important;
}

.ant-input-affix-wrapper .ant-input-clear-icon:hover {
  color: rgba(0, 0, 0, 0.45) !important;
}

/* Style for the placeholder */
.ant-input::-moz-placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}
.ant-input::placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Style for the search icon */
.ant-input-prefix .anticon {
  color: rgba(0, 0, 0, 0.25) !important;
}

