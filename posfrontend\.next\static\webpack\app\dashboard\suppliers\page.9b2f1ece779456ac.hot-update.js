"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/suppliers/page",{

/***/ "(app-pages-browser)/./src/components/ui/ResponsiveTable.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/ResponsiveTable.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HybridTable: () => (/* binding */ HybridTable),\n/* harmony export */   ResponsiveTable: () => (/* binding */ ResponsiveTable),\n/* harmony export */   ResponsiveTableGrid: () => (/* binding */ ResponsiveTableGrid),\n/* harmony export */   TableCell: () => (/* binding */ TableCell),\n/* harmony export */   TableHeader: () => (/* binding */ TableHeader),\n/* harmony export */   TableRow: () => (/* binding */ TableRow),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ResponsiveTable,ResponsiveTableGrid,TableHeader,TableCell,TableRow,HybridTable,default auto */ \n\n\n/**\n * Responsive table wrapper that provides horizontal scrolling\n * Uses CSS Grid for better responsiveness\n */ const ResponsiveTable = (param)=>{\n    let { children, className, minWidth = \"800px\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-full overflow-x-auto overflow-y-visible\", \"border border-gray-200 rounded-lg shadow-sm\", \"bg-white\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-w-full\",\n            style: {\n                minWidth\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ResponsiveTable;\n/**\n * CSS Grid-based responsive table for better control\n */ const ResponsiveTableGrid = (param)=>{\n    let { children, columns, className, minWidth = \"800px\" } = param;\n    const isMobile =  true && window.innerWidth < 768;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-full overflow-x-auto overflow-y-visible\", \"border border-gray-200 rounded-lg shadow-sm\", \"bg-white\", // Ensure smooth scrolling on mobile\n        \"scroll-smooth\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"gap-0\", isMobile ? \"grid\" : \"block\" // Use grid only on mobile\n            ),\n            style: isMobile ? {\n                gridTemplateColumns: columns,\n                minWidth,\n                width: \"max-content\"\n            } : {},\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ResponsiveTableGrid;\nconst TableHeader = (param)=>{\n    let { children, className, sticky } = param;\n    // For mobile, disable sticky positioning to allow proper scrolling\n    const isMobile =  true && window.innerWidth < 768;\n    const stickyClasses = {\n        left: !isMobile ? \"sticky left-0 z-20 bg-gray-50 border-r border-gray-200\" : \"\",\n        right: !isMobile ? \"sticky right-0 z-20 bg-gray-50 border-l border-gray-200\" : \"\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-gray-50 border-b border-gray-200\", \"font-medium text-xs text-gray-700 uppercase tracking-wider\", \"px-3 py-3 text-left\", \"sticky top-0 z-10\", sticky && stickyClasses[sticky], className),\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = TableHeader;\nconst TableCell = (param)=>{\n    let { children, className, sticky } = param;\n    // For mobile, disable sticky positioning to allow proper scrolling\n    const isMobile =  true && window.innerWidth < 768;\n    const stickyClasses = {\n        left: !isMobile ? \"sticky left-0 z-10 bg-white border-r border-gray-200\" : \"\",\n        right: !isMobile ? \"sticky right-0 z-10 bg-white border-l border-gray-200\" : \"\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-3 py-4 text-sm text-gray-900\", \"border-b border-gray-200\", \"whitespace-nowrap\", sticky && stickyClasses[sticky], className),\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, undefined);\n};\n_c3 = TableCell;\nconst TableRow = (param)=>{\n    let { children, className, selected = false, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"contents\", selected && \"bg-blue-50\", onClick && \"cursor-pointer hover:bg-gray-50\", className),\n        onClick: onClick,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, undefined);\n};\n_c4 = TableRow;\nconst HybridTable = (param)=>{\n    let { children, className, columns = \"1fr\", minWidth = \"800px\" } = param;\n    const isMobile =  true && window.innerWidth < 768;\n    if (isMobile) {\n        // Use CSS Grid for mobile\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-full overflow-x-auto overflow-y-visible\", \"border border-gray-200 rounded-lg shadow-sm\", \"bg-white scroll-smooth\", className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-0\",\n                style: {\n                    gridTemplateColumns: columns,\n                    minWidth,\n                    width: \"max-content\"\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n                lineNumber: 202,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n            lineNumber: 196,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Use traditional HTML table for desktop\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-full overflow-x-auto overflow-y-visible\", \"border border-gray-200 rounded-lg shadow-sm\", \"bg-white\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n            className: \"min-w-full divide-y divide-gray-200\",\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n            lineNumber: 224,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\ResponsiveTable.tsx\",\n        lineNumber: 218,\n        columnNumber: 5\n    }, undefined);\n};\n_c5 = HybridTable;\n// Export default as ResponsiveTable for backward compatibility\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResponsiveTable);\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"ResponsiveTable\");\n$RefreshReg$(_c1, \"ResponsiveTableGrid\");\n$RefreshReg$(_c2, \"TableHeader\");\n$RefreshReg$(_c3, \"TableCell\");\n$RefreshReg$(_c4, \"TableRow\");\n$RefreshReg$(_c5, \"HybridTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/ResponsiveTable.tsx\n"));

/***/ })

});