"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/sales/page",{

/***/ "(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx":
/*!*************************************************!*\
  !*** ./src/components/Sales/SalesFormPanel.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/empty/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/image/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PrinterOutlined.js\");\n/* harmony import */ var _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/reduxRTK/services/salesApi */ \"(app-pages-browser)/./src/reduxRTK/services/salesApi.ts\");\n/* harmony import */ var _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/reduxRTK/services/productApi */ \"(app-pages-browser)/./src/reduxRTK/services/productApi.ts\");\n/* harmony import */ var _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/reduxRTK/services/userStoreApi */ \"(app-pages-browser)/./src/reduxRTK/services/userStoreApi.ts\");\n/* harmony import */ var _components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/SlidingPanel */ \"(app-pages-browser)/./src/components/ui/SlidingPanel.tsx\");\n/* harmony import */ var _utils_showMessage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/showMessage */ \"(app-pages-browser)/./src/utils/showMessage.ts\");\n/* harmony import */ var _utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/cloudinaryUtils */ \"(app-pages-browser)/./src/utils/cloudinaryUtils.ts\");\n/* harmony import */ var _sales_panels_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./sales-panels.css */ \"(app-pages-browser)/./src/components/Sales/sales-panels.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst SalesFormPanel = (param)=>{\n    let { isOpen, onClose, onSuccess } = param;\n    var _productsData_data_products, _productsData_data;\n    _s();\n    const [form] = _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm();\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalAmount, setTotalAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingReceipt, setIsGeneratingReceipt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptPreviewVisible, setReceiptPreviewVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptUrl, setReceiptUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPrinted, setHasPrinted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            console.log(\"Items state changed:\", items);\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items\n    ]);\n    const { data: productsData, isLoading: isLoadingProducts, refetch: refetchProducts } = (0,_reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery)({\n        page: 1,\n        limit: 1000,\n        search: searchTerm\n    }, {\n        // Force refetch when component mounts and when panel opens\n        refetchOnMountOrArgChange: true,\n        refetchOnFocus: false,\n        refetchOnReconnect: true\n    });\n    // Debug products data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (productsData) {\n                var _productsData_data, _productsData_data_products, _productsData_data1;\n                console.log(\"🛒 Products loaded:\", {\n                    total: ((_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.total) || 0,\n                    productsCount: ((_productsData_data1 = productsData.data) === null || _productsData_data1 === void 0 ? void 0 : (_productsData_data_products = _productsData_data1.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.length) || 0,\n                    isLoading: isLoadingProducts\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        productsData,\n        isLoadingProducts\n    ]);\n    // Get current user ID from auth state\n    const getCurrentUserId = ()=>{\n        if (true) {\n            var _state_auth_user, _state_auth;\n            // @ts-ignore - Redux state is exposed for debugging\n            const state = window.__REDUX_STATE;\n            return (state === null || state === void 0 ? void 0 : (_state_auth = state.auth) === null || _state_auth === void 0 ? void 0 : (_state_auth_user = _state_auth.user) === null || _state_auth_user === void 0 ? void 0 : _state_auth_user.id) || 0;\n        }\n        return 0;\n    };\n    // Fetch user stores\n    const { data: userStoresData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery)(getCurrentUserId());\n    // Fetch default store\n    const { data: defaultStoreData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery)(getCurrentUserId());\n    // Set default store when data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (defaultStoreData === null || defaultStoreData === void 0 ? void 0 : defaultStoreData.data) {\n                setSelectedStore(defaultStoreData.data);\n                form.setFieldsValue({\n                    storeId: defaultStoreData.data.id\n                });\n            } else if ((userStoresData === null || userStoresData === void 0 ? void 0 : userStoresData.data) && userStoresData.data.length > 0) {\n                setSelectedStore(userStoresData.data[0]);\n                form.setFieldsValue({\n                    storeId: userStoresData.data[0].id\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        defaultStoreData,\n        userStoresData,\n        form\n    ]);\n    // Create sale mutation\n    const [createSale, { isLoading: isSubmitting }] = (0,_reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation)();\n    // Calculate total amount whenever items change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (items && items.length > 0) {\n                const total = items.reduce({\n                    \"SalesFormPanel.useEffect.total\": (sum, item)=>sum + item.price * item.quantity\n                }[\"SalesFormPanel.useEffect.total\"], 0);\n                setTotalAmount(total);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: total\n                    });\n                }\n                // Debug log to check items state\n                console.log(\"Current items in useEffect:\", items);\n            } else {\n                setTotalAmount(0);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: 0\n                    });\n                }\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items,\n        form\n    ]);\n    // Handle panel open/close\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (isOpen) {\n                // When panel opens, ensure we have fresh product data\n                console.log(\"🛒 Sales panel opened - fetching fresh product data\");\n                refetchProducts();\n            } else {\n                // Reset form when panel is closed\n                if (form) {\n                    form.resetFields();\n                }\n                setItems([]);\n                setSelectedProduct(null);\n                setQuantity(1);\n                setTotalAmount(0);\n                setReceiptUrl(null);\n                setReceiptPreviewVisible(false);\n                setHasPrinted(false);\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        isOpen,\n        form,\n        refetchProducts\n    ]);\n    // Handle adding an item to the sale\n    const handleAddItem = ()=>{\n        if (!selectedProduct) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Please select a product\");\n            return;\n        }\n        if (quantity <= 0) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Quantity must be greater than 0\");\n            return;\n        }\n        if (selectedProduct.stockQuantity < quantity) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Only \".concat(selectedProduct.stockQuantity, \" units available in stock\"));\n            return;\n        }\n        console.log(\"Adding item with product:\", selectedProduct);\n        // Check if product already exists in items\n        const existingItemIndex = items.findIndex((item)=>item.productId === selectedProduct.id);\n        if (existingItemIndex >= 0) {\n            // Update existing item\n            const updatedItems = [\n                ...items\n            ];\n            const newQuantity = updatedItems[existingItemIndex].quantity + quantity;\n            if (newQuantity > selectedProduct.stockQuantity) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Cannot add more than \".concat(selectedProduct.stockQuantity, \" units of this product\"));\n                return;\n            }\n            updatedItems[existingItemIndex].quantity = newQuantity;\n            // Make sure the product name is set\n            if (!updatedItems[existingItemIndex].productName) {\n                updatedItems[existingItemIndex].productName = selectedProduct.name;\n            }\n            // Update the state with the new array\n            console.log(\"Updating existing item. New items array:\", updatedItems);\n            setItems([\n                ...updatedItems\n            ]); // Create a new array reference to force re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Updated quantity of \".concat(selectedProduct.name));\n        } else {\n            // Add new item\n            const newItem = {\n                productId: selectedProduct.id,\n                productName: selectedProduct.name,\n                quantity,\n                price: typeof selectedProduct.price === \"string\" ? parseFloat(selectedProduct.price) : selectedProduct.price\n            };\n            // Create a new array with the new item\n            const newItems = [\n                ...items,\n                newItem\n            ];\n            // Update the state with the new array\n            console.log(\"Adding new item. New items array:\", newItems);\n            setItems(newItems); // This should trigger a re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Added \".concat(quantity, \" \").concat(selectedProduct.name, \" to sale\"));\n        }\n        // Reset selection\n        setSelectedProduct(null);\n        setQuantity(1);\n        if (form) {\n            form.setFieldsValue({\n                productId: undefined,\n                quantity: 1\n            });\n        }\n    };\n    // Handle removing an item from the sale\n    const handleRemoveItem = (index)=>{\n        const updatedItems = [\n            ...items\n        ];\n        updatedItems.splice(index, 1);\n        setItems(updatedItems);\n    };\n    // Handle printing receipt - directly trigger print dialog\n    const handlePrintReceipt = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n            if (!receiptUrl || hasPrinted) {\n                console.log(\"Skipping print: \", !receiptUrl ? \"No receipt URL\" : \"Already printed\");\n                return;\n            }\n            console.log(\"Printing receipt:\", receiptUrl);\n            // Mark as printed immediately to prevent multiple print dialogs\n            setHasPrinted(true);\n            // Create a hidden iframe to load the image\n            const iframe = document.createElement(\"iframe\");\n            iframe.style.display = \"none\";\n            document.body.appendChild(iframe);\n            // Set up the iframe content with the image and print CSS\n            iframe.onload = ({\n                \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                    if (iframe.contentWindow) {\n                        // Write the HTML content to the iframe\n                        iframe.contentWindow.document.write('\\n          <!DOCTYPE html>\\n          <html>\\n            <head>\\n              <title>Print Receipt</title>\\n              <style>\\n                body {\\n                  margin: 0;\\n                  padding: 0;\\n                  display: flex;\\n                  justify-content: center;\\n                  align-items: center;\\n                  height: 100vh;\\n                }\\n                img {\\n                  max-width: 100%;\\n                  max-height: 100vh;\\n                }\\n                @media print {\\n                  body {\\n                    margin: 0;\\n                    padding: 0;\\n                  }\\n                  img {\\n                    width: 100%;\\n                    height: auto;\\n                  }\\n                }\\n              </style>\\n            </head>\\n            <body>\\n              <img src=\"'.concat(receiptUrl, '\" alt=\"Receipt\" />\\n            </body>\\n          </html>\\n        '));\n                        // Close the document\n                        iframe.contentWindow.document.close();\n                        // Use a single print trigger with a delay to ensure the image is loaded\n                        setTimeout({\n                            \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                if (iframe.contentWindow) {\n                                    try {\n                                        // Print the iframe content\n                                        iframe.contentWindow.focus();\n                                        iframe.contentWindow.print();\n                                    } catch (e) {\n                                        console.error(\"Error printing receipt:\", e);\n                                    }\n                                    // Remove the iframe after printing\n                                    setTimeout({\n                                        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                            document.body.removeChild(iframe);\n                                        }\n                                    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 1000);\n                                }\n                            }\n                        }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 500);\n                    }\n                }\n            })[\"SalesFormPanel.useCallback[handlePrintReceipt]\"];\n            // Set the iframe source to trigger the onload event\n            iframe.src = \"about:blank\";\n        }\n    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], [\n        receiptUrl,\n        hasPrinted\n    ]);\n    // Effect to automatically print receipt when modal is shown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (receiptPreviewVisible && receiptUrl && !hasPrinted) {\n                // Add a small delay to ensure the receipt image is loaded\n                const timer = setTimeout({\n                    \"SalesFormPanel.useEffect.timer\": ()=>{\n                        handlePrintReceipt(); // This now handles the hasPrinted state internally\n                    }\n                }[\"SalesFormPanel.useEffect.timer\"], 800);\n                return ({\n                    \"SalesFormPanel.useEffect\": ()=>clearTimeout(timer)\n                })[\"SalesFormPanel.useEffect\"];\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        receiptPreviewVisible,\n        receiptUrl,\n        hasPrinted,\n        handlePrintReceipt\n    ]);\n    // Handle form submission\n    const handleSubmit = async (values)=>{\n        try {\n            var _userStoresData_data;\n            if (items.length === 0) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Please add at least one item to the sale\");\n                return;\n            }\n            // Check if store is selected\n            if (!selectedStore) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"No store information available. Please set up your store in your profile settings.\");\n                return;\n            }\n            // Set loading state for receipt generation\n            setIsGeneratingReceipt(true);\n            // Get store information for receipt\n            const storeInfo = selectedStore || (userStoresData === null || userStoresData === void 0 ? void 0 : (_userStoresData_data = userStoresData.data) === null || _userStoresData_data === void 0 ? void 0 : _userStoresData_data.find((store)=>store.id === values.storeId)) || {\n                name: \"POS System\"\n            };\n            // Generate receipt HTML\n            const receiptHTML = (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__.generateReceiptHTML)({\n                id: Date.now(),\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                transactionDate: new Date().toISOString(),\n                items: items.map((item)=>({\n                        productName: item.productName,\n                        quantity: item.quantity,\n                        price: item.price\n                    }))\n            }, storeInfo);\n            // Generate receipt image and get URL\n            let receiptUrl = \"https://receipt.example.com/placeholder\";\n            try {\n                receiptUrl = await (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__.generateReceiptImage)(receiptHTML);\n            } catch (error) {\n                console.error(\"Failed to generate receipt image:\", error);\n            // Continue with placeholder URL if image generation fails\n            }\n            const saleData = {\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                items: items.map((item)=>({\n                        productId: item.productId,\n                        quantity: item.quantity,\n                        price: item.price\n                    })),\n                receiptUrl,\n                storeId: selectedStore === null || selectedStore === void 0 ? void 0 : selectedStore.id\n            };\n            const response = await createSale(saleData).unwrap();\n            if (response.success) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Sale created successfully\");\n                // Store the receipt URL for preview\n                setReceiptUrl(receiptUrl);\n                // Show receipt preview modal and offer print option\n                setReceiptPreviewVisible(true);\n                // Refresh product data to get updated stock quantities\n                refetchProducts();\n                // Trigger the success callback to refresh the list WITHOUT closing the panel\n                setTimeout(()=>{\n                    if (onSuccess) {\n                        // Call refetch directly instead of closing the panel\n                        refetchProducts();\n                    }\n                }, 300);\n            // Keep the panel open until the user explicitly closes it\n            // This ensures the receipt modal stays visible\n            } else {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", response.message || \"Failed to create sale\");\n            }\n        } catch (error) {\n            var _error_data;\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", ((_error_data = error.data) === null || _error_data === void 0 ? void 0 : _error_data.message) || \"An error occurred while creating the sale\");\n        } finally{\n            setIsGeneratingReceipt(false);\n        }\n    };\n    // Debug log to check items state when rendering\n    console.log(\"Rendering with items:\", items);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        title: \"Point of Sale\",\n        isOpen: isOpen,\n        onClose: onClose,\n        width: \"95%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sales-form mt-10 min-h-screen bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between border-b border-gray-200 bg-gray-50 px-6 py-3 shadow-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"mr-3 text-2xl text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"m-0 text-xl font-bold text-gray-800\",\n                                        children: \"New Transaction\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg font-bold text-gray-800\",\n                                children: [\n                                    \"Total:\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600\",\n                                        children: [\n                                            \"GHS \",\n                                            totalAmount.toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-4 lg:grid-cols-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4 flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-800\",\n                                                            children: \"Product Selection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-1 text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 512,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \" Required fields\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 gap-4 md:grid-cols-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"md:col-span-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"text-gray-800\",\n                                                                        children: [\n                                                                            \"Product \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 521,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 520,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        showSearch: true,\n                                                                        placeholder: isLoadingProducts ? \"Loading products...\" : \"Search products...\",\n                                                                        optionFilterProp: \"children\",\n                                                                        loading: isLoadingProducts,\n                                                                        disabled: isLoadingProducts,\n                                                                        onChange: (value)=>{\n                                                                            var _productsData_data;\n                                                                            const product = productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products.find((p)=>p.id === value);\n                                                                            console.log(\"Selected product:\", product);\n                                                                            if (product) {\n                                                                                // Make a deep copy to avoid reference issues\n                                                                                setSelectedProduct({\n                                                                                    ...product,\n                                                                                    // Ensure price is properly formatted\n                                                                                    price: typeof product.price === \"string\" ? product.price : String(product.price)\n                                                                                });\n                                                                            } else {\n                                                                                setSelectedProduct(null);\n                                                                            }\n                                                                        },\n                                                                        onSearch: setSearchTerm,\n                                                                        filterOption: false,\n                                                                        className: \"text-gray-800\",\n                                                                        size: \"large\",\n                                                                        suffixIcon: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            spin: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 558,\n                                                                            columnNumber: 29\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 560,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        notFoundContent: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            size: \"small\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 565,\n                                                                            columnNumber: 29\n                                                                        }, void 0) : \"No products found\",\n                                                                        children: productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : (_productsData_data_products = _productsData_data.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"].Option, {\n                                                                                value: product.id,\n                                                                                disabled: product.stockQuantity <= 0,\n                                                                                children: [\n                                                                                    product.name,\n                                                                                    \" \",\n                                                                                    product.stockQuantity <= 0 ? \"(Out of Stock)\" : \"(Stock: \".concat(product.stockQuantity, \")\")\n                                                                                ]\n                                                                            }, product.id, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 572,\n                                                                                columnNumber: 27\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 523,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-gray-800\",\n                                                                    children: [\n                                                                        \"Qty \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-red-500\",\n                                                                            children: \"*\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 589,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    min: 1,\n                                                                    value: quantity,\n                                                                    onChange: (value)=>setQuantity(value || 1),\n                                                                    style: {\n                                                                        width: \"100%\"\n                                                                    },\n                                                                    className: \"text-gray-800\",\n                                                                    size: \"large\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    type: \"primary\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    onClick: handleAddItem,\n                                                    className: \"mt-2 h-12 w-full bg-blue-600 text-base font-medium hover:bg-blue-700\",\n                                                    disabled: !selectedProduct,\n                                                    size: \"large\",\n                                                    children: \"Add to Cart\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"mb-4 flex items-center text-lg font-semibold text-gray-800\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \" Cart Items\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-[350px] overflow-x-auto overflow-y-auto\",\n                                                    children: items.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-lg border border-gray-200 bg-gray-50 p-8 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500\",\n                                                                children: \"No items in cart\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 624,\n                                                                columnNumber: 27\n                                                            }, void 0),\n                                                            image: _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"].PRESENTED_IMAGE_SIMPLE\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                        className: \"min-w-full overflow-hidden rounded-lg border border-gray-200 bg-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                className: \"sticky top-0 z-10 bg-gray-50 text-gray-700\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"px-4 py-3 text-left font-medium\",\n                                                                            children: \"Product\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 635,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"w-20 px-4 py-3 text-center font-medium\",\n                                                                            children: \"Qty\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 638,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"w-28 px-4 py-3 text-right font-medium\",\n                                                                            children: \"Price\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 641,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"w-28 px-4 py-3 text-right font-medium\",\n                                                                            children: \"Subtotal\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 644,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"w-16 px-4 py-3 text-center font-medium\",\n                                                                            children: \"Action\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 647,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 634,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"border-b border-gray-200 hover:bg-gray-50\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 font-medium text-gray-800\",\n                                                                                children: item.productName || \"Unknown Product\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 658,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-center text-gray-800\",\n                                                                                children: item.quantity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 661,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-right text-gray-800\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    Number(item.price).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 664,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-right font-medium text-gray-800\",\n                                                                                children: [\n                                                                                    \"GHS\",\n                                                                                    \" \",\n                                                                                    (Number(item.price) * item.quantity).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 667,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 673,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    onClick: ()=>handleRemoveItem(index),\n                                                                                    type: \"text\",\n                                                                                    danger: true,\n                                                                                    className: \"text-red-500 hover:bg-gray-100 hover:text-red-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 672,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 671,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, \"\".concat(item.productId, \"-\").concat(index), true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 654,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tfoot\", {\n                                                                className: \"sticky bottom-0 z-10 bg-gray-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            colSpan: 3,\n                                                                            className: \"px-4 py-3 text-right font-bold text-gray-800\",\n                                                                            children: \"Total\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 685,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            colSpan: 2,\n                                                                            className: \"px-4 py-3 text-right font-bold text-green-600\",\n                                                                            children: [\n                                                                                \"GHS \",\n                                                                                totalAmount.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 691,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sticky top-4 rounded-lg border border-gray-200 bg-white p-4 shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"mb-4 flex items-center text-lg font-semibold text-gray-800\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 709,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" Checkout\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                form: form,\n                                                layout: \"vertical\",\n                                                initialValues: {\n                                                    paymentMethod: \"cash\"\n                                                },\n                                                onFinish: handleSubmit,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 rounded-lg border border-gray-200 bg-gray-50 p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"Items:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 722,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-gray-800\",\n                                                                        children: items.length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 723,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 721,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"Total Quantity:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 728,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-gray-800\",\n                                                                        children: items.reduce((sum, item)=>sum + item.quantity, 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 729,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between text-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"Subtotal:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 734,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-gray-800\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 735,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 733,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"my-4 border-t border-gray-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 739,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-800\",\n                                                                        children: \"Total:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 741,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-green-600\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 742,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 720,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    selectedStore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"mb-2 block text-gray-700\",\n                                                                children: \"Store\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 751,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center rounded-lg border border-gray-200 bg-gray-50 p-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"mr-2 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-800\",\n                                                                        children: selectedStore.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 754,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"hidden\",\n                                                                        name: \"storeId\",\n                                                                        value: selectedStore.id\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 757,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 752,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 750,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4 rounded-lg border border-gray-200 bg-gray-50 p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"No store information available. Please set up your store in your profile settings.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                        name: \"paymentMethod\",\n                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-700\",\n                                                            children: [\n                                                                \"Payment Method \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 777,\n                                                                    columnNumber: 40\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 776,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        rules: [\n                                                            {\n                                                                required: true,\n                                                                message: \"Please select a payment method\"\n                                                            }\n                                                        ],\n                                                        initialValue: \"cash\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"text-gray-800\",\n                                                            size: \"large\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"].Option, {\n                                                                    value: \"cash\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mr-2\",\n                                                                                children: \"\\uD83D\\uDCB5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 791,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \" Cash\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 790,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 789,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"].Option, {\n                                                                    value: \"card\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mr-2\",\n                                                                                children: \"\\uD83D\\uDCB3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 796,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \" Card\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 795,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 794,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"].Option, {\n                                                                    value: \"mobile_money\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mr-2\",\n                                                                                children: \"\\uD83D\\uDCF1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 801,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \" Mobile Money\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 800,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 799,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 788,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-8 space-y-4\",\n                                                        children: [\n                                                            receiptPreviewVisible ? // Show \"Done\" button when receipt is visible\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 812,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                onClick: ()=>{\n                                                                    // Close the modal and reset the receipt state\n                                                                    setReceiptPreviewVisible(false);\n                                                                    setReceiptUrl(null);\n                                                                    setHasPrinted(false);\n                                                                    // Reset the form to start a new sale\n                                                                    form.resetFields();\n                                                                    setItems([]);\n                                                                    setSelectedProduct(null);\n                                                                    setQuantity(1);\n                                                                    setTotalAmount(0);\n                                                                },\n                                                                className: \"text-md font-small h-14 w-full bg-green-600 hover:bg-green-700\",\n                                                                size: \"large\",\n                                                                children: \"Start New Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 810,\n                                                                columnNumber: 23\n                                                            }, undefined) : // Show \"Complete Sale\" button when creating a sale\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                type: \"primary\",\n                                                                htmlType: \"submit\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 836,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                loading: isSubmitting || isGeneratingReceipt,\n                                                                disabled: items.length === 0,\n                                                                className: \"text-md font-small h-14 w-full bg-green-600 hover:bg-green-700\",\n                                                                size: \"large\",\n                                                                children: isGeneratingReceipt ? \"Generating Receipt\" : \"Complete Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 833,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                onClick: onClose,\n                                                                className: \"h-12 w-full border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200\",\n                                                                size: \"large\",\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 848,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 807,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 707,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 487,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-gray-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            className: \"mr-2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 867,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Receipt Preview\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 868,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 866,\n                    columnNumber: 11\n                }, void 0),\n                open: receiptPreviewVisible,\n                onCancel: ()=>{\n                    // Close the modal and reset the receipt state\n                    setReceiptPreviewVisible(false);\n                    setReceiptUrl(null);\n                    setHasPrinted(false);\n                    // Reset the form to start a new sale\n                    form.resetFields();\n                    setItems([]);\n                    setSelectedProduct(null);\n                    setQuantity(1);\n                    setTotalAmount(0);\n                },\n                width: 500,\n                centered: true,\n                className: \"receipt-preview-modal\",\n                footer: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        onClick: ()=>{\n                            // Close the modal and reset the receipt state\n                            setReceiptPreviewVisible(false);\n                            setReceiptUrl(null);\n                            setHasPrinted(false);\n                            // Reset the form to start a new sale\n                            form.resetFields();\n                            setItems([]);\n                            setSelectedProduct(null);\n                            setQuantity(1);\n                            setTotalAmount(0);\n                        },\n                        className: \"border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200\",\n                        children: \"Close & New Sale\"\n                    }, \"close\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 889,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        type: \"primary\",\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 911,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: ()=>{\n                            // If already printed once, reset the flag to allow printing again\n                            if (hasPrinted) {\n                                setHasPrinted(false);\n                            }\n                            handlePrintReceipt();\n                        },\n                        className: \"bg-blue-600 hover:bg-blue-700\",\n                        children: hasPrinted ? \"Print Again\" : \"Print Receipt\"\n                    }, \"print\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 908,\n                        columnNumber: 11\n                    }, void 0)\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: receiptUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"receipt-image-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            src: receiptUrl,\n                            alt: \"Receipt\",\n                            className: \"receipt-image\",\n                            style: {\n                                maxWidth: \"100%\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 928,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 927,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-64 items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            size: \"large\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 937,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 936,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 925,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 864,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n        lineNumber: 481,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SalesFormPanel, \"1/38FKgYVc7r3Srw9LN2gx/dVME=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm,\n        _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery,\n        _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation\n    ];\n});\n_c = SalesFormPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SalesFormPanel);\nvar _c;\n$RefreshReg$(_c, \"SalesFormPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx\n"));

/***/ })

});