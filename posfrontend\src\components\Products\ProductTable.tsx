"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Tag, Checkbox, notification } from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import {
  EditOutlined,
  EyeOutlined,
  DeleteOutlined,
  DollarOutlined,
  ShoppingOutlined,
  BarcodeOutlined,
  CalendarOutlined,
  PlusCircleOutlined,
  DeleteFilled
} from "@ant-design/icons";
import { ResponsiveTableGrid, TableHeader, TableCell, TableRow } from "@/components/ui/ResponsiveTable";
import { Product } from "@/reduxRTK/services/productApi";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { UserRole } from "@/types/user";

interface ProductTableProps {
  products: Product[];
  loading: boolean;
  onView: (productId: number) => void;
  onEdit: (product: Product) => void;
  onDelete: (productId: number) => void;
  onBulkDelete?: (productIds: number[]) => void;
  onAdjustStock: (product: Product) => void;
  isMobile?: boolean;
}

const ProductTable: React.FC<ProductTableProps> = ({
  products,
  loading,
  onView,
  onEdit,
  onDelete,
  onBulkDelete,
  onAdjustStock,
  isMobile = false,
}) => {
  const user = useSelector((state: RootState) => state.auth.user);
  const userRole = user?.role as UserRole;

  // State for selected products
  const [selectedProducts, setSelectedProducts] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Handle select all checkbox change
  const handleSelectAllChange = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked;
    setSelectAll(checked);

    if (checked) {
      // Select all products that the user can delete
      const selectableProductIds = products
        .filter(product => canEditDelete(product))
        .map(product => product.id);
      setSelectedProducts(selectableProductIds);
    } else {
      // Deselect all products
      setSelectedProducts([]);
    }
  };

  // Handle individual checkbox change
  const handleCheckboxChange = (productId: number, checked: boolean) => {
    if (checked) {
      setSelectedProducts(prev => [...prev, productId]);
    } else {
      setSelectedProducts(prev => prev.filter(id => id !== productId));
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedProducts.length > 0 && onBulkDelete) {
      onBulkDelete(selectedProducts);
      setSelectedProducts([]);
      setSelectAll(false);
    } else {
      notification.warning({
        message: 'No products selected',
        description: 'Please select at least one product to delete.',
      });
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return dayjs(dateString).format("MMM D, YYYY");
  };

  // Format currency for display
  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat('en-GH', {
      style: 'currency',
      currency: 'GHS',
    }).format(parseFloat(amount));
  };

  // Check if user can edit/delete (superadmin can edit all, others only their own)
  const canEditDelete = (product: Product) => {
    if (userRole === "superadmin") return true;
    if (userRole === "admin" && user?.id === product.createdBy) return true;
    return false;
  };

  return (
    <div className="overflow-hidden bg-white">
      {/* Bulk Delete Button - Show only when products are selected */}
      {selectedProducts.length > 0 && (
        <div className="p-2 bg-gray-100 border-b flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            {selectedProducts.length} {selectedProducts.length === 1 ? 'product' : 'products'} selected
          </span>
          <Button
            type="primary"
            danger
            icon={<DeleteFilled />}
            onClick={handleBulkDelete}
            className="ml-2"
          >
            Delete Selected
          </Button>
        </div>
      )}

      <ResponsiveTableGrid
        columns={isMobile ? "50px 200px 100px 80px 120px 150px" : "50px 200px 100px 80px 100px 120px 150px"}
        minWidth={isMobile ? "800px" : "1000px"}
      >
        {/* Table Headers */}
        <TableHeader className="text-center">
          <Checkbox
            checked={selectAll}
            onChange={handleSelectAllChange}
            disabled={products.filter(product => canEditDelete(product)).length === 0}
          />
        </TableHeader>
        <TableHeader sticky={isMobile ? undefined : "left"}>
          <span className="flex items-center">
            <ShoppingOutlined className="mr-1" />
            Name
          </span>
        </TableHeader>
        <TableHeader>
          <span className="flex items-center">
            <DollarOutlined className="mr-1" />
            Price
          </span>
        </TableHeader>
        <TableHeader>
          <span className="flex items-center">
            <ShoppingOutlined className="mr-1" />
            Stock
          </span>
        </TableHeader>
        {!isMobile && (
          <TableHeader>
            <span className="flex items-center">
              <BarcodeOutlined className="mr-1" />
              SKU
            </span>
          </TableHeader>
        )}
        <TableHeader>
          <span className="flex items-center">
            <CalendarOutlined className="mr-1" />
            Created At
          </span>
        </TableHeader>
        <TableHeader sticky={isMobile ? undefined : "right"} className="text-right">
          Actions
        </TableHeader>
        {/* Table Rows */}
        {products.map((product) => (
          <TableRow
            key={product.id}
            selected={selectedProducts.includes(product.id)}
          >
            {/* Checkbox Column */}
            <TableCell className="text-center">
              {canEditDelete(product) && (
                <Checkbox
                  checked={selectedProducts.includes(product.id)}
                  onChange={(e) => handleCheckboxChange(product.id, e.target.checked)}
                />
              )}
            </TableCell>

            {/* Name Column - Always visible */}
            <TableCell sticky={isMobile ? undefined : "left"}>
              <div className="max-w-[180px] overflow-hidden text-ellipsis font-medium">
                {product.name}
              </div>
            </TableCell>

            {/* Price Column */}
            <TableCell>
              <span className="font-medium text-green-600">
                {formatCurrency(product.price)}
              </span>
            </TableCell>

            {/* Stock Column */}
            <TableCell>
              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                product.stockQuantity <= 0
                  ? 'bg-red-500 text-white'
                  : product.stockQuantity <= (product.minStockLevel || 5)
                    ? 'bg-yellow-500 text-white'
                    : 'bg-green-500 text-white'
              }`}>
                {product.stockQuantity}
              </span>
            </TableCell>

            {/* SKU Column - Desktop only */}
            {!isMobile && (
              <TableCell>
                <span className="font-mono text-xs">
                  {product.sku || 'N/A'}
                </span>
              </TableCell>
            )}

            {/* Created At Column */}
            <TableCell>
              {formatDate(product.createdAt)}
            </TableCell>

            {/* Actions Column - Always visible */}
            <TableCell sticky={isMobile ? undefined : "right"} className="text-right">
              <div className="flex justify-end space-x-1">
                <Tooltip title="View">
                  <Button
                    icon={<EyeOutlined />}
                    onClick={() => onView(product.id)}
                    type="text"
                    className="view-button text-green-500 hover:text-green-400"
                    size={isMobile ? "small" : "middle"}
                  />
                </Tooltip>

                {/* Only show stock adjustment button for admin users */}
                {userRole === "admin" && (
                  <Tooltip title="Adjust Stock">
                    <Button
                      icon={<PlusCircleOutlined />}
                      onClick={() => onAdjustStock(product)}
                      type="text"
                      className="adjust-stock-button text-purple-500 hover:text-purple-400"
                      size={isMobile ? "small" : "middle"}
                    />
                  </Tooltip>
                )}

                {canEditDelete(product) && (
                  <>
                    <Tooltip title="Edit">
                      <Button
                        icon={<EditOutlined />}
                        onClick={() => onEdit(product)}
                        type="text"
                        className="edit-button text-blue-500 hover:text-blue-400"
                        size={isMobile ? "small" : "middle"}
                      />
                    </Tooltip>
                    <Tooltip title="Delete">
                      <Button
                        icon={<DeleteOutlined />}
                        onClick={() => onDelete(product.id)}
                        type="text"
                        className="delete-button text-red-500 hover:text-red-400"
                        size={isMobile ? "small" : "middle"}
                      />
                    </Tooltip>
                  </>
                )}
              </div>
            </TableCell>
          </TableRow>
        ))}
      </ResponsiveTableGrid>
    </div>
  );
};

export default ProductTable;
