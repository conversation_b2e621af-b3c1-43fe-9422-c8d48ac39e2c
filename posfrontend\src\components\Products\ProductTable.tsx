"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Tag, Checkbox, notification } from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import {
  EditOutlined,
  EyeOutlined,
  DeleteOutlined,
  DollarOutlined,
  ShoppingOutlined,
  BarcodeOutlined,
  CalendarOutlined,
  PlusCircleOutlined,
  DeleteFilled
} from "@ant-design/icons";
import { ResponsiveTableGrid, TableHeader, TableCell, TableRow, HybridTable } from "@/components/ui/ResponsiveTable";
import { useResponsiveTable } from "@/hooks/useResponsiveTable";
import { Product } from "@/reduxRTK/services/productApi";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { UserRole } from "@/types/user";

interface ProductTableProps {
  products: Product[];
  loading: boolean;
  onView: (productId: number) => void;
  onEdit: (product: Product) => void;
  onDelete: (productId: number) => void;
  onBulkDelete?: (productIds: number[]) => void;
  onAdjustStock: (product: Product) => void;
  isMobile?: boolean;
}

const ProductTable: React.FC<ProductTableProps> = ({
  products,
  loading,
  onView,
  onEdit,
  onDelete,
  onBulkDelete,
  onAdjustStock,
  isMobile: propIsMobile = false,
}) => {
  // Use hook for responsive detection, fallback to prop
  const hookIsMobile = useResponsiveTable();
  const isMobile = propIsMobile || hookIsMobile;

  const user = useSelector((state: RootState) => state.auth.user);
  const userRole = user?.role as UserRole;

  // State for selected products
  const [selectedProducts, setSelectedProducts] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Handle select all checkbox change
  const handleSelectAllChange = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked;
    setSelectAll(checked);

    if (checked) {
      // Select all products that the user can delete
      const selectableProductIds = products
        .filter(product => canEditDelete(product))
        .map(product => product.id);
      setSelectedProducts(selectableProductIds);
    } else {
      // Deselect all products
      setSelectedProducts([]);
    }
  };

  // Handle individual checkbox change
  const handleCheckboxChange = (productId: number, checked: boolean) => {
    if (checked) {
      setSelectedProducts(prev => [...prev, productId]);
    } else {
      setSelectedProducts(prev => prev.filter(id => id !== productId));
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedProducts.length > 0 && onBulkDelete) {
      onBulkDelete(selectedProducts);
      setSelectedProducts([]);
      setSelectAll(false);
    } else {
      notification.warning({
        message: 'No products selected',
        description: 'Please select at least one product to delete.',
      });
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return dayjs(dateString).format("MMM D, YYYY");
  };

  // Format currency for display
  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat('en-GH', {
      style: 'currency',
      currency: 'GHS',
    }).format(parseFloat(amount));
  };

  // Check if user can edit/delete (superadmin can edit all, others only their own)
  const canEditDelete = (product: Product) => {
    if (userRole === "superadmin") return true;
    if (userRole === "admin" && user?.id === product.createdBy) return true;
    return false;
  };

  return (
    <div className="overflow-hidden bg-white">
      {/* Bulk Delete Button - Show only when products are selected */}
      {selectedProducts.length > 0 && (
        <div className="p-2 bg-gray-100 border-b flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            {selectedProducts.length} {selectedProducts.length === 1 ? 'product' : 'products'} selected
          </span>
          <Button
            type="primary"
            danger
            icon={<DeleteFilled />}
            onClick={handleBulkDelete}
            className="ml-2"
          >
            Delete Selected
          </Button>
        </div>
      )}

      {isMobile ? (
        // Mobile: Use CSS Grid
        <ResponsiveTableGrid
          columns="50px 200px 100px 80px 120px 150px"
          minWidth="800px"
        >
          {/* Mobile Headers */}
          <TableHeader className="text-center">
            <Checkbox
              checked={selectAll}
              onChange={handleSelectAllChange}
              disabled={products.filter(product => canEditDelete(product)).length === 0}
            />
          </TableHeader>
          <TableHeader>
            <span className="flex items-center">
              <ShoppingOutlined className="mr-1" />
              Name
            </span>
          </TableHeader>
          <TableHeader>
            <span className="flex items-center">
              <DollarOutlined className="mr-1" />
              Price
            </span>
          </TableHeader>
          <TableHeader>
            <span className="flex items-center">
              <ShoppingOutlined className="mr-1" />
              Stock
            </span>
          </TableHeader>
          <TableHeader>
            <span className="flex items-center">
              <CalendarOutlined className="mr-1" />
              Created At
            </span>
          </TableHeader>
          <TableHeader className="text-right">
            Actions
          </TableHeader>

          {/* Mobile Rows */}
          {products.map((product) => (
            <TableRow
              key={product.id}
              selected={selectedProducts.includes(product.id)}
            >
              <TableCell className="text-center">
                {canEditDelete(product) && (
                  <Checkbox
                    checked={selectedProducts.includes(product.id)}
                    onChange={(e) => handleCheckboxChange(product.id, e.target.checked)}
                  />
                )}
              </TableCell>
              <TableCell>
                <div className="max-w-[180px] overflow-hidden text-ellipsis font-medium">
                  {product.name}
                </div>
              </TableCell>
              <TableCell>
                <span className="font-medium text-green-600">
                  {formatCurrency(product.price)}
                </span>
              </TableCell>
              <TableCell>
                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  product.stockQuantity <= 0
                    ? 'bg-red-500 text-white'
                    : product.stockQuantity <= (product.minStockLevel || 5)
                      ? 'bg-yellow-500 text-white'
                      : 'bg-green-500 text-white'
                }`}>
                  {product.stockQuantity}
                </span>
              </TableCell>
              <TableCell>
                {formatDate(product.createdAt)}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-1">
                  <Tooltip title="View">
                    <Button
                      icon={<EyeOutlined />}
                      onClick={() => onView(product.id)}
                      type="text"
                      className="view-button text-green-500 hover:text-green-400"
                      size="small"
                    />
                  </Tooltip>
                  {userRole === "admin" && (
                    <Tooltip title="Adjust Stock">
                      <Button
                        icon={<PlusCircleOutlined />}
                        onClick={() => onAdjustStock(product)}
                        type="text"
                        className="adjust-stock-button text-purple-500 hover:text-purple-400"
                        size="small"
                      />
                    </Tooltip>
                  )}
                  {canEditDelete(product) && (
                    <>
                      <Tooltip title="Edit">
                        <Button
                          icon={<EditOutlined />}
                          onClick={() => onEdit(product)}
                          type="text"
                          className="edit-button text-blue-500 hover:text-blue-400"
                          size="small"
                        />
                      </Tooltip>
                      <Tooltip title="Delete">
                        <Button
                          icon={<DeleteOutlined />}
                          onClick={() => onDelete(product.id)}
                          type="text"
                          className="delete-button text-red-500 hover:text-red-400"
                          size="small"
                        />
                      </Tooltip>
                    </>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </ResponsiveTableGrid>
      ) : (
        // Desktop: Use traditional HTML table
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {/* Checkbox Column */}
                <th scope="col" className="w-10 px-3 py-3 text-center">
                  <Checkbox
                    checked={selectAll}
                    onChange={handleSelectAllChange}
                    disabled={products.filter(product => canEditDelete(product)).length === 0}
                  />
                </th>

                {/* Name Column - Always visible */}
                <th scope="col" className="sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <ShoppingOutlined className="mr-1" />
                    Name
                  </span>
                </th>

                {/* Price Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <DollarOutlined className="mr-1" />
                    Price
                  </span>
                </th>

                {/* Stock Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <ShoppingOutlined className="mr-1" />
                    Stock
                  </span>
                </th>

                {/* SKU Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <BarcodeOutlined className="mr-1" />
                    SKU
                  </span>
                </th>

                {/* Created At Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <CalendarOutlined className="mr-1" />
                    Created At
                  </span>
                </th>

                {/* Actions Column - Always visible */}
                <th scope="col" className="sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {products.map((product) => (
                <tr key={product.id} className={selectedProducts.includes(product.id) ? "bg-blue-50" : ""}>
                  {/* Checkbox Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-center">
                    {canEditDelete(product) && (
                      <Checkbox
                        checked={selectedProducts.includes(product.id)}
                        onChange={(e) => handleCheckboxChange(product.id, e.target.checked)}
                      />
                    )}
                  </td>

                  {/* Name Column - Always visible */}
                  <td className="sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800">
                    <div className="max-w-[120px] overflow-hidden text-ellipsis">
                      {product.name}
                    </div>
                  </td>

                  {/* Price Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    {formatCurrency(product.price)}
                  </td>

                  {/* Stock Column */}
                  <td className="px-3 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      product.stockQuantity <= 0
                        ? 'bg-red-500 text-white'
                        : product.stockQuantity <= (product.minStockLevel || 5)
                          ? 'bg-yellow-500 text-white'
                          : 'bg-green-500 text-white'
                    }`}>
                      {product.stockQuantity}
                    </span>
                  </td>

                  {/* SKU Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    {product.sku || 'N/A'}
                  </td>

                  {/* Created At Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    {formatDate(product.createdAt)}
                  </td>

                  {/* Actions Column - Always visible */}
                  <td className="sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-1">
                      <Tooltip title="View">
                        <Button
                          icon={<EyeOutlined />}
                          onClick={() => onView(product.id)}
                          type="text"
                          className="view-button text-green-500 hover:text-green-400"
                          size="middle"
                        />
                      </Tooltip>

                      {/* Only show stock adjustment button for admin users */}
                      {userRole === "admin" && (
                        <Tooltip title="Adjust Stock">
                          <Button
                            icon={<PlusCircleOutlined />}
                            onClick={() => onAdjustStock(product)}
                            type="text"
                            className="adjust-stock-button text-purple-500 hover:text-purple-400"
                            size="middle"
                          />
                        </Tooltip>
                      )}

                      {canEditDelete(product) && (
                        <>
                          <Tooltip title="Edit">
                            <Button
                              icon={<EditOutlined />}
                              onClick={() => onEdit(product)}
                              type="text"
                              className="edit-button text-blue-500 hover:text-blue-400"
                              size="middle"
                            />
                          </Tooltip>
                          <Tooltip title="Delete">
                            <Button
                              icon={<DeleteOutlined />}
                              onClick={() => onDelete(product.id)}
                              type="text"
                              className="delete-button text-red-500 hover:text-red-400"
                              size="middle"
                            />
                          </Tooltip>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

    </div>
  );
};

export default ProductTable;
