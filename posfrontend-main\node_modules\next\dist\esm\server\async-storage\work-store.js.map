{"version": 3, "sources": ["../../../src/server/async-storage/work-store.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\nimport type { IncrementalCache } from '../lib/incremental-cache'\nimport type { RenderOpts } from '../app-render/types'\nimport type { FetchMetric } from '../base-http'\nimport type { RequestLifecycleOpts } from '../base-server'\nimport type { FallbackRouteParams } from '../request/fallback-params'\nimport type { AppSegmentConfig } from '../../build/segment-config/app/app-segment-config'\nimport type { CacheLife } from '../use-cache/cache-life'\n\nimport { AfterContext } from '../after/after-context'\n\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\n\nexport type WorkStoreContext = {\n  /**\n   * The page that is being rendered. This relates to the path to the page file.\n   */\n  page: string\n\n  /**\n   * The route parameters that are currently unknown.\n   */\n  fallbackRouteParams: FallbackRouteParams | null\n\n  requestEndedState?: { ended?: boolean }\n  isPrefetchRequest?: boolean\n  renderOpts: {\n    cacheLifeProfiles?: { [profile: string]: CacheLife }\n    incrementalCache?: IncrementalCache\n    isOnDemandRevalidate?: boolean\n    fetchCache?: AppSegmentConfig['fetchCache']\n    isServerAction?: boolean\n    pendingWaitUntil?: Promise<any>\n    experimental: Pick<\n      RenderOpts['experimental'],\n      'isRoutePPREnabled' | 'dynamicIO' | 'authInterrupts'\n    >\n\n    /**\n     * Fetch metrics attached in patch-fetch.ts\n     **/\n    fetchMetrics?: FetchMetric[]\n\n    /**\n     * A hack around accessing the store value outside the context of the\n     * request.\n     *\n     * @internal\n     * @deprecated should only be used as a temporary workaround\n     */\n    // TODO: remove this when we resolve accessing the store outside the execution context\n    store?: WorkStore\n  } & Pick<\n    // Pull some properties from RenderOpts so that the docs are also\n    // mirrored.\n    RenderOpts,\n    | 'assetPrefix'\n    | 'supportsDynamicResponse'\n    | 'isRevalidate'\n    | 'nextExport'\n    | 'isDraftMode'\n    | 'isDebugDynamicAccesses'\n    | 'buildId'\n  > &\n    RequestLifecycleOpts &\n    Partial<Pick<RenderOpts, 'reactLoadableManifest'>>\n}\n\nexport function createWorkStore({\n  page,\n  fallbackRouteParams,\n  renderOpts,\n  requestEndedState,\n  isPrefetchRequest,\n}: WorkStoreContext): WorkStore {\n  /**\n   * Rules of Static & Dynamic HTML:\n   *\n   *    1.) We must generate static HTML unless the caller explicitly opts\n   *        in to dynamic HTML support.\n   *\n   *    2.) If dynamic HTML support is requested, we must honor that request\n   *        or throw an error. It is the sole responsibility of the caller to\n   *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n   *\n   *    3.) If the request is in draft mode, we must generate dynamic HTML.\n   *\n   *    4.) If the request is a server action, we must generate dynamic HTML.\n   *\n   * These rules help ensure that other existing features like request caching,\n   * coalescing, and ISR continue working as intended.\n   */\n  const isStaticGeneration =\n    !renderOpts.supportsDynamicResponse &&\n    !renderOpts.isDraftMode &&\n    !renderOpts.isServerAction\n\n  const store: WorkStore = {\n    isStaticGeneration,\n    page,\n    fallbackRouteParams,\n    route: normalizeAppPath(page),\n    incrementalCache:\n      // we fallback to a global incremental cache for edge-runtime locally\n      // so that it can access the fs cache without mocks\n      renderOpts.incrementalCache || (globalThis as any).__incrementalCache,\n    cacheLifeProfiles: renderOpts.cacheLifeProfiles,\n    isRevalidate: renderOpts.isRevalidate,\n    isPrerendering: renderOpts.nextExport,\n    fetchCache: renderOpts.fetchCache,\n    isOnDemandRevalidate: renderOpts.isOnDemandRevalidate,\n\n    isDraftMode: renderOpts.isDraftMode,\n\n    requestEndedState,\n    isPrefetchRequest,\n    buildId: renderOpts.buildId,\n    reactLoadableManifest: renderOpts?.reactLoadableManifest || {},\n    assetPrefix: renderOpts?.assetPrefix || '',\n\n    afterContext: createAfterContext(renderOpts),\n  }\n\n  // TODO: remove this when we resolve accessing the store outside the execution context\n  renderOpts.store = store\n\n  return store\n}\n\nfunction createAfterContext(renderOpts: RequestLifecycleOpts): AfterContext {\n  const { waitUntil, onClose, onAfterTaskError } = renderOpts\n  return new AfterContext({\n    waitUntil,\n    onClose,\n    onTaskError: onAfterTaskError,\n  })\n}\n"], "names": ["AfterContext", "normalizeAppPath", "createWorkStore", "page", "fallbackRouteParams", "renderOpts", "requestEndedState", "isPrefetchRequest", "isStaticGeneration", "supportsDynamicResponse", "isDraftMode", "isServerAction", "store", "route", "incrementalCache", "globalThis", "__incrementalCache", "cacheLifeProfiles", "isRevalidate", "isPrerendering", "nextExport", "fetchCache", "isOnDemandRevalidate", "buildId", "reactLoadableManifest", "assetPrefix", "afterContext", "createAfterContext", "waitUntil", "onClose", "onAfterTaskError", "onTaskError"], "mappings": "AASA,SAASA,YAAY,QAAQ,yBAAwB;AAErD,SAASC,gBAAgB,QAAQ,0CAAyC;AAyD1E,OAAO,SAASC,gBAAgB,EAC9BC,IAAI,EACJC,mBAAmB,EACnBC,UAAU,EACVC,iBAAiB,EACjBC,iBAAiB,EACA;IACjB;;;;;;;;;;;;;;;;GAgBC,GACD,MAAMC,qBACJ,CAACH,WAAWI,uBAAuB,IACnC,CAACJ,WAAWK,WAAW,IACvB,CAACL,WAAWM,cAAc;IAE5B,MAAMC,QAAmB;QACvBJ;QACAL;QACAC;QACAS,OAAOZ,iBAAiBE;QACxBW,kBACE,qEAAqE;QACrE,mDAAmD;QACnDT,WAAWS,gBAAgB,IAAI,AAACC,WAAmBC,kBAAkB;QACvEC,mBAAmBZ,WAAWY,iBAAiB;QAC/CC,cAAcb,WAAWa,YAAY;QACrCC,gBAAgBd,WAAWe,UAAU;QACrCC,YAAYhB,WAAWgB,UAAU;QACjCC,sBAAsBjB,WAAWiB,oBAAoB;QAErDZ,aAAaL,WAAWK,WAAW;QAEnCJ;QACAC;QACAgB,SAASlB,WAAWkB,OAAO;QAC3BC,uBAAuBnB,CAAAA,8BAAAA,WAAYmB,qBAAqB,KAAI,CAAC;QAC7DC,aAAapB,CAAAA,8BAAAA,WAAYoB,WAAW,KAAI;QAExCC,cAAcC,mBAAmBtB;IACnC;IAEA,sFAAsF;IACtFA,WAAWO,KAAK,GAAGA;IAEnB,OAAOA;AACT;AAEA,SAASe,mBAAmBtB,UAAgC;IAC1D,MAAM,EAAEuB,SAAS,EAAEC,OAAO,EAAEC,gBAAgB,EAAE,GAAGzB;IACjD,OAAO,IAAIL,aAAa;QACtB4B;QACAC;QACAE,aAAaD;IACf;AACF"}