"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-motion";
exports.ids = ["vendor-chunks/rc-motion"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-motion/es/CSSMotion.js":
/*!************************************************!*\
  !*** ./node_modules/rc-motion/es/CSSMotion.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   genCSSMotion: () => (/* binding */ genCSSMotion)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/Dom/findDOMNode */ \"(ssr)/./node_modules/rc-util/es/Dom/findDOMNode.js\");\n/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/ref */ \"(ssr)/./node_modules/rc-util/es/ref.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-motion/es/context.js\");\n/* harmony import */ var _DomWrapper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DomWrapper */ \"(ssr)/./node_modules/rc-motion/es/DomWrapper.js\");\n/* harmony import */ var _hooks_useStatus__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./hooks/useStatus */ \"(ssr)/./node_modules/rc-motion/es/hooks/useStatus.js\");\n/* harmony import */ var _hooks_useStepQueue__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./hooks/useStepQueue */ \"(ssr)/./node_modules/rc-motion/es/hooks/useStepQueue.js\");\n/* harmony import */ var _interface__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./interface */ \"(ssr)/./node_modules/rc-motion/es/interface.js\");\n/* harmony import */ var _util_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./util/motion */ \"(ssr)/./node_modules/rc-motion/es/util/motion.js\");\n\n\n\n\n/* eslint-disable react/default-props-match-prop-types, react/no-multi-comp, react/prop-types */\n\n\n\n\n\n\n\n\n\n\n\n/**\n * `transitionSupport` is used for none transition test case.\n * Default we use browser transition event support check.\n */\nfunction genCSSMotion(config) {\n  var transitionSupport = config;\n  if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(config) === 'object') {\n    transitionSupport = config.transitionSupport;\n  }\n  function isSupportTransition(props, contextMotion) {\n    return !!(props.motionName && transitionSupport && contextMotion !== false);\n  }\n  var CSSMotion = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.forwardRef(function (props, ref) {\n    var _props$visible = props.visible,\n      visible = _props$visible === void 0 ? true : _props$visible,\n      _props$removeOnLeave = props.removeOnLeave,\n      removeOnLeave = _props$removeOnLeave === void 0 ? true : _props$removeOnLeave,\n      forceRender = props.forceRender,\n      children = props.children,\n      motionName = props.motionName,\n      leavedClassName = props.leavedClassName,\n      eventProps = props.eventProps;\n    var _React$useContext = react__WEBPACK_IMPORTED_MODULE_7__.useContext(_context__WEBPACK_IMPORTED_MODULE_8__.Context),\n      contextMotion = _React$useContext.motion;\n    var supportMotion = isSupportTransition(props, contextMotion);\n\n    // Ref to the react node, it may be a HTMLElement\n    var nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)();\n    // Ref to the dom wrapper in case ref can not pass to HTMLElement\n    var wrapperNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)();\n    function getDomElement() {\n      try {\n        // Here we're avoiding call for findDOMNode since it's deprecated\n        // in strict mode. We're calling it only when node ref is not\n        // an instance of DOM HTMLElement. Otherwise use\n        // findDOMNode as a final resort\n        return nodeRef.current instanceof HTMLElement ? nodeRef.current : (0,rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(wrapperNodeRef.current);\n      } catch (e) {\n        // Only happen when `motionDeadline` trigger but element removed.\n        return null;\n      }\n    }\n    var _useStatus = (0,_hooks_useStatus__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(supportMotion, visible, getDomElement, props),\n      _useStatus2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useStatus, 4),\n      status = _useStatus2[0],\n      statusStep = _useStatus2[1],\n      statusStyle = _useStatus2[2],\n      mergedVisible = _useStatus2[3];\n\n    // Record whether content has rendered\n    // Will return null for un-rendered even when `removeOnLeave={false}`\n    var renderedRef = react__WEBPACK_IMPORTED_MODULE_7__.useRef(mergedVisible);\n    if (mergedVisible) {\n      renderedRef.current = true;\n    }\n\n    // ====================== Refs ======================\n    var setNodeRef = react__WEBPACK_IMPORTED_MODULE_7__.useCallback(function (node) {\n      nodeRef.current = node;\n      (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.fillRef)(ref, node);\n    }, [ref]);\n\n    // ===================== Render =====================\n    var motionChildren;\n    var mergedProps = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, eventProps), {}, {\n      visible: visible\n    });\n    if (!children) {\n      // No children\n      motionChildren = null;\n    } else if (status === _interface__WEBPACK_IMPORTED_MODULE_12__.STATUS_NONE) {\n      // Stable children\n      if (mergedVisible) {\n        motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), setNodeRef);\n      } else if (!removeOnLeave && renderedRef.current && leavedClassName) {\n        motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), {}, {\n          className: leavedClassName\n        }), setNodeRef);\n      } else if (forceRender || !removeOnLeave && !leavedClassName) {\n        motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), {}, {\n          style: {\n            display: 'none'\n          }\n        }), setNodeRef);\n      } else {\n        motionChildren = null;\n      }\n    } else {\n      // In motion\n      var statusSuffix;\n      if (statusStep === _interface__WEBPACK_IMPORTED_MODULE_12__.STEP_PREPARE) {\n        statusSuffix = 'prepare';\n      } else if ((0,_hooks_useStepQueue__WEBPACK_IMPORTED_MODULE_11__.isActive)(statusStep)) {\n        statusSuffix = 'active';\n      } else if (statusStep === _interface__WEBPACK_IMPORTED_MODULE_12__.STEP_START) {\n        statusSuffix = 'start';\n      }\n      var motionCls = (0,_util_motion__WEBPACK_IMPORTED_MODULE_13__.getTransitionName)(motionName, \"\".concat(status, \"-\").concat(statusSuffix));\n      motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), {}, {\n        className: classnames__WEBPACK_IMPORTED_MODULE_4___default()((0,_util_motion__WEBPACK_IMPORTED_MODULE_13__.getTransitionName)(motionName, status), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, motionCls, motionCls && statusSuffix), motionName, typeof motionName === 'string')),\n        style: statusStyle\n      }), setNodeRef);\n    }\n\n    // Auto inject ref if child node not have `ref` props\n    if ( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.isValidElement(motionChildren) && (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.supportRef)(motionChildren)) {\n      var originNodeRef = (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.getNodeRef)(motionChildren);\n      if (!originNodeRef) {\n        motionChildren = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.cloneElement(motionChildren, {\n          ref: setNodeRef\n        });\n      }\n    }\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_DomWrapper__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n      ref: wrapperNodeRef\n    }, motionChildren);\n  });\n  CSSMotion.displayName = 'CSSMotion';\n  return CSSMotion;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (genCSSMotion(_util_motion__WEBPACK_IMPORTED_MODULE_13__.supportTransition));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/CSSMotion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/CSSMotionList.js":
/*!****************************************************!*\
  !*** ./node_modules/rc-motion/es/CSSMotionList.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   genCSSMotionList: () => (/* binding */ genCSSMotionList)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/assertThisInitialized */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createSuper.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _CSSMotion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./CSSMotion */ \"(ssr)/./node_modules/rc-motion/es/CSSMotion.js\");\n/* harmony import */ var _util_diff__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./util/diff */ \"(ssr)/./node_modules/rc-motion/es/util/diff.js\");\n/* harmony import */ var _util_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./util/motion */ \"(ssr)/./node_modules/rc-motion/es/util/motion.js\");\n\n\n\n\n\n\n\n\n\nvar _excluded = [\"component\", \"children\", \"onVisibleChanged\", \"onAllRemoved\"],\n  _excluded2 = [\"status\"];\n/* eslint react/prop-types: 0 */\n\n\n\n\nvar MOTION_PROP_NAMES = ['eventProps', 'visible', 'children', 'motionName', 'motionAppear', 'motionEnter', 'motionLeave', 'motionLeaveImmediately', 'motionDeadline', 'removeOnLeave', 'leavedClassName', 'onAppearPrepare', 'onAppearStart', 'onAppearActive', 'onAppearEnd', 'onEnterStart', 'onEnterActive', 'onEnterEnd', 'onLeaveStart', 'onLeaveActive', 'onLeaveEnd'];\n/**\n * Generate a CSSMotionList component with config\n * @param transitionSupport No need since CSSMotionList no longer depends on transition support\n * @param CSSMotion CSSMotion component\n */\nfunction genCSSMotionList(transitionSupport) {\n  var CSSMotion = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _CSSMotion__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n  var CSSMotionList = /*#__PURE__*/function (_React$Component) {\n    (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(CSSMotionList, _React$Component);\n    var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(CSSMotionList);\n    function CSSMotionList() {\n      var _this;\n      (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, CSSMotionList);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _super.call.apply(_super, [this].concat(args));\n      (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"state\", {\n        keyEntities: []\n      });\n      // ZombieJ: Return the count of rest keys. It's safe to refactor if need more info.\n      (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"removeKey\", function (removeKey) {\n        _this.setState(function (prevState) {\n          var nextKeyEntities = prevState.keyEntities.map(function (entity) {\n            if (entity.key !== removeKey) return entity;\n            return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, entity), {}, {\n              status: _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVED\n            });\n          });\n          return {\n            keyEntities: nextKeyEntities\n          };\n        }, function () {\n          var keyEntities = _this.state.keyEntities;\n          var restKeysCount = keyEntities.filter(function (_ref) {\n            var status = _ref.status;\n            return status !== _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVED;\n          }).length;\n          if (restKeysCount === 0 && _this.props.onAllRemoved) {\n            _this.props.onAllRemoved();\n          }\n        });\n      });\n      return _this;\n    }\n    (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(CSSMotionList, [{\n      key: \"render\",\n      value: function render() {\n        var _this2 = this;\n        var keyEntities = this.state.keyEntities;\n        var _this$props = this.props,\n          component = _this$props.component,\n          children = _this$props.children,\n          _onVisibleChanged = _this$props.onVisibleChanged,\n          onAllRemoved = _this$props.onAllRemoved,\n          restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_this$props, _excluded);\n        var Component = component || react__WEBPACK_IMPORTED_MODULE_9__.Fragment;\n        var motionProps = {};\n        MOTION_PROP_NAMES.forEach(function (prop) {\n          motionProps[prop] = restProps[prop];\n          delete restProps[prop];\n        });\n        delete restProps.keys;\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(Component, restProps, keyEntities.map(function (_ref2, index) {\n          var status = _ref2.status,\n            eventProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_ref2, _excluded2);\n          var visible = status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_ADD || status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_KEEP;\n          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(CSSMotion, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, motionProps, {\n            key: eventProps.key,\n            visible: visible,\n            eventProps: eventProps,\n            onVisibleChanged: function onVisibleChanged(changedVisible) {\n              _onVisibleChanged === null || _onVisibleChanged === void 0 || _onVisibleChanged(changedVisible, {\n                key: eventProps.key\n              });\n              if (!changedVisible) {\n                _this2.removeKey(eventProps.key);\n              }\n            }\n          }), function (props, ref) {\n            return children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, props), {}, {\n              index: index\n            }), ref);\n          });\n        }));\n      }\n    }], [{\n      key: \"getDerivedStateFromProps\",\n      value: function getDerivedStateFromProps(_ref3, _ref4) {\n        var keys = _ref3.keys;\n        var keyEntities = _ref4.keyEntities;\n        var parsedKeyObjects = (0,_util_diff__WEBPACK_IMPORTED_MODULE_11__.parseKeys)(keys);\n        var mixedKeyEntities = (0,_util_diff__WEBPACK_IMPORTED_MODULE_11__.diffKeys)(keyEntities, parsedKeyObjects);\n        return {\n          keyEntities: mixedKeyEntities.filter(function (entity) {\n            var prevEntity = keyEntities.find(function (_ref5) {\n              var key = _ref5.key;\n              return entity.key === key;\n            });\n\n            // Remove if already mark as removed\n            if (prevEntity && prevEntity.status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVED && entity.status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVE) {\n              return false;\n            }\n            return true;\n          })\n        };\n      }\n    }]);\n    return CSSMotionList;\n  }(react__WEBPACK_IMPORTED_MODULE_9__.Component);\n  (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(CSSMotionList, \"defaultProps\", {\n    component: 'div'\n  });\n  return CSSMotionList;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (genCSSMotionList(_util_motion__WEBPACK_IMPORTED_MODULE_12__.supportTransition));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/CSSMotionList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/DomWrapper.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-motion/es/DomWrapper.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createSuper.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nvar DomWrapper = /*#__PURE__*/function (_React$Component) {\n  (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(DomWrapper, _React$Component);\n  var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(DomWrapper);\n  function DomWrapper() {\n    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, DomWrapper);\n    return _super.apply(this, arguments);\n  }\n  (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(DomWrapper, [{\n    key: \"render\",\n    value: function render() {\n      return this.props.children;\n    }\n  }]);\n  return DomWrapper;\n}(react__WEBPACK_IMPORTED_MODULE_4__.Component);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DomWrapper);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL0RvbVdyYXBwZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF3RTtBQUNOO0FBQ047QUFDTTtBQUNuQztBQUMvQjtBQUNBLEVBQUUsK0VBQVM7QUFDWCxlQUFlLGtGQUFZO0FBQzNCO0FBQ0EsSUFBSSxxRkFBZTtBQUNuQjtBQUNBO0FBQ0EsRUFBRSxrRkFBWTtBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsQ0FBQyxDQUFDLDRDQUFlO0FBQ2pCLGlFQUFlLFVBQVUiLCJzb3VyY2VzIjpbIkU6XFxQUk9KRUNUU1xccG9zXFxwb3Nmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyYy1tb3Rpb25cXGVzXFxEb21XcmFwcGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfY2xhc3NDYWxsQ2hlY2sgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2NsYXNzQ2FsbENoZWNrXCI7XG5pbXBvcnQgX2NyZWF0ZUNsYXNzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9jcmVhdGVDbGFzc1wiO1xuaW1wb3J0IF9pbmhlcml0cyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vaW5oZXJpdHNcIjtcbmltcG9ydCBfY3JlYXRlU3VwZXIgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2NyZWF0ZVN1cGVyXCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG52YXIgRG9tV3JhcHBlciA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoX1JlYWN0JENvbXBvbmVudCkge1xuICBfaW5oZXJpdHMoRG9tV3JhcHBlciwgX1JlYWN0JENvbXBvbmVudCk7XG4gIHZhciBfc3VwZXIgPSBfY3JlYXRlU3VwZXIoRG9tV3JhcHBlcik7XG4gIGZ1bmN0aW9uIERvbVdyYXBwZXIoKSB7XG4gICAgX2NsYXNzQ2FsbENoZWNrKHRoaXMsIERvbVdyYXBwZXIpO1xuICAgIHJldHVybiBfc3VwZXIuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbiAgfVxuICBfY3JlYXRlQ2xhc3MoRG9tV3JhcHBlciwgW3tcbiAgICBrZXk6IFwicmVuZGVyXCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIHJlbmRlcigpIHtcbiAgICAgIHJldHVybiB0aGlzLnByb3BzLmNoaWxkcmVuO1xuICAgIH1cbiAgfV0pO1xuICByZXR1cm4gRG9tV3JhcHBlcjtcbn0oUmVhY3QuQ29tcG9uZW50KTtcbmV4cG9ydCBkZWZhdWx0IERvbVdyYXBwZXI7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/DomWrapper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/context.js":
/*!**********************************************!*\
  !*** ./node_modules/rc-motion/es/context.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Context: () => (/* binding */ Context),\n/* harmony export */   \"default\": () => (/* binding */ MotionProvider)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _excluded = [\"children\"];\n\nvar Context = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createContext({});\nfunction MotionProvider(_ref) {\n  var children = _ref.children,\n    props = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref, _excluded);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Context.Provider, {\n    value: props\n  }, children);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2NvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMEY7QUFDMUY7QUFDK0I7QUFDeEIsMkJBQTJCLGdEQUFtQixHQUFHO0FBQ3pDO0FBQ2Y7QUFDQSxZQUFZLDhGQUF3QjtBQUNwQyxzQkFBc0IsZ0RBQW1CO0FBQ3pDO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJFOlxcUFJPSkVDVFNcXHBvc1xccG9zZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmMtbW90aW9uXFxlc1xcY29udGV4dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RXaXRob3V0UHJvcGVydGllc1wiO1xudmFyIF9leGNsdWRlZCA9IFtcImNoaWxkcmVuXCJdO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuZXhwb3J0IHZhciBDb250ZXh0ID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUNvbnRleHQoe30pO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTW90aW9uUHJvdmlkZXIoX3JlZikge1xuICB2YXIgY2hpbGRyZW4gPSBfcmVmLmNoaWxkcmVuLFxuICAgIHByb3BzID0gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzKF9yZWYsIF9leGNsdWRlZCk7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChDb250ZXh0LlByb3ZpZGVyLCB7XG4gICAgdmFsdWU6IHByb3BzXG4gIH0sIGNoaWxkcmVuKTtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useDomMotionEvents.js":
/*!***************************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useDomMotionEvents.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _util_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/motion */ \"(ssr)/./node_modules/rc-motion/es/util/motion.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function (onInternalMotionEnd) {\n  var cacheElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n\n  // Remove events\n  function removeMotionEvents(element) {\n    if (element) {\n      element.removeEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.transitionEndName, onInternalMotionEnd);\n      element.removeEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.animationEndName, onInternalMotionEnd);\n    }\n  }\n\n  // Patch events\n  function patchMotionEvents(element) {\n    if (cacheElementRef.current && cacheElementRef.current !== element) {\n      removeMotionEvents(cacheElementRef.current);\n    }\n    if (element && element !== cacheElementRef.current) {\n      element.addEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.transitionEndName, onInternalMotionEnd);\n      element.addEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.animationEndName, onInternalMotionEnd);\n\n      // Save as cache in case dom removed trigger by `motionDeadline`\n      cacheElementRef.current = element;\n    }\n  }\n\n  // Clean up when removed\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    return function () {\n      removeMotionEvents(cacheElementRef.current);\n    };\n  }, []);\n  return [patchMotionEvents, removeMotionEvents];\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useDomMotionEvents.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js":
/*!**********************************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/Dom/canUseDom */ \"(ssr)/./node_modules/rc-util/es/Dom/canUseDom.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n// It's safe to use `useLayoutEffect` but the warning is annoying\nvar useIsomorphicLayoutEffect = (0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_0__[\"default\"])() ? react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_1__.useEffect;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useIsomorphicLayoutEffect);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2hvb2tzL3VzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpRDtBQUNFOztBQUVuRDtBQUNBLGdDQUFnQyxvRUFBUyxLQUFLLGtEQUFlLEdBQUcsNENBQVM7QUFDekUsaUVBQWUseUJBQXlCIiwic291cmNlcyI6WyJFOlxcUFJPSkVDVFNcXHBvc1xccG9zZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmMtbW90aW9uXFxlc1xcaG9va3NcXHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNhblVzZURvbSBmcm9tIFwicmMtdXRpbC9lcy9Eb20vY2FuVXNlRG9tXCI7XG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZUxheW91dEVmZmVjdCB9IGZyb20gJ3JlYWN0JztcblxuLy8gSXQncyBzYWZlIHRvIHVzZSBgdXNlTGF5b3V0RWZmZWN0YCBidXQgdGhlIHdhcm5pbmcgaXMgYW5ub3lpbmdcbnZhciB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0ID0gY2FuVXNlRG9tKCkgPyB1c2VMYXlvdXRFZmZlY3QgOiB1c2VFZmZlY3Q7XG5leHBvcnQgZGVmYXVsdCB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0OyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useNextFrame.js":
/*!*********************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useNextFrame.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function () {\n  var nextFrameRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  function cancelNextFrame() {\n    rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__[\"default\"].cancel(nextFrameRef.current);\n  }\n  function nextFrame(callback) {\n    var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 2;\n    cancelNextFrame();\n    var nextFrameId = (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(function () {\n      if (delay <= 1) {\n        callback({\n          isCanceled: function isCanceled() {\n            return nextFrameId !== nextFrameRef.current;\n          }\n        });\n      } else {\n        nextFrame(callback, delay - 1);\n      }\n    });\n    nextFrameRef.current = nextFrameId;\n  }\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [nextFrame, cancelNextFrame];\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2hvb2tzL3VzZU5leHRGcmFtZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWlDO0FBQ0Y7QUFDL0IsaUVBQWdCO0FBQ2hCLHFCQUFxQix5Q0FBWTtBQUNqQztBQUNBLElBQUksc0RBQUc7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwwREFBRztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULFFBQVE7QUFDUjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIkU6XFxQUk9KRUNUU1xccG9zXFxwb3Nmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyYy1tb3Rpb25cXGVzXFxob29rc1xcdXNlTmV4dEZyYW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCByYWYgZnJvbSBcInJjLXV0aWwvZXMvcmFmXCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5leHBvcnQgZGVmYXVsdCAoZnVuY3Rpb24gKCkge1xuICB2YXIgbmV4dEZyYW1lUmVmID0gUmVhY3QudXNlUmVmKG51bGwpO1xuICBmdW5jdGlvbiBjYW5jZWxOZXh0RnJhbWUoKSB7XG4gICAgcmFmLmNhbmNlbChuZXh0RnJhbWVSZWYuY3VycmVudCk7XG4gIH1cbiAgZnVuY3Rpb24gbmV4dEZyYW1lKGNhbGxiYWNrKSB7XG4gICAgdmFyIGRlbGF5ID0gYXJndW1lbnRzLmxlbmd0aCA+IDEgJiYgYXJndW1lbnRzWzFdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMV0gOiAyO1xuICAgIGNhbmNlbE5leHRGcmFtZSgpO1xuICAgIHZhciBuZXh0RnJhbWVJZCA9IHJhZihmdW5jdGlvbiAoKSB7XG4gICAgICBpZiAoZGVsYXkgPD0gMSkge1xuICAgICAgICBjYWxsYmFjayh7XG4gICAgICAgICAgaXNDYW5jZWxlZDogZnVuY3Rpb24gaXNDYW5jZWxlZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBuZXh0RnJhbWVJZCAhPT0gbmV4dEZyYW1lUmVmLmN1cnJlbnQ7XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG5leHRGcmFtZShjYWxsYmFjaywgZGVsYXkgLSAxKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgICBuZXh0RnJhbWVSZWYuY3VycmVudCA9IG5leHRGcmFtZUlkO1xuICB9XG4gIFJlYWN0LnVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgIGNhbmNlbE5leHRGcmFtZSgpO1xuICAgIH07XG4gIH0sIFtdKTtcbiAgcmV0dXJuIFtuZXh0RnJhbWUsIGNhbmNlbE5leHRGcmFtZV07XG59KTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useNextFrame.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useStatus.js":
/*!******************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useStatus.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useStatus)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-util */ \"(ssr)/./node_modules/rc-util/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-util/es/hooks/useState */ \"(ssr)/./node_modules/rc-util/es/hooks/useState.js\");\n/* harmony import */ var rc_util_es_hooks_useSyncState__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/hooks/useSyncState */ \"(ssr)/./node_modules/rc-util/es/hooks/useSyncState.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _interface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../interface */ \"(ssr)/./node_modules/rc-motion/es/interface.js\");\n/* harmony import */ var _useDomMotionEvents__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./useDomMotionEvents */ \"(ssr)/./node_modules/rc-motion/es/hooks/useDomMotionEvents.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./useIsomorphicLayoutEffect */ \"(ssr)/./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _useStepQueue__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./useStepQueue */ \"(ssr)/./node_modules/rc-motion/es/hooks/useStepQueue.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nfunction useStatus(supportMotion, visible, getElement, _ref) {\n  var _ref$motionEnter = _ref.motionEnter,\n    motionEnter = _ref$motionEnter === void 0 ? true : _ref$motionEnter,\n    _ref$motionAppear = _ref.motionAppear,\n    motionAppear = _ref$motionAppear === void 0 ? true : _ref$motionAppear,\n    _ref$motionLeave = _ref.motionLeave,\n    motionLeave = _ref$motionLeave === void 0 ? true : _ref$motionLeave,\n    motionDeadline = _ref.motionDeadline,\n    motionLeaveImmediately = _ref.motionLeaveImmediately,\n    onAppearPrepare = _ref.onAppearPrepare,\n    onEnterPrepare = _ref.onEnterPrepare,\n    onLeavePrepare = _ref.onLeavePrepare,\n    onAppearStart = _ref.onAppearStart,\n    onEnterStart = _ref.onEnterStart,\n    onLeaveStart = _ref.onLeaveStart,\n    onAppearActive = _ref.onAppearActive,\n    onEnterActive = _ref.onEnterActive,\n    onLeaveActive = _ref.onLeaveActive,\n    onAppearEnd = _ref.onAppearEnd,\n    onEnterEnd = _ref.onEnterEnd,\n    onLeaveEnd = _ref.onLeaveEnd,\n    onVisibleChanged = _ref.onVisibleChanged;\n  // Used for outer render usage to avoid `visible: false & status: none` to render nothing\n  var _useState = (0,rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState, 2),\n    asyncVisible = _useState2[0],\n    setAsyncVisible = _useState2[1];\n  var _useSyncState = (0,rc_util_es_hooks_useSyncState__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE),\n    _useSyncState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useSyncState, 2),\n    getStatus = _useSyncState2[0],\n    setStatus = _useSyncState2[1];\n  var _useState3 = (0,rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(null),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState3, 2),\n    style = _useState4[0],\n    setStyle = _useState4[1];\n  var currentStatus = getStatus();\n  var mountedRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(false);\n  var deadlineRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(null);\n\n  // =========================== Dom Node ===========================\n  function getDomElement() {\n    return getElement();\n  }\n\n  // ========================== Motion End ==========================\n  var activeRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(false);\n\n  /**\n   * Clean up status & style\n   */\n  function updateMotionEndStatus() {\n    setStatus(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE);\n    setStyle(null, true);\n  }\n  var onInternalMotionEnd = (0,rc_util__WEBPACK_IMPORTED_MODULE_3__.useEvent)(function (event) {\n    var status = getStatus();\n    // Do nothing since not in any transition status.\n    // This may happen when `motionDeadline` trigger.\n    if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE) {\n      return;\n    }\n    var element = getDomElement();\n    if (event && !event.deadline && event.target !== element) {\n      // event exists\n      // not initiated by deadline\n      // transitionEnd not fired by inner elements\n      return;\n    }\n    var currentActive = activeRef.current;\n    var canEnd;\n    if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR && currentActive) {\n      canEnd = onAppearEnd === null || onAppearEnd === void 0 ? void 0 : onAppearEnd(element, event);\n    } else if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER && currentActive) {\n      canEnd = onEnterEnd === null || onEnterEnd === void 0 ? void 0 : onEnterEnd(element, event);\n    } else if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE && currentActive) {\n      canEnd = onLeaveEnd === null || onLeaveEnd === void 0 ? void 0 : onLeaveEnd(element, event);\n    }\n\n    // Only update status when `canEnd` and not destroyed\n    if (currentActive && canEnd !== false) {\n      updateMotionEndStatus();\n    }\n  });\n  var _useDomMotionEvents = (0,_useDomMotionEvents__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(onInternalMotionEnd),\n    _useDomMotionEvents2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useDomMotionEvents, 1),\n    patchMotionEvents = _useDomMotionEvents2[0];\n\n  // ============================= Step =============================\n  var getEventHandlers = function getEventHandlers(targetStatus) {\n    switch (targetStatus) {\n      case _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR:\n        return (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE, onAppearPrepare), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START, onAppearStart), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE, onAppearActive);\n      case _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER:\n        return (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE, onEnterPrepare), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START, onEnterStart), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE, onEnterActive);\n      case _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE:\n        return (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE, onLeavePrepare), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START, onLeaveStart), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE, onLeaveActive);\n      default:\n        return {};\n    }\n  };\n  var eventHandlers = react__WEBPACK_IMPORTED_MODULE_6__.useMemo(function () {\n    return getEventHandlers(currentStatus);\n  }, [currentStatus]);\n  var _useStepQueue = (0,_useStepQueue__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(currentStatus, !supportMotion, function (newStep) {\n      // Only prepare step can be skip\n      if (newStep === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE) {\n        var onPrepare = eventHandlers[_interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE];\n        if (!onPrepare) {\n          return _useStepQueue__WEBPACK_IMPORTED_MODULE_10__.SkipStep;\n        }\n        return onPrepare(getDomElement());\n      }\n\n      // Rest step is sync update\n      if (step in eventHandlers) {\n        var _eventHandlers$step;\n        setStyle(((_eventHandlers$step = eventHandlers[step]) === null || _eventHandlers$step === void 0 ? void 0 : _eventHandlers$step.call(eventHandlers, getDomElement(), null)) || null);\n      }\n      if (step === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE && currentStatus !== _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE) {\n        // Patch events when motion needed\n        patchMotionEvents(getDomElement());\n        if (motionDeadline > 0) {\n          clearTimeout(deadlineRef.current);\n          deadlineRef.current = setTimeout(function () {\n            onInternalMotionEnd({\n              deadline: true\n            });\n          }, motionDeadline);\n        }\n      }\n      if (step === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARED) {\n        updateMotionEndStatus();\n      }\n      return _useStepQueue__WEBPACK_IMPORTED_MODULE_10__.DoStep;\n    }),\n    _useStepQueue2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useStepQueue, 2),\n    startStep = _useStepQueue2[0],\n    step = _useStepQueue2[1];\n  var active = (0,_useStepQueue__WEBPACK_IMPORTED_MODULE_10__.isActive)(step);\n  activeRef.current = active;\n\n  // ============================ Status ============================\n  var visibleRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(null);\n\n  // Update with new status\n  (0,_useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function () {\n    // When use Suspense, the `visible` will repeat trigger,\n    // But not real change of the `visible`, we need to skip it.\n    // https://github.com/ant-design/ant-design/issues/44379\n    if (mountedRef.current && visibleRef.current === visible) {\n      return;\n    }\n    setAsyncVisible(visible);\n    var isMounted = mountedRef.current;\n    mountedRef.current = true;\n\n    // if (!supportMotion) {\n    //   return;\n    // }\n\n    var nextStatus;\n\n    // Appear\n    if (!isMounted && visible && motionAppear) {\n      nextStatus = _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR;\n    }\n\n    // Enter\n    if (isMounted && visible && motionEnter) {\n      nextStatus = _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER;\n    }\n\n    // Leave\n    if (isMounted && !visible && motionLeave || !isMounted && motionLeaveImmediately && !visible && motionLeave) {\n      nextStatus = _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE;\n    }\n    var nextEventHandlers = getEventHandlers(nextStatus);\n\n    // Update to next status\n    if (nextStatus && (supportMotion || nextEventHandlers[_interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE])) {\n      setStatus(nextStatus);\n      startStep();\n    } else {\n      // Set back in case no motion but prev status has prepare step\n      setStatus(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE);\n    }\n    visibleRef.current = visible;\n  }, [visible]);\n\n  // ============================ Effect ============================\n  // Reset when motion changed\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    if (\n    // Cancel appear\n    currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR && !motionAppear ||\n    // Cancel enter\n    currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER && !motionEnter ||\n    // Cancel leave\n    currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE && !motionLeave) {\n      setStatus(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE);\n    }\n  }, [motionAppear, motionEnter, motionLeave]);\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    return function () {\n      mountedRef.current = false;\n      clearTimeout(deadlineRef.current);\n    };\n  }, []);\n\n  // Trigger `onVisibleChanged`\n  var firstMountChangeRef = react__WEBPACK_IMPORTED_MODULE_6__.useRef(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    // [visible & motion not end] => [!visible & motion end] still need trigger onVisibleChanged\n    if (asyncVisible) {\n      firstMountChangeRef.current = true;\n    }\n    if (asyncVisible !== undefined && currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE) {\n      // Skip first render is invisible since it's nothing changed\n      if (firstMountChangeRef.current || asyncVisible) {\n        onVisibleChanged === null || onVisibleChanged === void 0 || onVisibleChanged(asyncVisible);\n      }\n      firstMountChangeRef.current = true;\n    }\n  }, [asyncVisible, currentStatus]);\n\n  // ============================ Styles ============================\n  var mergedStyle = style;\n  if (eventHandlers[_interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE] && step === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START) {\n    mergedStyle = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n      transition: 'none'\n    }, mergedStyle);\n  }\n  return [currentStatus, step, mergedStyle, asyncVisible !== null && asyncVisible !== void 0 ? asyncVisible : visible];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2hvb2tzL3VzZVN0YXR1cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQXFFO0FBQ0c7QUFDRjtBQUNuQztBQUNjO0FBQ1E7QUFDMUI7QUFDVztBQUNrRztBQUN0RjtBQUNjO0FBQ007QUFDM0Q7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixxRUFBUTtBQUMxQixpQkFBaUIsb0ZBQWM7QUFDL0I7QUFDQTtBQUNBLHNCQUFzQix5RUFBWSxDQUFDLG1EQUFXO0FBQzlDLHFCQUFxQixvRkFBYztBQUNuQztBQUNBO0FBQ0EsbUJBQW1CLHFFQUFRO0FBQzNCLGlCQUFpQixvRkFBYztBQUMvQjtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsNkNBQU07QUFDekIsb0JBQW9CLDZDQUFNOztBQUUxQjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGtCQUFrQiw2Q0FBTTs7QUFFeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLG1EQUFXO0FBQ3pCO0FBQ0E7QUFDQSw0QkFBNEIsaURBQVE7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLG1EQUFXO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIscURBQWE7QUFDaEM7QUFDQSxNQUFNLG9CQUFvQixvREFBWTtBQUN0QztBQUNBLE1BQU0sb0JBQW9CLG9EQUFZO0FBQ3RDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsNEJBQTRCLCtEQUFrQjtBQUM5QywyQkFBMkIsb0ZBQWM7QUFDekM7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxxREFBYTtBQUN4QixlQUFlLHFGQUFlLENBQUMscUZBQWUsQ0FBQyxxRkFBZSxHQUFHLEVBQUUsb0RBQVksb0JBQW9CLGtEQUFVLGtCQUFrQixtREFBVztBQUMxSSxXQUFXLG9EQUFZO0FBQ3ZCLGVBQWUscUZBQWUsQ0FBQyxxRkFBZSxDQUFDLHFGQUFlLEdBQUcsRUFBRSxvREFBWSxtQkFBbUIsa0RBQVUsaUJBQWlCLG1EQUFXO0FBQ3hJLFdBQVcsb0RBQVk7QUFDdkIsZUFBZSxxRkFBZSxDQUFDLHFGQUFlLENBQUMscUZBQWUsR0FBRyxFQUFFLG9EQUFZLG1CQUFtQixrREFBVSxpQkFBaUIsbURBQVc7QUFDeEk7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsMENBQWE7QUFDbkM7QUFDQSxHQUFHO0FBQ0gsc0JBQXNCLDBEQUFZO0FBQ2xDO0FBQ0Esc0JBQXNCLG9EQUFZO0FBQ2xDLHNDQUFzQyxvREFBWTtBQUNsRDtBQUNBLGlCQUFpQixvREFBUTtBQUN6QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixtREFBVyxzQkFBc0IsbURBQVc7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsV0FBVztBQUNYO0FBQ0E7QUFDQSxtQkFBbUIscURBQWE7QUFDaEM7QUFDQTtBQUNBLGFBQWEsa0RBQU07QUFDbkIsS0FBSztBQUNMLHFCQUFxQixvRkFBYztBQUNuQztBQUNBO0FBQ0EsZUFBZSx3REFBUTtBQUN2Qjs7QUFFQTtBQUNBLG1CQUFtQiw2Q0FBTTs7QUFFekI7QUFDQSxFQUFFLHNFQUF5QjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQSxtQkFBbUIscURBQWE7QUFDaEM7O0FBRUE7QUFDQTtBQUNBLG1CQUFtQixvREFBWTtBQUMvQjs7QUFFQTtBQUNBO0FBQ0EsbUJBQW1CLG9EQUFZO0FBQy9CO0FBQ0E7O0FBRUE7QUFDQSwwREFBMEQsb0RBQVk7QUFDdEU7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLGdCQUFnQixtREFBVztBQUMzQjtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0EsRUFBRSxnREFBUztBQUNYO0FBQ0E7QUFDQSxzQkFBc0IscURBQWE7QUFDbkM7QUFDQSxzQkFBc0Isb0RBQVk7QUFDbEM7QUFDQSxzQkFBc0Isb0RBQVk7QUFDbEMsZ0JBQWdCLG1EQUFXO0FBQzNCO0FBQ0EsR0FBRztBQUNILEVBQUUsZ0RBQVM7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSw0QkFBNEIseUNBQVk7QUFDeEMsRUFBRSxnREFBUztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0RBQXdELG1EQUFXO0FBQ25FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBLG9CQUFvQixvREFBWSxjQUFjLGtEQUFVO0FBQ3hELGtCQUFrQixvRkFBYTtBQUMvQjtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkU6XFxQUk9KRUNUU1xccG9zXFxwb3Nmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyYy1tb3Rpb25cXGVzXFxob29rc1xcdXNlU3RhdHVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfb2JqZWN0U3ByZWFkIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyXCI7XG5pbXBvcnQgX2RlZmluZVByb3BlcnR5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9kZWZpbmVQcm9wZXJ0eVwiO1xuaW1wb3J0IF9zbGljZWRUb0FycmF5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9zbGljZWRUb0FycmF5XCI7XG5pbXBvcnQgeyB1c2VFdmVudCB9IGZyb20gJ3JjLXV0aWwnO1xuaW1wb3J0IHVzZVN0YXRlIGZyb20gXCJyYy11dGlsL2VzL2hvb2tzL3VzZVN0YXRlXCI7XG5pbXBvcnQgdXNlU3luY1N0YXRlIGZyb20gXCJyYy11dGlsL2VzL2hvb2tzL3VzZVN5bmNTdGF0ZVwiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBTVEFUVVNfQVBQRUFSLCBTVEFUVVNfRU5URVIsIFNUQVRVU19MRUFWRSwgU1RBVFVTX05PTkUsIFNURVBfQUNUSVZFLCBTVEVQX1BSRVBBUkUsIFNURVBfUFJFUEFSRUQsIFNURVBfU1RBUlQgfSBmcm9tIFwiLi4vaW50ZXJmYWNlXCI7XG5pbXBvcnQgdXNlRG9tTW90aW9uRXZlbnRzIGZyb20gXCIuL3VzZURvbU1vdGlvbkV2ZW50c1wiO1xuaW1wb3J0IHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QgZnJvbSBcIi4vdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdFwiO1xuaW1wb3J0IHVzZVN0ZXBRdWV1ZSwgeyBEb1N0ZXAsIGlzQWN0aXZlLCBTa2lwU3RlcCB9IGZyb20gXCIuL3VzZVN0ZXBRdWV1ZVwiO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlU3RhdHVzKHN1cHBvcnRNb3Rpb24sIHZpc2libGUsIGdldEVsZW1lbnQsIF9yZWYpIHtcbiAgdmFyIF9yZWYkbW90aW9uRW50ZXIgPSBfcmVmLm1vdGlvbkVudGVyLFxuICAgIG1vdGlvbkVudGVyID0gX3JlZiRtb3Rpb25FbnRlciA9PT0gdm9pZCAwID8gdHJ1ZSA6IF9yZWYkbW90aW9uRW50ZXIsXG4gICAgX3JlZiRtb3Rpb25BcHBlYXIgPSBfcmVmLm1vdGlvbkFwcGVhcixcbiAgICBtb3Rpb25BcHBlYXIgPSBfcmVmJG1vdGlvbkFwcGVhciA9PT0gdm9pZCAwID8gdHJ1ZSA6IF9yZWYkbW90aW9uQXBwZWFyLFxuICAgIF9yZWYkbW90aW9uTGVhdmUgPSBfcmVmLm1vdGlvbkxlYXZlLFxuICAgIG1vdGlvbkxlYXZlID0gX3JlZiRtb3Rpb25MZWF2ZSA9PT0gdm9pZCAwID8gdHJ1ZSA6IF9yZWYkbW90aW9uTGVhdmUsXG4gICAgbW90aW9uRGVhZGxpbmUgPSBfcmVmLm1vdGlvbkRlYWRsaW5lLFxuICAgIG1vdGlvbkxlYXZlSW1tZWRpYXRlbHkgPSBfcmVmLm1vdGlvbkxlYXZlSW1tZWRpYXRlbHksXG4gICAgb25BcHBlYXJQcmVwYXJlID0gX3JlZi5vbkFwcGVhclByZXBhcmUsXG4gICAgb25FbnRlclByZXBhcmUgPSBfcmVmLm9uRW50ZXJQcmVwYXJlLFxuICAgIG9uTGVhdmVQcmVwYXJlID0gX3JlZi5vbkxlYXZlUHJlcGFyZSxcbiAgICBvbkFwcGVhclN0YXJ0ID0gX3JlZi5vbkFwcGVhclN0YXJ0LFxuICAgIG9uRW50ZXJTdGFydCA9IF9yZWYub25FbnRlclN0YXJ0LFxuICAgIG9uTGVhdmVTdGFydCA9IF9yZWYub25MZWF2ZVN0YXJ0LFxuICAgIG9uQXBwZWFyQWN0aXZlID0gX3JlZi5vbkFwcGVhckFjdGl2ZSxcbiAgICBvbkVudGVyQWN0aXZlID0gX3JlZi5vbkVudGVyQWN0aXZlLFxuICAgIG9uTGVhdmVBY3RpdmUgPSBfcmVmLm9uTGVhdmVBY3RpdmUsXG4gICAgb25BcHBlYXJFbmQgPSBfcmVmLm9uQXBwZWFyRW5kLFxuICAgIG9uRW50ZXJFbmQgPSBfcmVmLm9uRW50ZXJFbmQsXG4gICAgb25MZWF2ZUVuZCA9IF9yZWYub25MZWF2ZUVuZCxcbiAgICBvblZpc2libGVDaGFuZ2VkID0gX3JlZi5vblZpc2libGVDaGFuZ2VkO1xuICAvLyBVc2VkIGZvciBvdXRlciByZW5kZXIgdXNhZ2UgdG8gYXZvaWQgYHZpc2libGU6IGZhbHNlICYgc3RhdHVzOiBub25lYCB0byByZW5kZXIgbm90aGluZ1xuICB2YXIgX3VzZVN0YXRlID0gdXNlU3RhdGUoKSxcbiAgICBfdXNlU3RhdGUyID0gX3NsaWNlZFRvQXJyYXkoX3VzZVN0YXRlLCAyKSxcbiAgICBhc3luY1Zpc2libGUgPSBfdXNlU3RhdGUyWzBdLFxuICAgIHNldEFzeW5jVmlzaWJsZSA9IF91c2VTdGF0ZTJbMV07XG4gIHZhciBfdXNlU3luY1N0YXRlID0gdXNlU3luY1N0YXRlKFNUQVRVU19OT05FKSxcbiAgICBfdXNlU3luY1N0YXRlMiA9IF9zbGljZWRUb0FycmF5KF91c2VTeW5jU3RhdGUsIDIpLFxuICAgIGdldFN0YXR1cyA9IF91c2VTeW5jU3RhdGUyWzBdLFxuICAgIHNldFN0YXR1cyA9IF91c2VTeW5jU3RhdGUyWzFdO1xuICB2YXIgX3VzZVN0YXRlMyA9IHVzZVN0YXRlKG51bGwpLFxuICAgIF91c2VTdGF0ZTQgPSBfc2xpY2VkVG9BcnJheShfdXNlU3RhdGUzLCAyKSxcbiAgICBzdHlsZSA9IF91c2VTdGF0ZTRbMF0sXG4gICAgc2V0U3R5bGUgPSBfdXNlU3RhdGU0WzFdO1xuICB2YXIgY3VycmVudFN0YXR1cyA9IGdldFN0YXR1cygpO1xuICB2YXIgbW91bnRlZFJlZiA9IHVzZVJlZihmYWxzZSk7XG4gIHZhciBkZWFkbGluZVJlZiA9IHVzZVJlZihudWxsKTtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT0gRG9tIE5vZGUgPT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIGZ1bmN0aW9uIGdldERvbUVsZW1lbnQoKSB7XG4gICAgcmV0dXJuIGdldEVsZW1lbnQoKTtcbiAgfVxuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09IE1vdGlvbiBFbmQgPT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgdmFyIGFjdGl2ZVJlZiA9IHVzZVJlZihmYWxzZSk7XG5cbiAgLyoqXG4gICAqIENsZWFuIHVwIHN0YXR1cyAmIHN0eWxlXG4gICAqL1xuICBmdW5jdGlvbiB1cGRhdGVNb3Rpb25FbmRTdGF0dXMoKSB7XG4gICAgc2V0U3RhdHVzKFNUQVRVU19OT05FKTtcbiAgICBzZXRTdHlsZShudWxsLCB0cnVlKTtcbiAgfVxuICB2YXIgb25JbnRlcm5hbE1vdGlvbkVuZCA9IHVzZUV2ZW50KGZ1bmN0aW9uIChldmVudCkge1xuICAgIHZhciBzdGF0dXMgPSBnZXRTdGF0dXMoKTtcbiAgICAvLyBEbyBub3RoaW5nIHNpbmNlIG5vdCBpbiBhbnkgdHJhbnNpdGlvbiBzdGF0dXMuXG4gICAgLy8gVGhpcyBtYXkgaGFwcGVuIHdoZW4gYG1vdGlvbkRlYWRsaW5lYCB0cmlnZ2VyLlxuICAgIGlmIChzdGF0dXMgPT09IFNUQVRVU19OT05FKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHZhciBlbGVtZW50ID0gZ2V0RG9tRWxlbWVudCgpO1xuICAgIGlmIChldmVudCAmJiAhZXZlbnQuZGVhZGxpbmUgJiYgZXZlbnQudGFyZ2V0ICE9PSBlbGVtZW50KSB7XG4gICAgICAvLyBldmVudCBleGlzdHNcbiAgICAgIC8vIG5vdCBpbml0aWF0ZWQgYnkgZGVhZGxpbmVcbiAgICAgIC8vIHRyYW5zaXRpb25FbmQgbm90IGZpcmVkIGJ5IGlubmVyIGVsZW1lbnRzXG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHZhciBjdXJyZW50QWN0aXZlID0gYWN0aXZlUmVmLmN1cnJlbnQ7XG4gICAgdmFyIGNhbkVuZDtcbiAgICBpZiAoc3RhdHVzID09PSBTVEFUVVNfQVBQRUFSICYmIGN1cnJlbnRBY3RpdmUpIHtcbiAgICAgIGNhbkVuZCA9IG9uQXBwZWFyRW5kID09PSBudWxsIHx8IG9uQXBwZWFyRW5kID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvbkFwcGVhckVuZChlbGVtZW50LCBldmVudCk7XG4gICAgfSBlbHNlIGlmIChzdGF0dXMgPT09IFNUQVRVU19FTlRFUiAmJiBjdXJyZW50QWN0aXZlKSB7XG4gICAgICBjYW5FbmQgPSBvbkVudGVyRW5kID09PSBudWxsIHx8IG9uRW50ZXJFbmQgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG9uRW50ZXJFbmQoZWxlbWVudCwgZXZlbnQpO1xuICAgIH0gZWxzZSBpZiAoc3RhdHVzID09PSBTVEFUVVNfTEVBVkUgJiYgY3VycmVudEFjdGl2ZSkge1xuICAgICAgY2FuRW5kID0gb25MZWF2ZUVuZCA9PT0gbnVsbCB8fCBvbkxlYXZlRW5kID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvbkxlYXZlRW5kKGVsZW1lbnQsIGV2ZW50KTtcbiAgICB9XG5cbiAgICAvLyBPbmx5IHVwZGF0ZSBzdGF0dXMgd2hlbiBgY2FuRW5kYCBhbmQgbm90IGRlc3Ryb3llZFxuICAgIGlmIChjdXJyZW50QWN0aXZlICYmIGNhbkVuZCAhPT0gZmFsc2UpIHtcbiAgICAgIHVwZGF0ZU1vdGlvbkVuZFN0YXR1cygpO1xuICAgIH1cbiAgfSk7XG4gIHZhciBfdXNlRG9tTW90aW9uRXZlbnRzID0gdXNlRG9tTW90aW9uRXZlbnRzKG9uSW50ZXJuYWxNb3Rpb25FbmQpLFxuICAgIF91c2VEb21Nb3Rpb25FdmVudHMyID0gX3NsaWNlZFRvQXJyYXkoX3VzZURvbU1vdGlvbkV2ZW50cywgMSksXG4gICAgcGF0Y2hNb3Rpb25FdmVudHMgPSBfdXNlRG9tTW90aW9uRXZlbnRzMlswXTtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PSBTdGVwID09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIHZhciBnZXRFdmVudEhhbmRsZXJzID0gZnVuY3Rpb24gZ2V0RXZlbnRIYW5kbGVycyh0YXJnZXRTdGF0dXMpIHtcbiAgICBzd2l0Y2ggKHRhcmdldFN0YXR1cykge1xuICAgICAgY2FzZSBTVEFUVVNfQVBQRUFSOlxuICAgICAgICByZXR1cm4gX2RlZmluZVByb3BlcnR5KF9kZWZpbmVQcm9wZXJ0eShfZGVmaW5lUHJvcGVydHkoe30sIFNURVBfUFJFUEFSRSwgb25BcHBlYXJQcmVwYXJlKSwgU1RFUF9TVEFSVCwgb25BcHBlYXJTdGFydCksIFNURVBfQUNUSVZFLCBvbkFwcGVhckFjdGl2ZSk7XG4gICAgICBjYXNlIFNUQVRVU19FTlRFUjpcbiAgICAgICAgcmV0dXJuIF9kZWZpbmVQcm9wZXJ0eShfZGVmaW5lUHJvcGVydHkoX2RlZmluZVByb3BlcnR5KHt9LCBTVEVQX1BSRVBBUkUsIG9uRW50ZXJQcmVwYXJlKSwgU1RFUF9TVEFSVCwgb25FbnRlclN0YXJ0KSwgU1RFUF9BQ1RJVkUsIG9uRW50ZXJBY3RpdmUpO1xuICAgICAgY2FzZSBTVEFUVVNfTEVBVkU6XG4gICAgICAgIHJldHVybiBfZGVmaW5lUHJvcGVydHkoX2RlZmluZVByb3BlcnR5KF9kZWZpbmVQcm9wZXJ0eSh7fSwgU1RFUF9QUkVQQVJFLCBvbkxlYXZlUHJlcGFyZSksIFNURVBfU1RBUlQsIG9uTGVhdmVTdGFydCksIFNURVBfQUNUSVZFLCBvbkxlYXZlQWN0aXZlKTtcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiB7fTtcbiAgICB9XG4gIH07XG4gIHZhciBldmVudEhhbmRsZXJzID0gUmVhY3QudXNlTWVtbyhmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIGdldEV2ZW50SGFuZGxlcnMoY3VycmVudFN0YXR1cyk7XG4gIH0sIFtjdXJyZW50U3RhdHVzXSk7XG4gIHZhciBfdXNlU3RlcFF1ZXVlID0gdXNlU3RlcFF1ZXVlKGN1cnJlbnRTdGF0dXMsICFzdXBwb3J0TW90aW9uLCBmdW5jdGlvbiAobmV3U3RlcCkge1xuICAgICAgLy8gT25seSBwcmVwYXJlIHN0ZXAgY2FuIGJlIHNraXBcbiAgICAgIGlmIChuZXdTdGVwID09PSBTVEVQX1BSRVBBUkUpIHtcbiAgICAgICAgdmFyIG9uUHJlcGFyZSA9IGV2ZW50SGFuZGxlcnNbU1RFUF9QUkVQQVJFXTtcbiAgICAgICAgaWYgKCFvblByZXBhcmUpIHtcbiAgICAgICAgICByZXR1cm4gU2tpcFN0ZXA7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG9uUHJlcGFyZShnZXREb21FbGVtZW50KCkpO1xuICAgICAgfVxuXG4gICAgICAvLyBSZXN0IHN0ZXAgaXMgc3luYyB1cGRhdGVcbiAgICAgIGlmIChzdGVwIGluIGV2ZW50SGFuZGxlcnMpIHtcbiAgICAgICAgdmFyIF9ldmVudEhhbmRsZXJzJHN0ZXA7XG4gICAgICAgIHNldFN0eWxlKCgoX2V2ZW50SGFuZGxlcnMkc3RlcCA9IGV2ZW50SGFuZGxlcnNbc3RlcF0pID09PSBudWxsIHx8IF9ldmVudEhhbmRsZXJzJHN0ZXAgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9ldmVudEhhbmRsZXJzJHN0ZXAuY2FsbChldmVudEhhbmRsZXJzLCBnZXREb21FbGVtZW50KCksIG51bGwpKSB8fCBudWxsKTtcbiAgICAgIH1cbiAgICAgIGlmIChzdGVwID09PSBTVEVQX0FDVElWRSAmJiBjdXJyZW50U3RhdHVzICE9PSBTVEFUVVNfTk9ORSkge1xuICAgICAgICAvLyBQYXRjaCBldmVudHMgd2hlbiBtb3Rpb24gbmVlZGVkXG4gICAgICAgIHBhdGNoTW90aW9uRXZlbnRzKGdldERvbUVsZW1lbnQoKSk7XG4gICAgICAgIGlmIChtb3Rpb25EZWFkbGluZSA+IDApIHtcbiAgICAgICAgICBjbGVhclRpbWVvdXQoZGVhZGxpbmVSZWYuY3VycmVudCk7XG4gICAgICAgICAgZGVhZGxpbmVSZWYuY3VycmVudCA9IHNldFRpbWVvdXQoZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgb25JbnRlcm5hbE1vdGlvbkVuZCh7XG4gICAgICAgICAgICAgIGRlYWRsaW5lOiB0cnVlXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICB9LCBtb3Rpb25EZWFkbGluZSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIGlmIChzdGVwID09PSBTVEVQX1BSRVBBUkVEKSB7XG4gICAgICAgIHVwZGF0ZU1vdGlvbkVuZFN0YXR1cygpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIERvU3RlcDtcbiAgICB9KSxcbiAgICBfdXNlU3RlcFF1ZXVlMiA9IF9zbGljZWRUb0FycmF5KF91c2VTdGVwUXVldWUsIDIpLFxuICAgIHN0YXJ0U3RlcCA9IF91c2VTdGVwUXVldWUyWzBdLFxuICAgIHN0ZXAgPSBfdXNlU3RlcFF1ZXVlMlsxXTtcbiAgdmFyIGFjdGl2ZSA9IGlzQWN0aXZlKHN0ZXApO1xuICBhY3RpdmVSZWYuY3VycmVudCA9IGFjdGl2ZTtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09IFN0YXR1cyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIHZhciB2aXNpYmxlUmVmID0gdXNlUmVmKG51bGwpO1xuXG4gIC8vIFVwZGF0ZSB3aXRoIG5ldyBzdGF0dXNcbiAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgLy8gV2hlbiB1c2UgU3VzcGVuc2UsIHRoZSBgdmlzaWJsZWAgd2lsbCByZXBlYXQgdHJpZ2dlcixcbiAgICAvLyBCdXQgbm90IHJlYWwgY2hhbmdlIG9mIHRoZSBgdmlzaWJsZWAsIHdlIG5lZWQgdG8gc2tpcCBpdC5cbiAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vYW50LWRlc2lnbi9hbnQtZGVzaWduL2lzc3Vlcy80NDM3OVxuICAgIGlmIChtb3VudGVkUmVmLmN1cnJlbnQgJiYgdmlzaWJsZVJlZi5jdXJyZW50ID09PSB2aXNpYmxlKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHNldEFzeW5jVmlzaWJsZSh2aXNpYmxlKTtcbiAgICB2YXIgaXNNb3VudGVkID0gbW91bnRlZFJlZi5jdXJyZW50O1xuICAgIG1vdW50ZWRSZWYuY3VycmVudCA9IHRydWU7XG5cbiAgICAvLyBpZiAoIXN1cHBvcnRNb3Rpb24pIHtcbiAgICAvLyAgIHJldHVybjtcbiAgICAvLyB9XG5cbiAgICB2YXIgbmV4dFN0YXR1cztcblxuICAgIC8vIEFwcGVhclxuICAgIGlmICghaXNNb3VudGVkICYmIHZpc2libGUgJiYgbW90aW9uQXBwZWFyKSB7XG4gICAgICBuZXh0U3RhdHVzID0gU1RBVFVTX0FQUEVBUjtcbiAgICB9XG5cbiAgICAvLyBFbnRlclxuICAgIGlmIChpc01vdW50ZWQgJiYgdmlzaWJsZSAmJiBtb3Rpb25FbnRlcikge1xuICAgICAgbmV4dFN0YXR1cyA9IFNUQVRVU19FTlRFUjtcbiAgICB9XG5cbiAgICAvLyBMZWF2ZVxuICAgIGlmIChpc01vdW50ZWQgJiYgIXZpc2libGUgJiYgbW90aW9uTGVhdmUgfHwgIWlzTW91bnRlZCAmJiBtb3Rpb25MZWF2ZUltbWVkaWF0ZWx5ICYmICF2aXNpYmxlICYmIG1vdGlvbkxlYXZlKSB7XG4gICAgICBuZXh0U3RhdHVzID0gU1RBVFVTX0xFQVZFO1xuICAgIH1cbiAgICB2YXIgbmV4dEV2ZW50SGFuZGxlcnMgPSBnZXRFdmVudEhhbmRsZXJzKG5leHRTdGF0dXMpO1xuXG4gICAgLy8gVXBkYXRlIHRvIG5leHQgc3RhdHVzXG4gICAgaWYgKG5leHRTdGF0dXMgJiYgKHN1cHBvcnRNb3Rpb24gfHwgbmV4dEV2ZW50SGFuZGxlcnNbU1RFUF9QUkVQQVJFXSkpIHtcbiAgICAgIHNldFN0YXR1cyhuZXh0U3RhdHVzKTtcbiAgICAgIHN0YXJ0U3RlcCgpO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBTZXQgYmFjayBpbiBjYXNlIG5vIG1vdGlvbiBidXQgcHJldiBzdGF0dXMgaGFzIHByZXBhcmUgc3RlcFxuICAgICAgc2V0U3RhdHVzKFNUQVRVU19OT05FKTtcbiAgICB9XG4gICAgdmlzaWJsZVJlZi5jdXJyZW50ID0gdmlzaWJsZTtcbiAgfSwgW3Zpc2libGVdKTtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09IEVmZmVjdCA9PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIC8vIFJlc2V0IHdoZW4gbW90aW9uIGNoYW5nZWRcbiAgdXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICBpZiAoXG4gICAgLy8gQ2FuY2VsIGFwcGVhclxuICAgIGN1cnJlbnRTdGF0dXMgPT09IFNUQVRVU19BUFBFQVIgJiYgIW1vdGlvbkFwcGVhciB8fFxuICAgIC8vIENhbmNlbCBlbnRlclxuICAgIGN1cnJlbnRTdGF0dXMgPT09IFNUQVRVU19FTlRFUiAmJiAhbW90aW9uRW50ZXIgfHxcbiAgICAvLyBDYW5jZWwgbGVhdmVcbiAgICBjdXJyZW50U3RhdHVzID09PSBTVEFUVVNfTEVBVkUgJiYgIW1vdGlvbkxlYXZlKSB7XG4gICAgICBzZXRTdGF0dXMoU1RBVFVTX05PTkUpO1xuICAgIH1cbiAgfSwgW21vdGlvbkFwcGVhciwgbW90aW9uRW50ZXIsIG1vdGlvbkxlYXZlXSk7XG4gIHVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgIG1vdW50ZWRSZWYuY3VycmVudCA9IGZhbHNlO1xuICAgICAgY2xlYXJUaW1lb3V0KGRlYWRsaW5lUmVmLmN1cnJlbnQpO1xuICAgIH07XG4gIH0sIFtdKTtcblxuICAvLyBUcmlnZ2VyIGBvblZpc2libGVDaGFuZ2VkYFxuICB2YXIgZmlyc3RNb3VudENoYW5nZVJlZiA9IFJlYWN0LnVzZVJlZihmYWxzZSk7XG4gIHVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgLy8gW3Zpc2libGUgJiBtb3Rpb24gbm90IGVuZF0gPT4gWyF2aXNpYmxlICYgbW90aW9uIGVuZF0gc3RpbGwgbmVlZCB0cmlnZ2VyIG9uVmlzaWJsZUNoYW5nZWRcbiAgICBpZiAoYXN5bmNWaXNpYmxlKSB7XG4gICAgICBmaXJzdE1vdW50Q2hhbmdlUmVmLmN1cnJlbnQgPSB0cnVlO1xuICAgIH1cbiAgICBpZiAoYXN5bmNWaXNpYmxlICE9PSB1bmRlZmluZWQgJiYgY3VycmVudFN0YXR1cyA9PT0gU1RBVFVTX05PTkUpIHtcbiAgICAgIC8vIFNraXAgZmlyc3QgcmVuZGVyIGlzIGludmlzaWJsZSBzaW5jZSBpdCdzIG5vdGhpbmcgY2hhbmdlZFxuICAgICAgaWYgKGZpcnN0TW91bnRDaGFuZ2VSZWYuY3VycmVudCB8fCBhc3luY1Zpc2libGUpIHtcbiAgICAgICAgb25WaXNpYmxlQ2hhbmdlZCA9PT0gbnVsbCB8fCBvblZpc2libGVDaGFuZ2VkID09PSB2b2lkIDAgfHwgb25WaXNpYmxlQ2hhbmdlZChhc3luY1Zpc2libGUpO1xuICAgICAgfVxuICAgICAgZmlyc3RNb3VudENoYW5nZVJlZi5jdXJyZW50ID0gdHJ1ZTtcbiAgICB9XG4gIH0sIFthc3luY1Zpc2libGUsIGN1cnJlbnRTdGF0dXNdKTtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09IFN0eWxlcyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIHZhciBtZXJnZWRTdHlsZSA9IHN0eWxlO1xuICBpZiAoZXZlbnRIYW5kbGVyc1tTVEVQX1BSRVBBUkVdICYmIHN0ZXAgPT09IFNURVBfU1RBUlQpIHtcbiAgICBtZXJnZWRTdHlsZSA9IF9vYmplY3RTcHJlYWQoe1xuICAgICAgdHJhbnNpdGlvbjogJ25vbmUnXG4gICAgfSwgbWVyZ2VkU3R5bGUpO1xuICB9XG4gIHJldHVybiBbY3VycmVudFN0YXR1cywgc3RlcCwgbWVyZ2VkU3R5bGUsIGFzeW5jVmlzaWJsZSAhPT0gbnVsbCAmJiBhc3luY1Zpc2libGUgIT09IHZvaWQgMCA/IGFzeW5jVmlzaWJsZSA6IHZpc2libGVdO1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useStatus.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useStepQueue.js":
/*!*********************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useStepQueue.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DoStep: () => (/* binding */ DoStep),\n/* harmony export */   SkipStep: () => (/* binding */ SkipStep),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isActive: () => (/* binding */ isActive)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/hooks/useState */ \"(ssr)/./node_modules/rc-util/es/hooks/useState.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _interface__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../interface */ \"(ssr)/./node_modules/rc-motion/es/interface.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useIsomorphicLayoutEffect */ \"(ssr)/./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _useNextFrame__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./useNextFrame */ \"(ssr)/./node_modules/rc-motion/es/hooks/useNextFrame.js\");\n\n\n\n\n\n\nvar FULL_STEP_QUEUE = [_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARE, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_START, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVE, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVATED];\nvar SIMPLE_STEP_QUEUE = [_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARE, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARED];\n\n/** Skip current step */\nvar SkipStep = false;\n/** Current step should be update in */\nvar DoStep = true;\nfunction isActive(step) {\n  return step === _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVE || step === _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVATED;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function (status, prepareOnly, callback) {\n  var _useState = (0,rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_NONE),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState, 2),\n    step = _useState2[0],\n    setStep = _useState2[1];\n  var _useNextFrame = (0,_useNextFrame__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n    _useNextFrame2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useNextFrame, 2),\n    nextFrame = _useNextFrame2[0],\n    cancelNextFrame = _useNextFrame2[1];\n  function startQueue() {\n    setStep(_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARE, true);\n  }\n  var STEP_QUEUE = prepareOnly ? SIMPLE_STEP_QUEUE : FULL_STEP_QUEUE;\n  (0,_useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function () {\n    if (step !== _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_NONE && step !== _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVATED) {\n      var index = STEP_QUEUE.indexOf(step);\n      var nextStep = STEP_QUEUE[index + 1];\n      var result = callback(step);\n      if (result === SkipStep) {\n        // Skip when no needed\n        setStep(nextStep, true);\n      } else if (nextStep) {\n        // Do as frame for step update\n        nextFrame(function (info) {\n          function doNext() {\n            // Skip since current queue is ood\n            if (info.isCanceled()) return;\n            setStep(nextStep, true);\n          }\n          if (result === true) {\n            doNext();\n          } else {\n            // Only promise should be async\n            Promise.resolve(result).then(doNext);\n          }\n        });\n      }\n    }\n  }, [status, step]);\n  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [startQueue, step];\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2hvb2tzL3VzZVN0ZXBRdWV1ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUFzRTtBQUNyQjtBQUNsQjtBQUNnRjtBQUMzQztBQUMxQjtBQUMxQyx1QkFBdUIsb0RBQVksRUFBRSxrREFBVSxFQUFFLG1EQUFXLEVBQUUsc0RBQWM7QUFDNUUseUJBQXlCLG9EQUFZLEVBQUUscURBQWE7O0FBRXBEO0FBQ087QUFDUDtBQUNPO0FBQ0E7QUFDUCxrQkFBa0IsbURBQVcsYUFBYSxzREFBYztBQUN4RDtBQUNBLGlFQUFnQjtBQUNoQixrQkFBa0IscUVBQVEsQ0FBQyxpREFBUztBQUNwQyxpQkFBaUIsb0ZBQWM7QUFDL0I7QUFDQTtBQUNBLHNCQUFzQix5REFBWTtBQUNsQyxxQkFBcUIsb0ZBQWM7QUFDbkM7QUFDQTtBQUNBO0FBQ0EsWUFBWSxvREFBWTtBQUN4QjtBQUNBO0FBQ0EsRUFBRSxzRUFBeUI7QUFDM0IsaUJBQWlCLGlEQUFTLGFBQWEsc0RBQWM7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLEdBQUc7QUFDSCxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIkU6XFxQUk9KRUNUU1xccG9zXFxwb3Nmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyYy1tb3Rpb25cXGVzXFxob29rc1xcdXNlU3RlcFF1ZXVlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfc2xpY2VkVG9BcnJheSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vc2xpY2VkVG9BcnJheVwiO1xuaW1wb3J0IHVzZVN0YXRlIGZyb20gXCJyYy11dGlsL2VzL2hvb2tzL3VzZVN0YXRlXCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBTVEVQX0FDVElWQVRFRCwgU1RFUF9BQ1RJVkUsIFNURVBfTk9ORSwgU1RFUF9QUkVQQVJFLCBTVEVQX1BSRVBBUkVELCBTVEVQX1NUQVJUIH0gZnJvbSBcIi4uL2ludGVyZmFjZVwiO1xuaW1wb3J0IHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QgZnJvbSBcIi4vdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdFwiO1xuaW1wb3J0IHVzZU5leHRGcmFtZSBmcm9tIFwiLi91c2VOZXh0RnJhbWVcIjtcbnZhciBGVUxMX1NURVBfUVVFVUUgPSBbU1RFUF9QUkVQQVJFLCBTVEVQX1NUQVJULCBTVEVQX0FDVElWRSwgU1RFUF9BQ1RJVkFURURdO1xudmFyIFNJTVBMRV9TVEVQX1FVRVVFID0gW1NURVBfUFJFUEFSRSwgU1RFUF9QUkVQQVJFRF07XG5cbi8qKiBTa2lwIGN1cnJlbnQgc3RlcCAqL1xuZXhwb3J0IHZhciBTa2lwU3RlcCA9IGZhbHNlO1xuLyoqIEN1cnJlbnQgc3RlcCBzaG91bGQgYmUgdXBkYXRlIGluICovXG5leHBvcnQgdmFyIERvU3RlcCA9IHRydWU7XG5leHBvcnQgZnVuY3Rpb24gaXNBY3RpdmUoc3RlcCkge1xuICByZXR1cm4gc3RlcCA9PT0gU1RFUF9BQ1RJVkUgfHwgc3RlcCA9PT0gU1RFUF9BQ1RJVkFURUQ7XG59XG5leHBvcnQgZGVmYXVsdCAoZnVuY3Rpb24gKHN0YXR1cywgcHJlcGFyZU9ubHksIGNhbGxiYWNrKSB7XG4gIHZhciBfdXNlU3RhdGUgPSB1c2VTdGF0ZShTVEVQX05PTkUpLFxuICAgIF91c2VTdGF0ZTIgPSBfc2xpY2VkVG9BcnJheShfdXNlU3RhdGUsIDIpLFxuICAgIHN0ZXAgPSBfdXNlU3RhdGUyWzBdLFxuICAgIHNldFN0ZXAgPSBfdXNlU3RhdGUyWzFdO1xuICB2YXIgX3VzZU5leHRGcmFtZSA9IHVzZU5leHRGcmFtZSgpLFxuICAgIF91c2VOZXh0RnJhbWUyID0gX3NsaWNlZFRvQXJyYXkoX3VzZU5leHRGcmFtZSwgMiksXG4gICAgbmV4dEZyYW1lID0gX3VzZU5leHRGcmFtZTJbMF0sXG4gICAgY2FuY2VsTmV4dEZyYW1lID0gX3VzZU5leHRGcmFtZTJbMV07XG4gIGZ1bmN0aW9uIHN0YXJ0UXVldWUoKSB7XG4gICAgc2V0U3RlcChTVEVQX1BSRVBBUkUsIHRydWUpO1xuICB9XG4gIHZhciBTVEVQX1FVRVVFID0gcHJlcGFyZU9ubHkgPyBTSU1QTEVfU1RFUF9RVUVVRSA6IEZVTExfU1RFUF9RVUVVRTtcbiAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgaWYgKHN0ZXAgIT09IFNURVBfTk9ORSAmJiBzdGVwICE9PSBTVEVQX0FDVElWQVRFRCkge1xuICAgICAgdmFyIGluZGV4ID0gU1RFUF9RVUVVRS5pbmRleE9mKHN0ZXApO1xuICAgICAgdmFyIG5leHRTdGVwID0gU1RFUF9RVUVVRVtpbmRleCArIDFdO1xuICAgICAgdmFyIHJlc3VsdCA9IGNhbGxiYWNrKHN0ZXApO1xuICAgICAgaWYgKHJlc3VsdCA9PT0gU2tpcFN0ZXApIHtcbiAgICAgICAgLy8gU2tpcCB3aGVuIG5vIG5lZWRlZFxuICAgICAgICBzZXRTdGVwKG5leHRTdGVwLCB0cnVlKTtcbiAgICAgIH0gZWxzZSBpZiAobmV4dFN0ZXApIHtcbiAgICAgICAgLy8gRG8gYXMgZnJhbWUgZm9yIHN0ZXAgdXBkYXRlXG4gICAgICAgIG5leHRGcmFtZShmdW5jdGlvbiAoaW5mbykge1xuICAgICAgICAgIGZ1bmN0aW9uIGRvTmV4dCgpIHtcbiAgICAgICAgICAgIC8vIFNraXAgc2luY2UgY3VycmVudCBxdWV1ZSBpcyBvb2RcbiAgICAgICAgICAgIGlmIChpbmZvLmlzQ2FuY2VsZWQoKSkgcmV0dXJuO1xuICAgICAgICAgICAgc2V0U3RlcChuZXh0U3RlcCwgdHJ1ZSk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGlmIChyZXN1bHQgPT09IHRydWUpIHtcbiAgICAgICAgICAgIGRvTmV4dCgpO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAvLyBPbmx5IHByb21pc2Ugc2hvdWxkIGJlIGFzeW5jXG4gICAgICAgICAgICBQcm9taXNlLnJlc29sdmUocmVzdWx0KS50aGVuKGRvTmV4dCk7XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFtzdGF0dXMsIHN0ZXBdKTtcbiAgUmVhY3QudXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgY2FuY2VsTmV4dEZyYW1lKCk7XG4gICAgfTtcbiAgfSwgW10pO1xuICByZXR1cm4gW3N0YXJ0UXVldWUsIHN0ZXBdO1xufSk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useStepQueue.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/index.js":
/*!********************************************!*\
  !*** ./node_modules/rc-motion/es/index.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSSMotionList: () => (/* reexport safe */ _CSSMotionList__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Provider: () => (/* reexport safe */ _context__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _CSSMotion__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CSSMotion */ \"(ssr)/./node_modules/rc-motion/es/CSSMotion.js\");\n/* harmony import */ var _CSSMotionList__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CSSMotionList */ \"(ssr)/./node_modules/rc-motion/es/CSSMotionList.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-motion/es/context.js\");\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_CSSMotion__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFvQztBQUNRO0FBQ0k7QUFDdkI7QUFDekIsaUVBQWUsa0RBQVMiLCJzb3VyY2VzIjpbIkU6XFxQUk9KRUNUU1xccG9zXFxwb3Nmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyYy1tb3Rpb25cXGVzXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQ1NTTW90aW9uIGZyb20gXCIuL0NTU01vdGlvblwiO1xuaW1wb3J0IENTU01vdGlvbkxpc3QgZnJvbSBcIi4vQ1NTTW90aW9uTGlzdFwiO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBQcm92aWRlciB9IGZyb20gXCIuL2NvbnRleHRcIjtcbmV4cG9ydCB7IENTU01vdGlvbkxpc3QgfTtcbmV4cG9ydCBkZWZhdWx0IENTU01vdGlvbjsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/interface.js":
/*!************************************************!*\
  !*** ./node_modules/rc-motion/es/interface.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STATUS_APPEAR: () => (/* binding */ STATUS_APPEAR),\n/* harmony export */   STATUS_ENTER: () => (/* binding */ STATUS_ENTER),\n/* harmony export */   STATUS_LEAVE: () => (/* binding */ STATUS_LEAVE),\n/* harmony export */   STATUS_NONE: () => (/* binding */ STATUS_NONE),\n/* harmony export */   STEP_ACTIVATED: () => (/* binding */ STEP_ACTIVATED),\n/* harmony export */   STEP_ACTIVE: () => (/* binding */ STEP_ACTIVE),\n/* harmony export */   STEP_NONE: () => (/* binding */ STEP_NONE),\n/* harmony export */   STEP_PREPARE: () => (/* binding */ STEP_PREPARE),\n/* harmony export */   STEP_PREPARED: () => (/* binding */ STEP_PREPARED),\n/* harmony export */   STEP_START: () => (/* binding */ STEP_START)\n/* harmony export */ });\nvar STATUS_NONE = 'none';\nvar STATUS_APPEAR = 'appear';\nvar STATUS_ENTER = 'enter';\nvar STATUS_LEAVE = 'leave';\nvar STEP_NONE = 'none';\nvar STEP_PREPARE = 'prepare';\nvar STEP_START = 'start';\nvar STEP_ACTIVE = 'active';\nvar STEP_ACTIVATED = 'end';\n/**\n * Used for disabled motion case.\n * Prepare stage will still work but start & active will be skipped.\n */\nvar STEP_PREPARED = 'prepared';//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2ludGVyZmFjZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsiRTpcXFBST0pFQ1RTXFxwb3NcXHBvc2Zyb250ZW5kXFxub2RlX21vZHVsZXNcXHJjLW1vdGlvblxcZXNcXGludGVyZmFjZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIFNUQVRVU19OT05FID0gJ25vbmUnO1xuZXhwb3J0IHZhciBTVEFUVVNfQVBQRUFSID0gJ2FwcGVhcic7XG5leHBvcnQgdmFyIFNUQVRVU19FTlRFUiA9ICdlbnRlcic7XG5leHBvcnQgdmFyIFNUQVRVU19MRUFWRSA9ICdsZWF2ZSc7XG5leHBvcnQgdmFyIFNURVBfTk9ORSA9ICdub25lJztcbmV4cG9ydCB2YXIgU1RFUF9QUkVQQVJFID0gJ3ByZXBhcmUnO1xuZXhwb3J0IHZhciBTVEVQX1NUQVJUID0gJ3N0YXJ0JztcbmV4cG9ydCB2YXIgU1RFUF9BQ1RJVkUgPSAnYWN0aXZlJztcbmV4cG9ydCB2YXIgU1RFUF9BQ1RJVkFURUQgPSAnZW5kJztcbi8qKlxuICogVXNlZCBmb3IgZGlzYWJsZWQgbW90aW9uIGNhc2UuXG4gKiBQcmVwYXJlIHN0YWdlIHdpbGwgc3RpbGwgd29yayBidXQgc3RhcnQgJiBhY3RpdmUgd2lsbCBiZSBza2lwcGVkLlxuICovXG5leHBvcnQgdmFyIFNURVBfUFJFUEFSRUQgPSAncHJlcGFyZWQnOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/interface.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/util/diff.js":
/*!************************************************!*\
  !*** ./node_modules/rc-motion/es/util/diff.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STATUS_ADD: () => (/* binding */ STATUS_ADD),\n/* harmony export */   STATUS_KEEP: () => (/* binding */ STATUS_KEEP),\n/* harmony export */   STATUS_REMOVE: () => (/* binding */ STATUS_REMOVE),\n/* harmony export */   STATUS_REMOVED: () => (/* binding */ STATUS_REMOVED),\n/* harmony export */   diffKeys: () => (/* binding */ diffKeys),\n/* harmony export */   parseKeys: () => (/* binding */ parseKeys),\n/* harmony export */   wrapKeyToObject: () => (/* binding */ wrapKeyToObject)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n\n\nvar STATUS_ADD = 'add';\nvar STATUS_KEEP = 'keep';\nvar STATUS_REMOVE = 'remove';\nvar STATUS_REMOVED = 'removed';\nfunction wrapKeyToObject(key) {\n  var keyObj;\n  if (key && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key) === 'object' && 'key' in key) {\n    keyObj = key;\n  } else {\n    keyObj = {\n      key: key\n    };\n  }\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, keyObj), {}, {\n    key: String(keyObj.key)\n  });\n}\nfunction parseKeys() {\n  var keys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return keys.map(wrapKeyToObject);\n}\nfunction diffKeys() {\n  var prevKeys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var currentKeys = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var list = [];\n  var currentIndex = 0;\n  var currentLen = currentKeys.length;\n  var prevKeyObjects = parseKeys(prevKeys);\n  var currentKeyObjects = parseKeys(currentKeys);\n\n  // Check prev keys to insert or keep\n  prevKeyObjects.forEach(function (keyObj) {\n    var hit = false;\n    for (var i = currentIndex; i < currentLen; i += 1) {\n      var currentKeyObj = currentKeyObjects[i];\n      if (currentKeyObj.key === keyObj.key) {\n        // New added keys should add before current key\n        if (currentIndex < i) {\n          list = list.concat(currentKeyObjects.slice(currentIndex, i).map(function (obj) {\n            return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, obj), {}, {\n              status: STATUS_ADD\n            });\n          }));\n          currentIndex = i;\n        }\n        list.push((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, currentKeyObj), {}, {\n          status: STATUS_KEEP\n        }));\n        currentIndex += 1;\n        hit = true;\n        break;\n      }\n    }\n\n    // If not hit, it means key is removed\n    if (!hit) {\n      list.push((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, keyObj), {}, {\n        status: STATUS_REMOVE\n      }));\n    }\n  });\n\n  // Add rest to the list\n  if (currentIndex < currentLen) {\n    list = list.concat(currentKeyObjects.slice(currentIndex).map(function (obj) {\n      return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, obj), {}, {\n        status: STATUS_ADD\n      });\n    }));\n  }\n\n  /**\n   * Merge same key when it remove and add again:\n   *    [1 - add, 2 - keep, 1 - remove] -> [1 - keep, 2 - keep]\n   */\n  var keys = {};\n  list.forEach(function (_ref) {\n    var key = _ref.key;\n    keys[key] = (keys[key] || 0) + 1;\n  });\n  var duplicatedKeys = Object.keys(keys).filter(function (key) {\n    return keys[key] > 1;\n  });\n  duplicatedKeys.forEach(function (matchKey) {\n    // Remove `STATUS_REMOVE` node.\n    list = list.filter(function (_ref2) {\n      var key = _ref2.key,\n        status = _ref2.status;\n      return key !== matchKey || status !== STATUS_REMOVE;\n    });\n\n    // Update `STATUS_ADD` to `STATUS_KEEP`\n    list.forEach(function (node) {\n      if (node.key === matchKey) {\n        // eslint-disable-next-line no-param-reassign\n        node.status = STATUS_KEEP;\n      }\n    });\n  });\n  return list;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/util/diff.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/util/motion.js":
/*!**************************************************!*\
  !*** ./node_modules/rc-motion/es/util/motion.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animationEndName: () => (/* binding */ animationEndName),\n/* harmony export */   getTransitionName: () => (/* binding */ getTransitionName),\n/* harmony export */   getVendorPrefixedEventName: () => (/* binding */ getVendorPrefixedEventName),\n/* harmony export */   getVendorPrefixes: () => (/* binding */ getVendorPrefixes),\n/* harmony export */   supportTransition: () => (/* binding */ supportTransition),\n/* harmony export */   transitionEndName: () => (/* binding */ transitionEndName)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/Dom/canUseDom */ \"(ssr)/./node_modules/rc-util/es/Dom/canUseDom.js\");\n\n\n// ================= Transition =================\n// Event wrapper. Copy from react source code\nfunction makePrefixMap(styleProp, eventName) {\n  var prefixes = {};\n  prefixes[styleProp.toLowerCase()] = eventName.toLowerCase();\n  prefixes[\"Webkit\".concat(styleProp)] = \"webkit\".concat(eventName);\n  prefixes[\"Moz\".concat(styleProp)] = \"moz\".concat(eventName);\n  prefixes[\"ms\".concat(styleProp)] = \"MS\".concat(eventName);\n  prefixes[\"O\".concat(styleProp)] = \"o\".concat(eventName.toLowerCase());\n  return prefixes;\n}\nfunction getVendorPrefixes(domSupport, win) {\n  var prefixes = {\n    animationend: makePrefixMap('Animation', 'AnimationEnd'),\n    transitionend: makePrefixMap('Transition', 'TransitionEnd')\n  };\n  if (domSupport) {\n    if (!('AnimationEvent' in win)) {\n      delete prefixes.animationend.animation;\n    }\n    if (!('TransitionEvent' in win)) {\n      delete prefixes.transitionend.transition;\n    }\n  }\n  return prefixes;\n}\nvar vendorPrefixes = getVendorPrefixes((0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(), typeof window !== 'undefined' ? window : {});\nvar style = {};\nif ((0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_1__[\"default\"])()) {\n  var _document$createEleme = document.createElement('div');\n  style = _document$createEleme.style;\n}\nvar prefixedEventNames = {};\nfunction getVendorPrefixedEventName(eventName) {\n  if (prefixedEventNames[eventName]) {\n    return prefixedEventNames[eventName];\n  }\n  var prefixMap = vendorPrefixes[eventName];\n  if (prefixMap) {\n    var stylePropList = Object.keys(prefixMap);\n    var len = stylePropList.length;\n    for (var i = 0; i < len; i += 1) {\n      var styleProp = stylePropList[i];\n      if (Object.prototype.hasOwnProperty.call(prefixMap, styleProp) && styleProp in style) {\n        prefixedEventNames[eventName] = prefixMap[styleProp];\n        return prefixedEventNames[eventName];\n      }\n    }\n  }\n  return '';\n}\nvar internalAnimationEndName = getVendorPrefixedEventName('animationend');\nvar internalTransitionEndName = getVendorPrefixedEventName('transitionend');\nvar supportTransition = !!(internalAnimationEndName && internalTransitionEndName);\nvar animationEndName = internalAnimationEndName || 'animationend';\nvar transitionEndName = internalTransitionEndName || 'transitionend';\nfunction getTransitionName(transitionName, transitionType) {\n  if (!transitionName) return null;\n  if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(transitionName) === 'object') {\n    var type = transitionType.replace(/-\\w/g, function (match) {\n      return match[1].toUpperCase();\n    });\n    return transitionName[type];\n  }\n  return \"\".concat(transitionName, \"-\").concat(transitionType);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/util/motion.js\n");

/***/ })

};
;