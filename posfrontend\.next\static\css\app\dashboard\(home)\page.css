/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/components/Sales/sales-panels.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/* Sale details panel styling */
.sale-detail-light .ant-descriptions-item-label {
  background-color: #f5f5f5 !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
}

.sale-detail-light .ant-descriptions-item-content {
  background-color: #ffffff !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
}

.sale-detail-light .ant-descriptions-bordered {
  border-color: #e8e8e8 !important;
}

/* Modern POS Sales Form Styling */
.sales-form .ant-form-item-label > label {
  color: #333 !important;
  font-weight: 500 !important;
}

/* Input and Select Styling */
.sales-form .ant-input-number-input::-moz-placeholder, .sales-form .ant-input::-moz-placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}
.sales-form .ant-select-selection-placeholder,
.sales-form .ant-input-number-input::placeholder,
.sales-form .ant-input::placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

.sales-form .ant-input,
.sales-form .ant-input-number-input,
.sales-form .ant-select-selection-item {
  color: #333 !important;
}

.sales-form .ant-select-selector,
.sales-form .ant-input-number,
.sales-form .ant-input {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

.sales-form .ant-select-selector:hover,
.sales-form .ant-input-number:hover,
.sales-form .ant-input:hover {
  border-color: #40a9ff !important;
}

.sales-form .ant-select-focused .ant-select-selector,
.sales-form .ant-input-number-focused,
.sales-form .ant-input:focus {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.sales-form .ant-select-arrow,
.sales-form .ant-input-number-handler-wrap {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Button Styling */
.sales-form .ant-btn-primary {
  border: none !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  transition: all 0.3s ease !important;
}

.sales-form .ant-btn-primary:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.sales-form .ant-btn-primary:active {
  transform: translateY(0) !important;
}

/* Table Styling */
.sales-form table {
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 8px;
  overflow: hidden;
  width: 100%;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.sales-form table th {
  font-weight: 500;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
}

.sales-form table th,
.sales-form table td {
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
}

.sales-form table tbody tr:last-child td {
  border-bottom: none;
}

.sales-form table tbody tr {
  transition: background-color 0.2s ease;
}

.sales-form table tbody tr:hover {
  background-color: rgba(245, 245, 245, 0.7) !important;
}

/* Empty State Styling */
.sales-form .ant-empty-description {
  color: #666 !important;
}

.sales-form .ant-empty-img-simple-ellipse {
  fill: #f5f5f5 !important;
}

.sales-form .ant-empty-img-simple-g {
  stroke: #d9d9d9 !important;
}

.sales-form .ant-empty-img-simple-path {
  fill: #e8e8e8 !important;
}

/* Form Validation Styling */
.sales-form .ant-form-item-has-error .ant-input,
.sales-form .ant-form-item-has-error .ant-input-number,
.sales-form .ant-form-item-has-error .ant-select-selector,
.sales-form .ant-form-item-has-error .ant-picker {
  border-color: #ef4444 !important;
}

.sales-form .ant-form-item-explain-error {
  color: #ef4444 !important;
  font-size: 0.8rem !important;
  margin-top: 4px !important;
}

/* Checkout Section Styling */
.sales-form .bg-\[#111827\] {
  border-radius: 8px;
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  background-color: #f5f5f5 !important;
}

/* Scrollbar Styling */
.sales-form .overflow-y-auto::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.sales-form .overflow-y-auto::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 4px;
}

.sales-form .overflow-y-auto::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 4px;
}

.sales-form .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* Sales action buttons */
.view-button {
  color: #10b981 !important; /* green-500 */
}

.view-button:hover {
  color: #34d399 !important; /* green-400 */
}

.edit-button {
  color: #3b82f6 !important; /* blue-500 */
}

.edit-button:hover {
  color: #60a5fa !important; /* blue-400 */
}

.delete-button {
  color: #ef4444 !important; /* red-500 */
}

.delete-button:hover {
  color: #f87171 !important; /* red-400 */
}

/* Sales table styling */
.sales-table {
  background-color: #ffffff !important;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.sales-table .ant-table {
  background-color: transparent !important;
}

.sales-table .ant-table-thead > tr > th {
  background-color: #f5f5f5 !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  font-size: 0.75rem !important;
  letter-spacing: 0.05em !important;
  padding: 12px 16px !important;
}

.sales-table .ant-table-tbody > tr > td {
  background-color: #ffffff !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
  padding: 12px 16px !important;
}

.sales-table .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5 !important;
}

.sales-table .ant-table-summary {
  background-color: #f5f5f5 !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
}

.sales-table .ant-table-summary-cell {
  background-color: #f5f5f5 !important;
  color: #333 !important;
  border-color: #e8e8e8 !important;
  font-weight: 600;
  padding: 12px 16px !important;
}

.sales-table .ant-empty-description {
  color: #666 !important;
}

/* Add borders to the table */
.sales-table .ant-table-container {
  border: 1px solid #e8e8e8 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

/* Style the table header */
.sales-table .ant-table-thead > tr > th:first-child {
  border-top-left-radius: 8px !important;
}

.sales-table .ant-table-thead > tr > th:last-child {
  border-top-right-radius: 8px !important;
}

/* Sliding Panel Styling */
.ant-drawer-content {
  background-color: #ffffff !important;
}

.ant-drawer-header {
  background-color: #f5f5f5 !important;
  border-bottom: 1px solid #e8e8e8 !important;
}

.ant-drawer-title {
  color: #333 !important;
  font-weight: 600 !important;
}

.ant-drawer-close {
  color: #333 !important;
}

/* Custom styling for the sliding panel */

/* Receipt Preview Modal Styling */
.receipt-preview-modal .ant-modal-content {
  background-color: #ffffff !important;
}

.receipt-preview-modal .ant-modal-header {
  background-color: #f5f5f5 !important;
  border-bottom: 1px solid #e8e8e8 !important;
}

.receipt-preview-modal .ant-modal-body {
  background-color: #ffffff !important;
}

.receipt-preview-modal .ant-modal-footer {
  background-color: #f5f5f5 !important;
  border-top: 1px solid #e8e8e8 !important;
}

/* Sliding panels now use z-index 9999 and cover the full screen properly */

/* Mobile Responsiveness */
@media (max-width: 768px) {
  /* Make the sliding panel full width on mobile */
  .fixed.inset-0.z-\[1000\].overflow-hidden {
    left: 0 !important;
  }

  /* Make the panel content full width */
  .sales-form {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
  }

  /* Adjust grid layout for mobile */
  .sales-form .grid {
    grid-template-columns: 1fr !important;
  }

  /* Adjust table for mobile */
  .sales-form table {
    font-size: 0.8rem;
  }

  .sales-form table th,
  .sales-form table td {
    padding: 6px 8px;
  }

  /* Adjust buttons for mobile */
  .sales-form .ant-btn {
    font-size: 0.9rem !important;
    height: auto !important;
    padding: 8px 12px !important;
  }

  /* Adjust headings for mobile */
  .sales-form h3 {
    font-size: 1rem !important;
  }

  /* Ensure checkout section is visible */
  .sales-form .sticky {
    position: relative !important;
    top: 0 !important;
  }
}


