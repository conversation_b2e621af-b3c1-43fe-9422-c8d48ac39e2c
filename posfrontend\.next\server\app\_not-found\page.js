/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=E%3A%5CPROJECTS%5Cpos%5Cposfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CPROJECTS%5Cpos%5Cposfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=E%3A%5CPROJECTS%5Cpos%5Cposfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CPROJECTS%5Cpos%5Cposfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=E%3A%5CPROJECTS%5Cpos%5Cposfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CPROJECTS%5Cpos%5Cposfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cflatpickr%5C%5Cdist%5C%5Cflatpickr.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnextjs-toploader%5C%5Cdist%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobal-spinner.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CLayouts%5C%5Csidebar%5C%5Csidebar-context.tsx%22%2C%22ids%22%3A%5B%22SidebarProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccss%5C%5Csatoshi.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccss%5C%5Cstyle.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Cprovider%5C%5CProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cflatpickr%5C%5Cdist%5C%5Cflatpickr.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnextjs-toploader%5C%5Cdist%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobal-spinner.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CLayouts%5C%5Csidebar%5C%5Csidebar-context.tsx%22%2C%22ids%22%3A%5B%22SidebarProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccss%5C%5Csatoshi.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccss%5C%5Cstyle.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Cprovider%5C%5CProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/nextjs-toploader/dist/index.js */ \"(rsc)/./node_modules/nextjs-toploader/dist/index.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(rsc)/./src/app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Layouts/sidebar/sidebar-context.tsx */ \"(rsc)/./src/components/Layouts/sidebar/sidebar-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/provider/Provider.tsx */ \"(rsc)/./src/provider/Provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cflatpickr%5C%5Cdist%5C%5Cflatpickr.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnextjs-toploader%5C%5Cdist%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobal-spinner.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CLayouts%5C%5Csidebar%5C%5Csidebar-context.tsx%22%2C%22ids%22%3A%5B%22SidebarProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccss%5C%5Csatoshi.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccss%5C%5Cstyle.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Cprovider%5C%5CProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cflatpickr%5C%5Cdist%5C%5Cflatpickr.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnextjs-toploader%5C%5Cdist%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobal-spinner.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CLayouts%5C%5Csidebar%5C%5Csidebar-context.tsx%22%2C%22ids%22%3A%5B%22SidebarProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccss%5C%5Csatoshi.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccss%5C%5Cstyle.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Cprovider%5C%5CProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cflatpickr%5C%5Cdist%5C%5Cflatpickr.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnextjs-toploader%5C%5Cdist%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobal-spinner.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CLayouts%5C%5Csidebar%5C%5Csidebar-context.tsx%22%2C%22ids%22%3A%5B%22SidebarProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccss%5C%5Csatoshi.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccss%5C%5Cstyle.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Cprovider%5C%5CProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/nextjs-toploader/dist/index.js */ \"(ssr)/./node_modules/nextjs-toploader/dist/index.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Layouts/sidebar/sidebar-context.tsx */ \"(ssr)/./src/components/Layouts/sidebar/sidebar-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/provider/Provider.tsx */ \"(ssr)/./src/provider/Provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cflatpickr%5C%5Cdist%5C%5Cflatpickr.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnextjs-toploader%5C%5Cdist%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobal-spinner.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CLayouts%5C%5Csidebar%5C%5Csidebar-context.tsx%22%2C%22ids%22%3A%5B%22SidebarProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccss%5C%5Csatoshi.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Ccss%5C%5Cstyle.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Csrc%5C%5Cprovider%5C%5CProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CPROJECTS%5C%5Cpos%5C%5Cposfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/api/apicaller.ts":
/*!******************************!*\
  !*** ./src/api/apicaller.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiCaller: () => (/* binding */ ApiCaller),\n/* harmony export */   apiCallerWithAuth: () => (/* binding */ apiCallerWithAuth)\n/* harmony export */ });\nconst BASEURL = \"http://localhost:5005/api/v1\";\n// const BASEURL = \"https://nexapoapi.up.railway.app/api/v1\";\n// 🔐 API CALLER WITH AUTH (for all requests except login)\nconst apiCallerWithAuth = async (payloaddata, urlpath, token)=>{\n    // Changed to ApiResponse to include success, message, and data\n    try {\n        // Log request details for debugging (remove in production)\n        console.log(\"API Request:\", {\n            url: `${BASEURL}${urlpath}`,\n            method: \"POST\",\n            payload: payloaddata,\n            hasToken: !!token\n        });\n        const headers = {\n            Accept: \"application/json\",\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`\n        };\n        const options = {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(payloaddata),\n            credentials: \"same-origin\",\n            mode: \"cors\"\n        };\n        // Make the fetch request\n        const fetchResult = await fetch(`${BASEURL}${urlpath}`, options);\n        // Try to parse the JSON response\n        let result;\n        try {\n            result = await fetchResult.json();\n        } catch (parseError) {\n            console.error(\"Failed to parse JSON response:\", parseError);\n            return {\n                success: false,\n                message: `Failed to parse server response: ${fetchResult.statusText || \"Unknown error\"}`\n            };\n        }\n        // Check if the request was successful\n        if (fetchResult.ok) {\n            return {\n                success: true,\n                message: result.message || \"Action completed successfully\",\n                data: result.data || null\n            };\n        } else {\n            // Log the error response for debugging\n            console.error(\"API Error Response:\", {\n                status: fetchResult.status,\n                statusText: fetchResult.statusText,\n                result\n            });\n            return {\n                success: false,\n                message: result.message || `Request failed with status: ${fetchResult.status} ${fetchResult.statusText}`\n            };\n        }\n    } catch (error) {\n        // Log detailed error information\n        console.error(\"API call failed:\", error);\n        // Check if it's a network error\n        const isNetworkError = error instanceof TypeError && error.message.includes('fetch');\n        return {\n            success: false,\n            message: isNetworkError ? \"Network error: Unable to connect to the server. Please check your internet connection.\" : `Error: ${error.message || \"Unable to complete action. An unknown error occurred!\"}`\n        };\n    }\n};\n// 🔓 LOGIN ONLY API CALLER (no token)\nconst ApiCaller = async (urlpath, payloaddata)=>{\n    // Changed to ApiResponse to include success, message, and data\n    try {\n        // Log request details for debugging (remove in production)\n        console.log(\"Login API Request:\", {\n            url: `${BASEURL}${urlpath}`,\n            method: \"POST\",\n            payload: payloaddata\n        });\n        const headers = {\n            Accept: \"application/json\",\n            \"Content-Type\": \"application/json\"\n        };\n        const options = {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(payloaddata),\n            credentials: \"same-origin\",\n            mode: \"cors\"\n        };\n        // Make the fetch request\n        const fetchResult = await fetch(`${BASEURL}${urlpath}`, options);\n        // Try to parse the JSON response\n        let result;\n        try {\n            result = await fetchResult.json();\n        } catch (parseError) {\n            console.error(\"Failed to parse JSON response:\", parseError);\n            return {\n                success: false,\n                message: `Failed to parse server response: ${fetchResult.statusText || \"Unknown error\"}`\n            };\n        }\n        // Check if the request was successful\n        if (fetchResult.ok) {\n            return {\n                success: true,\n                message: result.message || \"Login successful\",\n                data: result.data || null\n            };\n        } else {\n            // Log the error response for debugging\n            console.error(\"Login API Error Response:\", {\n                status: fetchResult.status,\n                statusText: fetchResult.statusText,\n                result\n            });\n            return {\n                success: false,\n                message: result.message || `Login failed with status: ${fetchResult.status} ${fetchResult.statusText}`\n            };\n        }\n    } catch (error) {\n        // Log detailed error information\n        console.error(\"Login API call failed:\", error);\n        // Check if it's a network error\n        const isNetworkError = error instanceof TypeError && error.message.includes('fetch');\n        return {\n            success: false,\n            message: isNetworkError ? \"Network error: Unable to connect to the server. Please check your internet connection.\" : `Error: ${error.message || \"Unable to login. An unknown error occurred!\"}`\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/api/apicaller.ts\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layouts_sidebar_sidebar_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layouts/sidebar/sidebar-context */ \"(ssr)/./src/components/Layouts/sidebar/sidebar-context.tsx\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        defaultTheme: \"light\",\n        attribute: \"class\",\n        enableSystem: false,\n        forcedTheme: \"light\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layouts_sidebar_sidebar_context__WEBPACK_IMPORTED_MODULE_1__.SidebarProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\providers.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRStFO0FBQ25DO0FBRXJDLFNBQVNFLFVBQVUsRUFBRUMsUUFBUSxFQUFpQztJQUNuRSxxQkFDRSw4REFBQ0Ysc0RBQWFBO1FBQUNHLGNBQWE7UUFBUUMsV0FBVTtRQUFRQyxjQUFjO1FBQU9DLGFBQVk7a0JBQ3JGLDRFQUFDUCx3RkFBZUE7c0JBQUVHOzs7Ozs7Ozs7OztBQUd4QiIsInNvdXJjZXMiOlsiRTpcXFBST0pFQ1RTXFxwb3NcXHBvc2Zyb250ZW5kXFxzcmNcXGFwcFxccHJvdmlkZXJzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgU2lkZWJhclByb3ZpZGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy9MYXlvdXRzL3NpZGViYXIvc2lkZWJhci1jb250ZXh0XCI7XG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBQcm92aWRlcnMoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIDxUaGVtZVByb3ZpZGVyIGRlZmF1bHRUaGVtZT1cImxpZ2h0XCIgYXR0cmlidXRlPVwiY2xhc3NcIiBlbmFibGVTeXN0ZW09e2ZhbHNlfSBmb3JjZWRUaGVtZT1cImxpZ2h0XCI+XG4gICAgICA8U2lkZWJhclByb3ZpZGVyPntjaGlsZHJlbn08L1NpZGViYXJQcm92aWRlcj5cbiAgICA8L1RoZW1lUHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiU2lkZWJhclByb3ZpZGVyIiwiVGhlbWVQcm92aWRlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIiwiZGVmYXVsdFRoZW1lIiwiYXR0cmlidXRlIiwiZW5hYmxlU3lzdGVtIiwiZm9yY2VkVGhlbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layouts/sidebar/sidebar-context.tsx":
/*!************************************************************!*\
  !*** ./src/components/Layouts/sidebar/sidebar-context.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SidebarProvider: () => (/* binding */ SidebarProvider),\n/* harmony export */   useSidebarContext: () => (/* binding */ useSidebarContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_use_mobile__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/use-mobile */ \"(ssr)/./src/hooks/use-mobile.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useSidebarContext,SidebarProvider auto */ \n\n\nconst SidebarContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)(null);\nfunction useSidebarContext() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(SidebarContext);\n    if (!context) {\n        throw new Error(\"useSidebarContext must be used within a SidebarProvider\");\n    }\n    return context;\n}\nfunction SidebarProvider({ children, defaultOpen = true }) {\n    // On mobile, always start with sidebar closed\n    const isMobile = (0,_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_1__.useIsMobile)();\n    // Track previous mobile state to detect transitions\n    const [prevIsMobile, setPrevIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(isMobile);\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(isMobile ? false : defaultOpen);\n    // Track if sidebar was manually toggled\n    const [wasManuallyToggled, setWasManuallyToggled] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // When screen size changes, update sidebar state\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SidebarProvider.useEffect\": ()=>{\n            // Only auto-adjust if screen size actually changed\n            if (prevIsMobile !== isMobile) {\n                // If switching to desktop, always open sidebar\n                if (!isMobile) {\n                    setIsOpen(true);\n                    setWasManuallyToggled(false);\n                    console.log(\"Switching to desktop - opening sidebar\");\n                } else if (!wasManuallyToggled) {\n                    setIsOpen(false);\n                    console.log(\"Switching to mobile - closing sidebar (not manually toggled)\");\n                } else {\n                    console.log(\"Switching to mobile - keeping sidebar state (manually toggled)\");\n                }\n                console.log(\"SidebarProvider - Screen size changed:\", {\n                    prevIsMobile,\n                    isMobile,\n                    isOpen: !isMobile ? true : wasManuallyToggled ? isOpen : false,\n                    wasManuallyToggled\n                });\n                // Update previous mobile state\n                setPrevIsMobile(isMobile);\n            }\n        }\n    }[\"SidebarProvider.useEffect\"], [\n        isMobile,\n        prevIsMobile,\n        wasManuallyToggled,\n        isOpen\n    ]);\n    function toggleSidebar() {\n        // Mark as manually toggled and prevent auto-closing\n        setWasManuallyToggled(true);\n        // Use a timeout to ensure state updates don't conflict\n        setTimeout(()=>{\n            setIsOpen((prev)=>{\n                const newState = !prev;\n                console.log(\"Sidebar manually toggled:\", {\n                    newState,\n                    isMobile\n                });\n                return newState;\n            });\n        }, 50);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContext.Provider, {\n        value: {\n            state: isOpen ? \"expanded\" : \"collapsed\",\n            isOpen,\n            setIsOpen,\n            isMobile,\n            toggleSidebar\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Layouts\\\\sidebar\\\\sidebar-context.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layouts/sidebar/sidebar-context.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/LoadingSpinner.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/LoadingSpinner.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Spin_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Spin!=!antd */ \"(ssr)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_LoadingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=LoadingOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n/**\n * LoadingSpinner component that uses Ant Design's Spin component\n * Simple, centered spinner with no text\n */ const LoadingSpinner = ({ size = \"large\", fullScreen = false, tip })=>{\n    const antIcon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LoadingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        style: {\n            fontSize: 24\n        },\n        spin: true\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n        lineNumber: 22,\n        columnNumber: 19\n    }, undefined);\n    // For fullScreen loading\n    if (fullScreen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex flex-col items-center justify-center bg-white/80\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Spin_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                size: size,\n                indicator: antIcon,\n                tip: tip\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, undefined);\n    }\n    // For regular loading\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full w-full items-center justify-center py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Spin_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            size: size,\n            indicator: antIcon,\n            tip: tip\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingSpinner);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/use-mobile.ts":
/*!*********************************!*\
  !*** ./src/hooks/use-mobile.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MOBILE_BREAKPOINT: () => (/* binding */ MOBILE_BREAKPOINT),\n/* harmony export */   useIsMobile: () => (/* binding */ useIsMobile)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MOBILE_BREAKPOINT = 850;\nfunction useIsMobile() {\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useIsMobile.useEffect\": ()=>{\n            const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\n            const onChange = {\n                \"useIsMobile.useEffect.onChange\": ()=>{\n                    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\n                }\n            }[\"useIsMobile.useEffect.onChange\"];\n            setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\n            mql.addEventListener(\"change\", onChange);\n            return ({\n                \"useIsMobile.useEffect\": ()=>mql.removeEventListener(\"change\", onChange)\n            })[\"useIsMobile.useEffect\"];\n        }\n    }[\"useIsMobile.useEffect\"], []);\n    return !!isMobile;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlLW1vYmlsZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTRDO0FBRXJDLE1BQU1FLG9CQUFvQixJQUFJO0FBRTlCLFNBQVNDO0lBQ2QsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdKLCtDQUFRQTtJQUV4Q0QsZ0RBQVNBO2lDQUFDO1lBQ1IsTUFBTU0sTUFBTUMsT0FBT0MsVUFBVSxDQUFDLENBQUMsWUFBWSxFQUFFTixvQkFBb0IsRUFBRSxHQUFHLENBQUM7WUFFdkUsTUFBTU87a0RBQVc7b0JBQ2ZKLFlBQVlFLE9BQU9HLFVBQVUsR0FBR1I7Z0JBQ2xDOztZQUVBRyxZQUFZRSxPQUFPRyxVQUFVLEdBQUdSO1lBRWhDSSxJQUFJSyxnQkFBZ0IsQ0FBQyxVQUFVRjtZQUMvQjt5Q0FBTyxJQUFNSCxJQUFJTSxtQkFBbUIsQ0FBQyxVQUFVSDs7UUFDakQ7Z0NBQUcsRUFBRTtJQUVMLE9BQU8sQ0FBQyxDQUFDTDtBQUNYIiwic291cmNlcyI6WyJFOlxcUFJPSkVDVFNcXHBvc1xccG9zZnJvbnRlbmRcXHNyY1xcaG9va3NcXHVzZS1tb2JpbGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xuXG5leHBvcnQgY29uc3QgTU9CSUxFX0JSRUFLUE9JTlQgPSA4NTA7XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VJc01vYmlsZSgpIHtcbiAgY29uc3QgW2lzTW9iaWxlLCBzZXRJc01vYmlsZV0gPSB1c2VTdGF0ZTxib29sZWFuPigpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgbXFsID0gd2luZG93Lm1hdGNoTWVkaWEoYChtYXgtd2lkdGg6ICR7TU9CSUxFX0JSRUFLUE9JTlQgLSAxfXB4KWApO1xuXG4gICAgY29uc3Qgb25DaGFuZ2UgPSAoKSA9PiB7XG4gICAgICBzZXRJc01vYmlsZSh3aW5kb3cuaW5uZXJXaWR0aCA8IE1PQklMRV9CUkVBS1BPSU5UKTtcbiAgICB9O1xuXG4gICAgc2V0SXNNb2JpbGUod2luZG93LmlubmVyV2lkdGggPCBNT0JJTEVfQlJFQUtQT0lOVCk7XG5cbiAgICBtcWwuYWRkRXZlbnRMaXN0ZW5lcihcImNoYW5nZVwiLCBvbkNoYW5nZSk7XG4gICAgcmV0dXJuICgpID0+IG1xbC5yZW1vdmVFdmVudExpc3RlbmVyKFwiY2hhbmdlXCIsIG9uQ2hhbmdlKTtcbiAgfSwgW10pO1xuXG4gIHJldHVybiAhIWlzTW9iaWxlO1xufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiTU9CSUxFX0JSRUFLUE9JTlQiLCJ1c2VJc01vYmlsZSIsImlzTW9iaWxlIiwic2V0SXNNb2JpbGUiLCJtcWwiLCJ3aW5kb3ciLCJtYXRjaE1lZGlhIiwib25DaGFuZ2UiLCJpbm5lcldpZHRoIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/use-mobile.ts\n");

/***/ }),

/***/ "(ssr)/./src/provider/Provider.tsx":
/*!***********************************!*\
  !*** ./src/provider/Provider.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var redux_persist_integration_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! redux-persist/integration/react */ \"(ssr)/./node_modules/redux-persist/es/integration/react.js\");\n/* harmony import */ var _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/reduxRTK/store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n/* harmony import */ var _ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ant-design/nextjs-registry */ \"(ssr)/./node_modules/@ant-design/nextjs-registry/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_Spin_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Spin!=!antd */ \"(ssr)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_LoadingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=LoadingOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(ssr)/./src/components/ui/LoadingSpinner.tsx\");\n/* harmony import */ var _reduxRTK_services_authApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/reduxRTK/services/authApi */ \"(ssr)/./src/reduxRTK/services/authApi.ts\");\n/* harmony import */ var _reduxRTK_services_authSlice__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/reduxRTK/services/authSlice */ \"(ssr)/./src/reduxRTK/services/authSlice.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nconst ClientProvider = ({ children })=>{\n    const [isReady, setIsReady] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ClientProvider.useEffect\": ()=>{\n            console.log(\"ClientProvider mounted\");\n            setIsReady(true);\n            // Set up global refresh function for user data\n            window.__FORCE_REFRESH_USER_DATA = ({\n                \"ClientProvider.useEffect\": async ()=>{\n                    console.log('🔄 Global force refresh user data triggered');\n                    try {\n                        // Get current auth state\n                        const state = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_3__.store.getState();\n                        const { user, accessToken } = state.auth;\n                        if (!user || !accessToken) {\n                            console.log('⚠️ No user or access token for refresh');\n                            return;\n                        }\n                        // Clear cache and fetch fresh data\n                        _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_3__.store.dispatch(_reduxRTK_services_authApi__WEBPACK_IMPORTED_MODULE_6__.userApi.util.invalidateTags([\n                            'User'\n                        ]));\n                        _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_3__.store.dispatch(_reduxRTK_services_authApi__WEBPACK_IMPORTED_MODULE_6__.userApi.util.resetApiState());\n                        // Wait a moment for cache clearing\n                        await new Promise({\n                            \"ClientProvider.useEffect\": (resolve)=>setTimeout(resolve, 500)\n                        }[\"ClientProvider.useEffect\"]);\n                        // Fetch fresh user data\n                        const result = await _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_3__.store.dispatch(_reduxRTK_services_authApi__WEBPACK_IMPORTED_MODULE_6__.userApi.endpoints.getCurrentUser.initiate(undefined, {\n                            forceRefetch: true\n                        }));\n                        if ('data' in result && result.data?.success && result.data.data) {\n                            const freshUser = result.data.data;\n                            console.log('✅ Global refresh: Fresh user data fetched:', {\n                                id: freshUser.id,\n                                paymentStatus: freshUser.paymentStatus\n                            });\n                            // Update Redux state\n                            _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_3__.store.dispatch((0,_reduxRTK_services_authSlice__WEBPACK_IMPORTED_MODULE_7__.setUser)({\n                                user: freshUser,\n                                accessToken\n                            }));\n                            console.log('✅ Global refresh: Redux state updated');\n                        } else {\n                            console.log('❌ Global refresh: Failed to fetch fresh user data');\n                        }\n                    } catch (error) {\n                        console.error('❌ Global refresh error:', error);\n                    }\n                }\n            })[\"ClientProvider.useEffect\"];\n            console.log('✅ Global refresh function set up');\n        }\n    }[\"ClientProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_8__.Provider, {\n        store: _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_3__.store,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(redux_persist_integration_react__WEBPACK_IMPORTED_MODULE_2__.PersistGate, {\n            loading: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center bg-white/80\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: \"large\",\n                    indicator: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LoadingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        style: {\n                            fontSize: 24\n                        },\n                        spin: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\provider\\\\Provider.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 43\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\provider\\\\Provider.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\provider\\\\Provider.tsx\",\n                lineNumber: 77,\n                columnNumber: 11\n            }, void 0),\n            persistor: _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_3__.persistor,\n            onBeforeLift: ()=>{\n                console.log(\"PersistGate - Before lift\");\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_4__.AntdRegistry, {\n                children: isReady ? children : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    fullScreen: true\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\provider\\\\Provider.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 33\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\provider\\\\Provider.tsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\provider\\\\Provider.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\provider\\\\Provider.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClientProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/provider/Provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/customBaseQuery.ts":
/*!*****************************************!*\
  !*** ./src/reduxRTK/customBaseQuery.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   customBaseQuery: () => (/* binding */ customBaseQuery)\n/* harmony export */ });\n/* harmony import */ var _api_apicaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/api/apicaller */ \"(ssr)/./src/api/apicaller.ts\");\n\n// Custom Base Query Function to handle both authenticated and non-authenticated requests\nconst customBaseQuery = async (args)=>{\n    const { urlpath, payloaddata, token } = args;\n    try {\n        // Handle login request separately (no token required)\n        if (urlpath === \"/login\" || urlpath === \"/logout\") {\n            const result = await (0,_api_apicaller__WEBPACK_IMPORTED_MODULE_0__.ApiCaller)(urlpath, payloaddata);\n            if (result.success) {\n                return {\n                    data: result\n                }; // Return data directly if successful\n            } else {\n                return {\n                    error: result\n                }; // Return error if login fails\n            }\n        }\n        // Handle other authenticated requests (requires token)\n        if (token) {\n            console.log(\"Making authenticated request:\", {\n                urlpath,\n                payloaddata,\n                hasToken: !!token\n            });\n            try {\n                // Ensure token is not empty\n                if (!token.trim()) {\n                    console.error(\"Empty token provided for authenticated request\");\n                    return {\n                        error: {\n                            success: false,\n                            message: \"Authentication token is empty. Please log in again.\"\n                        }\n                    };\n                }\n                const result = await (0,_api_apicaller__WEBPACK_IMPORTED_MODULE_0__.apiCallerWithAuth)(payloaddata, urlpath, token);\n                console.log(\"Authenticated request result:\", {\n                    success: result.success,\n                    urlpath\n                });\n                if (result.success) {\n                    return {\n                        data: result\n                    }; // Return data if successful\n                } else {\n                    console.error(\"API error:\", {\n                        urlpath,\n                        error: result.message\n                    });\n                    return {\n                        error: result\n                    }; // Return error if the request fails\n                }\n            } catch (error) {\n                console.error(\"API call exception:\", {\n                    urlpath,\n                    error\n                });\n                return {\n                    error: {\n                        success: false,\n                        message: `Error in API call: ${error.message || \"Unknown error\"}`\n                    }\n                };\n            }\n        }\n        // If no conditions match, return an error\n        console.error(\"Invalid API call - No token provided or invalid request structure\", {\n            urlpath,\n            hasToken: !!token,\n            payloaddata\n        });\n        // Provide a more helpful error message based on the situation\n        let errorMessage = \"Authentication error\";\n        if (!token) {\n            errorMessage = \"No authentication token provided. Please log in again.\";\n        } else if (!urlpath) {\n            errorMessage = \"Invalid API call - No URL path provided.\";\n        } else {\n            errorMessage = \"Invalid API call - Please check your request structure.\";\n        }\n        return {\n            error: {\n                success: false,\n                message: errorMessage\n            }\n        };\n    } catch (error) {\n        console.error(\"API call failed:\", error);\n        return {\n            error: {\n                success: false,\n                message: \"An error occurred during the API call\"\n            }\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/customBaseQuery.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/authApi.ts":
/*!******************************************!*\
  !*** ./src/reduxRTK/services/authApi.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBulkDeleteUsersMutation: () => (/* binding */ useBulkDeleteUsersMutation),\n/* harmony export */   useChangePasswordMutation: () => (/* binding */ useChangePasswordMutation),\n/* harmony export */   useCreateUserMutation: () => (/* binding */ useCreateUserMutation),\n/* harmony export */   useDeleteUserMutation: () => (/* binding */ useDeleteUserMutation),\n/* harmony export */   useGetAllUsersQuery: () => (/* binding */ useGetAllUsersQuery),\n/* harmony export */   useGetCurrentUserQuery: () => (/* binding */ useGetCurrentUserQuery),\n/* harmony export */   useGetUserByIdQuery: () => (/* binding */ useGetUserByIdQuery),\n/* harmony export */   useLoginUserMutation: () => (/* binding */ useLoginUserMutation),\n/* harmony export */   useLogoutUserMutation: () => (/* binding */ useLogoutUserMutation),\n/* harmony export */   useUpdateUserMutation: () => (/* binding */ useUpdateUserMutation),\n/* harmony export */   userApi: () => (/* binding */ userApi)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../customBaseQuery */ \"(ssr)/./src/reduxRTK/customBaseQuery.ts\");\n/* harmony import */ var _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/reduxRTK/store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n// services/userApi.ts\n\n // Import custom baseQuery\n\nconst userApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__.createApi)({\n    reducerPath: 'userApi',\n    baseQuery: _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__.customBaseQuery,\n    tagTypes: [\n        'User'\n    ],\n    endpoints: (builder)=>({\n            // 🔐 Login - NO token required\n            loginUser: builder.mutation({\n                query: (credentials)=>({\n                        urlpath: '/login',\n                        payloaddata: credentials\n                    })\n            }),\n            // 🆕 Register new user\n            createUser: builder.mutation({\n                query: (newUser)=>{\n                    // Get token from store\n                    const token = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth.accessToken || undefined;\n                    return {\n                        urlpath: '/users',\n                        payloaddata: {\n                            mode: 'createnew',\n                            ...newUser\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'User'\n                ]\n            }),\n            // 📄 Get all users (paginated)\n            getAllUsers: builder.query({\n                query: ({ page = 1, limit = 10, search = '' })=>{\n                    // Get token from store\n                    const token = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth.accessToken || undefined;\n                    // Log the search parameters\n                    console.log(\"getAllUsers query params:\", {\n                        page,\n                        limit,\n                        search\n                    });\n                    return {\n                        urlpath: '/users',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            page,\n                            limit,\n                            search: search.trim()\n                        },\n                        token\n                    };\n                },\n                // Force refetch when search parameters change\n                keepUnusedDataFor: 0,\n                providesTags: [\n                    'User'\n                ]\n            }),\n            // 🔍 Get single user by ID\n            getUserById: builder.query({\n                query: (userId)=>{\n                    // Get token from store\n                    const token = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth.accessToken || undefined;\n                    return {\n                        urlpath: '/users',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            userId\n                        },\n                        token\n                    };\n                },\n                providesTags: [\n                    'User'\n                ]\n            }),\n            // 👤 Get current user data (for refreshing user state)\n            getCurrentUser: builder.query({\n                query: ()=>{\n                    // Get token from store\n                    const token = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth.accessToken || undefined;\n                    return {\n                        urlpath: '/users',\n                        payloaddata: {\n                            mode: 'current'\n                        },\n                        token\n                    };\n                },\n                providesTags: [\n                    'User'\n                ]\n            }),\n            // ✏️ Update user\n            updateUser: builder.mutation({\n                query: ({ userId, data })=>{\n                    // Get token from store\n                    const token = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth.accessToken || undefined;\n                    return {\n                        urlpath: '/users',\n                        payloaddata: {\n                            mode: 'update',\n                            userId,\n                            ...data\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'User'\n                ]\n            }),\n            // ❌ Delete user (single)\n            deleteUser: builder.mutation({\n                query: (userId)=>{\n                    // Get token from store and ensure it's string or undefined (not null)\n                    const token = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth.accessToken || undefined;\n                    return {\n                        urlpath: '/users',\n                        payloaddata: {\n                            mode: 'delete',\n                            userId\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'User'\n                ]\n            }),\n            // ❌ Bulk delete users\n            bulkDeleteUsers: builder.mutation({\n                query: (userIds)=>{\n                    // Get token from store and ensure it's string or undefined (not null)\n                    const token = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth.accessToken || undefined;\n                    return {\n                        urlpath: '/users',\n                        payloaddata: {\n                            mode: 'delete',\n                            userIds\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'User'\n                ]\n            }),\n            logoutUser: builder.mutation({\n                query: (data)=>({\n                        urlpath: '/logout',\n                        method: 'POST',\n                        payloaddata: data\n                    })\n            }),\n            // 🔑 Change password\n            changePassword: builder.mutation({\n                query: ({ userId, currentPassword, newPassword })=>{\n                    // Get token from store\n                    const token = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth.accessToken || undefined;\n                    return {\n                        urlpath: '/users',\n                        payloaddata: {\n                            mode: 'update',\n                            userId,\n                            currentPassword,\n                            newPassword,\n                            passwordChange: true\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'User'\n                ]\n            })\n        })\n});\nconst { useLoginUserMutation, useCreateUserMutation, useGetAllUsersQuery, useGetUserByIdQuery, useGetCurrentUserQuery, useUpdateUserMutation, useDeleteUserMutation, useBulkDeleteUsersMutation, useLogoutUserMutation, useChangePasswordMutation } = userApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/authApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/authSlice.ts":
/*!********************************************!*\
  !*** ./src/reduxRTK/services/authSlice.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearUser: () => (/* binding */ clearUser),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   setUser: () => (/* binding */ setUser)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n\nconst initialState = {\n    user: null,\n    accessToken: null\n};\nconst authSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: 'auth',\n    initialState,\n    reducers: {\n        setUser: (state, action)=>{\n            state.user = action.payload.user;\n            state.accessToken = action.payload.accessToken;\n        },\n        clearUser: (state)=>{\n            state.user = null;\n            state.accessToken = null;\n        }\n    }\n});\nconst { setUser, clearUser } = authSlice.actions;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (authSlice.reducer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/authSlice.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/categoryApi.ts":
/*!**********************************************!*\
  !*** ./src/reduxRTK/services/categoryApi.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categoryApi: () => (/* binding */ categoryApi),\n/* harmony export */   useBulkDeleteCategoriesMutation: () => (/* binding */ useBulkDeleteCategoriesMutation),\n/* harmony export */   useCreateCategoryMutation: () => (/* binding */ useCreateCategoryMutation),\n/* harmony export */   useDeleteCategoryMutation: () => (/* binding */ useDeleteCategoryMutation),\n/* harmony export */   useGetAllCategoriesQuery: () => (/* binding */ useGetAllCategoriesQuery),\n/* harmony export */   useGetCategoryByIdQuery: () => (/* binding */ useGetCategoryByIdQuery),\n/* harmony export */   useUpdateCategoryMutation: () => (/* binding */ useUpdateCategoryMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../customBaseQuery */ \"(ssr)/./src/reduxRTK/customBaseQuery.ts\");\n/* harmony import */ var _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/reduxRTK/store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n// services/categoryApi.ts\n\n\n\nconst categoryApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__.createApi)({\n    reducerPath: 'categoryApi',\n    baseQuery: _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__.customBaseQuery,\n    tagTypes: [\n        'Category'\n    ],\n    endpoints: (builder)=>({\n            // Get all categories (paginated)\n            getAllCategories: builder.query({\n                query: ({ page = 1, limit = 10, search = '' })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Log the search parameters\n                    console.log(\"getAllCategories query params:\", {\n                        page,\n                        limit,\n                        search\n                    });\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/categories',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            page,\n                            limit,\n                            search: search.trim()\n                        },\n                        token\n                    };\n                },\n                // Force refetch when search parameters change\n                keepUnusedDataFor: 0,\n                providesTags: [\n                    'Category'\n                ]\n            }),\n            // Get category by ID\n            getCategoryById: builder.query({\n                query: (categoryId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/categories',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            categoryId\n                        },\n                        token\n                    };\n                },\n                providesTags: (_result, _error, id)=>[\n                        {\n                            type: 'Category',\n                            id\n                        }\n                    ]\n            }),\n            // Create new category\n            createCategory: builder.mutation({\n                query: (categoryData)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Debug logging\n                    console.log('Creating category with token:', token ? 'Token exists (not showing for security)' : 'No token found');\n                    console.log('Category data:', categoryData);\n                    console.log('Auth state:', {\n                        hasUser: !!authState?.user,\n                        hasToken: !!authState?.accessToken,\n                        userRole: authState?.user?.role\n                    });\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/categories',\n                        payloaddata: {\n                            mode: 'createnew',\n                            ...categoryData\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Category'\n                ]\n            }),\n            // Update category\n            updateCategory: builder.mutation({\n                query: ({ categoryId, data })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/categories',\n                        payloaddata: {\n                            mode: 'update',\n                            categoryId,\n                            ...data\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: (_result, _error, { categoryId })=>[\n                        {\n                            type: 'Category',\n                            id: categoryId\n                        },\n                        'Category'\n                    ]\n            }),\n            // Delete category (single)\n            deleteCategory: builder.mutation({\n                query: (categoryId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/categories',\n                        payloaddata: {\n                            mode: 'delete',\n                            categoryId\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Category'\n                ]\n            }),\n            // Bulk delete categories\n            bulkDeleteCategories: builder.mutation({\n                query: (categoryIds)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/categories',\n                        payloaddata: {\n                            mode: 'delete',\n                            categoryIds\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Category'\n                ]\n            })\n        })\n});\nconst { useGetAllCategoriesQuery, useGetCategoryByIdQuery, useCreateCategoryMutation, useUpdateCategoryMutation, useDeleteCategoryMutation, useBulkDeleteCategoriesMutation } = categoryApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/categoryApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/dashboardApi.ts":
/*!***********************************************!*\
  !*** ./src/reduxRTK/services/dashboardApi.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dashboardApi: () => (/* binding */ dashboardApi),\n/* harmony export */   useGetDashboardStatsQuery: () => (/* binding */ useGetDashboardStatsQuery)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../customBaseQuery */ \"(ssr)/./src/reduxRTK/customBaseQuery.ts\");\n/* harmony import */ var _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/reduxRTK/store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n// services/dashboardApi.ts\n\n\n\nconst dashboardApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__.createApi)({\n    reducerPath: 'dashboardApi',\n    baseQuery: _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__.customBaseQuery,\n    tagTypes: [\n        'Dashboard'\n    ],\n    endpoints: (builder)=>({\n            // Get dashboard statistics\n            getDashboardStats: builder.query({\n                query: ()=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    // Add multiple cache-busting mechanisms\n                    const timestamp = new Date().getTime();\n                    const randomId = Math.random().toString(36).substring(7);\n                    const sessionId = Date.now() + Math.random();\n                    console.log(`🔄 Dashboard API Call - Timestamp: ${timestamp}, Random: ${randomId}, Session: ${sessionId}`);\n                    console.log(`🔐 Using token: ${token.substring(0, 20)}...`);\n                    return {\n                        urlpath: '/dashboard',\n                        payloaddata: {\n                            mode: 'stats',\n                            timestamp,\n                            randomId,\n                            sessionId,\n                            cacheBuster: `${timestamp}_${randomId}`\n                        },\n                        token\n                    };\n                },\n                keepUnusedDataFor: 0,\n                providesTags: [\n                    'Dashboard'\n                ]\n            })\n        })\n});\n// Export hooks for usage in components\nconst { useGetDashboardStatsQuery } = dashboardApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/dashboardApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/expenseApi.ts":
/*!*********************************************!*\
  !*** ./src/reduxRTK/services/expenseApi.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   expenseApi: () => (/* binding */ expenseApi),\n/* harmony export */   useCreateExpenseMutation: () => (/* binding */ useCreateExpenseMutation),\n/* harmony export */   useDeleteExpenseMutation: () => (/* binding */ useDeleteExpenseMutation),\n/* harmony export */   useGetAllExpensesQuery: () => (/* binding */ useGetAllExpensesQuery),\n/* harmony export */   useGetExpenseByIdQuery: () => (/* binding */ useGetExpenseByIdQuery),\n/* harmony export */   useGetExpenseStatsQuery: () => (/* binding */ useGetExpenseStatsQuery),\n/* harmony export */   useUpdateExpenseMutation: () => (/* binding */ useUpdateExpenseMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../customBaseQuery */ \"(ssr)/./src/reduxRTK/customBaseQuery.ts\");\n/* harmony import */ var _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/reduxRTK/store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n\n\n\nconst expenseApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__.createApi)({\n    reducerPath: 'expenseApi',\n    baseQuery: _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__.customBaseQuery,\n    tagTypes: [\n        'Expense'\n    ],\n    endpoints: (builder)=>({\n            // Get all expenses (paginated)\n            getAllExpenses: builder.query({\n                query: ({ page = 1, limit = 10, search = '', categoryId, startDate, endDate })=>{\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/expenses',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            page,\n                            limit,\n                            search,\n                            categoryId,\n                            startDate,\n                            endDate\n                        },\n                        token\n                    };\n                },\n                keepUnusedDataFor: 0,\n                providesTags: [\n                    'Expense'\n                ]\n            }),\n            // Get expense by ID\n            getExpenseById: builder.query({\n                query: (expenseId)=>{\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/expenses',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            expenseId\n                        },\n                        token\n                    };\n                },\n                providesTags: (_result, _error, id)=>[\n                        {\n                            type: 'Expense',\n                            id\n                        }\n                    ]\n            }),\n            // Create new expense\n            createExpense: builder.mutation({\n                query: (expenseData)=>{\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/expenses',\n                        payloaddata: {\n                            mode: 'createnew',\n                            ...expenseData\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Expense'\n                ]\n            }),\n            // Update expense\n            updateExpense: builder.mutation({\n                query: ({ expenseId, expenseData })=>{\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/expenses',\n                        payloaddata: {\n                            mode: 'update',\n                            expenseId,\n                            ...expenseData\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: (_result, _error, { expenseId })=>[\n                        {\n                            type: 'Expense',\n                            id: expenseId\n                        },\n                        'Expense'\n                    ]\n            }),\n            // Delete expense\n            deleteExpense: builder.mutation({\n                query: (expenseId)=>{\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/expenses',\n                        payloaddata: {\n                            mode: 'delete',\n                            expenseId\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Expense'\n                ]\n            }),\n            // Get expense statistics\n            getExpenseStats: builder.query({\n                query: ({ startDate, endDate } = {})=>{\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/expenses',\n                        payloaddata: {\n                            mode: 'stats',\n                            startDate,\n                            endDate\n                        },\n                        token\n                    };\n                },\n                keepUnusedDataFor: 0,\n                providesTags: [\n                    'Expense'\n                ]\n            })\n        })\n});\n// Export hooks for usage in components\nconst { useGetAllExpensesQuery, useGetExpenseByIdQuery, useCreateExpenseMutation, useUpdateExpenseMutation, useDeleteExpenseMutation, useGetExpenseStatsQuery } = expenseApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/expenseApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/expenseCategoryApi.ts":
/*!*****************************************************!*\
  !*** ./src/reduxRTK/services/expenseCategoryApi.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   expenseCategoryApi: () => (/* binding */ expenseCategoryApi),\n/* harmony export */   useCreateExpenseCategoryMutation: () => (/* binding */ useCreateExpenseCategoryMutation),\n/* harmony export */   useDeleteExpenseCategoryMutation: () => (/* binding */ useDeleteExpenseCategoryMutation),\n/* harmony export */   useGetAllExpenseCategoriesQuery: () => (/* binding */ useGetAllExpenseCategoriesQuery),\n/* harmony export */   useGetExpenseCategoryByIdQuery: () => (/* binding */ useGetExpenseCategoryByIdQuery),\n/* harmony export */   useUpdateExpenseCategoryMutation: () => (/* binding */ useUpdateExpenseCategoryMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../customBaseQuery */ \"(ssr)/./src/reduxRTK/customBaseQuery.ts\");\n/* harmony import */ var _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/reduxRTK/store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n\n\n\nconst expenseCategoryApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__.createApi)({\n    reducerPath: 'expenseCategoryApi',\n    baseQuery: _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__.customBaseQuery,\n    tagTypes: [\n        'ExpenseCategory'\n    ],\n    endpoints: (builder)=>({\n            // Get all expense categories (paginated)\n            getAllExpenseCategories: builder.query({\n                query: ({ page = 1, limit = 50, search = '' })=>{\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/expense-categories',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            page,\n                            limit,\n                            search\n                        },\n                        token\n                    };\n                },\n                keepUnusedDataFor: 300,\n                providesTags: [\n                    'ExpenseCategory'\n                ]\n            }),\n            // Get expense category by ID\n            getExpenseCategoryById: builder.query({\n                query: (categoryId)=>{\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/expense-categories',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            categoryId\n                        },\n                        token\n                    };\n                },\n                providesTags: (_result, _error, id)=>[\n                        {\n                            type: 'ExpenseCategory',\n                            id\n                        }\n                    ]\n            }),\n            // Create new expense category\n            createExpenseCategory: builder.mutation({\n                query: (categoryData)=>{\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/expense-categories',\n                        payloaddata: {\n                            mode: 'createnew',\n                            ...categoryData\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'ExpenseCategory'\n                ]\n            }),\n            // Update expense category\n            updateExpenseCategory: builder.mutation({\n                query: ({ categoryId, categoryData })=>{\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/expense-categories',\n                        payloaddata: {\n                            mode: 'update',\n                            categoryId,\n                            ...categoryData\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: (_result, _error, { categoryId })=>[\n                        {\n                            type: 'ExpenseCategory',\n                            id: categoryId\n                        },\n                        'ExpenseCategory'\n                    ]\n            }),\n            // Delete expense category\n            deleteExpenseCategory: builder.mutation({\n                query: (categoryId)=>{\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/expense-categories',\n                        payloaddata: {\n                            mode: 'delete',\n                            categoryId\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'ExpenseCategory'\n                ]\n            })\n        })\n});\n// Export hooks for usage in components\nconst { useGetAllExpenseCategoriesQuery, useGetExpenseCategoryByIdQuery, useCreateExpenseCategoryMutation, useUpdateExpenseCategoryMutation, useDeleteExpenseCategoryMutation } = expenseCategoryApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/expenseCategoryApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/paymentApi.ts":
/*!*********************************************!*\
  !*** ./src/reduxRTK/services/paymentApi.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   paymentApi: () => (/* binding */ paymentApi),\n/* harmony export */   useGetPaymentByIdQuery: () => (/* binding */ useGetPaymentByIdQuery),\n/* harmony export */   useGetPaymentHistoryQuery: () => (/* binding */ useGetPaymentHistoryQuery),\n/* harmony export */   useInitializePaymentMutation: () => (/* binding */ useInitializePaymentMutation),\n/* harmony export */   useUpdateUserPaymentStatusMutation: () => (/* binding */ useUpdateUserPaymentStatusMutation),\n/* harmony export */   useVerifyPaymentMutation: () => (/* binding */ useVerifyPaymentMutation),\n/* harmony export */   useVerifyPaystackPaymentMutation: () => (/* binding */ useVerifyPaystackPaymentMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../customBaseQuery */ \"(ssr)/./src/reduxRTK/customBaseQuery.ts\");\n/* harmony import */ var _store_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n// services/paymentApi.ts\n\n\n\nconst paymentApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__.createApi)({\n    reducerPath: 'paymentApi',\n    baseQuery: _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__.customBaseQuery,\n    tagTypes: [\n        'Payment'\n    ],\n    endpoints: (builder)=>({\n            // Initialize a Paystack payment\n            initializePayment: builder.mutation({\n                query: (paymentData)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/payment',\n                        payloaddata: {\n                            mode: 'initialize',\n                            ...paymentData\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Payment'\n                ]\n            }),\n            // Get payment history (paginated)\n            getPaymentHistory: builder.query({\n                query: ({ page = 1, limit = 10 })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/payment',\n                        payloaddata: {\n                            mode: 'history',\n                            page,\n                            limit\n                        },\n                        token\n                    };\n                },\n                providesTags: [\n                    'Payment'\n                ]\n            }),\n            // Get payment by ID\n            getPaymentById: builder.query({\n                query: (paymentId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/payment',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            paymentId\n                        },\n                        token\n                    };\n                },\n                providesTags: [\n                    'Payment'\n                ]\n            }),\n            // Verify Paystack payment by reference\n            verifyPaystackPayment: builder.mutation({\n                query: ({ reference })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/payment',\n                        payloaddata: {\n                            mode: 'verify-paystack',\n                            reference\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Payment'\n                ]\n            }),\n            // Verify payment status by transaction ID\n            verifyPayment: builder.mutation({\n                query: ({ transactionId })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/payment',\n                        payloaddata: {\n                            mode: 'verify',\n                            transactionId\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Payment'\n                ]\n            }),\n            // Update user payment status (for admin/superadmin)\n            updateUserPaymentStatus: builder.mutation({\n                query: ({ userId, paymentStatus })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/users',\n                        payloaddata: {\n                            mode: 'update',\n                            userId,\n                            paymentStatus\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Payment'\n                ]\n            })\n        })\n});\nconst { useInitializePaymentMutation, useGetPaymentHistoryQuery, useGetPaymentByIdQuery, useVerifyPaystackPaymentMutation, useVerifyPaymentMutation, useUpdateUserPaymentStatusMutation } = paymentApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/paymentApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/productApi.ts":
/*!*********************************************!*\
  !*** ./src/reduxRTK/services/productApi.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   productApi: () => (/* binding */ productApi),\n/* harmony export */   useBulkDeleteProductsMutation: () => (/* binding */ useBulkDeleteProductsMutation),\n/* harmony export */   useCreateProductMutation: () => (/* binding */ useCreateProductMutation),\n/* harmony export */   useDeleteProductMutation: () => (/* binding */ useDeleteProductMutation),\n/* harmony export */   useGetAllProductsQuery: () => (/* binding */ useGetAllProductsQuery),\n/* harmony export */   useGetProductByIdQuery: () => (/* binding */ useGetProductByIdQuery),\n/* harmony export */   useUpdateProductMutation: () => (/* binding */ useUpdateProductMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../customBaseQuery */ \"(ssr)/./src/reduxRTK/customBaseQuery.ts\");\n/* harmony import */ var _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/reduxRTK/store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n// services/productApi.ts\n\n\n\nconst productApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__.createApi)({\n    reducerPath: 'productApi',\n    baseQuery: _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__.customBaseQuery,\n    tagTypes: [\n        'Product'\n    ],\n    endpoints: (builder)=>({\n            // Get all products (paginated)\n            getAllProducts: builder.query({\n                query: ({ page = 1, limit = 10, search = '' })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/products',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            page,\n                            limit,\n                            search: search.trim()\n                        },\n                        token\n                    };\n                },\n                keepUnusedDataFor: 0,\n                providesTags: [\n                    'Product'\n                ]\n            }),\n            // Get product by ID\n            getProductById: builder.query({\n                query: (productId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/products',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            productId\n                        },\n                        token\n                    };\n                },\n                keepUnusedDataFor: 0,\n                providesTags: (_result, _error, id)=>[\n                        {\n                            type: 'Product',\n                            id\n                        }\n                    ]\n            }),\n            // Create new product\n            createProduct: builder.mutation({\n                query: (productData)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    // Backend expects an array of products\n                    return {\n                        urlpath: '/products',\n                        payloaddata: {\n                            mode: 'createnew',\n                            productsData: [\n                                productData\n                            ]\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Product'\n                ]\n            }),\n            // Update product\n            updateProduct: builder.mutation({\n                query: ({ productId, data })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/products',\n                        payloaddata: {\n                            mode: 'update',\n                            productId,\n                            ...data\n                        },\n                        token\n                    };\n                },\n                // Invalidate all Product tags to ensure the list is refreshed\n                invalidatesTags: [\n                    'Product'\n                ]\n            }),\n            // Delete product (single)\n            deleteProduct: builder.mutation({\n                query: (productId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/products',\n                        payloaddata: {\n                            mode: 'delete',\n                            productId\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Product'\n                ]\n            }),\n            // Bulk delete products\n            bulkDeleteProducts: builder.mutation({\n                query: (productIds)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/products',\n                        payloaddata: {\n                            mode: 'delete',\n                            productIds\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Product'\n                ]\n            })\n        })\n});\nconst { useGetAllProductsQuery, useGetProductByIdQuery, useCreateProductMutation, useUpdateProductMutation, useDeleteProductMutation, useBulkDeleteProductsMutation } = productApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/productApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/purchaseApi.ts":
/*!**********************************************!*\
  !*** ./src/reduxRTK/services/purchaseApi.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   purchaseApi: () => (/* binding */ purchaseApi),\n/* harmony export */   useBulkDeletePurchasesMutation: () => (/* binding */ useBulkDeletePurchasesMutation),\n/* harmony export */   useCreatePurchaseMutation: () => (/* binding */ useCreatePurchaseMutation),\n/* harmony export */   useDeletePurchaseMutation: () => (/* binding */ useDeletePurchaseMutation),\n/* harmony export */   useGetAllPurchasesQuery: () => (/* binding */ useGetAllPurchasesQuery),\n/* harmony export */   useGetPurchaseByIdQuery: () => (/* binding */ useGetPurchaseByIdQuery),\n/* harmony export */   useUpdatePurchaseMutation: () => (/* binding */ useUpdatePurchaseMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../customBaseQuery */ \"(ssr)/./src/reduxRTK/customBaseQuery.ts\");\n/* harmony import */ var _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/reduxRTK/store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n// services/purchaseApi.ts\n\n\n\nconst purchaseApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__.createApi)({\n    reducerPath: 'purchaseApi',\n    baseQuery: _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__.customBaseQuery,\n    tagTypes: [\n        'Purchase'\n    ],\n    endpoints: (builder)=>({\n            // Get all purchases (paginated)\n            getAllPurchases: builder.query({\n                query: ({ page = 1, limit = 10, search = '' })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/purchases',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            page,\n                            limit,\n                            search: search.trim()\n                        },\n                        token\n                    };\n                },\n                keepUnusedDataFor: 0,\n                providesTags: [\n                    'Purchase'\n                ]\n            }),\n            // Get purchase by ID\n            getPurchaseById: builder.query({\n                query: (purchaseId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/purchases',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            purchaseId\n                        },\n                        token\n                    };\n                },\n                providesTags: (_result, _error, id)=>[\n                        {\n                            type: 'Purchase',\n                            id\n                        }\n                    ]\n            }),\n            // Create new purchase\n            createPurchase: builder.mutation({\n                query: (purchaseData)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/purchases',\n                        payloaddata: {\n                            mode: 'createnew',\n                            ...purchaseData\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Purchase'\n                ]\n            }),\n            // Update purchase\n            updatePurchase: builder.mutation({\n                query: ({ purchaseId, data })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/purchases',\n                        payloaddata: {\n                            mode: 'update',\n                            purchaseId,\n                            ...data\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: (_result, _error, { purchaseId })=>[\n                        {\n                            type: 'Purchase',\n                            id: purchaseId\n                        },\n                        'Purchase'\n                    ]\n            }),\n            // Delete purchase (single)\n            deletePurchase: builder.mutation({\n                query: (purchaseId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/purchases',\n                        payloaddata: {\n                            mode: 'delete',\n                            purchaseId\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Purchase'\n                ]\n            }),\n            // Bulk delete purchases\n            bulkDeletePurchases: builder.mutation({\n                query: (purchaseIds)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/purchases',\n                        payloaddata: {\n                            mode: 'delete',\n                            purchaseIds\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Purchase'\n                ]\n            })\n        })\n});\nconst { useGetAllPurchasesQuery, useGetPurchaseByIdQuery, useCreatePurchaseMutation, useUpdatePurchaseMutation, useDeletePurchaseMutation, useBulkDeletePurchasesMutation } = purchaseApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/purchaseApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/receiptApi.ts":
/*!*********************************************!*\
  !*** ./src/reduxRTK/services/receiptApi.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   receiptApi: () => (/* binding */ receiptApi),\n/* harmony export */   useBulkDeleteReceiptsMutation: () => (/* binding */ useBulkDeleteReceiptsMutation),\n/* harmony export */   useDeleteReceiptMutation: () => (/* binding */ useDeleteReceiptMutation),\n/* harmony export */   useGetAllReceiptsQuery: () => (/* binding */ useGetAllReceiptsQuery),\n/* harmony export */   useGetReceiptBySaleIdQuery: () => (/* binding */ useGetReceiptBySaleIdQuery)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../customBaseQuery */ \"(ssr)/./src/reduxRTK/customBaseQuery.ts\");\n/* harmony import */ var _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/reduxRTK/store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n// services/receiptApi.ts\n\n\n\nconst receiptApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__.createApi)({\n    reducerPath: 'receiptApi',\n    baseQuery: _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__.customBaseQuery,\n    tagTypes: [\n        'Receipt'\n    ],\n    endpoints: (builder)=>({\n            // Get all receipts (paginated)\n            getAllReceipts: builder.query({\n                query: ({ page = 1, limit = 10, search = '' })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/receipt',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            page,\n                            limit,\n                            search\n                        },\n                        token\n                    };\n                },\n                providesTags: [\n                    'Receipt'\n                ]\n            }),\n            // Get receipt by sale ID\n            getReceiptBySaleId: builder.query({\n                query: (saleId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/receipt',\n                        payloaddata: {\n                            mode: 'retrieveBySaleId',\n                            saleId\n                        },\n                        token\n                    };\n                },\n                providesTags: [\n                    'Receipt'\n                ]\n            }),\n            // Delete receipt (single)\n            deleteReceipt: builder.mutation({\n                query: (receiptId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/receipt',\n                        payloaddata: {\n                            mode: 'delete',\n                            id: receiptId\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Receipt'\n                ]\n            }),\n            // Bulk delete receipts\n            bulkDeleteReceipts: builder.mutation({\n                query: (receiptIds)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/receipt',\n                        payloaddata: {\n                            mode: 'delete',\n                            receiptIds\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Receipt'\n                ]\n            })\n        })\n});\nconst { useGetAllReceiptsQuery, useGetReceiptBySaleIdQuery, useDeleteReceiptMutation, useBulkDeleteReceiptsMutation } = receiptApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/receiptApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/salesApi.ts":
/*!*******************************************!*\
  !*** ./src/reduxRTK/services/salesApi.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   salesApi: () => (/* binding */ salesApi),\n/* harmony export */   useBulkDeleteSalesMutation: () => (/* binding */ useBulkDeleteSalesMutation),\n/* harmony export */   useCreateSaleMutation: () => (/* binding */ useCreateSaleMutation),\n/* harmony export */   useDeleteSaleMutation: () => (/* binding */ useDeleteSaleMutation),\n/* harmony export */   useGetAllSalesQuery: () => (/* binding */ useGetAllSalesQuery),\n/* harmony export */   useGetSaleByIdQuery: () => (/* binding */ useGetSaleByIdQuery),\n/* harmony export */   useUpdateSaleMutation: () => (/* binding */ useUpdateSaleMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../customBaseQuery */ \"(ssr)/./src/reduxRTK/customBaseQuery.ts\");\n/* harmony import */ var _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/reduxRTK/store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n// services/salesApi.ts\n\n\n\nconst salesApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__.createApi)({\n    reducerPath: 'salesApi',\n    baseQuery: _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__.customBaseQuery,\n    tagTypes: [\n        'Sale'\n    ],\n    endpoints: (builder)=>({\n            // Get all sales (paginated)\n            getAllSales: builder.query({\n                query: ({ page = 1, limit = 10, search = '' })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/sales',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            page,\n                            limit,\n                            search: search.trim()\n                        },\n                        token\n                    };\n                },\n                keepUnusedDataFor: 0,\n                providesTags: [\n                    'Sale'\n                ]\n            }),\n            // Get sale by ID\n            getSaleById: builder.query({\n                query: (saleId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/sales',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            saleId\n                        },\n                        token\n                    };\n                },\n                providesTags: (result, error, id)=>[\n                        {\n                            type: 'Sale',\n                            id\n                        }\n                    ]\n            }),\n            // Create new sale\n            createSale: builder.mutation({\n                query: (saleData)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/sales',\n                        payloaddata: {\n                            mode: 'createnew',\n                            saleData\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Sale'\n                ]\n            }),\n            // Update sale\n            updateSale: builder.mutation({\n                query: ({ saleId, updateData })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/sales',\n                        payloaddata: {\n                            mode: 'update',\n                            saleId,\n                            ...updateData\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: (result, error, { saleId })=>[\n                        {\n                            type: 'Sale',\n                            id: saleId\n                        }\n                    ]\n            }),\n            // Delete sale (single)\n            deleteSale: builder.mutation({\n                query: (saleId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/sales',\n                        payloaddata: {\n                            mode: 'delete',\n                            saleId\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Sale'\n                ]\n            }),\n            // Bulk delete sales\n            bulkDeleteSales: builder.mutation({\n                query: (saleIds)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/sales',\n                        payloaddata: {\n                            mode: 'delete',\n                            saleIds\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Sale'\n                ]\n            })\n        })\n});\nconst { useGetAllSalesQuery, useGetSaleByIdQuery, useCreateSaleMutation, useUpdateSaleMutation, useDeleteSaleMutation, useBulkDeleteSalesMutation } = salesApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/salesApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/stockAdjustmentApi.ts":
/*!*****************************************************!*\
  !*** ./src/reduxRTK/services/stockAdjustmentApi.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stockAdjustmentApi: () => (/* binding */ stockAdjustmentApi),\n/* harmony export */   useCreateStockAdjustmentMutation: () => (/* binding */ useCreateStockAdjustmentMutation),\n/* harmony export */   useDeleteStockAdjustmentMutation: () => (/* binding */ useDeleteStockAdjustmentMutation),\n/* harmony export */   useGetAllStockAdjustmentsQuery: () => (/* binding */ useGetAllStockAdjustmentsQuery),\n/* harmony export */   useGetStockAdjustmentByIdQuery: () => (/* binding */ useGetStockAdjustmentByIdQuery),\n/* harmony export */   useUpdateStockAdjustmentMutation: () => (/* binding */ useUpdateStockAdjustmentMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../customBaseQuery */ \"(ssr)/./src/reduxRTK/customBaseQuery.ts\");\n/* harmony import */ var _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/reduxRTK/store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n// services/stockAdjustmentApi.ts\n\n\n\nconst stockAdjustmentApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__.createApi)({\n    reducerPath: 'stockAdjustmentApi',\n    baseQuery: _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__.customBaseQuery,\n    tagTypes: [\n        'StockAdjustment'\n    ],\n    endpoints: (builder)=>({\n            // Get all stock adjustments (paginated)\n            getAllStockAdjustments: builder.query({\n                query: ({ page = 1, limit = 10, search = '' })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/stockadjustment',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            page,\n                            limit,\n                            search: search.trim()\n                        },\n                        token\n                    };\n                },\n                keepUnusedDataFor: 0,\n                providesTags: [\n                    'StockAdjustment'\n                ]\n            }),\n            // Get stock adjustment by ID\n            getStockAdjustmentById: builder.query({\n                query: (adjustmentId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/stockadjustment',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            adjustmentId\n                        },\n                        token\n                    };\n                },\n                providesTags: (_result, _error, id)=>[\n                        {\n                            type: 'StockAdjustment',\n                            id\n                        }\n                    ]\n            }),\n            // Create new stock adjustment\n            createStockAdjustment: builder.mutation({\n                query: (adjustmentData)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/stockadjustment',\n                        payloaddata: {\n                            mode: 'createnew',\n                            ...adjustmentData\n                        },\n                        token\n                    };\n                },\n                // Invalidate StockAdjustment tags to refresh data\n                invalidatesTags: [\n                    'StockAdjustment'\n                ]\n            }),\n            // Update stock adjustment\n            updateStockAdjustment: builder.mutation({\n                query: ({ adjustmentId, data })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/stockadjustment',\n                        payloaddata: {\n                            mode: 'update',\n                            adjustmentId,\n                            ...data\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: (_result, _error, { adjustmentId })=>[\n                        {\n                            type: 'StockAdjustment',\n                            id: adjustmentId\n                        },\n                        'StockAdjustment'\n                    ]\n            }),\n            // Delete stock adjustment\n            deleteStockAdjustment: builder.mutation({\n                query: (adjustmentId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/stockadjustment',\n                        payloaddata: {\n                            mode: 'delete',\n                            adjustmentId\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'StockAdjustment'\n                ]\n            })\n        })\n});\nconst { useGetAllStockAdjustmentsQuery, useGetStockAdjustmentByIdQuery, useCreateStockAdjustmentMutation, useUpdateStockAdjustmentMutation, useDeleteStockAdjustmentMutation } = stockAdjustmentApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/stockAdjustmentApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/storeApi.ts":
/*!*******************************************!*\
  !*** ./src/reduxRTK/services/storeApi.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   storeApi: () => (/* binding */ storeApi),\n/* harmony export */   useBulkDeleteStoresMutation: () => (/* binding */ useBulkDeleteStoresMutation),\n/* harmony export */   useCreateStoreMutation: () => (/* binding */ useCreateStoreMutation),\n/* harmony export */   useDeleteStoreMutation: () => (/* binding */ useDeleteStoreMutation),\n/* harmony export */   useGetAllStoresQuery: () => (/* binding */ useGetAllStoresQuery),\n/* harmony export */   useGetStoreByIdQuery: () => (/* binding */ useGetStoreByIdQuery),\n/* harmony export */   useUpdateStoreMutation: () => (/* binding */ useUpdateStoreMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../customBaseQuery */ \"(ssr)/./src/reduxRTK/customBaseQuery.ts\");\n/* harmony import */ var _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/reduxRTK/store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n// services/storeApi.ts\n\n\n\nconst storeApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__.createApi)({\n    reducerPath: 'storeApi',\n    baseQuery: _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__.customBaseQuery,\n    tagTypes: [\n        'Store'\n    ],\n    endpoints: (builder)=>({\n            // Get all stores (paginated)\n            getAllStores: builder.query({\n                query: ({ page = 1, limit = 10, search = '' })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/stores',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            page,\n                            limit,\n                            search: search.trim()\n                        },\n                        token\n                    };\n                },\n                keepUnusedDataFor: 0,\n                providesTags: [\n                    'Store'\n                ]\n            }),\n            // Get store by ID\n            getStoreById: builder.query({\n                query: (storeId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/stores',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            storeId\n                        },\n                        token\n                    };\n                },\n                providesTags: (result, error, id)=>[\n                        {\n                            type: 'Store',\n                            id\n                        }\n                    ]\n            }),\n            // Create new store\n            createStore: builder.mutation({\n                query: (storeData)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/stores',\n                        payloaddata: {\n                            mode: 'createnew',\n                            ...storeData\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Store'\n                ]\n            }),\n            // Update store\n            updateStore: builder.mutation({\n                query: ({ storeId, updateData })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/stores',\n                        payloaddata: {\n                            mode: 'update',\n                            storeId,\n                            ...updateData\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: (result, error, { storeId })=>[\n                        {\n                            type: 'Store',\n                            id: storeId\n                        }\n                    ]\n            }),\n            // Delete store\n            deleteStore: builder.mutation({\n                query: (storeId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/stores',\n                        payloaddata: {\n                            mode: 'delete',\n                            storeId\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Store'\n                ]\n            }),\n            // Bulk delete stores\n            bulkDeleteStores: builder.mutation({\n                query: (storeIds)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/stores',\n                        payloaddata: {\n                            mode: 'delete',\n                            storeIds\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Store'\n                ]\n            })\n        })\n});\nconst { useGetAllStoresQuery, useGetStoreByIdQuery, useCreateStoreMutation, useUpdateStoreMutation, useDeleteStoreMutation, useBulkDeleteStoresMutation } = storeApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/storeApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/supplierApi.ts":
/*!**********************************************!*\
  !*** ./src/reduxRTK/services/supplierApi.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supplierApi: () => (/* binding */ supplierApi),\n/* harmony export */   useBulkDeleteSuppliersMutation: () => (/* binding */ useBulkDeleteSuppliersMutation),\n/* harmony export */   useCreateSupplierMutation: () => (/* binding */ useCreateSupplierMutation),\n/* harmony export */   useDeleteSupplierMutation: () => (/* binding */ useDeleteSupplierMutation),\n/* harmony export */   useGetAllSuppliersQuery: () => (/* binding */ useGetAllSuppliersQuery),\n/* harmony export */   useGetSupplierByIdQuery: () => (/* binding */ useGetSupplierByIdQuery),\n/* harmony export */   useUpdateSupplierMutation: () => (/* binding */ useUpdateSupplierMutation)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../customBaseQuery */ \"(ssr)/./src/reduxRTK/customBaseQuery.ts\");\n/* harmony import */ var _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/reduxRTK/store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n// services/supplierApi.ts\n\n\n\nconst supplierApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__.createApi)({\n    reducerPath: 'supplierApi',\n    baseQuery: _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__.customBaseQuery,\n    tagTypes: [\n        'Supplier'\n    ],\n    endpoints: (builder)=>({\n            // Get all suppliers (paginated)\n            getAllSuppliers: builder.query({\n                query: ({ page = 1, limit = 10, search = '' })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    // Get user ID from store\n                    const userId = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth.user?.id;\n                    console.log(\"API call - User ID:\", userId);\n                    return {\n                        urlpath: '/suppliers',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            page,\n                            limit,\n                            search: search.trim(),\n                            // Include userId for debugging, but we'll filter client-side\n                            userId\n                        },\n                        token\n                    };\n                },\n                keepUnusedDataFor: 0,\n                providesTags: [\n                    'Supplier'\n                ]\n            }),\n            // Get supplier by ID\n            getSupplierById: builder.query({\n                query: (supplierId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/suppliers',\n                        payloaddata: {\n                            mode: 'retrieve',\n                            supplierId\n                        },\n                        token\n                    };\n                },\n                providesTags: (_result, _error, id)=>[\n                        {\n                            type: 'Supplier',\n                            id\n                        }\n                    ]\n            }),\n            // Create new supplier\n            createSupplier: builder.mutation({\n                query: (supplierData)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/suppliers',\n                        payloaddata: {\n                            mode: 'createnew',\n                            ...supplierData\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Supplier'\n                ]\n            }),\n            // Update supplier\n            updateSupplier: builder.mutation({\n                query: ({ supplierId, data })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/suppliers',\n                        payloaddata: {\n                            mode: 'update',\n                            supplierId,\n                            ...data\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: (_result, _error, { supplierId })=>[\n                        {\n                            type: 'Supplier',\n                            id: supplierId\n                        },\n                        'Supplier'\n                    ]\n            }),\n            // Delete supplier (single)\n            deleteSupplier: builder.mutation({\n                query: (supplierId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/suppliers',\n                        payloaddata: {\n                            mode: 'delete',\n                            supplierId\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Supplier'\n                ]\n            }),\n            // Bulk delete suppliers\n            bulkDeleteSuppliers: builder.mutation({\n                query: (supplierIds)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/suppliers',\n                        payloaddata: {\n                            mode: 'delete',\n                            supplierIds\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'Supplier'\n                ]\n            })\n        })\n});\nconst { useGetAllSuppliersQuery, useGetSupplierByIdQuery, useCreateSupplierMutation, useUpdateSupplierMutation, useDeleteSupplierMutation, useBulkDeleteSuppliersMutation } = supplierApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvcmVkdXhSVEsvc2VydmljZXMvc3VwcGxpZXJBcGkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFBLDBCQUEwQjtBQUMrQjtBQUNKO0FBRU47QUFxQ3hDLE1BQU1HLGNBQWNILHVFQUFTQSxDQUFDO0lBQ25DSSxhQUFhO0lBQ2JDLFdBQVdKLDZEQUFlQTtJQUMxQkssVUFBVTtRQUFDO0tBQVc7SUFDdEJDLFdBQVcsQ0FBQ0MsVUFBYTtZQUN2QixnQ0FBZ0M7WUFDaENDLGlCQUFpQkQsUUFBUUUsS0FBSyxDQUFzRjtnQkFDbEhBLE9BQU8sQ0FBQyxFQUFFQyxPQUFPLENBQUMsRUFBRUMsUUFBUSxFQUFFLEVBQUVDLFNBQVMsRUFBRSxFQUFFO29CQUMzQyw2REFBNkQ7b0JBQzdELE1BQU1DLFlBQVlaLHdEQUFLQSxDQUFDYSxRQUFRLEdBQUdDLElBQUk7b0JBQ3ZDLE1BQU1DLFFBQVFILFdBQVdJLGVBQWU7b0JBRXhDLDJEQUEyRDtvQkFDM0QsSUFBSSxDQUFDRCxPQUFPO3dCQUNWRSxRQUFRQyxLQUFLLENBQUM7d0JBQ2QsTUFBTSxJQUFJQyxNQUFNO29CQUNsQjtvQkFFQSx5QkFBeUI7b0JBQ3pCLE1BQU1DLFNBQVNwQix3REFBS0EsQ0FBQ2EsUUFBUSxHQUFHQyxJQUFJLENBQUNPLElBQUksRUFBRUM7b0JBRTNDTCxRQUFRTSxHQUFHLENBQUMsdUJBQXVCSDtvQkFFbkMsT0FBTzt3QkFDTEksU0FBUzt3QkFDVEMsYUFBYTs0QkFDWEMsTUFBTTs0QkFDTmpCOzRCQUNBQzs0QkFDQUMsUUFBUUEsT0FBT2dCLElBQUk7NEJBQ25CLDZEQUE2RDs0QkFDN0RQO3dCQUNGO3dCQUNBTDtvQkFDRjtnQkFDRjtnQkFDQWEsbUJBQW1CO2dCQUNuQkMsY0FBYztvQkFBQztpQkFBVztZQUM1QjtZQUVBLHFCQUFxQjtZQUNyQkMsaUJBQWlCeEIsUUFBUUUsS0FBSyxDQUFnQztnQkFDNURBLE9BQU8sQ0FBQ3VCO29CQUNOLDZEQUE2RDtvQkFDN0QsTUFBTW5CLFlBQVlaLHdEQUFLQSxDQUFDYSxRQUFRLEdBQUdDLElBQUk7b0JBQ3ZDLE1BQU1DLFFBQVFILFdBQVdJLGVBQWU7b0JBRXhDLDJEQUEyRDtvQkFDM0QsSUFBSSxDQUFDRCxPQUFPO3dCQUNWRSxRQUFRQyxLQUFLLENBQUM7d0JBQ2QsTUFBTSxJQUFJQyxNQUFNO29CQUNsQjtvQkFFQSxPQUFPO3dCQUNMSyxTQUFTO3dCQUNUQyxhQUFhOzRCQUNYQyxNQUFNOzRCQUNOSzt3QkFDRjt3QkFDQWhCO29CQUNGO2dCQUNGO2dCQUNBYyxjQUFjLENBQUNHLFNBQVNDLFFBQVFYLEtBQU87d0JBQUM7NEJBQUVZLE1BQU07NEJBQVlaO3dCQUFHO3FCQUFFO1lBQ25FO1lBRUEsc0JBQXNCO1lBQ3RCYSxnQkFBZ0I3QixRQUFROEIsUUFBUSxDQUEyQztnQkFDekU1QixPQUFPLENBQUM2QjtvQkFDTiw2REFBNkQ7b0JBQzdELE1BQU16QixZQUFZWix3REFBS0EsQ0FBQ2EsUUFBUSxHQUFHQyxJQUFJO29CQUN2QyxNQUFNQyxRQUFRSCxXQUFXSSxlQUFlO29CQUV4QywyREFBMkQ7b0JBQzNELElBQUksQ0FBQ0QsT0FBTzt3QkFDVkUsUUFBUUMsS0FBSyxDQUFDO3dCQUNkLE1BQU0sSUFBSUMsTUFBTTtvQkFDbEI7b0JBRUEsT0FBTzt3QkFDTEssU0FBUzt3QkFDVEMsYUFBYTs0QkFDWEMsTUFBTTs0QkFDTixHQUFHVyxZQUFZO3dCQUNqQjt3QkFDQXRCO29CQUNGO2dCQUNGO2dCQUNBdUIsaUJBQWlCO29CQUFDO2lCQUFXO1lBQy9CO1lBRUEsa0JBQWtCO1lBQ2xCQyxnQkFBZ0JqQyxRQUFROEIsUUFBUSxDQUF5RTtnQkFDdkc1QixPQUFPLENBQUMsRUFBRXVCLFVBQVUsRUFBRVMsSUFBSSxFQUFFO29CQUMxQiw2REFBNkQ7b0JBQzdELE1BQU01QixZQUFZWix3REFBS0EsQ0FBQ2EsUUFBUSxHQUFHQyxJQUFJO29CQUN2QyxNQUFNQyxRQUFRSCxXQUFXSSxlQUFlO29CQUV4QywyREFBMkQ7b0JBQzNELElBQUksQ0FBQ0QsT0FBTzt3QkFDVkUsUUFBUUMsS0FBSyxDQUFDO3dCQUNkLE1BQU0sSUFBSUMsTUFBTTtvQkFDbEI7b0JBRUEsT0FBTzt3QkFDTEssU0FBUzt3QkFDVEMsYUFBYTs0QkFDWEMsTUFBTTs0QkFDTks7NEJBQ0EsR0FBR1MsSUFBSTt3QkFDVDt3QkFDQXpCO29CQUNGO2dCQUNGO2dCQUNBdUIsaUJBQWlCLENBQUNOLFNBQVNDLFFBQVEsRUFBRUYsVUFBVSxFQUFFLEdBQUs7d0JBQ3BEOzRCQUFFRyxNQUFNOzRCQUFZWixJQUFJUzt3QkFBVzt3QkFDbkM7cUJBQ0Q7WUFDSDtZQUVBLDJCQUEyQjtZQUMzQlUsZ0JBQWdCbkMsUUFBUThCLFFBQVEsQ0FBNEM7Z0JBQzFFNUIsT0FBTyxDQUFDdUI7b0JBQ04sNkRBQTZEO29CQUM3RCxNQUFNbkIsWUFBWVosd0RBQUtBLENBQUNhLFFBQVEsR0FBR0MsSUFBSTtvQkFDdkMsTUFBTUMsUUFBUUgsV0FBV0ksZUFBZTtvQkFFeEMsMkRBQTJEO29CQUMzRCxJQUFJLENBQUNELE9BQU87d0JBQ1ZFLFFBQVFDLEtBQUssQ0FBQzt3QkFDZCxNQUFNLElBQUlDLE1BQU07b0JBQ2xCO29CQUVBLE9BQU87d0JBQ0xLLFNBQVM7d0JBQ1RDLGFBQWE7NEJBQ1hDLE1BQU07NEJBQ05LO3dCQUNGO3dCQUNBaEI7b0JBQ0Y7Z0JBQ0Y7Z0JBQ0F1QixpQkFBaUI7b0JBQUM7aUJBQVc7WUFDL0I7WUFFQSx3QkFBd0I7WUFDeEJJLHFCQUFxQnBDLFFBQVE4QixRQUFRLENBQWtEO2dCQUNyRjVCLE9BQU8sQ0FBQ21DO29CQUNOLDZEQUE2RDtvQkFDN0QsTUFBTS9CLFlBQVlaLHdEQUFLQSxDQUFDYSxRQUFRLEdBQUdDLElBQUk7b0JBQ3ZDLE1BQU1DLFFBQVFILFdBQVdJLGVBQWU7b0JBRXhDLDJEQUEyRDtvQkFDM0QsSUFBSSxDQUFDRCxPQUFPO3dCQUNWRSxRQUFRQyxLQUFLLENBQUM7d0JBQ2QsTUFBTSxJQUFJQyxNQUFNO29CQUNsQjtvQkFFQSxPQUFPO3dCQUNMSyxTQUFTO3dCQUNUQyxhQUFhOzRCQUNYQyxNQUFNOzRCQUNOaUI7d0JBQ0Y7d0JBQ0E1QjtvQkFDRjtnQkFDRjtnQkFDQXVCLGlCQUFpQjtvQkFBQztpQkFBVztZQUMvQjtRQUNGO0FBQ0YsR0FBRztBQUVJLE1BQU0sRUFDWE0sdUJBQXVCLEVBQ3ZCQyx1QkFBdUIsRUFDdkJDLHlCQUF5QixFQUN6QkMseUJBQXlCLEVBQ3pCQyx5QkFBeUIsRUFDekJDLDhCQUE4QixFQUMvQixHQUFHaEQsWUFBWSIsInNvdXJjZXMiOlsiRTpcXFBST0pFQ1RTXFxwb3NcXHBvc2Zyb250ZW5kXFxzcmNcXHJlZHV4UlRLXFxzZXJ2aWNlc1xcc3VwcGxpZXJBcGkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc2VydmljZXMvc3VwcGxpZXJBcGkudHNcbmltcG9ydCB7IGNyZWF0ZUFwaSB9IGZyb20gJ0ByZWR1eGpzL3Rvb2xraXQvcXVlcnkvcmVhY3QnO1xuaW1wb3J0IHsgY3VzdG9tQmFzZVF1ZXJ5IH0gZnJvbSAnLi4vY3VzdG9tQmFzZVF1ZXJ5JztcbmltcG9ydCB7IEFwaVJlc3BvbnNlIH0gZnJvbSAnQC90eXBlcy91c2VyJztcbmltcG9ydCB7IHN0b3JlIH0gZnJvbSAnQC9yZWR1eFJUSy9zdG9yZS9zdG9yZSc7XG5cbi8vIERlZmluZSBTdXBwbGllciB0eXBlc1xuZXhwb3J0IGludGVyZmFjZSBTdXBwbGllciB7XG4gIGlkOiBudW1iZXI7XG4gIG5hbWU6IHN0cmluZztcbiAgZW1haWw6IHN0cmluZztcbiAgcGhvbmU6IHN0cmluZztcbiAgYWRkcmVzczogc3RyaW5nO1xuICBjb250YWN0UGVyc29uOiBzdHJpbmc7XG4gIGNyZWF0ZWRCeTogbnVtYmVyO1xuICBjcmVhdGVkQXQ6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBQYWdpbmF0ZWRTdXBwbGllcnMge1xuICB0b3RhbDogbnVtYmVyO1xuICBwYWdlOiBudW1iZXI7XG4gIHBlclBhZ2U6IG51bWJlcjtcbiAgc3VwcGxpZXJzOiBTdXBwbGllcltdO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIENyZWF0ZVN1cHBsaWVyRHRvIHtcbiAgbmFtZTogc3RyaW5nO1xuICBlbWFpbD86IHN0cmluZztcbiAgcGhvbmU/OiBzdHJpbmc7XG4gIGFkZHJlc3M/OiBzdHJpbmc7XG4gIGNvbnRhY3RQZXJzb24/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgVXBkYXRlU3VwcGxpZXJEdG8ge1xuICBuYW1lPzogc3RyaW5nO1xuICBlbWFpbD86IHN0cmluZztcbiAgcGhvbmU/OiBzdHJpbmc7XG4gIGFkZHJlc3M/OiBzdHJpbmc7XG4gIGNvbnRhY3RQZXJzb24/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBjb25zdCBzdXBwbGllckFwaSA9IGNyZWF0ZUFwaSh7XG4gIHJlZHVjZXJQYXRoOiAnc3VwcGxpZXJBcGknIGFzIGNvbnN0LFxuICBiYXNlUXVlcnk6IGN1c3RvbUJhc2VRdWVyeSxcbiAgdGFnVHlwZXM6IFsnU3VwcGxpZXInXSBhcyBjb25zdCxcbiAgZW5kcG9pbnRzOiAoYnVpbGRlcikgPT4gKHtcbiAgICAvLyBHZXQgYWxsIHN1cHBsaWVycyAocGFnaW5hdGVkKVxuICAgIGdldEFsbFN1cHBsaWVyczogYnVpbGRlci5xdWVyeTxBcGlSZXNwb25zZTxQYWdpbmF0ZWRTdXBwbGllcnM+LCB7IHBhZ2U/OiBudW1iZXI7IGxpbWl0PzogbnVtYmVyOyBzZWFyY2g/OiBzdHJpbmcgfT4oe1xuICAgICAgcXVlcnk6ICh7IHBhZ2UgPSAxLCBsaW1pdCA9IDEwLCBzZWFyY2ggPSAnJyB9KTogeyB1cmxwYXRoOiBzdHJpbmc7IHBheWxvYWRkYXRhOiBhbnk7IHRva2VuPzogc3RyaW5nIH0gPT4ge1xuICAgICAgICAvLyBHZXQgdG9rZW4gZnJvbSBzdG9yZSAtIGVuc3VyZSBpdCdzIGEgc3RyaW5nLCBub3QgdW5kZWZpbmVkXG4gICAgICAgIGNvbnN0IGF1dGhTdGF0ZSA9IHN0b3JlLmdldFN0YXRlKCkuYXV0aDtcbiAgICAgICAgY29uc3QgdG9rZW4gPSBhdXRoU3RhdGU/LmFjY2Vzc1Rva2VuIHx8ICcnO1xuXG4gICAgICAgIC8vIENoZWNrIGlmIHRva2VuIGlzIG1pc3NpbmcgYW5kIHRocm93IGEgbW9yZSBoZWxwZnVsIGVycm9yXG4gICAgICAgIGlmICghdG9rZW4pIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdBdXRoZW50aWNhdGlvbiB0b2tlbiBpcyBtaXNzaW5nLiBVc2VyIG1heSBuZWVkIHRvIGxvZyBpbiBhZ2Fpbi4nKTtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHRva2VuIGlzIG1pc3NpbmcuIFBsZWFzZSBsb2cgaW4gYWdhaW4uJyk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBHZXQgdXNlciBJRCBmcm9tIHN0b3JlXG4gICAgICAgIGNvbnN0IHVzZXJJZCA9IHN0b3JlLmdldFN0YXRlKCkuYXV0aC51c2VyPy5pZDtcblxuICAgICAgICBjb25zb2xlLmxvZyhcIkFQSSBjYWxsIC0gVXNlciBJRDpcIiwgdXNlcklkKTtcblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHVybHBhdGg6ICcvc3VwcGxpZXJzJyxcbiAgICAgICAgICBwYXlsb2FkZGF0YToge1xuICAgICAgICAgICAgbW9kZTogJ3JldHJpZXZlJyxcbiAgICAgICAgICAgIHBhZ2UsXG4gICAgICAgICAgICBsaW1pdCxcbiAgICAgICAgICAgIHNlYXJjaDogc2VhcmNoLnRyaW0oKSxcbiAgICAgICAgICAgIC8vIEluY2x1ZGUgdXNlcklkIGZvciBkZWJ1Z2dpbmcsIGJ1dCB3ZSdsbCBmaWx0ZXIgY2xpZW50LXNpZGVcbiAgICAgICAgICAgIHVzZXJJZFxuICAgICAgICAgIH0sXG4gICAgICAgICAgdG9rZW4sXG4gICAgICAgIH07XG4gICAgICB9LFxuICAgICAga2VlcFVudXNlZERhdGFGb3I6IDAsXG4gICAgICBwcm92aWRlc1RhZ3M6IFsnU3VwcGxpZXInXSxcbiAgICB9KSxcblxuICAgIC8vIEdldCBzdXBwbGllciBieSBJRFxuICAgIGdldFN1cHBsaWVyQnlJZDogYnVpbGRlci5xdWVyeTxBcGlSZXNwb25zZTxTdXBwbGllcj4sIG51bWJlcj4oe1xuICAgICAgcXVlcnk6IChzdXBwbGllcklkKTogeyB1cmxwYXRoOiBzdHJpbmc7IHBheWxvYWRkYXRhOiBhbnk7IHRva2VuPzogc3RyaW5nIH0gPT4ge1xuICAgICAgICAvLyBHZXQgdG9rZW4gZnJvbSBzdG9yZSAtIGVuc3VyZSBpdCdzIGEgc3RyaW5nLCBub3QgdW5kZWZpbmVkXG4gICAgICAgIGNvbnN0IGF1dGhTdGF0ZSA9IHN0b3JlLmdldFN0YXRlKCkuYXV0aDtcbiAgICAgICAgY29uc3QgdG9rZW4gPSBhdXRoU3RhdGU/LmFjY2Vzc1Rva2VuIHx8ICcnO1xuXG4gICAgICAgIC8vIENoZWNrIGlmIHRva2VuIGlzIG1pc3NpbmcgYW5kIHRocm93IGEgbW9yZSBoZWxwZnVsIGVycm9yXG4gICAgICAgIGlmICghdG9rZW4pIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdBdXRoZW50aWNhdGlvbiB0b2tlbiBpcyBtaXNzaW5nLiBVc2VyIG1heSBuZWVkIHRvIGxvZyBpbiBhZ2Fpbi4nKTtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHRva2VuIGlzIG1pc3NpbmcuIFBsZWFzZSBsb2cgaW4gYWdhaW4uJyk7XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHVybHBhdGg6ICcvc3VwcGxpZXJzJyxcbiAgICAgICAgICBwYXlsb2FkZGF0YToge1xuICAgICAgICAgICAgbW9kZTogJ3JldHJpZXZlJyxcbiAgICAgICAgICAgIHN1cHBsaWVySWQsXG4gICAgICAgICAgfSxcbiAgICAgICAgICB0b2tlbixcbiAgICAgICAgfTtcbiAgICAgIH0sXG4gICAgICBwcm92aWRlc1RhZ3M6IChfcmVzdWx0LCBfZXJyb3IsIGlkKSA9PiBbeyB0eXBlOiAnU3VwcGxpZXInLCBpZCB9XSxcbiAgICB9KSxcblxuICAgIC8vIENyZWF0ZSBuZXcgc3VwcGxpZXJcbiAgICBjcmVhdGVTdXBwbGllcjogYnVpbGRlci5tdXRhdGlvbjxBcGlSZXNwb25zZTxTdXBwbGllcj4sIENyZWF0ZVN1cHBsaWVyRHRvPih7XG4gICAgICBxdWVyeTogKHN1cHBsaWVyRGF0YSk6IHsgdXJscGF0aDogc3RyaW5nOyBwYXlsb2FkZGF0YTogYW55OyB0b2tlbj86IHN0cmluZyB9ID0+IHtcbiAgICAgICAgLy8gR2V0IHRva2VuIGZyb20gc3RvcmUgLSBlbnN1cmUgaXQncyBhIHN0cmluZywgbm90IHVuZGVmaW5lZFxuICAgICAgICBjb25zdCBhdXRoU3RhdGUgPSBzdG9yZS5nZXRTdGF0ZSgpLmF1dGg7XG4gICAgICAgIGNvbnN0IHRva2VuID0gYXV0aFN0YXRlPy5hY2Nlc3NUb2tlbiB8fCAnJztcblxuICAgICAgICAvLyBDaGVjayBpZiB0b2tlbiBpcyBtaXNzaW5nIGFuZCB0aHJvdyBhIG1vcmUgaGVscGZ1bCBlcnJvclxuICAgICAgICBpZiAoIXRva2VuKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignQXV0aGVudGljYXRpb24gdG9rZW4gaXMgbWlzc2luZy4gVXNlciBtYXkgbmVlZCB0byBsb2cgaW4gYWdhaW4uJyk7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdBdXRoZW50aWNhdGlvbiB0b2tlbiBpcyBtaXNzaW5nLiBQbGVhc2UgbG9nIGluIGFnYWluLicpO1xuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICB1cmxwYXRoOiAnL3N1cHBsaWVycycsXG4gICAgICAgICAgcGF5bG9hZGRhdGE6IHtcbiAgICAgICAgICAgIG1vZGU6ICdjcmVhdGVuZXcnLFxuICAgICAgICAgICAgLi4uc3VwcGxpZXJEYXRhLFxuICAgICAgICAgIH0sXG4gICAgICAgICAgdG9rZW4sXG4gICAgICAgIH07XG4gICAgICB9LFxuICAgICAgaW52YWxpZGF0ZXNUYWdzOiBbJ1N1cHBsaWVyJ10sXG4gICAgfSksXG5cbiAgICAvLyBVcGRhdGUgc3VwcGxpZXJcbiAgICB1cGRhdGVTdXBwbGllcjogYnVpbGRlci5tdXRhdGlvbjxBcGlSZXNwb25zZTxTdXBwbGllcj4sIHsgc3VwcGxpZXJJZDogbnVtYmVyOyBkYXRhOiBVcGRhdGVTdXBwbGllckR0byB9Pih7XG4gICAgICBxdWVyeTogKHsgc3VwcGxpZXJJZCwgZGF0YSB9KTogeyB1cmxwYXRoOiBzdHJpbmc7IHBheWxvYWRkYXRhOiBhbnk7IHRva2VuPzogc3RyaW5nIH0gPT4ge1xuICAgICAgICAvLyBHZXQgdG9rZW4gZnJvbSBzdG9yZSAtIGVuc3VyZSBpdCdzIGEgc3RyaW5nLCBub3QgdW5kZWZpbmVkXG4gICAgICAgIGNvbnN0IGF1dGhTdGF0ZSA9IHN0b3JlLmdldFN0YXRlKCkuYXV0aDtcbiAgICAgICAgY29uc3QgdG9rZW4gPSBhdXRoU3RhdGU/LmFjY2Vzc1Rva2VuIHx8ICcnO1xuXG4gICAgICAgIC8vIENoZWNrIGlmIHRva2VuIGlzIG1pc3NpbmcgYW5kIHRocm93IGEgbW9yZSBoZWxwZnVsIGVycm9yXG4gICAgICAgIGlmICghdG9rZW4pIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdBdXRoZW50aWNhdGlvbiB0b2tlbiBpcyBtaXNzaW5nLiBVc2VyIG1heSBuZWVkIHRvIGxvZyBpbiBhZ2Fpbi4nKTtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHRva2VuIGlzIG1pc3NpbmcuIFBsZWFzZSBsb2cgaW4gYWdhaW4uJyk7XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHVybHBhdGg6ICcvc3VwcGxpZXJzJyxcbiAgICAgICAgICBwYXlsb2FkZGF0YToge1xuICAgICAgICAgICAgbW9kZTogJ3VwZGF0ZScsXG4gICAgICAgICAgICBzdXBwbGllcklkLFxuICAgICAgICAgICAgLi4uZGF0YSxcbiAgICAgICAgICB9LFxuICAgICAgICAgIHRva2VuLFxuICAgICAgICB9O1xuICAgICAgfSxcbiAgICAgIGludmFsaWRhdGVzVGFnczogKF9yZXN1bHQsIF9lcnJvciwgeyBzdXBwbGllcklkIH0pID0+IFtcbiAgICAgICAgeyB0eXBlOiAnU3VwcGxpZXInLCBpZDogc3VwcGxpZXJJZCB9LFxuICAgICAgICAnU3VwcGxpZXInLFxuICAgICAgXSxcbiAgICB9KSxcblxuICAgIC8vIERlbGV0ZSBzdXBwbGllciAoc2luZ2xlKVxuICAgIGRlbGV0ZVN1cHBsaWVyOiBidWlsZGVyLm11dGF0aW9uPEFwaVJlc3BvbnNlPHsgc3VjY2VzczogYm9vbGVhbiB9PiwgbnVtYmVyPih7XG4gICAgICBxdWVyeTogKHN1cHBsaWVySWQpOiB7IHVybHBhdGg6IHN0cmluZzsgcGF5bG9hZGRhdGE6IGFueTsgdG9rZW4/OiBzdHJpbmcgfSA9PiB7XG4gICAgICAgIC8vIEdldCB0b2tlbiBmcm9tIHN0b3JlIC0gZW5zdXJlIGl0J3MgYSBzdHJpbmcsIG5vdCB1bmRlZmluZWRcbiAgICAgICAgY29uc3QgYXV0aFN0YXRlID0gc3RvcmUuZ2V0U3RhdGUoKS5hdXRoO1xuICAgICAgICBjb25zdCB0b2tlbiA9IGF1dGhTdGF0ZT8uYWNjZXNzVG9rZW4gfHwgJyc7XG5cbiAgICAgICAgLy8gQ2hlY2sgaWYgdG9rZW4gaXMgbWlzc2luZyBhbmQgdGhyb3cgYSBtb3JlIGhlbHBmdWwgZXJyb3JcbiAgICAgICAgaWYgKCF0b2tlbikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHRva2VuIGlzIG1pc3NpbmcuIFVzZXIgbWF5IG5lZWQgdG8gbG9nIGluIGFnYWluLicpO1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignQXV0aGVudGljYXRpb24gdG9rZW4gaXMgbWlzc2luZy4gUGxlYXNlIGxvZyBpbiBhZ2Fpbi4nKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgdXJscGF0aDogJy9zdXBwbGllcnMnLFxuICAgICAgICAgIHBheWxvYWRkYXRhOiB7XG4gICAgICAgICAgICBtb2RlOiAnZGVsZXRlJyxcbiAgICAgICAgICAgIHN1cHBsaWVySWQsXG4gICAgICAgICAgfSxcbiAgICAgICAgICB0b2tlbixcbiAgICAgICAgfTtcbiAgICAgIH0sXG4gICAgICBpbnZhbGlkYXRlc1RhZ3M6IFsnU3VwcGxpZXInXSxcbiAgICB9KSxcblxuICAgIC8vIEJ1bGsgZGVsZXRlIHN1cHBsaWVyc1xuICAgIGJ1bGtEZWxldGVTdXBwbGllcnM6IGJ1aWxkZXIubXV0YXRpb248QXBpUmVzcG9uc2U8eyBkZWxldGVkSWRzOiBudW1iZXJbXSB9PiwgbnVtYmVyW10+KHtcbiAgICAgIHF1ZXJ5OiAoc3VwcGxpZXJJZHMpOiB7IHVybHBhdGg6IHN0cmluZzsgcGF5bG9hZGRhdGE6IGFueTsgdG9rZW4/OiBzdHJpbmcgfSA9PiB7XG4gICAgICAgIC8vIEdldCB0b2tlbiBmcm9tIHN0b3JlIC0gZW5zdXJlIGl0J3MgYSBzdHJpbmcsIG5vdCB1bmRlZmluZWRcbiAgICAgICAgY29uc3QgYXV0aFN0YXRlID0gc3RvcmUuZ2V0U3RhdGUoKS5hdXRoO1xuICAgICAgICBjb25zdCB0b2tlbiA9IGF1dGhTdGF0ZT8uYWNjZXNzVG9rZW4gfHwgJyc7XG5cbiAgICAgICAgLy8gQ2hlY2sgaWYgdG9rZW4gaXMgbWlzc2luZyBhbmQgdGhyb3cgYSBtb3JlIGhlbHBmdWwgZXJyb3JcbiAgICAgICAgaWYgKCF0b2tlbikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHRva2VuIGlzIG1pc3NpbmcuIFVzZXIgbWF5IG5lZWQgdG8gbG9nIGluIGFnYWluLicpO1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignQXV0aGVudGljYXRpb24gdG9rZW4gaXMgbWlzc2luZy4gUGxlYXNlIGxvZyBpbiBhZ2Fpbi4nKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgdXJscGF0aDogJy9zdXBwbGllcnMnLFxuICAgICAgICAgIHBheWxvYWRkYXRhOiB7XG4gICAgICAgICAgICBtb2RlOiAnZGVsZXRlJyxcbiAgICAgICAgICAgIHN1cHBsaWVySWRzLFxuICAgICAgICAgIH0sXG4gICAgICAgICAgdG9rZW4sXG4gICAgICAgIH07XG4gICAgICB9LFxuICAgICAgaW52YWxpZGF0ZXNUYWdzOiBbJ1N1cHBsaWVyJ10sXG4gICAgfSksXG4gIH0pLFxufSk7XG5cbmV4cG9ydCBjb25zdCB7XG4gIHVzZUdldEFsbFN1cHBsaWVyc1F1ZXJ5LFxuICB1c2VHZXRTdXBwbGllckJ5SWRRdWVyeSxcbiAgdXNlQ3JlYXRlU3VwcGxpZXJNdXRhdGlvbixcbiAgdXNlVXBkYXRlU3VwcGxpZXJNdXRhdGlvbixcbiAgdXNlRGVsZXRlU3VwcGxpZXJNdXRhdGlvbixcbiAgdXNlQnVsa0RlbGV0ZVN1cHBsaWVyc011dGF0aW9uLFxufSA9IHN1cHBsaWVyQXBpO1xuIl0sIm5hbWVzIjpbImNyZWF0ZUFwaSIsImN1c3RvbUJhc2VRdWVyeSIsInN0b3JlIiwic3VwcGxpZXJBcGkiLCJyZWR1Y2VyUGF0aCIsImJhc2VRdWVyeSIsInRhZ1R5cGVzIiwiZW5kcG9pbnRzIiwiYnVpbGRlciIsImdldEFsbFN1cHBsaWVycyIsInF1ZXJ5IiwicGFnZSIsImxpbWl0Iiwic2VhcmNoIiwiYXV0aFN0YXRlIiwiZ2V0U3RhdGUiLCJhdXRoIiwidG9rZW4iLCJhY2Nlc3NUb2tlbiIsImNvbnNvbGUiLCJlcnJvciIsIkVycm9yIiwidXNlcklkIiwidXNlciIsImlkIiwibG9nIiwidXJscGF0aCIsInBheWxvYWRkYXRhIiwibW9kZSIsInRyaW0iLCJrZWVwVW51c2VkRGF0YUZvciIsInByb3ZpZGVzVGFncyIsImdldFN1cHBsaWVyQnlJZCIsInN1cHBsaWVySWQiLCJfcmVzdWx0IiwiX2Vycm9yIiwidHlwZSIsImNyZWF0ZVN1cHBsaWVyIiwibXV0YXRpb24iLCJzdXBwbGllckRhdGEiLCJpbnZhbGlkYXRlc1RhZ3MiLCJ1cGRhdGVTdXBwbGllciIsImRhdGEiLCJkZWxldGVTdXBwbGllciIsImJ1bGtEZWxldGVTdXBwbGllcnMiLCJzdXBwbGllcklkcyIsInVzZUdldEFsbFN1cHBsaWVyc1F1ZXJ5IiwidXNlR2V0U3VwcGxpZXJCeUlkUXVlcnkiLCJ1c2VDcmVhdGVTdXBwbGllck11dGF0aW9uIiwidXNlVXBkYXRlU3VwcGxpZXJNdXRhdGlvbiIsInVzZURlbGV0ZVN1cHBsaWVyTXV0YXRpb24iLCJ1c2VCdWxrRGVsZXRlU3VwcGxpZXJzTXV0YXRpb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/supplierApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/services/userStoreApi.ts":
/*!***********************************************!*\
  !*** ./src/reduxRTK/services/userStoreApi.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAssociateUserWithStoreMutation: () => (/* binding */ useAssociateUserWithStoreMutation),\n/* harmony export */   useCreateUserStoreMutation: () => (/* binding */ useCreateUserStoreMutation),\n/* harmony export */   useGetUserDefaultStoreQuery: () => (/* binding */ useGetUserDefaultStoreQuery),\n/* harmony export */   useGetUserStoresQuery: () => (/* binding */ useGetUserStoresQuery),\n/* harmony export */   useRemoveUserFromStoreMutation: () => (/* binding */ useRemoveUserFromStoreMutation),\n/* harmony export */   useSetUserDefaultStoreMutation: () => (/* binding */ useSetUserDefaultStoreMutation),\n/* harmony export */   useUpdateUserStoreMutation: () => (/* binding */ useUpdateUserStoreMutation),\n/* harmony export */   userStoreApi: () => (/* binding */ userStoreApi)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../customBaseQuery */ \"(ssr)/./src/reduxRTK/customBaseQuery.ts\");\n/* harmony import */ var _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/reduxRTK/store/store */ \"(ssr)/./src/reduxRTK/store/store.ts\");\n// services/userStoreApi.ts\n\n\n\nconst userStoreApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_2__.createApi)({\n    reducerPath: 'userStoreApi',\n    baseQuery: _customBaseQuery__WEBPACK_IMPORTED_MODULE_0__.customBaseQuery,\n    tagTypes: [\n        'UserStore',\n        'Store'\n    ],\n    endpoints: (builder)=>({\n            // Get all stores for a user\n            getUserStores: builder.query({\n                query: (userId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/user-stores',\n                        payloaddata: {\n                            mode: 'getUserStores',\n                            userId\n                        },\n                        token\n                    };\n                },\n                keepUnusedDataFor: 0,\n                providesTags: [\n                    'UserStore',\n                    'Store'\n                ]\n            }),\n            // Get default store for a user\n            getUserDefaultStore: builder.query({\n                query: (userId)=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/user-stores',\n                        payloaddata: {\n                            mode: 'getDefaultStore',\n                            userId\n                        },\n                        token\n                    };\n                },\n                providesTags: [\n                    'UserStore',\n                    'Store'\n                ]\n            }),\n            // Associate user with store\n            associateUserWithStore: builder.mutation({\n                query: ({ userId, storeId, isDefault = false })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/user-stores',\n                        payloaddata: {\n                            mode: 'associate',\n                            userId,\n                            storeId,\n                            isDefault\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'UserStore',\n                    'Store'\n                ]\n            }),\n            // Set default store for user\n            setUserDefaultStore: builder.mutation({\n                query: ({ userId, storeId })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/user-stores',\n                        payloaddata: {\n                            mode: 'setDefaultStore',\n                            userId,\n                            storeId\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'UserStore',\n                    'Store'\n                ]\n            }),\n            // Remove user-store association\n            removeUserFromStore: builder.mutation({\n                query: ({ userId, storeId })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/user-stores',\n                        payloaddata: {\n                            mode: 'removeAssociation',\n                            userId,\n                            storeId\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'UserStore',\n                    'Store'\n                ]\n            }),\n            // Create a store for a user\n            createUserStore: builder.mutation({\n                query: ({ data })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/user-stores',\n                        payloaddata: {\n                            mode: 'createStore',\n                            ...data\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'UserStore',\n                    'Store'\n                ]\n            }),\n            // Update a store for a user\n            updateUserStore: builder.mutation({\n                query: ({ storeId, data })=>{\n                    // Get token from store - ensure it's a string, not undefined\n                    const authState = _reduxRTK_store_store__WEBPACK_IMPORTED_MODULE_1__.store.getState().auth;\n                    const token = authState?.accessToken || '';\n                    // Check if token is missing and throw a more helpful error\n                    if (!token) {\n                        console.error('Authentication token is missing. User may need to log in again.');\n                        throw new Error('Authentication token is missing. Please log in again.');\n                    }\n                    return {\n                        urlpath: '/user-stores',\n                        payloaddata: {\n                            mode: 'updateStore',\n                            storeId,\n                            ...data\n                        },\n                        token\n                    };\n                },\n                invalidatesTags: [\n                    'UserStore',\n                    'Store'\n                ]\n            })\n        })\n});\nconst { useGetUserStoresQuery, useGetUserDefaultStoreQuery, useAssociateUserWithStoreMutation, useSetUserDefaultStoreMutation, useRemoveUserFromStoreMutation, useCreateUserStoreMutation, useUpdateUserStoreMutation } = userStoreApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/services/userStoreApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/storage.ts":
/*!*********************************!*\
  !*** ./src/reduxRTK/storage.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var redux_persist_lib_storage_createWebStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! redux-persist/lib/storage/createWebStorage */ \"(ssr)/./node_modules/redux-persist/lib/storage/createWebStorage.js\");\n\n// Create a custom storage that handles the case when localStorage is not available\nconst createNoopStorage = ()=>{\n    return {\n        getItem (_key) {\n            return Promise.resolve(null);\n        },\n        setItem (_key, value) {\n            return Promise.resolve(value);\n        },\n        removeItem (_key) {\n            return Promise.resolve();\n        }\n    };\n};\n// Create storage that works in both browser and server environments\nconst storage =  false ? 0 : createNoopStorage();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (storage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvcmVkdXhSVEsvc3RvcmFnZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwRTtBQUUxRSxtRkFBbUY7QUFDbkYsTUFBTUMsb0JBQW9CO0lBQ3hCLE9BQU87UUFDTEMsU0FBUUMsSUFBWTtZQUNsQixPQUFPQyxRQUFRQyxPQUFPLENBQUM7UUFDekI7UUFDQUMsU0FBUUgsSUFBWSxFQUFFSSxLQUFVO1lBQzlCLE9BQU9ILFFBQVFDLE9BQU8sQ0FBQ0U7UUFDekI7UUFDQUMsWUFBV0wsSUFBWTtZQUNyQixPQUFPQyxRQUFRQyxPQUFPO1FBQ3hCO0lBQ0Y7QUFDRjtBQUVBLG9FQUFvRTtBQUNwRSxNQUFNSSxVQUFVLE1BQTZCLEdBQ3pDVCxDQUF5QixHQUN6QkM7QUFFSixpRUFBZVEsT0FBT0EsRUFBQyIsInNvdXJjZXMiOlsiRTpcXFBST0pFQ1RTXFxwb3NcXHBvc2Zyb250ZW5kXFxzcmNcXHJlZHV4UlRLXFxzdG9yYWdlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVXZWJTdG9yYWdlIGZyb20gJ3JlZHV4LXBlcnNpc3QvbGliL3N0b3JhZ2UvY3JlYXRlV2ViU3RvcmFnZSc7XG5cbi8vIENyZWF0ZSBhIGN1c3RvbSBzdG9yYWdlIHRoYXQgaGFuZGxlcyB0aGUgY2FzZSB3aGVuIGxvY2FsU3RvcmFnZSBpcyBub3QgYXZhaWxhYmxlXG5jb25zdCBjcmVhdGVOb29wU3RvcmFnZSA9ICgpID0+IHtcbiAgcmV0dXJuIHtcbiAgICBnZXRJdGVtKF9rZXk6IHN0cmluZykge1xuICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZShudWxsKTtcbiAgICB9LFxuICAgIHNldEl0ZW0oX2tleTogc3RyaW5nLCB2YWx1ZTogYW55KSB7XG4gICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKHZhbHVlKTtcbiAgICB9LFxuICAgIHJlbW92ZUl0ZW0oX2tleTogc3RyaW5nKSB7XG4gICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCk7XG4gICAgfVxuICB9O1xufTtcblxuLy8gQ3JlYXRlIHN0b3JhZ2UgdGhhdCB3b3JrcyBpbiBib3RoIGJyb3dzZXIgYW5kIHNlcnZlciBlbnZpcm9ubWVudHNcbmNvbnN0IHN0b3JhZ2UgPSB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyBcbiAgPyBjcmVhdGVXZWJTdG9yYWdlKCdsb2NhbCcpXG4gIDogY3JlYXRlTm9vcFN0b3JhZ2UoKTtcblxuZXhwb3J0IGRlZmF1bHQgc3RvcmFnZTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVXZWJTdG9yYWdlIiwiY3JlYXRlTm9vcFN0b3JhZ2UiLCJnZXRJdGVtIiwiX2tleSIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0SXRlbSIsInZhbHVlIiwicmVtb3ZlSXRlbSIsInN0b3JhZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/storage.ts\n");

/***/ }),

/***/ "(ssr)/./src/reduxRTK/store/store.ts":
/*!*************************************!*\
  !*** ./src/reduxRTK/store/store.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   persistor: () => (/* binding */ persistor),\n/* harmony export */   store: () => (/* binding */ store)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/redux/dist/redux.mjs\");\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/* harmony import */ var redux_persist__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! redux-persist */ \"(ssr)/./node_modules/redux-persist/es/index.js\");\n/* harmony import */ var _reduxRTK_services_authSlice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/reduxRTK/services/authSlice */ \"(ssr)/./src/reduxRTK/services/authSlice.ts\");\n/* harmony import */ var _reduxRTK_services_authApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/reduxRTK/services/authApi */ \"(ssr)/./src/reduxRTK/services/authApi.ts\");\n/* harmony import */ var _reduxRTK_services_paymentApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/reduxRTK/services/paymentApi */ \"(ssr)/./src/reduxRTK/services/paymentApi.ts\");\n/* harmony import */ var _reduxRTK_services_categoryApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/reduxRTK/services/categoryApi */ \"(ssr)/./src/reduxRTK/services/categoryApi.ts\");\n/* harmony import */ var _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/reduxRTK/services/productApi */ \"(ssr)/./src/reduxRTK/services/productApi.ts\");\n/* harmony import */ var _reduxRTK_services_stockAdjustmentApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/reduxRTK/services/stockAdjustmentApi */ \"(ssr)/./src/reduxRTK/services/stockAdjustmentApi.ts\");\n/* harmony import */ var _reduxRTK_services_supplierApi__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/reduxRTK/services/supplierApi */ \"(ssr)/./src/reduxRTK/services/supplierApi.ts\");\n/* harmony import */ var _reduxRTK_services_purchaseApi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/reduxRTK/services/purchaseApi */ \"(ssr)/./src/reduxRTK/services/purchaseApi.ts\");\n/* harmony import */ var _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/reduxRTK/services/salesApi */ \"(ssr)/./src/reduxRTK/services/salesApi.ts\");\n/* harmony import */ var _reduxRTK_services_storeApi__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/reduxRTK/services/storeApi */ \"(ssr)/./src/reduxRTK/services/storeApi.ts\");\n/* harmony import */ var _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/reduxRTK/services/userStoreApi */ \"(ssr)/./src/reduxRTK/services/userStoreApi.ts\");\n/* harmony import */ var _reduxRTK_services_receiptApi__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/reduxRTK/services/receiptApi */ \"(ssr)/./src/reduxRTK/services/receiptApi.ts\");\n/* harmony import */ var _reduxRTK_services_dashboardApi__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/reduxRTK/services/dashboardApi */ \"(ssr)/./src/reduxRTK/services/dashboardApi.ts\");\n/* harmony import */ var _reduxRTK_services_expenseApi__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/reduxRTK/services/expenseApi */ \"(ssr)/./src/reduxRTK/services/expenseApi.ts\");\n/* harmony import */ var _reduxRTK_services_expenseCategoryApi__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/reduxRTK/services/expenseCategoryApi */ \"(ssr)/./src/reduxRTK/services/expenseCategoryApi.ts\");\n/* harmony import */ var _reduxRTK_storage__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/reduxRTK/storage */ \"(ssr)/./src/reduxRTK/storage.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n // Custom storage that handles SSR\nconst persistConfig = {\n    key: 'root',\n    storage: _reduxRTK_storage__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    whitelist: [\n        'auth'\n    ]\n};\nconst rootReducer = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_17__.combineReducers)({\n    auth: _reduxRTK_services_authSlice__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    [_reduxRTK_services_authApi__WEBPACK_IMPORTED_MODULE_2__.userApi.reducerPath]: _reduxRTK_services_authApi__WEBPACK_IMPORTED_MODULE_2__.userApi.reducer,\n    [_reduxRTK_services_paymentApi__WEBPACK_IMPORTED_MODULE_3__.paymentApi.reducerPath]: _reduxRTK_services_paymentApi__WEBPACK_IMPORTED_MODULE_3__.paymentApi.reducer,\n    [_reduxRTK_services_categoryApi__WEBPACK_IMPORTED_MODULE_4__.categoryApi.reducerPath]: _reduxRTK_services_categoryApi__WEBPACK_IMPORTED_MODULE_4__.categoryApi.reducer,\n    [_reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_5__.productApi.reducerPath]: _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_5__.productApi.reducer,\n    [_reduxRTK_services_stockAdjustmentApi__WEBPACK_IMPORTED_MODULE_6__.stockAdjustmentApi.reducerPath]: _reduxRTK_services_stockAdjustmentApi__WEBPACK_IMPORTED_MODULE_6__.stockAdjustmentApi.reducer,\n    [_reduxRTK_services_supplierApi__WEBPACK_IMPORTED_MODULE_7__.supplierApi.reducerPath]: _reduxRTK_services_supplierApi__WEBPACK_IMPORTED_MODULE_7__.supplierApi.reducer,\n    [_reduxRTK_services_purchaseApi__WEBPACK_IMPORTED_MODULE_8__.purchaseApi.reducerPath]: _reduxRTK_services_purchaseApi__WEBPACK_IMPORTED_MODULE_8__.purchaseApi.reducer,\n    [_reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_9__.salesApi.reducerPath]: _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_9__.salesApi.reducer,\n    [_reduxRTK_services_storeApi__WEBPACK_IMPORTED_MODULE_10__.storeApi.reducerPath]: _reduxRTK_services_storeApi__WEBPACK_IMPORTED_MODULE_10__.storeApi.reducer,\n    [_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_11__.userStoreApi.reducerPath]: _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_11__.userStoreApi.reducer,\n    [_reduxRTK_services_receiptApi__WEBPACK_IMPORTED_MODULE_12__.receiptApi.reducerPath]: _reduxRTK_services_receiptApi__WEBPACK_IMPORTED_MODULE_12__.receiptApi.reducer,\n    [_reduxRTK_services_dashboardApi__WEBPACK_IMPORTED_MODULE_13__.dashboardApi.reducerPath]: _reduxRTK_services_dashboardApi__WEBPACK_IMPORTED_MODULE_13__.dashboardApi.reducer,\n    [_reduxRTK_services_expenseApi__WEBPACK_IMPORTED_MODULE_14__.expenseApi.reducerPath]: _reduxRTK_services_expenseApi__WEBPACK_IMPORTED_MODULE_14__.expenseApi.reducer,\n    [_reduxRTK_services_expenseCategoryApi__WEBPACK_IMPORTED_MODULE_15__.expenseCategoryApi.reducerPath]: _reduxRTK_services_expenseCategoryApi__WEBPACK_IMPORTED_MODULE_15__.expenseCategoryApi.reducer\n});\nconst persistedReducer = (0,redux_persist__WEBPACK_IMPORTED_MODULE_0__.persistReducer)(persistConfig, rootReducer);\nconst store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_18__.configureStore)({\n    reducer: persistedReducer,\n    middleware: (getDefaultMiddleware)=>getDefaultMiddleware({\n            serializableCheck: false\n        }).concat(_reduxRTK_services_authApi__WEBPACK_IMPORTED_MODULE_2__.userApi.middleware, _reduxRTK_services_paymentApi__WEBPACK_IMPORTED_MODULE_3__.paymentApi.middleware, _reduxRTK_services_categoryApi__WEBPACK_IMPORTED_MODULE_4__.categoryApi.middleware, _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_5__.productApi.middleware, _reduxRTK_services_stockAdjustmentApi__WEBPACK_IMPORTED_MODULE_6__.stockAdjustmentApi.middleware, _reduxRTK_services_supplierApi__WEBPACK_IMPORTED_MODULE_7__.supplierApi.middleware, _reduxRTK_services_purchaseApi__WEBPACK_IMPORTED_MODULE_8__.purchaseApi.middleware, _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_9__.salesApi.middleware, _reduxRTK_services_storeApi__WEBPACK_IMPORTED_MODULE_10__.storeApi.middleware, _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_11__.userStoreApi.middleware, _reduxRTK_services_receiptApi__WEBPACK_IMPORTED_MODULE_12__.receiptApi.middleware, _reduxRTK_services_dashboardApi__WEBPACK_IMPORTED_MODULE_13__.dashboardApi.middleware, _reduxRTK_services_expenseApi__WEBPACK_IMPORTED_MODULE_14__.expenseApi.middleware, _reduxRTK_services_expenseCategoryApi__WEBPACK_IMPORTED_MODULE_15__.expenseCategoryApi.middleware)\n});\n// Expose Redux state for debugging\nif (false) {}\nconst persistor = (0,redux_persist__WEBPACK_IMPORTED_MODULE_0__.persistStore)(store);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/reduxRTK/store/store.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/global-spinner.css":
/*!************************************!*\
  !*** ./src/app/global-spinner.css ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e5c8d78c3713\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbC1zcGlubmVyLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiRTpcXFBST0pFQ1RTXFxwb3NcXHBvc2Zyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFsLXNwaW5uZXIuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZTVjOGQ3OGMzNzEzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/global-spinner.css\n");

/***/ }),

/***/ "(rsc)/./src/css/satoshi.css":
/*!*****************************!*\
  !*** ./src/css/satoshi.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1193134113d9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY3NzL3NhdG9zaGkuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxcUFJPSkVDVFNcXHBvc1xccG9zZnJvbnRlbmRcXHNyY1xcY3NzXFxzYXRvc2hpLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjExOTMxMzQxMTNkOVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/css/satoshi.css\n");

/***/ }),

/***/ "(rsc)/./src/css/style.css":
/*!***************************!*\
  !*** ./src/css/style.css ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ddb152145ccd\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY3NzL3N0eWxlLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiRTpcXFBST0pFQ1RTXFxwb3NcXHBvc2Zyb250ZW5kXFxzcmNcXGNzc1xcc3R5bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZGRiMTUyMTQ1Y2NkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/css/style.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _css_satoshi_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/css/satoshi.css */ \"(rsc)/./src/css/satoshi.css\");\n/* harmony import */ var _css_style_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/css/style.css */ \"(rsc)/./src/css/style.css\");\n/* harmony import */ var flatpickr_dist_flatpickr_min_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! flatpickr/dist/flatpickr.min.css */ \"(rsc)/./node_modules/flatpickr/dist/flatpickr.min.css\");\n/* harmony import */ var _global_spinner_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./global-spinner.css */ \"(rsc)/./src/app/global-spinner.css\");\n/* harmony import */ var nextjs_toploader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! nextjs-toploader */ \"(rsc)/./node_modules/nextjs-toploader/dist/index.js\");\n/* harmony import */ var nextjs_toploader__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(nextjs_toploader__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n/* harmony import */ var _provider_Provider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/provider/Provider */ \"(rsc)/./src/provider/Provider.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_Layouts_sidebar_sidebar_context__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Layouts/sidebar/sidebar-context */ \"(rsc)/./src/components/Layouts/sidebar/sidebar-context.tsx\");\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"POS System | Inventory Management\",\n    description: \"Point of Sale and Inventory Management System\",\n    icons: {\n        icon: [\n            {\n                url: \"/favicon.ico\",\n                sizes: \"any\"\n            },\n            {\n                url: \"/images/logo/logo-small.png\"\n            }\n        ]\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            suppressHydrationWarning: true,\n            className: \"h-screen w-screen overflow-x-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_6__.Providers, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_provider_Provider__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layouts_sidebar_sidebar_context__WEBPACK_IMPORTED_MODULE_9__.SidebarProvider, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((nextjs_toploader__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                color: \"#5750F1\",\n                                showSpinner: true\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 15\n                            }, this),\n                            children,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_8__.Toaster, {\n                                position: \"top-center\",\n                                reverseOrder: false\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ Providers)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Providers = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\PROJECTS\\pos\\posfrontend\\src\\app\\providers.tsx",
"Providers",
);

/***/ }),

/***/ "(rsc)/./src/components/Layouts/sidebar/sidebar-context.tsx":
/*!************************************************************!*\
  !*** ./src/components/Layouts/sidebar/sidebar-context.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SidebarProvider: () => (/* binding */ SidebarProvider),
/* harmony export */   useSidebarContext: () => (/* binding */ useSidebarContext)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useSidebarContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useSidebarContext() from the server but useSidebarContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\sidebar-context.tsx",
"useSidebarContext",
);const SidebarProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\PROJECTS\\pos\\posfrontend\\src\\components\\Layouts\\sidebar\\sidebar-context.tsx",
"SidebarProvider",
);

/***/ }),

/***/ "(rsc)/./src/provider/Provider.tsx":
/*!***********************************!*\
  !*** ./src/provider/Provider.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\provider\\\\Provider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\PROJECTS\\pos\\posfrontend\\src\\provider\\Provider.tsx",
"default",
));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/antd","vendor-chunks/@ant-design","vendor-chunks/@reduxjs","vendor-chunks/rc-util","vendor-chunks/react-redux","vendor-chunks/immer","vendor-chunks/redux-persist","vendor-chunks/prop-types","vendor-chunks/@babel","vendor-chunks/reselect","vendor-chunks/react-hot-toast","vendor-chunks/redux","vendor-chunks/stylis","vendor-chunks/nprogress","vendor-chunks/nextjs-toploader","vendor-chunks/react-is","vendor-chunks/throttle-debounce","vendor-chunks/next-themes","vendor-chunks/use-sync-external-store","vendor-chunks/@emotion","vendor-chunks/goober","vendor-chunks/object-assign","vendor-chunks/classnames","vendor-chunks/redux-thunk","vendor-chunks/flatpickr"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=E%3A%5CPROJECTS%5Cpos%5Cposfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CPROJECTS%5Cpos%5Cposfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();