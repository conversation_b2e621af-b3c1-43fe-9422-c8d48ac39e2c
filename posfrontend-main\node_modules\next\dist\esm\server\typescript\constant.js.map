{"version": 3, "sources": ["../../../src/server/typescript/constant.ts"], "sourcesContent": ["export const NEXT_TS_ERRORS = {\n  INVALID_SERVER_API: 71001,\n  INVALID_ENTRY_EXPORT: 71002,\n  INVALID_OPTION_VALUE: 71003,\n  MISPLACED_ENTRY_DIRECTIVE: 71004,\n  INVALID_PAGE_PROP: 71005,\n  INVALID_CONFIG_OPTION: 71006,\n  INVALID_CLIENT_ENTRY_PROP: 71007,\n  INVALID_METADATA_EXPORT: 71008,\n  INVALID_ERROR_COMPONENT: 71009,\n  INVALID_ENTRY_DIRECTIVE: 71010,\n  INVALID_SERVER_ENTRY_RETURN: 71011,\n}\n\nexport const ALLOWED_EXPORTS = [\n  'config',\n  'generateStaticParams',\n  'metadata',\n  'generateMetadata',\n  'viewport',\n  'generateViewport',\n]\n\nexport const LEGACY_CONFIG_EXPORT = 'config'\n\nexport const DISALLOWED_SERVER_REACT_APIS: string[] = [\n  'useState',\n  'useEffect',\n  'useLayoutEffect',\n  'useDeferredValue',\n  'useImperativeHandle',\n  'useInsertionEffect',\n  'useReducer',\n  'useRef',\n  'useSyncExternalStore',\n  'useTransition',\n  'Component',\n  'PureComponent',\n  'createContext',\n  'createFactory',\n  'experimental_useOptimistic',\n  'useOptimistic',\n  'useActionState',\n]\n\nexport const DISALLOWED_SERVER_REACT_DOM_APIS: string[] = [\n  'useFormStatus',\n  'useFormState',\n]\n\nexport const ALLOWED_PAGE_PROPS = ['params', 'searchParams']\nexport const ALLOWED_LAYOUT_PROPS = ['params', 'children']\n"], "names": ["NEXT_TS_ERRORS", "INVALID_SERVER_API", "INVALID_ENTRY_EXPORT", "INVALID_OPTION_VALUE", "MISPLACED_ENTRY_DIRECTIVE", "INVALID_PAGE_PROP", "INVALID_CONFIG_OPTION", "INVALID_CLIENT_ENTRY_PROP", "INVALID_METADATA_EXPORT", "INVALID_ERROR_COMPONENT", "INVALID_ENTRY_DIRECTIVE", "INVALID_SERVER_ENTRY_RETURN", "ALLOWED_EXPORTS", "LEGACY_CONFIG_EXPORT", "DISALLOWED_SERVER_REACT_APIS", "DISALLOWED_SERVER_REACT_DOM_APIS", "ALLOWED_PAGE_PROPS", "ALLOWED_LAYOUT_PROPS"], "mappings": "AAAA,OAAO,MAAMA,iBAAiB;IAC5BC,oBAAoB;IACpBC,sBAAsB;IACtBC,sBAAsB;IACtBC,2BAA2B;IAC3BC,mBAAmB;IACnBC,uBAAuB;IACvBC,2BAA2B;IAC3BC,yBAAyB;IACzBC,yBAAyB;IACzBC,yBAAyB;IACzBC,6BAA6B;AAC/B,EAAC;AAED,OAAO,MAAMC,kBAAkB;IAC7B;IACA;IACA;IACA;IACA;IACA;CACD,CAAA;AAED,OAAO,MAAMC,uBAAuB,SAAQ;AAE5C,OAAO,MAAMC,+BAAyC;IACpD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD,CAAA;AAED,OAAO,MAAMC,mCAA6C;IACxD;IACA;CACD,CAAA;AAED,OAAO,MAAMC,qBAAqB;IAAC;IAAU;CAAe,CAAA;AAC5D,OAAO,MAAMC,uBAAuB;IAAC;IAAU;CAAW,CAAA"}