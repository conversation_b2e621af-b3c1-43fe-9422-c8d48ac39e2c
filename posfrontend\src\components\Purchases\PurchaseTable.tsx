"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, Tooltip, Tag, Checkbox, notification } from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import {
  EditOutlined,
  EyeOutlined,
  DeleteOutlined,
  DollarOutlined,
  ShoppingOutlined,
  UserOutlined,
  CalendarOutlined,
  DeleteFilled
} from "@ant-design/icons";
import { ResponsiveTableGrid, TableHeader, TableCell, TableRow } from "@/components/ui/ResponsiveTable";
import { useResponsiveTable } from "@/hooks/useResponsiveTable";
import { Purchase } from "@/reduxRTK/services/purchaseApi";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { UserRole } from "@/types/user";
import "./purchase-panels.css";

interface PurchaseTableProps {
  purchases: Purchase[];
  loading: boolean;
  onView: (purchaseId: number) => void;
  onEdit: (purchase: Purchase) => void;
  onDelete: (purchaseId: number) => void;
  onBulkDelete?: (purchaseIds: number[]) => void;
  isMobile?: boolean;
}

const PurchaseTable: React.FC<PurchaseTableProps> = ({
  purchases,
  loading,
  onView,
  onEdit,
  onDelete,
  onBulkDelete,
  isMobile: propIsMobile = false,
}) => {
  // Use hook for responsive detection, fallback to prop
  const hookIsMobile = useResponsiveTable();
  const isMobile = propIsMobile || hookIsMobile;

  const user = useSelector((state: RootState) => state.auth.user);
  const userRole = user?.role as UserRole;

  // State for selected purchases
  const [selectedPurchases, setSelectedPurchases] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Handle select all checkbox change
  const handleSelectAllChange = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked;
    setSelectAll(checked);

    if (checked) {
      // Select all purchases that the user can delete
      const selectablePurchaseIds = purchases
        .filter(purchase => canEditDelete(purchase))
        .map(purchase => purchase.id);
      setSelectedPurchases(selectablePurchaseIds);
    } else {
      // Deselect all purchases
      setSelectedPurchases([]);
    }
  };

  // Handle individual checkbox change
  const handleCheckboxChange = (purchaseId: number, checked: boolean) => {
    if (checked) {
      setSelectedPurchases(prev => [...prev, purchaseId]);
    } else {
      setSelectedPurchases(prev => prev.filter(id => id !== purchaseId));
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedPurchases.length > 0 && onBulkDelete) {
      onBulkDelete(selectedPurchases);
      setSelectedPurchases([]);
      setSelectAll(false);
    } else {
      notification.warning({
        message: 'No purchases selected',
        description: 'Please select at least one purchase to delete.',
      });
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return dayjs(dateString).format("MMM D, YYYY");
  };

  // Format currency for display
  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat('en-GH', {
      style: 'currency',
      currency: 'GHS',
      minimumFractionDigits: 2
    }).format(Number(amount));
  };

  // Check if user can edit/delete (admin can edit/delete all purchases they can see)
  const canEditDelete = (purchase: Purchase) => {
    // Admin can edit/delete all purchases they can see
    // The backend already filters purchases based on permissions
    return userRole === "admin";
  };

  return (
    <div className="overflow-hidden bg-white">
      {/* Bulk Delete Button - Show only when purchases are selected */}
      {selectedPurchases.length > 0 && (
        <div className="p-2 bg-gray-100 border-b flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            {selectedPurchases.length} {selectedPurchases.length === 1 ? 'purchase' : 'purchases'} selected
          </span>
          <Button
            type="primary"
            danger
            icon={<DeleteFilled />}
            onClick={handleBulkDelete}
            className="ml-2"
          >
            Delete Selected
          </Button>
        </div>
      )}

      {isMobile ? (
        // Mobile: Use CSS Grid
        <ResponsiveTableGrid
          columns="50px 200px 120px 120px 120px 150px"
          minWidth="800px"
        >
          {/* Mobile Headers */}
          <TableHeader className="text-center">
            <Checkbox
              checked={selectAll}
              onChange={handleSelectAllChange}
              disabled={purchases.filter(purchase => canEditDelete(purchase)).length === 0}
            />
          </TableHeader>
          <TableHeader>
            <span className="flex items-center">
              <ShoppingOutlined className="mr-1" />
              Product
            </span>
          </TableHeader>
          <TableHeader>
            <span className="flex items-center">
              <UserOutlined className="mr-1" />
              Supplier
            </span>
          </TableHeader>
          <TableHeader>
            Quantity
          </TableHeader>
          <TableHeader>
            <span className="flex items-center">
              <CalendarOutlined className="mr-1" />
              Date
            </span>
          </TableHeader>
          <TableHeader className="text-right">
            Actions
          </TableHeader>

          {/* Mobile Rows */}
          {purchases.map((purchase) => (
            <TableRow
              key={purchase.id}
              selected={selectedPurchases.includes(purchase.id)}
            >
              <TableCell className="text-center">
                {canEditDelete(purchase) && (
                  <Checkbox
                    checked={selectedPurchases.includes(purchase.id)}
                    onChange={(e) => handleCheckboxChange(purchase.id, e.target.checked)}
                  />
                )}
              </TableCell>
              <TableCell>
                <div className="max-w-[180px] overflow-hidden text-ellipsis font-medium">
                  {purchase.productName}
                </div>
              </TableCell>
              <TableCell>
                <div className="max-w-[120px] overflow-hidden text-ellipsis text-gray-600">
                  {purchase.supplierName}
                </div>
              </TableCell>
              <TableCell>
                <span className="font-medium">
                  {purchase.quantity}
                </span>
              </TableCell>
              <TableCell>
                <span className="text-sm">
                  {formatDate(purchase.purchaseDate)}
                </span>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-1">
                  <Tooltip title="View">
                    <Button
                      icon={<EyeOutlined />}
                      onClick={() => onView(purchase.id)}
                      type="text"
                      className="view-button text-green-500 hover:text-green-400"
                      size="small"
                    />
                  </Tooltip>
                  {canEditDelete(purchase) && (
                    <>
                      <Tooltip title="Edit">
                        <Button
                          icon={<EditOutlined />}
                          onClick={() => onEdit(purchase)}
                          type="text"
                          className="edit-button text-blue-500 hover:text-blue-400"
                          size="small"
                        />
                      </Tooltip>
                      <Tooltip title="Delete">
                        <Button
                          icon={<DeleteOutlined />}
                          onClick={() => onDelete(purchase.id)}
                          type="text"
                          className="delete-button text-red-500 hover:text-red-400"
                          size="small"
                        />
                      </Tooltip>
                    </>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </ResponsiveTableGrid>
      ) : (
        // Desktop: Use traditional HTML table
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {/* Checkbox Column */}
                <th scope="col" className="w-10 px-3 py-3 text-center">
                  <Checkbox
                    checked={selectAll}
                    onChange={handleSelectAllChange}
                    disabled={purchases.filter(purchase => canEditDelete(purchase)).length === 0}
                  />
                </th>

                {/* Product Column - Always visible */}
                <th scope="col" className="sticky left-0 z-10 bg-gray-50 px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <ShoppingOutlined className="mr-1" />
                    Product
                  </span>
                </th>

                {/* Supplier Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <UserOutlined className="mr-1" />
                    Supplier
                  </span>
                </th>

                {/* Quantity Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <ShoppingOutlined className="mr-1" />
                    Quantity
                  </span>
                </th>

                {/* Cost Price Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <DollarOutlined className="mr-1" />
                    Cost Price
                  </span>
                </th>

                {/* Total Cost Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <DollarOutlined className="mr-1" />
                    Total Cost
                  </span>
                </th>

                {/* Purchase Date Column */}
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  <span className="flex items-center">
                    <CalendarOutlined className="mr-1" />
                    Date
                  </span>
                </th>

                {/* Actions Column - Always visible */}
                <th scope="col" className="sticky right-0 z-10 bg-gray-50 px-3 py-3 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {purchases.map((purchase) => (
                <tr key={purchase.id} className={selectedPurchases.includes(purchase.id) ? "bg-blue-50" : ""}>
                  {/* Checkbox Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-center">
                    {canEditDelete(purchase) && (
                      <Checkbox
                        checked={selectedPurchases.includes(purchase.id)}
                        onChange={(e) => handleCheckboxChange(purchase.id, e.target.checked)}
                      />
                    )}
                  </td>

                  {/* Product Column - Always visible */}
                  <td className="sticky left-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-gray-800">
                    <div className="max-w-[120px] overflow-hidden text-ellipsis">
                      {purchase.productName}
                    </div>
                  </td>

                  {/* Supplier Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    {purchase.supplierName || 'N/A'}
                  </td>

                  {/* Quantity Column */}
                  <td className="px-3 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-500 text-white">
                      {purchase.quantity}
                    </span>
                  </td>

                  {/* Cost Price Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    {formatCurrency(purchase.costPrice)}
                  </td>

                  {/* Total Cost Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    {formatCurrency(purchase.totalCost)}
                  </td>

                  {/* Purchase Date Column */}
                  <td className="px-3 py-4 whitespace-nowrap text-gray-800">
                    {formatDate(purchase.purchaseDate)}
                  </td>

                  {/* Actions Column - Always visible */}
                  <td className="sticky right-0 z-10 bg-white px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-1">
                      <Tooltip title="View">
                        <Button
                          icon={<EyeOutlined />}
                          onClick={() => onView(purchase.id)}
                          type="text"
                          className="view-button text-green-500"
                          size="middle"
                        />
                      </Tooltip>
                      {canEditDelete(purchase) && (
                        <>
                          <Tooltip title="Edit">
                            <Button
                              icon={<EditOutlined />}
                              onClick={() => onEdit(purchase)}
                              type="text"
                              className="edit-button text-blue-500"
                              size="middle"
                            />
                          </Tooltip>
                          <Tooltip title="Delete">
                            <Button
                              icon={<DeleteOutlined />}
                              onClick={() => onDelete(purchase.id)}
                              type="text"
                              className="delete-button text-red-500"
                              size="middle"
                            />
                          </Tooltip>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default PurchaseTable;
