"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/compute-scroll-into-view";
exports.ids = ["vendor-chunks/compute-scroll-into-view"];
exports.modules = {

/***/ "(ssr)/./node_modules/compute-scroll-into-view/dist/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/compute-scroll-into-view/dist/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compute: () => (/* binding */ r)\n/* harmony export */ });\nconst t=t=>\"object\"==typeof t&&null!=t&&1===t.nodeType,e=(t,e)=>(!e||\"hidden\"!==t)&&(\"visible\"!==t&&\"clip\"!==t),n=(t,n)=>{if(t.clientHeight<t.scrollHeight||t.clientWidth<t.scrollWidth){const o=getComputedStyle(t,null);return e(o.overflowY,n)||e(o.overflowX,n)||(t=>{const e=(t=>{if(!t.ownerDocument||!t.ownerDocument.defaultView)return null;try{return t.ownerDocument.defaultView.frameElement}catch(t){return null}})(t);return!!e&&(e.clientHeight<t.scrollHeight||e.clientWidth<t.scrollWidth)})(t)}return!1},o=(t,e,n,o,l,r,i,s)=>r<t&&i>e||r>t&&i<e?0:r<=t&&s<=n||i>=e&&s>=n?r-t-o:i>e&&s<n||r<t&&s>n?i-e+l:0,l=t=>{const e=t.parentElement;return null==e?t.getRootNode().host||null:e},r=(e,r)=>{var i,s,d,h;if(\"undefined\"==typeof document)return[];const{scrollMode:c,block:f,inline:u,boundary:a,skipOverflowHiddenElements:g}=r,p=\"function\"==typeof a?a:t=>t!==a;if(!t(e))throw new TypeError(\"Invalid target\");const m=document.scrollingElement||document.documentElement,w=[];let W=e;for(;t(W)&&p(W);){if(W=l(W),W===m){w.push(W);break}null!=W&&W===document.body&&n(W)&&!n(document.documentElement)||null!=W&&n(W,g)&&w.push(W)}const b=null!=(s=null==(i=window.visualViewport)?void 0:i.width)?s:innerWidth,H=null!=(h=null==(d=window.visualViewport)?void 0:d.height)?h:innerHeight,{scrollX:y,scrollY:M}=window,{height:v,width:E,top:x,right:C,bottom:I,left:R}=e.getBoundingClientRect(),{top:T,right:B,bottom:F,left:V}=(t=>{const e=window.getComputedStyle(t);return{top:parseFloat(e.scrollMarginTop)||0,right:parseFloat(e.scrollMarginRight)||0,bottom:parseFloat(e.scrollMarginBottom)||0,left:parseFloat(e.scrollMarginLeft)||0}})(e);let k=\"start\"===f||\"nearest\"===f?x-T:\"end\"===f?I+F:x+v/2-T+F,D=\"center\"===u?R+E/2-V+B:\"end\"===u?C+B:R-V;const L=[];for(let t=0;t<w.length;t++){const e=w[t],{height:l,width:r,top:i,right:s,bottom:d,left:h}=e.getBoundingClientRect();if(\"if-needed\"===c&&x>=0&&R>=0&&I<=H&&C<=b&&(e===m&&!n(e)||x>=i&&I<=d&&R>=h&&C<=s))return L;const a=getComputedStyle(e),g=parseInt(a.borderLeftWidth,10),p=parseInt(a.borderTopWidth,10),W=parseInt(a.borderRightWidth,10),T=parseInt(a.borderBottomWidth,10);let B=0,F=0;const V=\"offsetWidth\"in e?e.offsetWidth-e.clientWidth-g-W:0,S=\"offsetHeight\"in e?e.offsetHeight-e.clientHeight-p-T:0,X=\"offsetWidth\"in e?0===e.offsetWidth?0:r/e.offsetWidth:0,Y=\"offsetHeight\"in e?0===e.offsetHeight?0:l/e.offsetHeight:0;if(m===e)B=\"start\"===f?k:\"end\"===f?k-H:\"nearest\"===f?o(M,M+H,H,p,T,M+k,M+k+v,v):k-H/2,F=\"start\"===u?D:\"center\"===u?D-b/2:\"end\"===u?D-b:o(y,y+b,b,g,W,y+D,y+D+E,E),B=Math.max(0,B+M),F=Math.max(0,F+y);else{B=\"start\"===f?k-i-p:\"end\"===f?k-d+T+S:\"nearest\"===f?o(i,d,l,p,T+S,k,k+v,v):k-(i+l/2)+S/2,F=\"start\"===u?D-h-g:\"center\"===u?D-(h+r/2)+V/2:\"end\"===u?D-s+W+V:o(h,s,r,g,W+V,D,D+E,E);const{scrollLeft:t,scrollTop:n}=e;B=0===Y?0:Math.max(0,Math.min(n+B/Y,e.scrollHeight-l/Y+S)),F=0===X?0:Math.max(0,Math.min(t+F/X,e.scrollWidth-r/X+V)),k+=n-B,D+=t-F}L.push({el:e,top:B,left:F})}return L};//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY29tcHV0ZS1zY3JvbGwtaW50by12aWV3L2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDBIQUEwSCwrREFBK0QsaUNBQWlDLGdEQUFnRCxhQUFhLDhEQUE4RCxJQUFJLGdEQUFnRCxTQUFTLGFBQWEsS0FBSyx3RUFBd0UsS0FBSyxTQUFTLHlHQUF5Ryx3QkFBd0IsNENBQTRDLFdBQVcsWUFBWSx5Q0FBeUMsTUFBTSxzRUFBc0UscUNBQXFDLCtDQUErQyxpRUFBaUUsUUFBUSxLQUFLLFdBQVcsRUFBRSxpQkFBaUIsVUFBVSxNQUFNLDJGQUEyRix5SkFBeUosb0JBQW9CLFNBQVMsK0NBQStDLDRCQUE0Qiw4QkFBOEIsTUFBTSxtQ0FBbUMsT0FBTyxpS0FBaUssS0FBSyx3R0FBd0csV0FBVyxZQUFZLFdBQVcsS0FBSyxjQUFjLCtDQUErQywyQkFBMkIsNEZBQTRGLGtLQUFrSyxZQUFZLDRPQUE0TyxzTUFBc00sS0FBSyxpTEFBaUwsTUFBTSx5QkFBeUIsR0FBRyxtSUFBbUksUUFBUSxrQkFBa0IsRUFBRSxVQUErQiIsInNvdXJjZXMiOlsiRTpcXFBST0pFQ1RTXFxwb3NcXHBvc2Zyb250ZW5kXFxub2RlX21vZHVsZXNcXGNvbXB1dGUtc2Nyb2xsLWludG8tdmlld1xcZGlzdFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgdD10PT5cIm9iamVjdFwiPT10eXBlb2YgdCYmbnVsbCE9dCYmMT09PXQubm9kZVR5cGUsZT0odCxlKT0+KCFlfHxcImhpZGRlblwiIT09dCkmJihcInZpc2libGVcIiE9PXQmJlwiY2xpcFwiIT09dCksbj0odCxuKT0+e2lmKHQuY2xpZW50SGVpZ2h0PHQuc2Nyb2xsSGVpZ2h0fHx0LmNsaWVudFdpZHRoPHQuc2Nyb2xsV2lkdGgpe2NvbnN0IG89Z2V0Q29tcHV0ZWRTdHlsZSh0LG51bGwpO3JldHVybiBlKG8ub3ZlcmZsb3dZLG4pfHxlKG8ub3ZlcmZsb3dYLG4pfHwodD0+e2NvbnN0IGU9KHQ9PntpZighdC5vd25lckRvY3VtZW50fHwhdC5vd25lckRvY3VtZW50LmRlZmF1bHRWaWV3KXJldHVybiBudWxsO3RyeXtyZXR1cm4gdC5vd25lckRvY3VtZW50LmRlZmF1bHRWaWV3LmZyYW1lRWxlbWVudH1jYXRjaCh0KXtyZXR1cm4gbnVsbH19KSh0KTtyZXR1cm4hIWUmJihlLmNsaWVudEhlaWdodDx0LnNjcm9sbEhlaWdodHx8ZS5jbGllbnRXaWR0aDx0LnNjcm9sbFdpZHRoKX0pKHQpfXJldHVybiExfSxvPSh0LGUsbixvLGwscixpLHMpPT5yPHQmJmk+ZXx8cj50JiZpPGU/MDpyPD10JiZzPD1ufHxpPj1lJiZzPj1uP3ItdC1vOmk+ZSYmczxufHxyPHQmJnM+bj9pLWUrbDowLGw9dD0+e2NvbnN0IGU9dC5wYXJlbnRFbGVtZW50O3JldHVybiBudWxsPT1lP3QuZ2V0Um9vdE5vZGUoKS5ob3N0fHxudWxsOmV9LHI9KGUscik9Pnt2YXIgaSxzLGQsaDtpZihcInVuZGVmaW5lZFwiPT10eXBlb2YgZG9jdW1lbnQpcmV0dXJuW107Y29uc3R7c2Nyb2xsTW9kZTpjLGJsb2NrOmYsaW5saW5lOnUsYm91bmRhcnk6YSxza2lwT3ZlcmZsb3dIaWRkZW5FbGVtZW50czpnfT1yLHA9XCJmdW5jdGlvblwiPT10eXBlb2YgYT9hOnQ9PnQhPT1hO2lmKCF0KGUpKXRocm93IG5ldyBUeXBlRXJyb3IoXCJJbnZhbGlkIHRhcmdldFwiKTtjb25zdCBtPWRvY3VtZW50LnNjcm9sbGluZ0VsZW1lbnR8fGRvY3VtZW50LmRvY3VtZW50RWxlbWVudCx3PVtdO2xldCBXPWU7Zm9yKDt0KFcpJiZwKFcpOyl7aWYoVz1sKFcpLFc9PT1tKXt3LnB1c2goVyk7YnJlYWt9bnVsbCE9VyYmVz09PWRvY3VtZW50LmJvZHkmJm4oVykmJiFuKGRvY3VtZW50LmRvY3VtZW50RWxlbWVudCl8fG51bGwhPVcmJm4oVyxnKSYmdy5wdXNoKFcpfWNvbnN0IGI9bnVsbCE9KHM9bnVsbD09KGk9d2luZG93LnZpc3VhbFZpZXdwb3J0KT92b2lkIDA6aS53aWR0aCk/czppbm5lcldpZHRoLEg9bnVsbCE9KGg9bnVsbD09KGQ9d2luZG93LnZpc3VhbFZpZXdwb3J0KT92b2lkIDA6ZC5oZWlnaHQpP2g6aW5uZXJIZWlnaHQse3Njcm9sbFg6eSxzY3JvbGxZOk19PXdpbmRvdyx7aGVpZ2h0OnYsd2lkdGg6RSx0b3A6eCxyaWdodDpDLGJvdHRvbTpJLGxlZnQ6Un09ZS5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKSx7dG9wOlQscmlnaHQ6Qixib3R0b206RixsZWZ0OlZ9PSh0PT57Y29uc3QgZT13aW5kb3cuZ2V0Q29tcHV0ZWRTdHlsZSh0KTtyZXR1cm57dG9wOnBhcnNlRmxvYXQoZS5zY3JvbGxNYXJnaW5Ub3ApfHwwLHJpZ2h0OnBhcnNlRmxvYXQoZS5zY3JvbGxNYXJnaW5SaWdodCl8fDAsYm90dG9tOnBhcnNlRmxvYXQoZS5zY3JvbGxNYXJnaW5Cb3R0b20pfHwwLGxlZnQ6cGFyc2VGbG9hdChlLnNjcm9sbE1hcmdpbkxlZnQpfHwwfX0pKGUpO2xldCBrPVwic3RhcnRcIj09PWZ8fFwibmVhcmVzdFwiPT09Zj94LVQ6XCJlbmRcIj09PWY/SStGOngrdi8yLVQrRixEPVwiY2VudGVyXCI9PT11P1IrRS8yLVYrQjpcImVuZFwiPT09dT9DK0I6Ui1WO2NvbnN0IEw9W107Zm9yKGxldCB0PTA7dDx3Lmxlbmd0aDt0Kyspe2NvbnN0IGU9d1t0XSx7aGVpZ2h0Omwsd2lkdGg6cix0b3A6aSxyaWdodDpzLGJvdHRvbTpkLGxlZnQ6aH09ZS5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtpZihcImlmLW5lZWRlZFwiPT09YyYmeD49MCYmUj49MCYmSTw9SCYmQzw9YiYmKGU9PT1tJiYhbihlKXx8eD49aSYmSTw9ZCYmUj49aCYmQzw9cykpcmV0dXJuIEw7Y29uc3QgYT1nZXRDb21wdXRlZFN0eWxlKGUpLGc9cGFyc2VJbnQoYS5ib3JkZXJMZWZ0V2lkdGgsMTApLHA9cGFyc2VJbnQoYS5ib3JkZXJUb3BXaWR0aCwxMCksVz1wYXJzZUludChhLmJvcmRlclJpZ2h0V2lkdGgsMTApLFQ9cGFyc2VJbnQoYS5ib3JkZXJCb3R0b21XaWR0aCwxMCk7bGV0IEI9MCxGPTA7Y29uc3QgVj1cIm9mZnNldFdpZHRoXCJpbiBlP2Uub2Zmc2V0V2lkdGgtZS5jbGllbnRXaWR0aC1nLVc6MCxTPVwib2Zmc2V0SGVpZ2h0XCJpbiBlP2Uub2Zmc2V0SGVpZ2h0LWUuY2xpZW50SGVpZ2h0LXAtVDowLFg9XCJvZmZzZXRXaWR0aFwiaW4gZT8wPT09ZS5vZmZzZXRXaWR0aD8wOnIvZS5vZmZzZXRXaWR0aDowLFk9XCJvZmZzZXRIZWlnaHRcImluIGU/MD09PWUub2Zmc2V0SGVpZ2h0PzA6bC9lLm9mZnNldEhlaWdodDowO2lmKG09PT1lKUI9XCJzdGFydFwiPT09Zj9rOlwiZW5kXCI9PT1mP2stSDpcIm5lYXJlc3RcIj09PWY/byhNLE0rSCxILHAsVCxNK2ssTStrK3Ysdik6ay1ILzIsRj1cInN0YXJ0XCI9PT11P0Q6XCJjZW50ZXJcIj09PXU/RC1iLzI6XCJlbmRcIj09PXU/RC1iOm8oeSx5K2IsYixnLFcseStELHkrRCtFLEUpLEI9TWF0aC5tYXgoMCxCK00pLEY9TWF0aC5tYXgoMCxGK3kpO2Vsc2V7Qj1cInN0YXJ0XCI9PT1mP2staS1wOlwiZW5kXCI9PT1mP2stZCtUK1M6XCJuZWFyZXN0XCI9PT1mP28oaSxkLGwscCxUK1MsayxrK3Ysdik6ay0oaStsLzIpK1MvMixGPVwic3RhcnRcIj09PXU/RC1oLWc6XCJjZW50ZXJcIj09PXU/RC0oaCtyLzIpK1YvMjpcImVuZFwiPT09dT9ELXMrVytWOm8oaCxzLHIsZyxXK1YsRCxEK0UsRSk7Y29uc3R7c2Nyb2xsTGVmdDp0LHNjcm9sbFRvcDpufT1lO0I9MD09PVk/MDpNYXRoLm1heCgwLE1hdGgubWluKG4rQi9ZLGUuc2Nyb2xsSGVpZ2h0LWwvWStTKSksRj0wPT09WD8wOk1hdGgubWF4KDAsTWF0aC5taW4odCtGL1gsZS5zY3JvbGxXaWR0aC1yL1grVikpLGsrPW4tQixEKz10LUZ9TC5wdXNoKHtlbDplLHRvcDpCLGxlZnQ6Rn0pfXJldHVybiBMfTtleHBvcnR7ciBhcyBjb21wdXRlfTsvLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/compute-scroll-into-view/dist/index.js\n");

/***/ })

};
;