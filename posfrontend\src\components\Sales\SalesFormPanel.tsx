"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Form,
  Button,
  Select,
  InputNumber,
  Empty,
  Spin,
  Modal,
  Image,
} from "antd";
import {
  DeleteOutlined,
  PlusOutlined,
  ShoppingCartOutlined,
  SearchOutlined,
  LoadingOutlined,
  ShopOutlined,
  PrinterOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import {
  useCreateSaleMutation,
  CreateSaleDto,
  CreateSaleItemDto,
} from "@/reduxRTK/services/salesApi";
import {
  useGetAllProductsQuery,
  Product,
} from "@/reduxRTK/services/productApi";
import {
  useGetUserStoresQuery,
  useGetUserDefaultStoreQuery,
} from "@/reduxRTK/services/userStoreApi";
import SlidingPanel from "@/components/ui/SlidingPanel";
import { showMessage } from "@/utils/showMessage";
import {
  generateReceiptHTML,
  generateReceiptImage,
} from "@/utils/cloudinaryUtils";
import { Store } from "@/types/store";
import "./sales-panels.css";

interface SalesFormPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const SalesFormPanel: React.FC<SalesFormPanelProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [items, setItems] = useState<
    (CreateSaleItemDto & { productName: string })[]
  >([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [quantity, setQuantity] = useState<number>(1);
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStore, setSelectedStore] = useState<Store | null>(null);
  const [isGeneratingReceipt, setIsGeneratingReceipt] = useState(false);
  const [receiptPreviewVisible, setReceiptPreviewVisible] = useState(false);
  const [receiptUrl, setReceiptUrl] = useState<string | null>(null);
  const [hasPrinted, setHasPrinted] = useState(false);

  // Debug state changes
  useEffect(() => {
    console.log("Items state changed:", items);
  }, [items]);


  const {
    data: productsData,
    isLoading: isLoadingProducts,
    refetch: refetchProducts,
  } = useGetAllProductsQuery(
    {
      page: 1,
      limit: 1000, // Increased limit to get more products for sales
      search: searchTerm,
    },
    {
      // Force refetch when component mounts and when panel opens
      refetchOnMountOrArgChange: true,
      refetchOnFocus: false,
      refetchOnReconnect: true, // Refetch when reconnecting
    },
  );

  // Debug products data
  useEffect(() => {
    if (productsData) {
      console.log("🛒 Products loaded:", {
        total: productsData.data?.total || 0,
        productsCount: productsData.data?.products?.length || 0,
        isLoading: isLoadingProducts,
      });
    }
  }, [productsData, isLoadingProducts]);

  // Get current user ID from auth state
  const getCurrentUserId = () => {
    if (typeof window !== "undefined") {
      // @ts-ignore - Redux state is exposed for debugging
      const state = window.__REDUX_STATE;
      return state?.auth?.user?.id || 0;
    }
    return 0;
  };

  // Fetch user stores
  const { data: userStoresData } = useGetUserStoresQuery(getCurrentUserId());

  // Fetch default store
  const { data: defaultStoreData } =
    useGetUserDefaultStoreQuery(getCurrentUserId());

  // Set default store when data is loaded
  useEffect(() => {
    if (defaultStoreData?.data) {
      setSelectedStore(defaultStoreData.data);
      form.setFieldsValue({ storeId: defaultStoreData.data.id });
    } else if (userStoresData?.data && userStoresData.data.length > 0) {
      setSelectedStore(userStoresData.data[0]);
      form.setFieldsValue({ storeId: userStoresData.data[0].id });
    }
  }, [defaultStoreData, userStoresData, form]);

  // Create sale mutation
  const [createSale, { isLoading: isSubmitting }] = useCreateSaleMutation();

  // Calculate total amount whenever items change
  useEffect(() => {
    if (items && items.length > 0) {
      const total = items.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0,
      );
      setTotalAmount(total);
      if (form) {
        form.setFieldsValue({ totalAmount: total });
      }

      // Debug log to check items state
      console.log("Current items in useEffect:", items);
    } else {
      setTotalAmount(0);
      if (form) {
        form.setFieldsValue({ totalAmount: 0 });
      }
    }
  }, [items, form]);

  // Handle panel open/close
  useEffect(() => {
    if (isOpen) {
      // When panel opens, ensure we have fresh product data
      console.log("🛒 Sales panel opened - fetching fresh product data");
      refetchProducts();
    } else {
      // Reset form when panel is closed
      if (form) {
        form.resetFields();
      }
      setItems([]);
      setSelectedProduct(null);
      setQuantity(1);
      setTotalAmount(0);
      setReceiptUrl(null);
      setReceiptPreviewVisible(false);
      setHasPrinted(false);
    }
  }, [isOpen, form, refetchProducts]);

  // Handle adding an item to the sale
  const handleAddItem = () => {
    if (!selectedProduct) {
      showMessage("error", "Please select a product");
      return;
    }

    if (quantity <= 0) {
      showMessage("error", "Quantity must be greater than 0");
      return;
    }

    if (selectedProduct.stockQuantity < quantity) {
      showMessage(
        "error",
        `Only ${selectedProduct.stockQuantity} units available in stock`,
      );
      return;
    }

    console.log("Adding item with product:", selectedProduct);

    // Check if product already exists in items
    const existingItemIndex = items.findIndex(
      (item) => item.productId === selectedProduct.id,
    );

    if (existingItemIndex >= 0) {
      // Update existing item
      const updatedItems = [...items];
      const newQuantity = updatedItems[existingItemIndex].quantity + quantity;

      if (newQuantity > selectedProduct.stockQuantity) {
        showMessage(
          "error",
          `Cannot add more than ${selectedProduct.stockQuantity} units of this product`,
        );
        return;
      }

      updatedItems[existingItemIndex].quantity = newQuantity;

      // Make sure the product name is set
      if (!updatedItems[existingItemIndex].productName) {
        updatedItems[existingItemIndex].productName = selectedProduct.name;
      }

      // Update the state with the new array
      console.log("Updating existing item. New items array:", updatedItems);
      setItems([...updatedItems]); // Create a new array reference to force re-render

      // Show success message
      showMessage("success", `Updated quantity of ${selectedProduct.name}`);
    } else {
      // Add new item
      const newItem = {
        productId: selectedProduct.id,
        productName: selectedProduct.name, // Make sure this is correctly set
        quantity,
        price:
          typeof selectedProduct.price === "string"
            ? parseFloat(selectedProduct.price)
            : selectedProduct.price,
      };

      // Create a new array with the new item
      const newItems = [...items, newItem];

      // Update the state with the new array
      console.log("Adding new item. New items array:", newItems);
      setItems(newItems); // This should trigger a re-render

      // Show success message
      showMessage(
        "success",
        `Added ${quantity} ${selectedProduct.name} to sale`,
      );
    }

    // Reset selection
    setSelectedProduct(null);
    setQuantity(1);
    if (form) {
      form.setFieldsValue({ productId: undefined, quantity: 1 });
    }
  };

  // Handle removing an item from the sale
  const handleRemoveItem = (index: number) => {
    const updatedItems = [...items];
    updatedItems.splice(index, 1);
    setItems(updatedItems);
  };

  // Handle printing receipt - directly trigger print dialog
  const handlePrintReceipt = useCallback(() => {

    if (!receiptUrl || hasPrinted) {
      console.log(
        "Skipping print: ",
        !receiptUrl ? "No receipt URL" : "Already printed",
      );
      return;
    }

    console.log("Printing receipt:", receiptUrl);

    // Mark as printed immediately to prevent multiple print dialogs
    setHasPrinted(true);

    // Create a hidden iframe to load the image
    const iframe = document.createElement("iframe");
    iframe.style.display = "none";
    document.body.appendChild(iframe);

    // Set up the iframe content with the image and print CSS
    iframe.onload = () => {
      if (iframe.contentWindow) {
        // Write the HTML content to the iframe
        iframe.contentWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Print Receipt</title>
              <style>
                body {
                  margin: 0;
                  padding: 0;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  height: 100vh;
                }
                img {
                  max-width: 100%;
                  max-height: 100vh;
                }
                @media print {
                  body {
                    margin: 0;
                    padding: 0;
                  }
                  img {
                    width: 100%;
                    height: auto;
                  }
                }
              </style>
            </head>
            <body>
              <img src="${receiptUrl}" alt="Receipt" />
            </body>
          </html>
        `);

        // Close the document
        iframe.contentWindow.document.close();

        // Use a single print trigger with a delay to ensure the image is loaded
        setTimeout(() => {
          if (iframe.contentWindow) {
            try {
              // Print the iframe content
              iframe.contentWindow.focus();
              iframe.contentWindow.print();
            } catch (e) {
              console.error("Error printing receipt:", e);
            }

            // Remove the iframe after printing
            setTimeout(() => {
              document.body.removeChild(iframe);
            }, 1000);
          }
        }, 500);
      }
    };

    // Set the iframe source to trigger the onload event
    iframe.src = "about:blank";
  }, [receiptUrl, hasPrinted]);

  // Effect to automatically print receipt when modal is shown
  useEffect(() => {
    if (receiptPreviewVisible && receiptUrl && !hasPrinted) {
      // Add a small delay to ensure the receipt image is loaded
      const timer = setTimeout(() => {
        handlePrintReceipt(); // This now handles the hasPrinted state internally
      }, 800);

      return () => clearTimeout(timer);
    }
  }, [receiptPreviewVisible, receiptUrl, hasPrinted, handlePrintReceipt]);

  // Handle form submission
  const handleSubmit = async () => {
    try {
      if (items.length === 0) {
        showMessage("error", "Please add at least one item to the sale");
        return;
      }

      // Validate form fields
      const values = await form.validateFields();

      // Check if store is selected
      if (!selectedStore) {
        showMessage(
          "error",
          "No store information available. Please set up your store in your profile settings.",
        );
        return;
      }

      // Set loading state for receipt generation
      setIsGeneratingReceipt(true);

      // Get store information for receipt
      const storeInfo = selectedStore ||
        userStoresData?.data?.find((store) => store.id === values.storeId) || {
          name: "POS System",
        };

      // Generate receipt HTML
      const receiptHTML = generateReceiptHTML(
        {
          id: Date.now(), // Temporary ID until we get the real one
          totalAmount,
          paymentMethod: values.paymentMethod,
          transactionDate: new Date().toISOString(),
          items: items.map((item) => ({
            productName: item.productName,
            quantity: item.quantity,
            price: item.price,
          })),
        },
        storeInfo,
      );

      // Generate receipt image and get URL
      let receiptUrl = "https://receipt.example.com/placeholder";
      try {
        receiptUrl = await generateReceiptImage(receiptHTML);
      } catch (error) {
        console.error("Failed to generate receipt image:", error);
        // Continue with placeholder URL if image generation fails
      }

      const saleData: CreateSaleDto = {
        totalAmount,
        paymentMethod: values.paymentMethod,
        items: items.map((item) => ({
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
        })),
        receiptUrl,
        storeId: selectedStore?.id,
      };

      const response = await createSale(saleData).unwrap();

      if (response.success) {
        showMessage("success", "Sale created successfully");

        // Store the receipt URL for preview
        setReceiptUrl(receiptUrl);

        // Show receipt preview modal and offer print option
        setReceiptPreviewVisible(true);



        // Refresh product data to get updated stock quantities
        refetchProducts();

        // Trigger the success callback to refresh the list WITHOUT closing the panel

        setTimeout(() => {
          if (onSuccess) {
            // Call refetch directly instead of closing the panel
            refetchProducts();
          }
        }, 300);

        // Keep the panel open until the user explicitly closes it
        // This ensures the receipt modal stays visible
      } else {
        showMessage("error", response.message || "Failed to create sale");
      }
    } catch (error: any) {
      showMessage(
        "error",
        error.data?.message || "An error occurred while creating the sale",
      );
    } finally {
      setIsGeneratingReceipt(false);
    }
  };

  // Debug log to check items state when rendering
  console.log("Rendering with items:", items);

  return (
    <SlidingPanel
      title="Point of Sale"
      isOpen={isOpen}
      onClose={onClose}
      width="95%"
    >
      <div className="sales-form mt-10 min-h-screen bg-white">
        {/* Modern POS Header */}
        <div className="flex items-center justify-between border-b border-gray-200 bg-gray-50 px-6 py-3 shadow-md">
          <div className="flex items-center">
            <ShoppingCartOutlined className="mr-3 text-2xl text-blue-500" />
            <h2 className="m-0 text-xl font-bold text-gray-800">
              New Transaction
            </h2>
          </div>
          <div className="text-lg font-bold text-gray-800">
            Total:{" "}
            <span className="text-green-600">GHS {totalAmount.toFixed(2)}</span>
          </div>
        </div>

        <div className="p-4">
          <div className="grid grid-cols-1 gap-4 lg:grid-cols-4">
            {/* Left Column - Product Selection */}
            <div className="lg:col-span-3">
              <div className="mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-lg">
                <div className="mb-4 flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-800">
                    Product Selection
                  </h3>
                  <div className="text-sm text-gray-600">
                    <span className="mr-1 text-red-500">*</span> Required fields
                  </div>
                </div>

                {/* Product Selection - No Form wrapper to avoid conflicts */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <div className="md:col-span-2">
                    <div className="mb-2">
                      <label className="text-gray-800">
                        Product <span className="text-red-500">*</span>
                      </label>
                      <Select
                        showSearch
                        placeholder={
                          isLoadingProducts
                            ? "Loading products..."
                            : "Search products..."
                        }
                        optionFilterProp="children"
                        loading={isLoadingProducts}
                        disabled={isLoadingProducts}
                        onChange={(value) => {
                          const product = productsData?.data?.products.find(
                            (p) => p.id === value,
                          );
                          console.log("Selected product:", product);
                          if (product) {
                            // Make a deep copy to avoid reference issues
                            setSelectedProduct({
                              ...product,
                              // Ensure price is properly formatted
                              price:
                                typeof product.price === "string"
                                  ? product.price
                                  : String(product.price),
                            });
                          } else {
                            setSelectedProduct(null);
                          }
                        }}
                        onSearch={setSearchTerm}
                        filterOption={false}
                        className="text-gray-800"
                        size="large"
                        suffixIcon={
                          isLoadingProducts ? (
                            <LoadingOutlined spin />
                          ) : (
                            <SearchOutlined />
                          )
                        }
                        notFoundContent={
                          isLoadingProducts ? (
                            <Spin size="small" />
                          ) : (
                            "No products found"
                          )
                        }
                      >
                        {productsData?.data?.products?.map((product) => (
                          <Select.Option
                            key={product.id}
                            value={product.id}
                            disabled={product.stockQuantity <= 0}
                          >
                            {product.name}{" "}
                            {product.stockQuantity <= 0
                              ? "(Out of Stock)"
                              : `(Stock: ${product.stockQuantity})`}
                          </Select.Option>
                        ))}
                      </Select>
                    </div>

                    <div>
                      <label className="text-gray-800">
                        Qty <span className="text-red-500">*</span>
                      </label>
                      <InputNumber
                        min={1}
                        value={quantity}
                        onChange={(value) => setQuantity(value || 1)}
                        style={{ width: "100%" }}
                        className="text-gray-800"
                        size="large"
                      />
                    </div>
                  </div>

                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleAddItem}
                    className="mt-2 h-12 w-full bg-blue-600 text-base font-medium hover:bg-blue-700"
                    disabled={!selectedProduct}
                    size="large"
                  >
                    Add to Cart
                  </Button>
              </div>

              {/* Cart Items */}
              <div className="mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-lg">
                <h3 className="mb-4 flex items-center text-lg font-semibold text-gray-800">
                  <ShoppingCartOutlined className="mr-2" /> Cart Items
                </h3>
                <div className="max-h-[350px] overflow-x-auto overflow-y-auto">
                  {items.length === 0 ? (
                    <div className="rounded-lg border border-gray-200 bg-gray-50 p-8 text-center">
                      <Empty
                        description={
                          <span className="text-gray-500">
                            No items in cart
                          </span>
                        }
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                      />
                    </div>
                  ) : (
                    <table className="min-w-full overflow-hidden rounded-lg border border-gray-200 bg-white">
                      <thead className="sticky top-0 z-10 bg-gray-50 text-gray-700">
                        <tr>
                          <th className="px-4 py-3 text-left font-medium">
                            Product
                          </th>
                          <th className="w-20 px-4 py-3 text-center font-medium">
                            Qty
                          </th>
                          <th className="w-28 px-4 py-3 text-right font-medium">
                            Price
                          </th>
                          <th className="w-28 px-4 py-3 text-right font-medium">
                            Subtotal
                          </th>
                          <th className="w-16 px-4 py-3 text-center font-medium">
                            Action
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {items.map((item, index) => (
                          <tr
                            key={`${item.productId}-${index}`}
                            className="border-b border-gray-200 hover:bg-gray-50"
                          >
                            <td className="px-4 py-3 font-medium text-gray-800">
                              {item.productName || "Unknown Product"}
                            </td>
                            <td className="px-4 py-3 text-center text-gray-800">
                              {item.quantity}
                            </td>
                            <td className="px-4 py-3 text-right text-gray-800">
                              GHS {Number(item.price).toFixed(2)}
                            </td>
                            <td className="px-4 py-3 text-right font-medium text-gray-800">
                              GHS{" "}
                              {(Number(item.price) * item.quantity).toFixed(2)}
                            </td>
                            <td className="px-4 py-3 text-center">
                              <Button
                                icon={<DeleteOutlined />}
                                onClick={() => handleRemoveItem(index)}
                                type="text"
                                danger
                                className="text-red-500 hover:bg-gray-100 hover:text-red-400"
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot className="sticky bottom-0 z-10 bg-gray-100">
                        <tr>
                          <td
                            colSpan={3}
                            className="px-4 py-3 text-right font-bold text-gray-800"
                          >
                            Total
                          </td>
                          <td
                            colSpan={2}
                            className="px-4 py-3 text-right font-bold text-green-600"
                          >
                            GHS {totalAmount.toFixed(2)}
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  )}
                </div>
              </div>
            </div>

            {/* Right Column - Payment Information */}
            <div>
              <div className="sticky top-4 rounded-lg border border-gray-200 bg-white p-4 shadow-lg">
                <h3 className="mb-4 flex items-center text-lg font-semibold text-gray-800">
                  <ShoppingCartOutlined className="mr-2" /> Checkout
                </h3>

                <Form
                  form={form}
                  layout="vertical"
                  initialValues={{
                    paymentMethod: "cash",
                  }}
                  onFinish={handleSubmit}
                >
                  <div className="mb-6 rounded-lg border border-gray-200 bg-gray-50 p-4">
                    <div className="mb-4 flex items-center justify-between">
                      <span className="text-gray-600">Items:</span>
                      <span className="font-medium text-gray-800">
                        {items.length}
                      </span>
                    </div>
                    <div className="mb-4 flex items-center justify-between">
                      <span className="text-gray-600">Total Quantity:</span>
                      <span className="font-medium text-gray-800">
                        {items.reduce((sum, item) => sum + item.quantity, 0)}
                      </span>
                    </div>
                    <div className="mb-4 flex items-center justify-between text-lg">
                      <span className="text-gray-600">Subtotal:</span>
                      <span className="font-bold text-gray-800">
                        GHS {totalAmount.toFixed(2)}
                      </span>
                    </div>
                    <div className="my-4 border-t border-gray-300"></div>
                    <div className="flex items-center justify-between text-xl">
                      <span className="text-gray-800">Total:</span>
                      <span className="font-bold text-green-600">
                        GHS {totalAmount.toFixed(2)}
                      </span>
                    </div>
                  </div>

                  {/* Store information is now automatically associated with the admin user */}
                  {selectedStore ? (
                    <div className="mb-4">
                      <label className="mb-2 block text-gray-700">Store</label>
                      <div className="flex items-center rounded-lg border border-gray-200 bg-gray-50 p-3">
                        <ShopOutlined className="mr-2 text-blue-500" />
                        <span className="text-gray-800">
                          {selectedStore.name}
                        </span>
                        <input
                          type="hidden"
                          name="storeId"
                          value={selectedStore.id}
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="mb-4 rounded-lg border border-gray-200 bg-gray-50 p-3">
                      <p className="text-gray-600">
                        No store information available. Please set up your store
                        in your profile settings.
                      </p>
                    </div>
                  )}

                  <Form.Item
                    name="paymentMethod"
                    label={
                      <span className="text-gray-700">
                        Payment Method <span className="text-red-500">*</span>
                      </span>
                    }
                    rules={[
                      {
                        required: true,
                        message: "Please select a payment method",
                      },
                    ]}
                    initialValue="cash"
                  >
                    <Select className="text-gray-800" size="large">
                      <Select.Option value="cash">
                        <div className="flex items-center">
                          <span className="mr-2">💵</span> Cash
                        </div>
                      </Select.Option>
                      <Select.Option value="card">
                        <div className="flex items-center">
                          <span className="mr-2">💳</span> Card
                        </div>
                      </Select.Option>
                      <Select.Option value="mobile_money">
                        <div className="flex items-center">
                          <span className="mr-2">📱</span> Mobile Money
                        </div>
                      </Select.Option>
                    </Select>
                  </Form.Item>

                  <div className="mt-8 space-y-4">
                    {receiptPreviewVisible ? (
                      // Show "Done" button when receipt is visible
                      <Button
                        type="primary"
                        icon={<ShoppingCartOutlined />}
                        onClick={() => {
                          // Close the modal and reset the receipt state
                          setReceiptPreviewVisible(false);
                          setReceiptUrl(null);
                          setHasPrinted(false);

                          // Reset the form to start a new sale
                          form.resetFields();
                          setItems([]);
                          setSelectedProduct(null);
                          setQuantity(1);
                          setTotalAmount(0);
                        }}
                        className="text-md font-small h-14 w-full bg-green-600 hover:bg-green-700"
                        size="large"
                      >
                        Start New Sale
                      </Button>
                    ) : (
                      // Show "Complete Sale" button when creating a sale
                      <Button
                        type="primary"
                        htmlType="submit"
                        icon={<ShoppingCartOutlined />}
                        loading={isSubmitting || isGeneratingReceipt}
                        disabled={items.length === 0}
                        className="text-md font-small h-14 w-full bg-green-600 hover:bg-green-700"
                        size="large"
                      >
                        {isGeneratingReceipt
                          ? "Generating Receipt"
                          : "Complete Sale"}
                      </Button>
                    )}

                    <Button
                      onClick={onClose}
                      className="h-12 w-full border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200"
                      size="large"
                    >
                      Cancel
                    </Button>
                  </div>
                </Form>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Receipt Preview Modal */}
      <Modal
        title={
          <div className="flex items-center text-gray-800">
            <PrinterOutlined className="mr-2" />
            <span>Receipt Preview</span>
          </div>
        }
        open={receiptPreviewVisible}
        onCancel={() => {
          // Close the modal and reset the receipt state
          setReceiptPreviewVisible(false);
          setReceiptUrl(null);
          setHasPrinted(false);

          // Reset the form to start a new sale
          form.resetFields();
          setItems([]);
          setSelectedProduct(null);
          setQuantity(1);
          setTotalAmount(0);
        }}
        width={500}
        centered
        className="receipt-preview-modal"
        footer={[
          <Button
            key="close"
            onClick={() => {
              // Close the modal and reset the receipt state
              setReceiptPreviewVisible(false);
              setReceiptUrl(null);
              setHasPrinted(false);

              // Reset the form to start a new sale
              form.resetFields();
              setItems([]);
              setSelectedProduct(null);
              setQuantity(1);
              setTotalAmount(0);
            }}
            className="border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200"
          >
            Close & New Sale
          </Button>,
          <Button
            key="print"
            type="primary"
            icon={<PrinterOutlined />}
            onClick={() => {
              // If already printed once, reset the flag to allow printing again
              if (hasPrinted) {
                setHasPrinted(false);
              }
              handlePrintReceipt();
            }}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {hasPrinted ? "Print Again" : "Print Receipt"}
          </Button>,
        ]}
      >
        <div className="flex flex-col items-center">
          {receiptUrl ? (
            <div className="receipt-image-container">
              <Image
                src={receiptUrl}
                alt="Receipt"
                className="receipt-image"
                style={{ maxWidth: "100%" }}
              />
            </div>
          ) : (
            <div className="flex h-64 items-center justify-center">
              <Spin size="large" />
            </div>
          )}
        </div>
      </Modal>
    </SlidingPanel>
  );
};

export default SalesFormPanel;
