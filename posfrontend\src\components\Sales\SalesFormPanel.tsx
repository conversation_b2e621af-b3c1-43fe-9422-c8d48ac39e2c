"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Form,
  Button,
  Select,
  InputNumber,
  Empty,
  Spin,
  Modal,
  Image,
} from "antd";
import {
  DeleteOutlined,
  PlusOutlined,
  ShoppingCartOutlined,
  SearchOutlined,
  LoadingOutlined,
  ShopOutlined,
  PrinterOutlined,
} from "@ant-design/icons";
import {
  useCreateSaleMutation,
  CreateSaleDto,
  CreateSaleItemDto,
} from "@/reduxRTK/services/salesApi";
import {
  useGetAllProductsQuery,
  Product,
} from "@/reduxRTK/services/productApi";
import {
  useGetUserStoresQuery,
  useGetUserDefaultStoreQuery,
} from "@/reduxRTK/services/userStoreApi";
import SlidingPanel from "@/components/ui/SlidingPanel";
import { showMessage } from "@/utils/showMessage";
import {
  generateReceiptHTML,
  generateReceiptImage,
} from "@/utils/cloudinaryUtils";
import { Store } from "@/types/store";

interface SalesFormPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const SalesFormPanel: React.FC<SalesFormPanelProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [items, setItems] = useState<
    (CreateSaleItemDto & { productName: string })[]
  >([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [quantity, setQuantity] = useState<number>(1);
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStore, setSelectedStore] = useState<Store | null>(null);
  const [isGeneratingReceipt, setIsGeneratingReceipt] = useState(false);
  const [receiptPreviewVisible, setReceiptPreviewVisible] = useState(false);
  const [receiptUrl, setReceiptUrl] = useState<string | null>(null);
  const [hasPrinted, setHasPrinted] = useState(false);

  const {
    data: productsData,
    isLoading: isLoadingProducts,
    refetch: refetchProducts,
  } = useGetAllProductsQuery(
    {
      page: 1,
      limit: 1000,
      search: searchTerm,
    },
    {
      refetchOnMountOrArgChange: true,
    },
  );

  // Get current user ID from auth state
  const getCurrentUserId = () => {
    if (typeof window !== "undefined") {
      // @ts-ignore - Redux state is exposed for debugging
      const state = window.__REDUX_STATE;
      return state?.auth?.user?.id || 0;
    }
    return 0;
  };

  // Fetch user stores
  const { data: userStoresData } = useGetUserStoresQuery(getCurrentUserId());

  // Fetch default store
  const { data: defaultStoreData } =
    useGetUserDefaultStoreQuery(getCurrentUserId());

  // Set default store when data is loaded
  useEffect(() => {
    if (defaultStoreData?.data) {
      setSelectedStore(defaultStoreData.data);
    } else if (userStoresData?.data && userStoresData.data.length > 0) {
      setSelectedStore(userStoresData.data[0]);
    }
  }, [defaultStoreData, userStoresData]);

  // Create sale mutation
  const [createSale, { isLoading: isSubmitting }] = useCreateSaleMutation();

  // Calculate total amount whenever items change
  useEffect(() => {
    const total = items.reduce(
      (sum, item) => sum + item.price * item.quantity,
      0,
    );
    setTotalAmount(total);
  }, [items]);

  // Reset form when panel opens/closes
  useEffect(() => {
    if (isOpen) {
      refetchProducts();
    } else {
      form.resetFields();
      setItems([]);
      setSelectedProduct(null);
      setQuantity(1);
      setTotalAmount(0);
      setReceiptPreviewVisible(false);
      setReceiptUrl(null);
      setHasPrinted(false);
    }
  }, [isOpen, form, refetchProducts]);

  // Handle adding an item to the sale
  const handleAddItem = () => {
    if (!selectedProduct) {
      showMessage("error", "Please select a product");
      return;
    }

    if (quantity <= 0) {
      showMessage("error", "Quantity must be greater than 0");
      return;
    }

    if (selectedProduct.stockQuantity < quantity) {
      showMessage(
        "error",
        `Only ${selectedProduct.stockQuantity} units available in stock`,
      );
      return;
    }

    // Check if product already exists in items
    const existingItemIndex = items.findIndex(
      (item) => item.productId === selectedProduct.id,
    );

    if (existingItemIndex >= 0) {
      // Update existing item
      const updatedItems = [...items];
      const newQuantity = updatedItems[existingItemIndex].quantity + quantity;

      if (newQuantity > selectedProduct.stockQuantity) {
        showMessage(
          "error",
          `Cannot add more than ${selectedProduct.stockQuantity} units of this product`,
        );
        return;
      }

      updatedItems[existingItemIndex].quantity = newQuantity;
      setItems(updatedItems);
      showMessage("success", `Updated quantity of ${selectedProduct.name}`);
    } else {
      // Add new item
      const newItem = {
        productId: selectedProduct.id,
        productName: selectedProduct.name,
        quantity,
        price:
          typeof selectedProduct.price === "string"
            ? parseFloat(selectedProduct.price)
            : selectedProduct.price,
      };

      setItems([...items, newItem]);
      showMessage("success", `Added ${quantity} ${selectedProduct.name} to sale`);
    }

    // Reset selection
    setSelectedProduct(null);
    setQuantity(1);
  };

  // Handle removing an item from the sale
  const handleRemoveItem = (index: number) => {
    const updatedItems = [...items];
    updatedItems.splice(index, 1);
    setItems(updatedItems);
  };

  // Handle printing receipt - directly trigger print dialog
  const handlePrintReceipt = useCallback(() => {

    if (!receiptUrl || hasPrinted) {
      console.log(
        "Skipping print: ",
        !receiptUrl ? "No receipt URL" : "Already printed",
      );
      return;
    }

    console.log("Printing receipt:", receiptUrl);

    // Mark as printed immediately to prevent multiple print dialogs
    setHasPrinted(true);

    // Create a hidden iframe to load the image
    const iframe = document.createElement("iframe");
    iframe.style.display = "none";
    document.body.appendChild(iframe);

    // Set up the iframe content with the image and print CSS
    iframe.onload = () => {
      if (iframe.contentWindow) {
        // Write the HTML content to the iframe
        iframe.contentWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Print Receipt</title>
              <style>
                body {
                  margin: 0;
                  padding: 0;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  height: 100vh;
                }
                img {
                  max-width: 100%;
                  max-height: 100vh;
                }
                @media print {
                  body {
                    margin: 0;
                    padding: 0;
                  }
                  img {
                    width: 100%;
                    height: auto;
                  }
                }
              </style>
            </head>
            <body>
              <img src="${receiptUrl}" alt="Receipt" />
            </body>
          </html>
        `);

        // Close the document
        iframe.contentWindow.document.close();

        // Use a single print trigger with a delay to ensure the image is loaded
        setTimeout(() => {
          if (iframe.contentWindow) {
            try {
              // Print the iframe content
              iframe.contentWindow.focus();
              iframe.contentWindow.print();
            } catch (e) {
              console.error("Error printing receipt:", e);
            }

            // Remove the iframe after printing
            setTimeout(() => {
              document.body.removeChild(iframe);
            }, 1000);
          }
        }, 500);
      }
    };

    // Set the iframe source to trigger the onload event
    iframe.src = "about:blank";
  }, [receiptUrl, hasPrinted]);

  // Effect to automatically print receipt when modal is shown
  useEffect(() => {
    if (receiptPreviewVisible && receiptUrl && !hasPrinted) {
      // Add a small delay to ensure the receipt image is loaded
      const timer = setTimeout(() => {
        handlePrintReceipt(); // This now handles the hasPrinted state internally
      }, 800);

      return () => clearTimeout(timer);
    }
  }, [receiptPreviewVisible, receiptUrl, hasPrinted, handlePrintReceipt]);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    try {
      if (items.length === 0) {
        showMessage("error", "Please add at least one item to the sale");
        return;
      }

      if (!selectedStore) {
        showMessage(
          "error",
          "No store information available. Please set up your store in your profile settings.",
        );
        return;
      }

      setIsGeneratingReceipt(true);

      // Generate receipt HTML
      const receiptHTML = generateReceiptHTML(
        {
          id: Date.now(),
          totalAmount,
          paymentMethod: values.paymentMethod,
          transactionDate: new Date().toISOString(),
          items: items.map((item) => ({
            productName: item.productName,
            quantity: item.quantity,
            price: item.price,
          })),
        },
        selectedStore,
      );

      // Generate receipt image and get URL
      let receiptUrl = "https://receipt.example.com/placeholder";
      try {
        receiptUrl = await generateReceiptImage(receiptHTML);
      } catch (error) {
        console.error("Failed to generate receipt image:", error);
      }

      const saleData: CreateSaleDto = {
        totalAmount,
        paymentMethod: values.paymentMethod,
        items: items.map((item) => ({
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
        })),
        receiptUrl,
        storeId: selectedStore?.id,
      };

      const response = await createSale(saleData).unwrap();

      if (response.success) {
        showMessage("success", "Sale created successfully");
        setReceiptUrl(receiptUrl);
        setReceiptPreviewVisible(true);
        refetchProducts();

        if (onSuccess) {
          onSuccess();
        }
      } else {
        showMessage("error", response.message || "Failed to create sale");
      }
    } catch (error: any) {
      showMessage(
        "error",
        error.data?.message || "An error occurred while creating the sale",
      );
    } finally {
      setIsGeneratingReceipt(false);
    }
  };

  return (
    <SlidingPanel
      title="Point of Sale"
      isOpen={isOpen}
      onClose={onClose}
      width="90%"
    >
      <div className="sales-form p-6 bg-white">
        <div className="mb-4">
          <h2 className="text-xl font-bold text-gray-800 mb-2">New Transaction</h2>
          <div className="text-lg font-bold text-gray-800">
            Total: <span className="text-green-600">GHS {totalAmount.toFixed(2)}</span>
          </div>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            paymentMethod: "cash",
          }}
        >
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            {/* Product Selection */}
            <div className="lg:col-span-2">
              <div className="mb-6 border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-4">Product Selection</h3>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium mb-2">
                      Product <span className="text-red-500">*</span>
                    </label>
                    <Select
                      showSearch
                      placeholder="Search products..."
                      loading={isLoadingProducts}
                      onChange={(value) => {
                        const product = productsData?.data?.products.find(
                          (p) => p.id === value,
                        );
                        if (product) {
                          setSelectedProduct({
                            ...product,
                            price: String(product.price),
                          });
                        } else {
                          setSelectedProduct(null);
                        }
                      }}
                      onSearch={setSearchTerm}
                      filterOption={false}
                      size="large"
                      style={{ width: "100%" }}
                    >
                      {productsData?.data?.products?.map((product) => (
                        <Select.Option
                          key={product.id}
                          value={product.id}
                          disabled={product.stockQuantity <= 0}
                        >
                          {product.name} - GHS {Number(product.price).toFixed(2)}
                          {product.stockQuantity <= 0
                            ? " (Out of Stock)"
                            : ` (Stock: ${product.stockQuantity})`}
                        </Select.Option>
                      ))}
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Quantity <span className="text-red-500">*</span>
                    </label>
                    <InputNumber
                      min={1}
                      value={quantity}
                      onChange={(value) => setQuantity(value || 1)}
                      style={{ width: "100%" }}
                      size="large"
                    />
                  </div>
                </div>

                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddItem}
                  className="mt-4 w-full"
                  disabled={!selectedProduct}
                  size="large"
                >
                  Add to Cart
                </Button>
              </div>

              {/* Cart Items */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-4">
                  <ShoppingCartOutlined className="mr-2" />
                  Cart Items
                </h3>

                {items.length === 0 ? (
                  <div className="text-center py-8">
                    <Empty
                      description="No items in cart"
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                  </div>
                ) : (
                  <div className="space-y-2">
                    {items.map((item, index) => (
                      <div
                        key={`${item.productId}-${index}`}
                        className="flex items-center justify-between p-3 border border-gray-200 rounded"
                      >
                        <div className="flex-1">
                          <div className="font-medium">{item.productName}</div>
                          <div className="text-sm text-gray-500">
                            GHS {Number(item.price).toFixed(2)} x {item.quantity}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">
                            GHS {(Number(item.price) * item.quantity).toFixed(2)}
                          </span>
                          <Button
                            icon={<DeleteOutlined />}
                            onClick={() => handleRemoveItem(index)}
                            type="text"
                            danger
                            size="small"
                          />
                        </div>
                      </div>
                    ))}

                    <div className="border-t pt-3 mt-3">
                      <div className="flex justify-between items-center font-bold text-lg">
                        <span>Total:</span>
                        <span className="text-green-600">GHS {totalAmount.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Checkout Section */}
            <div>
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-4">Checkout</h3>

                {/* Order Summary */}
                <div className="mb-4 p-3 bg-gray-50 rounded">
                  <div className="flex justify-between mb-2">
                    <span>Items:</span>
                    <span>{items.length}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span>Total Quantity:</span>
                    <span>{items.reduce((sum, item) => sum + item.quantity, 0)}</span>
                  </div>
                  <div className="flex justify-between font-bold text-lg border-t pt-2">
                    <span>Total:</span>
                    <span className="text-green-600">GHS {totalAmount.toFixed(2)}</span>
                  </div>
                </div>

                {/* Store Info */}
                {selectedStore && (
                  <div className="mb-4 p-3 bg-blue-50 rounded">
                    <div className="flex items-center">
                      <ShopOutlined className="mr-2 text-blue-500" />
                      <span>{selectedStore.name}</span>
                    </div>
                  </div>
                )}

                {/* Payment Method */}
                <Form.Item
                  name="paymentMethod"
                  label="Payment Method"
                  rules={[
                    {
                      required: true,
                      message: "Please select a payment method",
                    },
                  ]}
                >
                  <Select size="large" placeholder="Select payment method">
                    <Select.Option value="cash">💵 Cash</Select.Option>
                    <Select.Option value="card">💳 Card</Select.Option>
                    <Select.Option value="mobile_money">📱 Mobile Money</Select.Option>
                  </Select>
                </Form.Item>

                {/* Action Buttons */}
                <div className="space-y-3">
                  {receiptPreviewVisible ? (
                    <Button
                      type="primary"
                      onClick={() => {
                        setReceiptPreviewVisible(false);
                        setReceiptUrl(null);
                        setHasPrinted(false);
                        form.resetFields();
                        setItems([]);
                        setSelectedProduct(null);
                        setQuantity(1);
                        setTotalAmount(0);
                      }}
                      className="w-full"
                      size="large"
                    >
                      Start New Sale
                    </Button>
                  ) : (
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={isSubmitting || isGeneratingReceipt}
                      disabled={items.length === 0}
                      className="w-full"
                      size="large"
                    >
                      {isGeneratingReceipt ? "Processing..." : "Complete Sale"}
                    </Button>
                  )}

                  <Button
                    onClick={onClose}
                    className="w-full"
                    size="large"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </Form>
      </div>

      {/* Receipt Preview Modal */}
      <Modal
        title="Receipt Preview"
        open={receiptPreviewVisible}
        onCancel={() => {
          setReceiptPreviewVisible(false);
          setReceiptUrl(null);
          setHasPrinted(false);
          form.resetFields();
          setItems([]);
          setSelectedProduct(null);
          setQuantity(1);
          setTotalAmount(0);
        }}
        width={500}
        centered
        footer={[
          <Button
            key="close"
            onClick={() => {
              setReceiptPreviewVisible(false);
              setReceiptUrl(null);
              setHasPrinted(false);
              form.resetFields();
              setItems([]);
              setSelectedProduct(null);
              setQuantity(1);
              setTotalAmount(0);
            }}
          >
            Close & New Sale
          </Button>,
          <Button
            key="print"
            type="primary"
            icon={<PrinterOutlined />}
            onClick={() => {
              if (hasPrinted) {
                setHasPrinted(false);
              }
              handlePrintReceipt();
            }}
          >
            {hasPrinted ? "Print Again" : "Print Receipt"}
          </Button>,
        ]}
      >
        <div className="flex flex-col items-center">
          {receiptUrl ? (
            <Image
              src={receiptUrl}
              alt="Receipt"
              style={{ maxWidth: "100%" }}
            />
          ) : (
            <div className="flex h-64 items-center justify-center">
              <Spin size="large" />
            </div>
          )}
        </div>
      </Modal>
    </SlidingPanel>
  );
};

export default SalesFormPanel;