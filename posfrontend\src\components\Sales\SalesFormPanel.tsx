"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Form,
  Button,
  Select,
  InputNumber,
  Empty,
  Spin,
  Modal,
  Image,
} from "antd";
import {
  DeleteOutlined,
  PlusOutlined,
  ShoppingCartOutlined,
  SearchOutlined,
  LoadingOutlined,
  ShopOutlined,
  PrinterOutlined,
  EyeOutlined,
  ReloadOutlined,
  DatabaseOutlined,
} from "@ant-design/icons";
import {
  useCreateSaleMutation,
  CreateSaleDto,
  CreateSaleItemDto,
} from "@/reduxRTK/services/salesApi";
import {
  useGetAllProductsQuery,
  Product,
} from "@/reduxRTK/services/productApi";
import {
  useGetUserStoresQuery,
  useGetUserDefaultStoreQuery,
} from "@/reduxRTK/services/userStoreApi";
import SlidingPanel from "@/components/ui/SlidingPanel";
import { showMessage } from "@/utils/showMessage";
import {
  generateReceiptHTML,
  generateReceiptImage,
} from "@/utils/cloudinaryUtils";
import { Store } from "@/types/store";
import "./sales-panels.css";

interface SalesFormPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const SalesFormPanel: React.FC<SalesFormPanelProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [items, setItems] = useState<
    (CreateSaleItemDto & { productName: string })[]
  >([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [quantity, setQuantity] = useState<number>(1);
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStore, setSelectedStore] = useState<Store | null>(null);
  const [isGeneratingReceipt, setIsGeneratingReceipt] = useState(false);
  const [receiptPreviewVisible, setReceiptPreviewVisible] = useState(false);
  const [receiptUrl, setReceiptUrl] = useState<string | null>(null);
  const [hasPrinted, setHasPrinted] = useState(false);

  // Debug state changes
  useEffect(() => {
    console.log("Items state changed:", items);
  }, [items]);


  const {
    data: productsData,
    isLoading: isLoadingProducts,
    refetch: refetchProducts,
    error: productsError,
    isFetching: isFetchingProducts,
  } = useGetAllProductsQuery(
    {
      page: 1,
      limit: 1000, // Increased limit to get more products for sales
      search: searchTerm,
    },
    {
      // Always fetch fresh data from database
      refetchOnMountOrArgChange: true,
      refetchOnFocus: true, // Refetch when window gains focus
      refetchOnReconnect: true, // Refetch when reconnecting
      // Skip caching entirely for sales form to ensure fresh stock data
      skip: false,
    },
  );

  // Enhanced products data monitoring and error handling
  useEffect(() => {
    if (productsData) {
      console.log("🛒 Fresh products loaded from database:", {
        total: productsData.data?.total || 0,
        productsCount: productsData.data?.products?.length || 0,
        isLoading: isLoadingProducts,
        isFetching: isFetchingProducts,
        timestamp: new Date().toISOString(),
      });
    }

    if (productsError) {
      console.error("❌ Error loading products:", productsError);
      showMessage("error", "Failed to load products. Please try again.");
    }
  }, [productsData, isLoadingProducts, isFetchingProducts, productsError]);

  // Get current user ID from auth state
  const getCurrentUserId = () => {
    if (typeof window !== "undefined") {
      // @ts-ignore - Redux state is exposed for debugging
      const state = window.__REDUX_STATE;
      return state?.auth?.user?.id || 0;
    }
    return 0;
  };

  // Fetch user stores
  const { data: userStoresData } = useGetUserStoresQuery(getCurrentUserId());

  // Fetch default store
  const { data: defaultStoreData } =
    useGetUserDefaultStoreQuery(getCurrentUserId());

  // Set default store when data is loaded
  useEffect(() => {
    if (defaultStoreData?.data) {
      setSelectedStore(defaultStoreData.data);
      form.setFieldsValue({ storeId: defaultStoreData.data.id });
    } else if (userStoresData?.data && userStoresData.data.length > 0) {
      setSelectedStore(userStoresData.data[0]);
      form.setFieldsValue({ storeId: userStoresData.data[0].id });
    }
  }, [defaultStoreData, userStoresData, form]);

  // Create sale mutation
  const [createSale, { isLoading: isSubmitting }] = useCreateSaleMutation();

  // Calculate total amount whenever items change
  useEffect(() => {
    if (items && items.length > 0) {
      const total = items.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0,
      );
      setTotalAmount(total);
      if (form) {
        form.setFieldsValue({ totalAmount: total });
      }

      // Debug log to check items state
      console.log("Current items in useEffect:", items);
    } else {
      setTotalAmount(0);
      if (form) {
        form.setFieldsValue({ totalAmount: 0 });
      }
    }
  }, [items, form]);

  // Enhanced panel open/close handling with forced data refresh
  useEffect(() => {
    if (isOpen) {
      // When panel opens, ALWAYS fetch fresh product data from database
      console.log("🛒 Sales panel opened - forcing fresh product data fetch from database");

      // Force refetch to ensure we get the latest stock quantities
      refetchProducts()
        .then((result) => {
          if (result.data) {
            console.log("✅ Fresh product data successfully loaded:", {
              productsCount: result.data.data?.products?.length || 0,
              timestamp: new Date().toISOString(),
            });
          }
        })
        .catch((error) => {
          console.error("❌ Failed to fetch fresh product data:", error);
          showMessage("error", "Failed to load current product data. Stock quantities may not be accurate.");
        });
    } else {
      // Reset form when panel is closed
      console.log("🛒 Sales panel closed - resetting form state");
      if (form) {
        form.resetFields();
      }
      setItems([]);
      setSelectedProduct(null);
      setQuantity(1);
      setTotalAmount(0);
      setReceiptUrl(null);
      setReceiptPreviewVisible(false);
      setHasPrinted(false);
      setSearchTerm(""); // Reset search term to ensure fresh data on next open
    }
  }, [isOpen, form, refetchProducts]);

  // Handle adding an item to the sale
  const handleAddItem = () => {
    if (!selectedProduct) {
      showMessage("error", "Please select a product");
      return;
    }

    if (quantity <= 0) {
      showMessage("error", "Quantity must be greater than 0");
      return;
    }

    if (selectedProduct.stockQuantity < quantity) {
      showMessage(
        "error",
        `Only ${selectedProduct.stockQuantity} units available in stock`,
      );
      return;
    }

    console.log("Adding item with product:", selectedProduct);

    // Check if product already exists in items
    const existingItemIndex = items.findIndex(
      (item) => item.productId === selectedProduct.id,
    );

    if (existingItemIndex >= 0) {
      // Update existing item
      const updatedItems = [...items];
      const newQuantity = updatedItems[existingItemIndex].quantity + quantity;

      if (newQuantity > selectedProduct.stockQuantity) {
        showMessage(
          "error",
          `Cannot add more than ${selectedProduct.stockQuantity} units of this product`,
        );
        return;
      }

      updatedItems[existingItemIndex].quantity = newQuantity;

      // Make sure the product name is set
      if (!updatedItems[existingItemIndex].productName) {
        updatedItems[existingItemIndex].productName = selectedProduct.name;
      }

      // Update the state with the new array
      console.log("Updating existing item. New items array:", updatedItems);
      setItems([...updatedItems]); // Create a new array reference to force re-render

      // Show success message
      showMessage("success", `Updated quantity of ${selectedProduct.name}`);
    } else {
      // Add new item
      const newItem = {
        productId: selectedProduct.id,
        productName: selectedProduct.name, // Make sure this is correctly set
        quantity,
        price:
          typeof selectedProduct.price === "string"
            ? parseFloat(selectedProduct.price)
            : selectedProduct.price,
      };

      // Create a new array with the new item
      const newItems = [...items, newItem];

      // Update the state with the new array
      console.log("Adding new item. New items array:", newItems);
      setItems(newItems); // This should trigger a re-render

      // Show success message
      showMessage(
        "success",
        `Added ${quantity} ${selectedProduct.name} to sale`,
      );
    }

    // Reset selection
    setSelectedProduct(null);
    setQuantity(1);
    if (form) {
      form.setFieldsValue({ productId: undefined, quantity: 1 });
    }
  };

  // Handle removing an item from the sale
  const handleRemoveItem = (index: number) => {
    const updatedItems = [...items];
    updatedItems.splice(index, 1);
    setItems(updatedItems);
  };

  // Handle printing receipt - directly trigger print dialog
  const handlePrintReceipt = useCallback(() => {

    if (!receiptUrl || hasPrinted) {
      console.log(
        "Skipping print: ",
        !receiptUrl ? "No receipt URL" : "Already printed",
      );
      return;
    }

    console.log("Printing receipt:", receiptUrl);

    // Mark as printed immediately to prevent multiple print dialogs
    setHasPrinted(true);

    // Create a hidden iframe to load the image
    const iframe = document.createElement("iframe");
    iframe.style.display = "none";
    document.body.appendChild(iframe);

    // Set up the iframe content with the image and print CSS
    iframe.onload = () => {
      if (iframe.contentWindow) {
        // Write the HTML content to the iframe
        iframe.contentWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Print Receipt</title>
              <style>
                body {
                  margin: 0;
                  padding: 0;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  height: 100vh;
                }
                img {
                  max-width: 100%;
                  max-height: 100vh;
                }
                @media print {
                  body {
                    margin: 0;
                    padding: 0;
                  }
                  img {
                    width: 100%;
                    height: auto;
                  }
                }
              </style>
            </head>
            <body>
              <img src="${receiptUrl}" alt="Receipt" />
            </body>
          </html>
        `);

        // Close the document
        iframe.contentWindow.document.close();

        // Use a single print trigger with a delay to ensure the image is loaded
        setTimeout(() => {
          if (iframe.contentWindow) {
            try {
              // Print the iframe content
              iframe.contentWindow.focus();
              iframe.contentWindow.print();
            } catch (e) {
              console.error("Error printing receipt:", e);
            }

            // Remove the iframe after printing
            setTimeout(() => {
              document.body.removeChild(iframe);
            }, 1000);
          }
        }, 500);
      }
    };

    // Set the iframe source to trigger the onload event
    iframe.src = "about:blank";
  }, [receiptUrl, hasPrinted]);

  // Effect to automatically print receipt when modal is shown
  useEffect(() => {
    if (receiptPreviewVisible && receiptUrl && !hasPrinted) {
      // Add a small delay to ensure the receipt image is loaded
      const timer = setTimeout(() => {
        handlePrintReceipt(); // This now handles the hasPrinted state internally
      }, 800);

      return () => clearTimeout(timer);
    }
  }, [receiptPreviewVisible, receiptUrl, hasPrinted, handlePrintReceipt]);

  // Handle form submission
  const handleSubmit = async () => {
    try {
      if (items.length === 0) {
        showMessage("error", "Please add at least one item to the sale");
        return;
      }

      // Validate form fields
      const values = await form.validateFields();

      // Check if store is selected
      if (!selectedStore) {
        showMessage(
          "error",
          "No store information available. Please set up your store in your profile settings.",
        );
        return;
      }

      // Set loading state for receipt generation
      setIsGeneratingReceipt(true);

      // Get store information for receipt
      const storeInfo = selectedStore ||
        userStoresData?.data?.find((store) => store.id === values.storeId) || {
          name: "POS System",
        };

      // Generate receipt HTML
      const receiptHTML = generateReceiptHTML(
        {
          id: Date.now(), // Temporary ID until we get the real one
          totalAmount,
          paymentMethod: values.paymentMethod,
          transactionDate: new Date().toISOString(),
          items: items.map((item) => ({
            productName: item.productName,
            quantity: item.quantity,
            price: item.price,
          })),
        },
        storeInfo,
      );

      // Generate receipt image and get URL
      let receiptUrl = "https://receipt.example.com/placeholder";
      try {
        receiptUrl = await generateReceiptImage(receiptHTML);
      } catch (error) {
        console.error("Failed to generate receipt image:", error);
        // Continue with placeholder URL if image generation fails
      }

      const saleData: CreateSaleDto = {
        totalAmount,
        paymentMethod: values.paymentMethod,
        items: items.map((item) => ({
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
        })),
        receiptUrl,
        storeId: selectedStore?.id,
      };

      const response = await createSale(saleData).unwrap();

      if (response.success) {
        showMessage("success", "Sale created successfully");

        // Store the receipt URL for preview
        setReceiptUrl(receiptUrl);

        // Show receipt preview modal and offer print option
        setReceiptPreviewVisible(true);



        // Force refresh product data to get updated stock quantities after sale
        console.log("🔄 Sale completed - refreshing product data to update stock quantities");
        refetchProducts()
          .then((result) => {
            if (result.data) {
              console.log("✅ Product data refreshed after sale - stock quantities updated");
            }
          })
          .catch((error) => {
            console.error("❌ Failed to refresh product data after sale:", error);
          });

        // Trigger the success callback to refresh the sales list
        setTimeout(() => {
          if (onSuccess) {
            console.log("📊 Triggering sales list refresh");
            onSuccess();
          }
        }, 300);

        // Keep the panel open until the user explicitly closes it
        // This ensures the receipt modal stays visible
      } else {
        showMessage("error", response.message || "Failed to create sale");
      }
    } catch (error: any) {
      showMessage(
        "error",
        error.data?.message || "An error occurred while creating the sale",
      );
    } finally {
      setIsGeneratingReceipt(false);
    }
  };

  // Debug log to check items state when rendering
  console.log("Rendering with items:", items);

  return (
    <SlidingPanel
      title="Point of Sale System"
      isOpen={isOpen}
      onClose={onClose}
      width="98%"
    >
      <div className="sales-form min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        {/* Enhanced POS Header */}
        <div className="sticky top-[-10] z-20 border-b border-gray-200 bg-white px-6 py-4 shadow-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg">
                <ShoppingCartOutlined className="text-xl text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">
                  Point of Sale
                </h2>
                <p className="text-sm text-gray-500">
                  {selectedStore?.name || 'NEXAPO POS System'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-6">
              <div className="text-right">
                <p className="text-sm text-gray-500">Transaction Total</p>
                <p className="text-3xl font-bold text-green-600">
                  GHS {totalAmount.toFixed(2)}
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-500">Items</p>
                <p className="text-xl font-semibold text-gray-700">
                  {items.length}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 gap-6 xl:grid-cols-3">
            {/* Left Column - Product Selection */}
            <div className="xl:col-span-2">
              <div className="mb-6 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-xl">
                <div className="border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-500">
                        <SearchOutlined className="text-sm text-white" />
                      </div>
                      <h3 className="text-lg font-bold text-gray-800">
                        Product Selection
                      </h3>
                      {(isLoadingProducts || isFetchingProducts) && (
                        <div className="flex items-center space-x-2 rounded-full bg-blue-100 px-3 py-1">
                          <LoadingOutlined className="text-blue-600" />
                          <span className="text-xs text-blue-600">Loading fresh data...</span>
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-3">
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={() => {
                          console.log("🔄 Manual refresh triggered");
                          refetchProducts();
                          showMessage("warning", "Refreshing product data...");
                        }}
                        loading={isLoadingProducts || isFetchingProducts}
                        size="small"
                        className="border-blue-300 text-blue-600 hover:bg-blue-50"
                        title="Refresh product data from database"
                      >
                        Refresh
                      </Button>
                      <div className="rounded-full bg-white px-3 py-1 text-xs text-gray-600 shadow-sm">
                        <span className="mr-1 text-red-500">*</span> Required fields
                      </div>
                    </div>
                  </div>
                  {productsData?.data?.products && (
                    <div className="mt-2 flex items-center space-x-2 text-xs text-gray-600">
                      <DatabaseOutlined />
                      <span>
                        {productsData.data.products.length} products loaded from database
                      </span>
                      <span className="text-green-600">
                        • Last updated: {new Date().toLocaleTimeString()}
                      </span>
                    </div>
                  )}
                </div>
                <div className="p-6">

                  <Form
                    form={form}
                    layout="vertical"
                    initialValues={{
                      paymentMethod: "cash",
                      quantity: 1,
                    }}
                    className="product-form"
                  >
                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                      <div className="lg:col-span-2">
                        <Form.Item
                          name="productId"
                          label={
                            <span className="flex items-center text-sm font-semibold text-gray-700">
                              <ShoppingCartOutlined className="mr-2 text-blue-500" />
                              Select Product <span className="ml-1 text-red-500">*</span>
                            </span>
                          }
                          className="mb-4"
                        >
                          <Select
                            showSearch
                            placeholder={
                              isLoadingProducts || isFetchingProducts
                                ? "🔄 Loading fresh product data from database..."
                                : productsError
                                ? "❌ Error loading products - click refresh"
                                : "🔍 Search and select a product..."
                            }
                            optionFilterProp="children"
                            loading={isLoadingProducts || isFetchingProducts}
                            disabled={isLoadingProducts || isFetchingProducts}
                            onChange={(value) => {
                              const product = productsData?.data?.products.find(
                                (p) => p.id === value,
                              );
                              console.log("Selected product from fresh database data:", product);
                              if (product) {
                                // Make a deep copy to avoid reference issues
                                setSelectedProduct({
                                  ...product,
                                  // Ensure price is properly formatted
                                  price:
                                    typeof product.price === "string"
                                      ? product.price
                                      : String(product.price),
                                });
                                console.log("✅ Product selected with current stock:", product.stockQuantity);
                              } else {
                                setSelectedProduct(null);
                              }
                            }}
                            onSearch={(value) => {
                              setSearchTerm(value);
                              // Trigger a fresh search from database
                              if (value.length > 2) {
                                console.log("🔍 Searching products in database:", value);
                              }
                            }}
                            filterOption={false}
                            className="rounded-lg border-gray-300 text-gray-800 shadow-sm"
                            size="large"
                            suffixIcon={
                              isLoadingProducts || isFetchingProducts ? (
                                <LoadingOutlined spin className="text-blue-500" />
                              ) : productsError ? (
                                <span className="text-red-500">❌</span>
                              ) : (
                                <SearchOutlined className="text-gray-400" />
                              )
                            }
                            notFoundContent={
                              isLoadingProducts || isFetchingProducts ? (
                                <div className="flex items-center justify-center py-6">
                                  <Spin size="small" />
                                  <span className="ml-2 text-gray-500">Loading fresh data from database...</span>
                                </div>
                              ) : productsError ? (
                                <div className="py-6 text-center">
                                  <div className="text-red-500">❌ Failed to load products</div>
                                  <Button
                                    size="small"
                                    onClick={() => refetchProducts()}
                                    className="mt-2"
                                  >
                                    Try Again
                                  </Button>
                                </div>
                              ) : (
                                <div className="py-4 text-center text-gray-500">
                                  No products found in database
                                </div>
                              )
                            }
                          >
                            {productsData?.data?.products?.map((product) => (
                              <Select.Option
                                key={product.id}
                                value={product.id}
                                disabled={product.stockQuantity <= 0}
                              >
                                <div className="flex items-center justify-between py-1">
                                  <div className="flex-1">
                                    <div className="font-medium text-gray-800">
                                      {product.name}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                      GHS {Number(product.price).toFixed(2)}
                                    </div>
                                  </div>
                                  <div className="text-right">
                                    {product.stockQuantity <= 0 ? (
                                      <span className="rounded-full bg-red-100 px-2 py-1 text-xs font-medium text-red-600">
                                        Out of Stock
                                      </span>
                                    ) : (
                                      <span className="rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-600">
                                        Stock: {product.stockQuantity}
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </Select.Option>
                            ))}
                          </Select>
                        </Form.Item>
                      </div>

                      <div>
                        <Form.Item
                          name="quantity"
                          label={
                            <span className="flex items-center text-sm font-semibold text-gray-700">
                              <span className="mr-2">📦</span>
                              Quantity <span className="ml-1 text-red-500">*</span>
                            </span>
                          }
                          className="mb-4"
                        >
                          <InputNumber
                            min={1}
                            max={selectedProduct?.stockQuantity || 999}
                            value={quantity}
                            onChange={(value) => setQuantity(value || 1)}
                            style={{ width: "100%" }}
                            className="rounded-lg border-gray-300 text-gray-800 shadow-sm"
                            size="large"
                            placeholder="Enter quantity"
                          />
                        </Form.Item>
                      </div>
                    </div>

                    {/* Selected Product Preview */}
                    {selectedProduct && (
                      <div className="mt-4 rounded-lg border border-blue-200 bg-blue-50 p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-semibold text-gray-800">
                              {selectedProduct.name}
                            </h4>
                            <p className="text-sm text-gray-600">
                              Price: GHS {Number(selectedProduct.price).toFixed(2)} |
                              Available: {selectedProduct.stockQuantity} units
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm text-gray-600">Subtotal</p>
                            <p className="text-lg font-bold text-green-600">
                              GHS {(Number(selectedProduct.price) * quantity).toFixed(2)}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}

                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={handleAddItem}
                      className="mt-6 h-14 w-full rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 text-lg font-semibold shadow-lg hover:from-blue-600 hover:to-blue-700 hover:shadow-xl"
                      disabled={!selectedProduct}
                      size="large"
                    >
                      🛒 Add to Cart
                    </Button>
                  </Form>
                </div>
              </div>

              {/* Enhanced Cart Items */}
              <div className="mb-6 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-xl">
                <div className="border-b border-gray-100 bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-green-500">
                        <ShoppingCartOutlined className="text-sm text-white" />
                      </div>
                      <h3 className="text-lg font-bold text-gray-800">
                        Shopping Cart
                      </h3>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className="rounded-full bg-white px-3 py-1 text-sm font-medium text-gray-600 shadow-sm">
                        {items.length} {items.length === 1 ? 'item' : 'items'}
                      </span>
                      <span className="rounded-full bg-green-100 px-3 py-1 text-sm font-bold text-green-700">
                        GHS {totalAmount.toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="max-h-[400px] overflow-x-auto overflow-y-auto">
                  {items.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-16">
                      <div className="mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-gray-100">
                        <ShoppingCartOutlined className="text-3xl text-gray-400" />
                      </div>
                      <h4 className="mb-2 text-lg font-semibold text-gray-600">
                        Your cart is empty
                      </h4>
                      <p className="text-sm text-gray-500">
                        Add products to start a new transaction
                      </p>
                    </div>
                  ) : (
                    <div className="divide-y divide-gray-100">
                      {/* Cart Header */}
                      <div className="grid grid-cols-12 gap-4 bg-gray-50 px-6 py-3 text-sm font-semibold text-gray-700">
                        <div className="col-span-5">Product</div>
                        <div className="col-span-2 text-center">Qty</div>
                        <div className="col-span-2 text-right">Price</div>
                        <div className="col-span-2 text-right">Subtotal</div>
                        <div className="col-span-1 text-center">Action</div>
                      </div>
                      {/* Cart Items */}
                      {items.map((item, index) => (
                        <div
                          key={`${item.productId}-${index}`}
                          className="grid grid-cols-12 gap-4 px-6 py-4 transition-colors hover:bg-gray-50"
                        >
                          <div className="col-span-5 flex items-center">
                            <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
                              <ShoppingCartOutlined className="text-blue-600" />
                            </div>
                            <div>
                              <p className="font-semibold text-gray-800">
                                {item.productName || "Unknown Product"}
                              </p>
                              <p className="text-sm text-gray-500">
                                Item #{index + 1}
                              </p>
                            </div>
                          </div>
                          <div className="col-span-2 flex items-center justify-center">
                            <span className="rounded-full bg-blue-100 px-3 py-1 text-sm font-semibold text-blue-700">
                              {item.quantity}
                            </span>
                          </div>
                          <div className="col-span-2 flex items-center justify-end">
                            <span className="text-gray-800">
                              GHS {Number(item.price).toFixed(2)}
                            </span>
                          </div>
                          <div className="col-span-2 flex items-center justify-end">
                            <span className="font-bold text-green-600">
                              GHS {(Number(item.price) * item.quantity).toFixed(2)}
                            </span>
                          </div>
                          <div className="col-span-1 flex items-center justify-center">
                            <Button
                              icon={<DeleteOutlined />}
                              onClick={() => handleRemoveItem(index)}
                              type="text"
                              danger
                              className="rounded-full text-red-500 hover:bg-red-50 hover:text-red-600"
                              size="small"
                            />
                          </div>
                        </div>
                      ))}
                      {/* Cart Total */}
                      <div className="border-t border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4">
                        <div className="flex items-center justify-between">
                          <div className="text-lg font-semibold text-gray-800">
                            Cart Total
                          </div>
                          <div className="text-2xl font-bold text-green-600">
                            GHS {totalAmount.toFixed(2)}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Right Column - Enhanced Checkout */}
            <div>
              <div className="sticky top-24 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-2xl">
                <div className="border-b border-gray-100 bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-purple-500">
                      <ShoppingCartOutlined className="text-sm text-white" />
                    </div>
                    <h3 className="text-lg font-bold text-gray-800">
                      Checkout
                    </h3>
                  </div>
                </div>
                <div className="p-6">

                  <Form form={form} layout="vertical">
                    {/* Enhanced Order Summary */}
                    <div className="mb-6 overflow-hidden rounded-lg border border-gray-200 bg-gradient-to-br from-gray-50 to-gray-100 shadow-inner">
                      <div className="border-b border-gray-200 bg-white px-4 py-3">
                        <h4 className="font-semibold text-gray-800">Order Summary</h4>
                      </div>
                      <div className="p-4 space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="flex items-center text-gray-600">
                            <span className="mr-2">📦</span> Items
                          </span>
                          <span className="rounded-full bg-blue-100 px-2 py-1 text-sm font-semibold text-blue-700">
                            {items.length}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="flex items-center text-gray-600">
                            <span className="mr-2">🔢</span> Total Quantity
                          </span>
                          <span className="font-medium text-gray-800">
                            {items.reduce((sum, item) => sum + item.quantity, 0)}
                          </span>
                        </div>
                        <div className="flex items-center justify-between border-t border-gray-300 pt-3">
                          <span className="flex items-center text-lg font-semibold text-gray-800">
                            <span className="mr-2">💰</span> Total Amount
                          </span>
                          <span className="text-2xl font-bold text-green-600">
                            GHS {totalAmount.toFixed(2)}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Store Information */}
                    {selectedStore ? (
                      <div className="mb-6">
                        <label className="mb-3 block text-sm font-semibold text-gray-700">
                          Store Information
                        </label>
                        <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                          <div className="flex items-center">
                            <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-green-500">
                              <ShopOutlined className="text-white" />
                            </div>
                            <div>
                              <p className="font-semibold text-gray-800">
                                {selectedStore.name}
                              </p>
                              <p className="text-sm text-gray-600">
                                Active Store
                              </p>
                            </div>
                          </div>
                          <input
                            type="hidden"
                            name="storeId"
                            value={selectedStore.id}
                          />
                        </div>
                      </div>
                    ) : (
                      <div className="mb-6 rounded-lg border border-orange-200 bg-orange-50 p-4">
                        <div className="flex items-center">
                          <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-orange-500">
                            <ShopOutlined className="text-white" />
                          </div>
                          <div>
                            <p className="font-semibold text-orange-800">
                              No Store Selected
                            </p>
                            <p className="text-sm text-orange-600">
                              Please set up your store in profile settings
                            </p>
                          </div>
                        </div>
                      </div>
                    )}

                    <Form.Item
                      name="paymentMethod"
                      label={
                        <span className="flex items-center text-sm font-semibold text-gray-700">
                          <span className="mr-2">💳</span>
                          Payment Method <span className="ml-1 text-red-500">*</span>
                        </span>
                      }
                      rules={[
                        {
                          required: true,
                          message: "Please select a payment method",
                        },
                      ]}
                      initialValue="cash"
                    >
                      <Select
                        className="rounded-lg border-gray-300 text-gray-800 shadow-sm"
                        size="large"
                        placeholder="Select payment method"
                      >
                        <Select.Option value="cash">
                          <div className="flex items-center justify-between py-2">
                            <div className="flex items-center">
                              <span className="mr-3 text-lg">💵</span>
                              <div>
                                <div className="font-medium">Cash Payment</div>
                                <div className="text-xs text-gray-500">Physical cash transaction</div>
                              </div>
                            </div>
                            <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-600">
                              Instant
                            </span>
                          </div>
                        </Select.Option>
                        <Select.Option value="card">
                          <div className="flex items-center justify-between py-2">
                            <div className="flex items-center">
                              <span className="mr-3 text-lg">💳</span>
                              <div>
                                <div className="font-medium">Card Payment</div>
                                <div className="text-xs text-gray-500">Credit/Debit card</div>
                              </div>
                            </div>
                            <span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-600">
                              Secure
                            </span>
                          </div>
                        </Select.Option>
                        <Select.Option value="mobile_money">
                          <div className="flex items-center justify-between py-2">
                            <div className="flex items-center">
                              <span className="mr-3 text-lg">📱</span>
                              <div>
                                <div className="font-medium">Mobile Money</div>
                                <div className="text-xs text-gray-500">MTN, Vodafone, AirtelTigo</div>
                              </div>
                            </div>
                            <span className="rounded-full bg-purple-100 px-2 py-1 text-xs text-purple-600">
                              Popular
                            </span>
                          </div>
                        </Select.Option>
                      </Select>
                    </Form.Item>

                    <div className="mt-8 space-y-4">
                      {receiptPreviewVisible ? (
                        // Enhanced "New Sale" button when receipt is visible
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => {
                            // Close the modal and reset the receipt state
                            setReceiptPreviewVisible(false);
                            setReceiptUrl(null);
                            setHasPrinted(false);

                            // Reset the form to start a new sale
                            form.resetFields();
                            setItems([]);
                            setSelectedProduct(null);
                            setQuantity(1);
                            setTotalAmount(0);
                          }}
                          className="h-16 w-full rounded-lg bg-gradient-to-r from-green-500 to-green-600 text-lg font-bold shadow-lg hover:from-green-600 hover:to-green-700 hover:shadow-xl"
                          size="large"
                        >
                          🛒 Start New Sale
                        </Button>
                      ) : (
                        // Enhanced "Complete Sale" button
                        <Button
                          type="primary"
                          icon={<ShoppingCartOutlined />}
                          onClick={handleSubmit}
                          loading={isSubmitting || isGeneratingReceipt}
                          disabled={items.length === 0}
                          className="h-16 w-full rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 text-lg font-bold shadow-lg hover:from-blue-600 hover:to-blue-700 hover:shadow-xl disabled:from-gray-400 disabled:to-gray-500"
                          size="large"
                        >
                          {isGeneratingReceipt ? (
                            <span className="flex items-center">
                              <LoadingOutlined className="mr-2" />
                              Generating Receipt...
                            </span>
                          ) : (
                            <span className="flex items-center">
                              💳 Complete Sale - GHS {totalAmount.toFixed(2)}
                            </span>
                          )}
                        </Button>
                      )}

                      <Button
                        onClick={onClose}
                        className="h-12 w-full rounded-lg border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400"
                        size="large"
                      >
                        ❌ Cancel Transaction
                      </Button>
                    </div>
                  </Form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Receipt Preview Modal */}
      <Modal
        title={
          <div className="flex items-center text-gray-800">
            <PrinterOutlined className="mr-2" />
            <span>Receipt Preview</span>
          </div>
        }
        open={receiptPreviewVisible}
        onCancel={() => {
          // Close the modal and reset the receipt state
          setReceiptPreviewVisible(false);
          setReceiptUrl(null);
          setHasPrinted(false);

          // Reset the form to start a new sale
          form.resetFields();
          setItems([]);
          setSelectedProduct(null);
          setQuantity(1);
          setTotalAmount(0);
        }}
        width={500}
        centered
        className="receipt-preview-modal"
        footer={[
          <Button
            key="close"
            onClick={() => {
              // Close the modal and reset the receipt state
              setReceiptPreviewVisible(false);
              setReceiptUrl(null);
              setHasPrinted(false);

              // Reset the form to start a new sale
              form.resetFields();
              setItems([]);
              setSelectedProduct(null);
              setQuantity(1);
              setTotalAmount(0);
            }}
            className="border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200"
          >
            Close & New Sale
          </Button>,
          <Button
            key="print"
            type="primary"
            icon={<PrinterOutlined />}
            onClick={() => {
              // If already printed once, reset the flag to allow printing again
              if (hasPrinted) {
                setHasPrinted(false);
              }
              handlePrintReceipt();
            }}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {hasPrinted ? "Print Again" : "Print Receipt"}
          </Button>,
        ]}
      >
        <div className="flex flex-col items-center">
          {receiptUrl ? (
            <div className="receipt-image-container">
              <Image
                src={receiptUrl}
                alt="Receipt"
                className="receipt-image"
                style={{ maxWidth: "100%" }}
              />
            </div>
          ) : (
            <div className="flex h-64 items-center justify-center">
              <Spin size="large" />
            </div>
          )}
        </div>
      </Modal>
    </SlidingPanel>
  );
};

export default SalesFormPanel;
