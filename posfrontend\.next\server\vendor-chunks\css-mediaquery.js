"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/css-mediaquery";
exports.ids = ["vendor-chunks/css-mediaquery"];
exports.modules = {

/***/ "(ssr)/./node_modules/css-mediaquery/index.js":
/*!**********************************************!*\
  !*** ./node_modules/css-mediaquery/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\n\n\n\nexports.match = matchQuery;\nexports.parse = parseQuery;\n\n// -----------------------------------------------------------------------------\n\nvar RE_MEDIA_QUERY     = /(?:(only|not)?\\s*([^\\s\\(\\)]+)(?:\\s*and)?\\s*)?(.+)?/i,\n    RE_MQ_EXPRESSION   = /\\(\\s*([^\\s\\:\\)]+)\\s*(?:\\:\\s*([^\\s\\)]+))?\\s*\\)/,\n    RE_MQ_FEATURE      = /^(?:(min|max)-)?(.+)/,\n    RE_LENGTH_UNIT     = /(em|rem|px|cm|mm|in|pt|pc)?$/,\n    RE_RESOLUTION_UNIT = /(dpi|dpcm|dppx)?$/;\n\nfunction matchQuery(mediaQuery, values) {\n    return parseQuery(mediaQuery).some(function (query) {\n        var inverse = query.inverse;\n\n        // Either the parsed or specified `type` is \"all\", or the types must be\n        // equal for a match.\n        var typeMatch = query.type === 'all' || values.type === query.type;\n\n        // Quit early when `type` doesn't match, but take \"not\" into account.\n        if ((typeMatch && inverse) || !(typeMatch || inverse)) {\n            return false;\n        }\n\n        var expressionsMatch = query.expressions.every(function (expression) {\n            var feature  = expression.feature,\n                modifier = expression.modifier,\n                expValue = expression.value,\n                value    = values[feature];\n\n            // Missing or falsy values don't match.\n            if (!value) { return false; }\n\n            switch (feature) {\n                case 'orientation':\n                case 'scan':\n                    return value.toLowerCase() === expValue.toLowerCase();\n\n                case 'width':\n                case 'height':\n                case 'device-width':\n                case 'device-height':\n                    expValue = toPx(expValue);\n                    value    = toPx(value);\n                    break;\n\n                case 'resolution':\n                    expValue = toDpi(expValue);\n                    value    = toDpi(value);\n                    break;\n\n                case 'aspect-ratio':\n                case 'device-aspect-ratio':\n                case /* Deprecated */ 'device-pixel-ratio':\n                    expValue = toDecimal(expValue);\n                    value    = toDecimal(value);\n                    break;\n\n                case 'grid':\n                case 'color':\n                case 'color-index':\n                case 'monochrome':\n                    expValue = parseInt(expValue, 10) || 1;\n                    value    = parseInt(value, 10) || 0;\n                    break;\n            }\n\n            switch (modifier) {\n                case 'min': return value >= expValue;\n                case 'max': return value <= expValue;\n                default   : return value === expValue;\n            }\n        });\n\n        return (expressionsMatch && !inverse) || (!expressionsMatch && inverse);\n    });\n}\n\nfunction parseQuery(mediaQuery) {\n    return mediaQuery.split(',').map(function (query) {\n        query = query.trim();\n\n        var captures    = query.match(RE_MEDIA_QUERY),\n            modifier    = captures[1],\n            type        = captures[2],\n            expressions = captures[3] || '',\n            parsed      = {};\n\n        parsed.inverse = !!modifier && modifier.toLowerCase() === 'not';\n        parsed.type    = type ? type.toLowerCase() : 'all';\n\n        // Split expressions into a list.\n        expressions = expressions.match(/\\([^\\)]+\\)/g) || [];\n\n        parsed.expressions = expressions.map(function (expression) {\n            var captures = expression.match(RE_MQ_EXPRESSION),\n                feature  = captures[1].toLowerCase().match(RE_MQ_FEATURE);\n\n            return {\n                modifier: feature[1],\n                feature : feature[2],\n                value   : captures[2]\n            };\n        });\n\n        return parsed;\n    });\n}\n\n// -- Utilities ----------------------------------------------------------------\n\nfunction toDecimal(ratio) {\n    var decimal = Number(ratio),\n        numbers;\n\n    if (!decimal) {\n        numbers = ratio.match(/^(\\d+)\\s*\\/\\s*(\\d+)$/);\n        decimal = numbers[1] / numbers[2];\n    }\n\n    return decimal;\n}\n\nfunction toDpi(resolution) {\n    var value = parseFloat(resolution),\n        units = String(resolution).match(RE_RESOLUTION_UNIT)[1];\n\n    switch (units) {\n        case 'dpcm': return value / 2.54;\n        case 'dppx': return value * 96;\n        default    : return value;\n    }\n}\n\nfunction toPx(length) {\n    var value = parseFloat(length),\n        units = String(length).match(RE_LENGTH_UNIT)[1];\n\n    switch (units) {\n        case 'em' : return value * 16;\n        case 'rem': return value * 16;\n        case 'cm' : return value * 96 / 2.54;\n        case 'mm' : return value * 96 / 2.54 / 10;\n        case 'in' : return value * 96;\n        case 'pt' : return value * 72;\n        case 'pc' : return value * 72 / 12;\n        default   : return value;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-mediaquery/index.js\n");

/***/ })

};
;