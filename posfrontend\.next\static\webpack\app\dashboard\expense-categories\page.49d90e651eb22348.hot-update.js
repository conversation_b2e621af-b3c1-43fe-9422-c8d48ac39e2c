"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/expense-categories/page",{

/***/ "(app-pages-browser)/./src/components/Expenses/ExpenseCategoryTable.tsx":
/*!**********************************************************!*\
  !*** ./src/components/Expenses/ExpenseCategoryTable.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/checkbox/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CrownOutlined,DeleteFilled,DeleteOutlined,EditOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteFilled.js\");\n/* harmony import */ var _barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CrownOutlined,DeleteFilled,DeleteOutlined,EditOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CrownOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CrownOutlined,DeleteFilled,DeleteOutlined,EditOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CrownOutlined,DeleteFilled,DeleteOutlined,EditOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _hooks_useResponsiveTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useResponsiveTable */ \"(app-pages-browser)/./src/hooks/useResponsiveTable.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst ExpenseCategoryTable = (param)=>{\n    let { categories, loading = false, onEdit, onDelete, onBulkDelete, selectedCategories = [], onSelectionChange, isMobile: propIsMobile = false } = param;\n    _s();\n    // Use hook for responsive detection, fallback to prop\n    const hookIsMobile = (0,_hooks_useResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.useResponsiveTable)();\n    const isMobile = propIsMobile || hookIsMobile;\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_3__.useSelector)({\n        \"ExpenseCategoryTable.useSelector[user]\": (state)=>state.auth.user\n    }[\"ExpenseCategoryTable.useSelector[user]\"]);\n    const userRole = user === null || user === void 0 ? void 0 : user.role;\n    // Check permissions\n    const canEdit = userRole === \"admin\" || userRole === \"superadmin\";\n    const canDelete = userRole === \"admin\" || userRole === \"superadmin\";\n    // Handle individual checkbox change\n    const handleCheckboxChange = (categoryId, checked)=>{\n        if (!onSelectionChange) return;\n        const newSelection = checked ? [\n            ...selectedCategories,\n            categoryId\n        ] : selectedCategories.filter((id)=>id !== categoryId);\n        onSelectionChange(newSelection);\n    };\n    // Handle select all checkbox\n    const handleSelectAll = (e)=>{\n        if (!onSelectionChange) return;\n        if (e.target.checked) {\n            // Only select non-default categories for bulk operations\n            const selectableIds = categories.filter((category)=>!category.isDefault).map((category)=>category.id);\n            onSelectionChange(selectableIds);\n        } else {\n            onSelectionChange([]);\n        }\n    };\n    // Check if all selectable categories are selected\n    const selectableCategories = categories.filter((category)=>!category.isDefault);\n    const isAllSelected = selectableCategories.length > 0 && selectedCategories.length === selectableCategories.length;\n    const isIndeterminate = selectedCategories.length > 0 && selectedCategories.length < selectableCategories.length;\n    if (isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                selectedCategories.length > 0 && canDelete && onBulkDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-700 text-sm\",\n                                children: [\n                                    selectedCategories.length,\n                                    \" category(s) selected\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                type: \"primary\",\n                                danger: true,\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 23\n                                }, void 0),\n                                onClick: ()=>onBulkDelete(selectedCategories),\n                                children: \"Delete Selected\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 11\n                }, undefined),\n                categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg p-4 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            onSelectionChange && !category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                checked: selectedCategories.includes(category.id),\n                                                onChange: (e)=>handleCheckboxChange(category.id, e.target.checked),\n                                                className: \"mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 rounded-full mr-2\",\n                                                        style: {\n                                                            backgroundColor: category.color\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900 text-sm flex items-center\",\n                                                            children: [\n                                                                category.name,\n                                                                category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"ml-2 text-yellow-500\",\n                                                                    title: \"System Default\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                                    lineNumber: 133,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1\",\n                                        children: [\n                                            canEdit && onEdit && !category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                title: \"Edit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    type: \"text\",\n                                                    size: \"small\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    onClick: ()=>onEdit(category)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            canDelete && onDelete && !category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                title: \"Delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    type: \"text\",\n                                                    size: \"small\",\n                                                    danger: true,\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    onClick: ()=>onDelete(category.id)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    category.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: category.description\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                color: category.color,\n                                                className: \"text-xs\",\n                                                children: category.color\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                color: \"gold\",\n                                                className: \"text-xs\",\n                                                children: \"System Default\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, category.id, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Desktop Table View\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-x-auto\",\n        children: [\n            selectedCategories.length > 0 && canDelete && onBulkDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-700\",\n                            children: [\n                                selectedCategories.length,\n                                \" category(s) selected\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            type: \"primary\",\n                            danger: true,\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 21\n                            }, void 0),\n                            onClick: ()=>onBulkDelete(selectedCategories),\n                            children: \"Delete Selected\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                lineNumber: 193,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        className: \"bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: [\n                                onSelectionChange && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-left\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        indeterminate: isIndeterminate,\n                                        checked: isAllSelected,\n                                        onChange: handleSelectAll\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: \"Category\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: \"Color\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: \"Type\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        className: \"bg-white divide-y divide-gray-200\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-gray-50\",\n                                children: [\n                                    onSelectionChange && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 whitespace-nowrap\",\n                                        children: !category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            checked: selectedCategories.includes(category.id),\n                                            onChange: (e)=>handleCheckboxChange(category.id, e.target.checked)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded-full mr-3\",\n                                                    style: {\n                                                        backgroundColor: category.color\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-900 flex items-center\",\n                                                        children: [\n                                                            category.name,\n                                                            category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"ml-2 text-yellow-500\",\n                                                                title: \"System Default\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-900 max-w-xs truncate\",\n                                            children: category.description || '-'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 whitespace-nowrap\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            color: category.color,\n                                            className: \"text-xs\",\n                                            children: category.color\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 whitespace-nowrap\",\n                                        children: category.isDefault ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            color: \"gold\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 43\n                                            }, void 0),\n                                            children: \"System Default\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            color: \"blue\",\n                                            children: \"Custom\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-end space-x-2\",\n                                            children: [\n                                                canEdit && onEdit && !category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    title: \"Edit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        type: \"text\",\n                                                        size: \"small\",\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        onClick: ()=>onEdit(category)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                canDelete && onDelete && !category.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    title: \"Delete\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        type: \"text\",\n                                                        size: \"small\",\n                                                        danger: true,\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CrownOutlined_DeleteFilled_DeleteOutlined_EditOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        onClick: ()=>onDelete(category.id)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, category.id, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Expenses\\\\ExpenseCategoryTable.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ExpenseCategoryTable, \"0FsLPU7pAYfF34MH2wThSQtpZUY=\", false, function() {\n    return [\n        _hooks_useResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.useResponsiveTable,\n        react_redux__WEBPACK_IMPORTED_MODULE_3__.useSelector\n    ];\n});\n_c = ExpenseCategoryTable;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ExpenseCategoryTable);\nvar _c;\n$RefreshReg$(_c, \"ExpenseCategoryTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Expenses/ExpenseCategoryTable.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useResponsiveTable.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useResponsiveTable.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useResponsiveTable: () => (/* binding */ useResponsiveTable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useResponsiveTable auto */ \n/**\n * Hook to determine if we should use mobile table layout\n * Returns true for mobile devices (width < 768px)\n */ const useResponsiveTable = ()=>{\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useResponsiveTable.useEffect\": ()=>{\n            const checkScreenSize = {\n                \"useResponsiveTable.useEffect.checkScreenSize\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"useResponsiveTable.useEffect.checkScreenSize\"];\n            // Check on mount\n            checkScreenSize();\n            // Add event listener for window resize\n            window.addEventListener('resize', checkScreenSize);\n            // Cleanup\n            return ({\n                \"useResponsiveTable.useEffect\": ()=>window.removeEventListener('resize', checkScreenSize)\n            })[\"useResponsiveTable.useEffect\"];\n        }\n    }[\"useResponsiveTable.useEffect\"], []);\n    return isMobile;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VSZXNwb25zaXZlVGFibGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O3dFQUU0QztBQUU1Qzs7O0NBR0MsR0FDTSxNQUFNRSxxQkFBcUI7SUFDaEMsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdKLCtDQUFRQSxDQUFDO0lBRXpDQyxnREFBU0E7d0NBQUM7WUFDUixNQUFNSTtnRUFBa0I7b0JBQ3RCRCxZQUFZRSxPQUFPQyxVQUFVLEdBQUc7Z0JBQ2xDOztZQUVBLGlCQUFpQjtZQUNqQkY7WUFFQSx1Q0FBdUM7WUFDdkNDLE9BQU9FLGdCQUFnQixDQUFDLFVBQVVIO1lBRWxDLFVBQVU7WUFDVjtnREFBTyxJQUFNQyxPQUFPRyxtQkFBbUIsQ0FBQyxVQUFVSjs7UUFDcEQ7dUNBQUcsRUFBRTtJQUVMLE9BQU9GO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsiRTpcXFBST0pFQ1RTXFxwb3NcXHBvc2Zyb250ZW5kXFxzcmNcXGhvb2tzXFx1c2VSZXNwb25zaXZlVGFibGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5cbi8qKlxuICogSG9vayB0byBkZXRlcm1pbmUgaWYgd2Ugc2hvdWxkIHVzZSBtb2JpbGUgdGFibGUgbGF5b3V0XG4gKiBSZXR1cm5zIHRydWUgZm9yIG1vYmlsZSBkZXZpY2VzICh3aWR0aCA8IDc2OHB4KVxuICovXG5leHBvcnQgY29uc3QgdXNlUmVzcG9uc2l2ZVRhYmxlID0gKCkgPT4ge1xuICBjb25zdCBbaXNNb2JpbGUsIHNldElzTW9iaWxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGNoZWNrU2NyZWVuU2l6ZSA9ICgpID0+IHtcbiAgICAgIHNldElzTW9iaWxlKHdpbmRvdy5pbm5lcldpZHRoIDwgNzY4KTtcbiAgICB9O1xuXG4gICAgLy8gQ2hlY2sgb24gbW91bnRcbiAgICBjaGVja1NjcmVlblNpemUoKTtcblxuICAgIC8vIEFkZCBldmVudCBsaXN0ZW5lciBmb3Igd2luZG93IHJlc2l6ZVxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCBjaGVja1NjcmVlblNpemUpO1xuXG4gICAgLy8gQ2xlYW51cFxuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgY2hlY2tTY3JlZW5TaXplKTtcbiAgfSwgW10pO1xuXG4gIHJldHVybiBpc01vYmlsZTtcbn07XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSZXNwb25zaXZlVGFibGUiLCJpc01vYmlsZSIsInNldElzTW9iaWxlIiwiY2hlY2tTY3JlZW5TaXplIiwid2luZG93IiwiaW5uZXJXaWR0aCIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useResponsiveTable.ts\n"));

/***/ })

});