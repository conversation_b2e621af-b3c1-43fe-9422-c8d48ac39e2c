"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/sales/page",{

/***/ "(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx":
/*!*************************************************!*\
  !*** ./src/components/Sales/SalesFormPanel.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/empty/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Empty,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/image/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PrinterOutlined.js\");\n/* harmony import */ var _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/reduxRTK/services/salesApi */ \"(app-pages-browser)/./src/reduxRTK/services/salesApi.ts\");\n/* harmony import */ var _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/reduxRTK/services/productApi */ \"(app-pages-browser)/./src/reduxRTK/services/productApi.ts\");\n/* harmony import */ var _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/reduxRTK/services/userStoreApi */ \"(app-pages-browser)/./src/reduxRTK/services/userStoreApi.ts\");\n/* harmony import */ var _components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/SlidingPanel */ \"(app-pages-browser)/./src/components/ui/SlidingPanel.tsx\");\n/* harmony import */ var _utils_showMessage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/showMessage */ \"(app-pages-browser)/./src/utils/showMessage.ts\");\n/* harmony import */ var _utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/cloudinaryUtils */ \"(app-pages-browser)/./src/utils/cloudinaryUtils.ts\");\n/* harmony import */ var _sales_panels_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./sales-panels.css */ \"(app-pages-browser)/./src/components/Sales/sales-panels.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst SalesFormPanel = (param)=>{\n    let { isOpen, onClose, onSuccess } = param;\n    var _productsData_data_products, _productsData_data;\n    _s();\n    const [form] = _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm();\n    const [productForm] = _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm();\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalAmount, setTotalAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingReceipt, setIsGeneratingReceipt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptPreviewVisible, setReceiptPreviewVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptUrl, setReceiptUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPrinted, setHasPrinted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            console.log(\"Items state changed:\", items);\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items\n    ]);\n    const { data: productsData, isLoading: isLoadingProducts, refetch: refetchProducts } = (0,_reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery)({\n        page: 1,\n        limit: 1000,\n        search: searchTerm\n    }, {\n        // Force refetch when component mounts and when panel opens\n        refetchOnMountOrArgChange: true,\n        refetchOnFocus: false,\n        refetchOnReconnect: true\n    });\n    // Debug products data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (productsData) {\n                var _productsData_data, _productsData_data_products, _productsData_data1;\n                console.log(\"🛒 Products loaded:\", {\n                    total: ((_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.total) || 0,\n                    productsCount: ((_productsData_data1 = productsData.data) === null || _productsData_data1 === void 0 ? void 0 : (_productsData_data_products = _productsData_data1.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.length) || 0,\n                    isLoading: isLoadingProducts\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        productsData,\n        isLoadingProducts\n    ]);\n    // Get current user ID from auth state\n    const getCurrentUserId = ()=>{\n        if (true) {\n            var _state_auth_user, _state_auth;\n            // @ts-ignore - Redux state is exposed for debugging\n            const state = window.__REDUX_STATE;\n            return (state === null || state === void 0 ? void 0 : (_state_auth = state.auth) === null || _state_auth === void 0 ? void 0 : (_state_auth_user = _state_auth.user) === null || _state_auth_user === void 0 ? void 0 : _state_auth_user.id) || 0;\n        }\n        return 0;\n    };\n    // Fetch user stores\n    const { data: userStoresData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery)(getCurrentUserId());\n    // Fetch default store\n    const { data: defaultStoreData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery)(getCurrentUserId());\n    // Set default store when data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (defaultStoreData === null || defaultStoreData === void 0 ? void 0 : defaultStoreData.data) {\n                setSelectedStore(defaultStoreData.data);\n                form.setFieldsValue({\n                    storeId: defaultStoreData.data.id\n                });\n            } else if ((userStoresData === null || userStoresData === void 0 ? void 0 : userStoresData.data) && userStoresData.data.length > 0) {\n                setSelectedStore(userStoresData.data[0]);\n                form.setFieldsValue({\n                    storeId: userStoresData.data[0].id\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        defaultStoreData,\n        userStoresData,\n        form\n    ]);\n    // Create sale mutation\n    const [createSale, { isLoading: isSubmitting }] = (0,_reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation)();\n    // Calculate total amount whenever items change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (items && items.length > 0) {\n                const total = items.reduce({\n                    \"SalesFormPanel.useEffect.total\": (sum, item)=>sum + item.price * item.quantity\n                }[\"SalesFormPanel.useEffect.total\"], 0);\n                setTotalAmount(total);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: total\n                    });\n                }\n                // Debug log to check items state\n                console.log(\"Current items in useEffect:\", items);\n            } else {\n                setTotalAmount(0);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: 0\n                    });\n                }\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items,\n        form\n    ]);\n    // Handle panel open/close\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (isOpen) {\n                // When panel opens, ensure we have fresh product data\n                console.log(\"🛒 Sales panel opened - fetching fresh product data\");\n                refetchProducts();\n            } else {\n                // Reset forms when panel is closed\n                if (form) {\n                    form.resetFields();\n                }\n                if (productForm) {\n                    productForm.resetFields();\n                }\n                setItems([]);\n                setSelectedProduct(null);\n                setQuantity(1);\n                setTotalAmount(0);\n                setReceiptUrl(null);\n                setReceiptPreviewVisible(false);\n                setHasPrinted(false);\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        isOpen,\n        form,\n        refetchProducts\n    ]);\n    // Handle adding an item to the sale\n    const handleAddItem = ()=>{\n        if (!selectedProduct) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Please select a product\");\n            return;\n        }\n        if (quantity <= 0) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Quantity must be greater than 0\");\n            return;\n        }\n        if (selectedProduct.stockQuantity < quantity) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Only \".concat(selectedProduct.stockQuantity, \" units available in stock\"));\n            return;\n        }\n        console.log(\"Adding item with product:\", selectedProduct);\n        // Check if product already exists in items\n        const existingItemIndex = items.findIndex((item)=>item.productId === selectedProduct.id);\n        if (existingItemIndex >= 0) {\n            // Update existing item\n            const updatedItems = [\n                ...items\n            ];\n            const newQuantity = updatedItems[existingItemIndex].quantity + quantity;\n            if (newQuantity > selectedProduct.stockQuantity) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Cannot add more than \".concat(selectedProduct.stockQuantity, \" units of this product\"));\n                return;\n            }\n            updatedItems[existingItemIndex].quantity = newQuantity;\n            // Make sure the product name is set\n            if (!updatedItems[existingItemIndex].productName) {\n                updatedItems[existingItemIndex].productName = selectedProduct.name;\n            }\n            // Update the state with the new array\n            console.log(\"Updating existing item. New items array:\", updatedItems);\n            setItems([\n                ...updatedItems\n            ]); // Create a new array reference to force re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Updated quantity of \".concat(selectedProduct.name));\n        } else {\n            // Add new item\n            const newItem = {\n                productId: selectedProduct.id,\n                productName: selectedProduct.name,\n                quantity,\n                price: typeof selectedProduct.price === \"string\" ? parseFloat(selectedProduct.price) : selectedProduct.price\n            };\n            // Create a new array with the new item\n            const newItems = [\n                ...items,\n                newItem\n            ];\n            // Update the state with the new array\n            console.log(\"Adding new item. New items array:\", newItems);\n            setItems(newItems); // This should trigger a re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Added \".concat(quantity, \" \").concat(selectedProduct.name, \" to sale\"));\n        }\n        // Reset selection\n        setSelectedProduct(null);\n        setQuantity(1);\n        if (productForm) {\n            productForm.setFieldsValue({\n                productId: undefined,\n                quantity: 1\n            });\n        }\n    };\n    // Handle removing an item from the sale\n    const handleRemoveItem = (index)=>{\n        const updatedItems = [\n            ...items\n        ];\n        updatedItems.splice(index, 1);\n        setItems(updatedItems);\n    };\n    // Handle printing receipt - directly trigger print dialog\n    const handlePrintReceipt = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n            if (!receiptUrl || hasPrinted) {\n                console.log(\"Skipping print: \", !receiptUrl ? \"No receipt URL\" : \"Already printed\");\n                return;\n            }\n            console.log(\"Printing receipt:\", receiptUrl);\n            // Mark as printed immediately to prevent multiple print dialogs\n            setHasPrinted(true);\n            // Create a hidden iframe to load the image\n            const iframe = document.createElement(\"iframe\");\n            iframe.style.display = \"none\";\n            document.body.appendChild(iframe);\n            // Set up the iframe content with the image and print CSS\n            iframe.onload = ({\n                \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                    if (iframe.contentWindow) {\n                        // Write the HTML content to the iframe\n                        iframe.contentWindow.document.write('\\n          <!DOCTYPE html>\\n          <html>\\n            <head>\\n              <title>Print Receipt</title>\\n              <style>\\n                body {\\n                  margin: 0;\\n                  padding: 0;\\n                  display: flex;\\n                  justify-content: center;\\n                  align-items: center;\\n                  height: 100vh;\\n                }\\n                img {\\n                  max-width: 100%;\\n                  max-height: 100vh;\\n                }\\n                @media print {\\n                  body {\\n                    margin: 0;\\n                    padding: 0;\\n                  }\\n                  img {\\n                    width: 100%;\\n                    height: auto;\\n                  }\\n                }\\n              </style>\\n            </head>\\n            <body>\\n              <img src=\"'.concat(receiptUrl, '\" alt=\"Receipt\" />\\n            </body>\\n          </html>\\n        '));\n                        // Close the document\n                        iframe.contentWindow.document.close();\n                        // Use a single print trigger with a delay to ensure the image is loaded\n                        setTimeout({\n                            \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                if (iframe.contentWindow) {\n                                    try {\n                                        // Print the iframe content\n                                        iframe.contentWindow.focus();\n                                        iframe.contentWindow.print();\n                                    } catch (e) {\n                                        console.error(\"Error printing receipt:\", e);\n                                    }\n                                    // Remove the iframe after printing\n                                    setTimeout({\n                                        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                            document.body.removeChild(iframe);\n                                        }\n                                    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 1000);\n                                }\n                            }\n                        }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 500);\n                    }\n                }\n            })[\"SalesFormPanel.useCallback[handlePrintReceipt]\"];\n            // Set the iframe source to trigger the onload event\n            iframe.src = \"about:blank\";\n        }\n    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], [\n        receiptUrl,\n        hasPrinted\n    ]);\n    // Effect to automatically print receipt when modal is shown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (receiptPreviewVisible && receiptUrl && !hasPrinted) {\n                // Add a small delay to ensure the receipt image is loaded\n                const timer = setTimeout({\n                    \"SalesFormPanel.useEffect.timer\": ()=>{\n                        handlePrintReceipt(); // This now handles the hasPrinted state internally\n                    }\n                }[\"SalesFormPanel.useEffect.timer\"], 800);\n                return ({\n                    \"SalesFormPanel.useEffect\": ()=>clearTimeout(timer)\n                })[\"SalesFormPanel.useEffect\"];\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        receiptPreviewVisible,\n        receiptUrl,\n        hasPrinted,\n        handlePrintReceipt\n    ]);\n    // Handle form submission\n    const handleSubmit = async ()=>{\n        try {\n            var _userStoresData_data;\n            if (items.length === 0) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Please add at least one item to the sale\");\n                return;\n            }\n            // Validate form fields\n            const values = await form.validateFields();\n            // Check if store is selected\n            if (!selectedStore) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"No store information available. Please set up your store in your profile settings.\");\n                return;\n            }\n            // Set loading state for receipt generation\n            setIsGeneratingReceipt(true);\n            // Get store information for receipt\n            const storeInfo = selectedStore || (userStoresData === null || userStoresData === void 0 ? void 0 : (_userStoresData_data = userStoresData.data) === null || _userStoresData_data === void 0 ? void 0 : _userStoresData_data.find((store)=>store.id === values.storeId)) || {\n                name: \"POS System\"\n            };\n            // Generate receipt HTML\n            const receiptHTML = (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__.generateReceiptHTML)({\n                id: Date.now(),\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                transactionDate: new Date().toISOString(),\n                items: items.map((item)=>({\n                        productName: item.productName,\n                        quantity: item.quantity,\n                        price: item.price\n                    }))\n            }, storeInfo);\n            // Generate receipt image and get URL\n            let receiptUrl = \"https://receipt.example.com/placeholder\";\n            try {\n                receiptUrl = await (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__.generateReceiptImage)(receiptHTML);\n            } catch (error) {\n                console.error(\"Failed to generate receipt image:\", error);\n            // Continue with placeholder URL if image generation fails\n            }\n            const saleData = {\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                items: items.map((item)=>({\n                        productId: item.productId,\n                        quantity: item.quantity,\n                        price: item.price\n                    })),\n                receiptUrl,\n                storeId: selectedStore === null || selectedStore === void 0 ? void 0 : selectedStore.id\n            };\n            const response = await createSale(saleData).unwrap();\n            if (response.success) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Sale created successfully\");\n                // Store the receipt URL for preview\n                setReceiptUrl(receiptUrl);\n                // Show receipt preview modal and offer print option\n                setReceiptPreviewVisible(true);\n                // Refresh product data to get updated stock quantities\n                refetchProducts();\n                // Trigger the success callback to refresh the list WITHOUT closing the panel\n                setTimeout(()=>{\n                    if (onSuccess) {\n                        // Call refetch directly instead of closing the panel\n                        refetchProducts();\n                    }\n                }, 300);\n            // Keep the panel open until the user explicitly closes it\n            // This ensures the receipt modal stays visible\n            } else {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", response.message || \"Failed to create sale\");\n            }\n        } catch (error) {\n            var _error_data;\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", ((_error_data = error.data) === null || _error_data === void 0 ? void 0 : _error_data.message) || \"An error occurred while creating the sale\");\n        } finally{\n            setIsGeneratingReceipt(false);\n        }\n    };\n    // Debug log to check items state when rendering\n    console.log(\"Rendering with items:\", items);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        title: \"Point of Sale\",\n        isOpen: isOpen,\n        onClose: onClose,\n        width: \"95%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sales-form min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between border-b border-slate-200 bg-white/80 backdrop-blur-sm px-6 py-4 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"text-xl text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"m-0 text-xl font-bold text-slate-800\",\n                                                children: \"New Transaction\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-slate-500 m-0\",\n                                                children: \"Point of Sale System\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-slate-500 m-0\",\n                                        children: \"Total Amount\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent\",\n                                        children: [\n                                            \"GHS \",\n                                            totalAmount.toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 496,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-6 lg:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 rounded-2xl border border-slate-200 bg-white/70 backdrop-blur-sm p-6 shadow-xl shadow-slate-200/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg mr-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"text-sm text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 524,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-bold text-slate-800 m-0\",\n                                                                            children: \"Product Selection\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 527,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-slate-500 m-0\",\n                                                                            children: \"Choose products for this transaction\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 526,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-sm text-slate-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-1 text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \" Required fields\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    form: productForm,\n                                                    layout: \"vertical\",\n                                                    initialValues: {\n                                                        quantity: 1\n                                                    },\n                                                    className: \"product-form\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 gap-6 md:grid-cols-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"md:col-span-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                                        name: \"productId\",\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-slate-700 font-medium\",\n                                                                            children: [\n                                                                                \"Product \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-500\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 552,\n                                                                                    columnNumber: 37\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 551,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        className: \"mb-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            showSearch: true,\n                                                                            placeholder: isLoadingProducts ? \"Loading products...\" : \"🔍 Search and select a product...\",\n                                                                            optionFilterProp: \"children\",\n                                                                            loading: isLoadingProducts,\n                                                                            disabled: isLoadingProducts,\n                                                                            onChange: (value)=>{\n                                                                                var _productsData_data;\n                                                                                const product = productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products.find((p)=>p.id === value);\n                                                                                console.log(\"Selected product:\", product);\n                                                                                if (product) {\n                                                                                    // Make a deep copy to avoid reference issues\n                                                                                    setSelectedProduct({\n                                                                                        ...product,\n                                                                                        // Ensure price is properly formatted\n                                                                                        price: typeof product.price === \"string\" ? product.price : String(product.price)\n                                                                                    });\n                                                                                } else {\n                                                                                    setSelectedProduct(null);\n                                                                                }\n                                                                            },\n                                                                            onSearch: setSearchTerm,\n                                                                            filterOption: false,\n                                                                            className: \"modern-select\",\n                                                                            size: \"large\",\n                                                                            suffixIcon: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                spin: true,\n                                                                                className: \"text-blue-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 592,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"text-slate-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 594,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            notFoundContent: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-center py-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        size: \"small\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 600,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"ml-2 text-slate-500\",\n                                                                                        children: \"Loading products...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 601,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 599,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center py-4 text-slate-500\",\n                                                                                children: \"No products found\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 604,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            children: productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : (_productsData_data_products = _productsData_data.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Option, {\n                                                                                    value: product.id,\n                                                                                    disabled: product.stockQuantity <= 0,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"font-medium text-slate-800\",\n                                                                                                        children: product.name\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 618,\n                                                                                                        columnNumber: 35\n                                                                                                    }, undefined),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-sm text-slate-500\",\n                                                                                                        children: [\n                                                                                                            \"GHS \",\n                                                                                                            Number(product.price).toFixed(2)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 619,\n                                                                                                        columnNumber: 35\n                                                                                                    }, undefined)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                lineNumber: 617,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-sm px-2 py-1 rounded-full \".concat(product.stockQuantity <= 0 ? 'bg-red-100 text-red-600' : product.stockQuantity <= 5 ? 'bg-yellow-100 text-yellow-600' : 'bg-green-100 text-green-600'),\n                                                                                                children: product.stockQuantity <= 0 ? \"Out of Stock\" : \"Stock: \".concat(product.stockQuantity)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                lineNumber: 623,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 616,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, product.id, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 611,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 557,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 548,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                                        name: \"quantity\",\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-slate-700 font-medium\",\n                                                                            children: [\n                                                                                \"Quantity \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-500\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 646,\n                                                                                    columnNumber: 38\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 645,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        className: \"mb-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            min: 1,\n                                                                            value: quantity,\n                                                                            onChange: (value)=>setQuantity(value || 1),\n                                                                            style: {\n                                                                                width: \"100%\"\n                                                                            },\n                                                                            className: \"modern-input\",\n                                                                            size: \"large\",\n                                                                            placeholder: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 651,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 642,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 641,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            type: \"primary\",\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 666,\n                                                                columnNumber: 27\n                                                            }, void 0),\n                                                            onClick: handleAddItem,\n                                                            className: \"mt-2 h-12 w-full bg-blue-600 text-base font-medium hover:bg-blue-700\",\n                                                            disabled: !selectedProduct,\n                                                            size: \"large\",\n                                                            children: \"Add to Cart\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"mb-4 flex items-center text-lg font-semibold text-gray-800\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 680,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \" Cart Items\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-[350px] overflow-x-auto overflow-y-auto\",\n                                                    children: items.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-lg border border-gray-200 bg-gray-50 p-8 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500\",\n                                                                children: \"No items in cart\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 687,\n                                                                columnNumber: 27\n                                                            }, void 0),\n                                                            image: _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"].PRESENTED_IMAGE_SIMPLE\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 685,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                        className: \"min-w-full overflow-hidden rounded-lg border border-gray-200 bg-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                className: \"sticky top-0 z-10 bg-gray-50 text-gray-700\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"px-4 py-3 text-left font-medium\",\n                                                                            children: \"Product\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 698,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"w-20 px-4 py-3 text-center font-medium\",\n                                                                            children: \"Qty\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 701,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"w-28 px-4 py-3 text-right font-medium\",\n                                                                            children: \"Price\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 704,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"w-28 px-4 py-3 text-right font-medium\",\n                                                                            children: \"Subtotal\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 707,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                            className: \"w-16 px-4 py-3 text-center font-medium\",\n                                                                            children: \"Action\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 710,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"border-b border-gray-200 hover:bg-gray-50\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 font-medium text-gray-800\",\n                                                                                children: item.productName || \"Unknown Product\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 721,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-center text-gray-800\",\n                                                                                children: item.quantity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 724,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-right text-gray-800\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    Number(item.price).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 727,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-right font-medium text-gray-800\",\n                                                                                children: [\n                                                                                    \"GHS\",\n                                                                                    \" \",\n                                                                                    (Number(item.price) * item.quantity).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 730,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-4 py-3 text-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 736,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    onClick: ()=>handleRemoveItem(index),\n                                                                                    type: \"text\",\n                                                                                    danger: true,\n                                                                                    className: \"text-red-500 hover:bg-gray-100 hover:text-red-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 735,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 734,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, \"\".concat(item.productId, \"-\").concat(index), true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 717,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 715,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tfoot\", {\n                                                                className: \"sticky bottom-0 z-10 bg-gray-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            colSpan: 3,\n                                                                            className: \"px-4 py-3 text-right font-bold text-gray-800\",\n                                                                            children: \"Total\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 748,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            colSpan: 2,\n                                                                            className: \"px-4 py-3 text-right font-bold text-green-600\",\n                                                                            children: [\n                                                                                \"GHS \",\n                                                                                totalAmount.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 754,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 747,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 746,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sticky top-4 rounded-lg border border-gray-200 bg-white p-4 shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"mb-4 flex items-center text-lg font-semibold text-gray-800\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" Checkout\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                form: form,\n                                                layout: \"vertical\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 rounded-lg border border-gray-200 bg-gray-50 p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"Items:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 778,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-gray-800\",\n                                                                        children: items.length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 779,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 777,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"Total Quantity:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 784,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-gray-800\",\n                                                                        children: items.reduce((sum, item)=>sum + item.quantity, 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 785,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 783,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between text-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"Subtotal:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 790,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-gray-800\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 791,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"my-4 border-t border-gray-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 795,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-800\",\n                                                                        children: \"Total:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 797,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-green-600\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 798,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 796,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    selectedStore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"mb-2 block text-gray-700\",\n                                                                children: \"Store\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 807,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center rounded-lg border border-gray-200 bg-gray-50 p-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"mr-2 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 809,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-800\",\n                                                                        children: selectedStore.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 810,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"hidden\",\n                                                                        name: \"storeId\",\n                                                                        value: selectedStore.id\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 813,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 808,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 806,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4 rounded-lg border border-gray-200 bg-gray-50 p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"No store information available. Please set up your store in your profile settings.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 822,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                        name: \"paymentMethod\",\n                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-700\",\n                                                            children: [\n                                                                \"Payment Method \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 833,\n                                                                    columnNumber: 40\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 832,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        rules: [\n                                                            {\n                                                                required: true,\n                                                                message: \"Please select a payment method\"\n                                                            }\n                                                        ],\n                                                        initialValue: \"cash\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"text-gray-800\",\n                                                            size: \"large\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Option, {\n                                                                    value: \"cash\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mr-2\",\n                                                                                children: \"\\uD83D\\uDCB5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 847,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \" Cash\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 846,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 845,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Option, {\n                                                                    value: \"card\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mr-2\",\n                                                                                children: \"\\uD83D\\uDCB3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 852,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \" Card\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 851,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 850,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Option, {\n                                                                    value: \"mobile_money\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mr-2\",\n                                                                                children: \"\\uD83D\\uDCF1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 857,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \" Mobile Money\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 856,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 855,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 829,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-8 space-y-4\",\n                                                        children: [\n                                                            receiptPreviewVisible ? // Show \"Done\" button when receipt is visible\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 868,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                onClick: ()=>{\n                                                                    // Close the modal and reset the receipt state\n                                                                    setReceiptPreviewVisible(false);\n                                                                    setReceiptUrl(null);\n                                                                    setHasPrinted(false);\n                                                                    // Reset the forms to start a new sale\n                                                                    form.resetFields();\n                                                                    productForm.resetFields();\n                                                                    setItems([]);\n                                                                    setSelectedProduct(null);\n                                                                    setQuantity(1);\n                                                                    setTotalAmount(0);\n                                                                },\n                                                                className: \"text-md font-small h-14 w-full bg-green-600 hover:bg-green-700\",\n                                                                size: \"large\",\n                                                                children: \"Start New Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 866,\n                                                                columnNumber: 23\n                                                            }, undefined) : // Show \"Complete Sale\" button when creating a sale\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 892,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                onClick: handleSubmit,\n                                                                loading: isSubmitting || isGeneratingReceipt,\n                                                                disabled: items.length === 0,\n                                                                className: \"text-md font-small h-14 w-full bg-green-600 hover:bg-green-700\",\n                                                                size: \"large\",\n                                                                children: isGeneratingReceipt ? \"Generating Receipt\" : \"Complete Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 890,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                onClick: onClose,\n                                                                className: \"h-12 w-full border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200\",\n                                                                size: \"large\",\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 905,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 769,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 494,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-gray-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            className: \"mr-2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 924,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Receipt Preview\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 925,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 923,\n                    columnNumber: 11\n                }, void 0),\n                open: receiptPreviewVisible,\n                onCancel: ()=>{\n                    // Close the modal and reset the receipt state\n                    setReceiptPreviewVisible(false);\n                    setReceiptUrl(null);\n                    setHasPrinted(false);\n                    // Reset the forms to start a new sale\n                    form.resetFields();\n                    productForm.resetFields();\n                    setItems([]);\n                    setSelectedProduct(null);\n                    setQuantity(1);\n                    setTotalAmount(0);\n                },\n                width: 500,\n                centered: true,\n                className: \"receipt-preview-modal\",\n                footer: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        onClick: ()=>{\n                            // Close the modal and reset the receipt state\n                            setReceiptPreviewVisible(false);\n                            setReceiptUrl(null);\n                            setHasPrinted(false);\n                            // Reset the forms to start a new sale\n                            form.resetFields();\n                            productForm.resetFields();\n                            setItems([]);\n                            setSelectedProduct(null);\n                            setQuantity(1);\n                            setTotalAmount(0);\n                        },\n                        className: \"border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200\",\n                        children: \"Close & New Sale\"\n                    }, \"close\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 947,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        type: \"primary\",\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 970,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: ()=>{\n                            // If already printed once, reset the flag to allow printing again\n                            if (hasPrinted) {\n                                setHasPrinted(false);\n                            }\n                            handlePrintReceipt();\n                        },\n                        className: \"bg-blue-600 hover:bg-blue-700\",\n                        children: hasPrinted ? \"Print Again\" : \"Print Receipt\"\n                    }, \"print\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 967,\n                        columnNumber: 11\n                    }, void 0)\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: receiptUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"receipt-image-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            src: receiptUrl,\n                            alt: \"Receipt\",\n                            className: \"receipt-image\",\n                            style: {\n                                maxWidth: \"100%\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 987,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 986,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-64 items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            size: \"large\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 996,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 995,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 984,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 921,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n        lineNumber: 488,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SalesFormPanel, \"ahv2kziGtLLfOAMc+sJghDBg4RM=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Empty_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm,\n        _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery,\n        _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation\n    ];\n});\n_c = SalesFormPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SalesFormPanel);\nvar _c;\n$RefreshReg$(_c, \"SalesFormPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx\n"));

/***/ })

});