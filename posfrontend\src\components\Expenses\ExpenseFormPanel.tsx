"use client";

import React, { useEffect } from "react";
import {
  Form,
  Input,
  InputNumber,
  Select,
  DatePicker,
  Switch,
  Button,
  Space,
  Divider,
  notification,
  Spin,
} from "antd";
import SlidingPanel from "@/components/ui/SlidingPanel";
import {
  DollarOutlined,
  CalendarOutlined,
  TagOutlined,
  UserOutlined,
  FileTextOutlined,
  CreditCardOutlined,
  CloseOutlined,
  SaveOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { useCreateExpenseMutation, useUpdateExpenseMutation, type Expense } from "@/reduxRTK/services/expenseApi";
import { useGetAllExpenseCategoriesQuery } from "@/reduxRTK/services/expenseCategoryApi";
import { showMessage } from "@/utils/showMessage";
import dayjs from "dayjs";

const { Option } = Select;
const { TextArea } = Input;

interface ExpenseFormPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  expense?: Expense | null; // For editing
}

const ExpenseFormPanel: React.FC<ExpenseFormPanelProps> = ({
  isOpen,
  onClose,
  onSuccess,
  expense,
}) => {
  const [form] = Form.useForm();
  const isEditing = !!expense;

  // API hooks
  const [createExpense, { isLoading: isCreating }] = useCreateExpenseMutation();
  const [updateExpense, { isLoading: isUpdating }] = useUpdateExpenseMutation();
  const { data: categoriesData, isLoading: categoriesLoading } = useGetAllExpenseCategoriesQuery({
    page: 1,
    limit: 100,
  });

  const categories = categoriesData?.data?.categories || [];
  const isSubmitting = isCreating || isUpdating;

  // Payment method options
  const paymentMethods = [
    { value: 'cash', label: 'Cash' },
    { value: 'card', label: 'Card' },
    { value: 'mobile_money', label: 'Mobile Money' },
    { value: 'bank_transfer', label: 'Bank Transfer' },
    { value: 'cheque', label: 'Cheque' },
  ];

  // Recurring frequency options
  const recurringFrequencies = [
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' },
    { value: 'quarterly', label: 'Quarterly' },
    { value: 'yearly', label: 'Yearly' },
  ];

  // Reset form when panel opens/closes or expense changes
  useEffect(() => {
    if (isOpen) {
      if (isEditing && expense) {
        form.setFieldsValue({
          title: expense.title,
          description: expense.description,
          amount: parseFloat(expense.amount),
          categoryId: expense.categoryId,
          expenseDate: expense.expenseDate ? dayjs(expense.expenseDate) : dayjs(),
          paymentMethod: expense.paymentMethod,
          vendor: expense.vendor,
          isRecurring: expense.isRecurring,
          recurringFrequency: expense.recurringFrequency,
          tags: expense.tags,
        });
      } else {
        form.resetFields();
        form.setFieldsValue({
          expenseDate: dayjs(),
          paymentMethod: 'cash',
          isRecurring: false,
        });
      }
    }
  }, [isOpen, isEditing, expense, form]);

  const handleSubmit = async (values: any) => {
    try {
      const expenseData: any = {
        title: values.title,
        description: values.description,
        amount: values.amount,
        expenseDate: values.expenseDate?.toISOString(),
        paymentMethod: values.paymentMethod,
        vendor: values.vendor,
        isRecurring: values.isRecurring || false,
        recurringFrequency: values.isRecurring ? values.recurringFrequency : undefined,
        tags: values.tags,
      };

      // Only include categoryId if it has a value
      if (values.categoryId) {
        expenseData.categoryId = values.categoryId;
      }

      let result;
      if (isEditing && expense) {
        result = await updateExpense({
          expenseId: expense.id,
          expenseData,
        }).unwrap();
      } else {
        result = await createExpense(expenseData).unwrap();
      }

      if (result.success) {
        showMessage.success(
          isEditing ? "Expense updated successfully!" : "Expense created successfully!"
        );
        form.resetFields();
        onSuccess?.();
        onClose();
      } else {
        showMessage.error(result.message || "Failed to save expense");
      }
    } catch (error: any) {
      console.error("Error saving expense:", error);
      showMessage.error(
        error?.data?.message ||
        error?.message ||
        `Failed to ${isEditing ? 'update' : 'create'} expense`
      );
    }
  };

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  // Panel title
  const panelTitle = isEditing ? "Edit Expense" : "Add New Expense";

  // Panel footer with action buttons
  const panelFooter = (
    <div className="flex justify-end space-x-2">
      <Button
        onClick={handleClose}
        disabled={isSubmitting}
        className="text-gray-700 hover:text-gray-900"
        style={{ borderColor: '#d9d9d9', background: '#f5f5f5' }}
      >
        Cancel
      </Button>
      <Button
        type="primary"
        onClick={() => form.submit()}
        loading={isSubmitting}
        icon={isSubmitting ? <LoadingOutlined /> : <SaveOutlined />}
      >
        {isEditing ? "Update" : "Create"} Expense
      </Button>
    </div>
  );

  return (
    <SlidingPanel
      isOpen={isOpen}
      onClose={handleClose}
      title={panelTitle}
      width="600px"
      footer={panelFooter}
    >
      {/* Form heading with icon */}
      <div className="mb-6 border-b border-gray-200 pb-4">
        <h2 className="text-xl font-bold text-gray-800 flex items-center">
          {isEditing ? (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Editing Expense: {expense?.title}
            </>
          ) : (
            <>
              <DollarOutlined className="mr-2" />
              Add New Expense
            </>
          )}
        </h2>
        <p className="text-gray-600 mt-1">
          {isEditing ? "Update expense information" : "Record a new business expense"}
        </p>
      </div>

      {/* Required fields indicator */}
      <div className="mb-4 text-sm text-gray-600">
        <span className="text-red-500 mr-1">*</span> indicates required fields
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className="expense-form"
        requiredMark={true}
      >
        {/* Basic Information */}
        <div className="mb-6">
          <h3 className="text-gray-800 text-lg font-medium mb-4 border-b border-gray-200 pb-2">
            Basic Information
          </h3>

          <Form.Item
            name="title"
            label={<span className="flex items-center"><FileTextOutlined className="mr-1" /> Expense Title <span className="text-red-500 ml-1">*</span></span>}
            rules={[{ required: true, message: "Please enter expense title" }]}
            tooltip="Brief description of the expense"
          >
            <Input
              placeholder="e.g., Office Rent, Utilities, Equipment Purchase"
              maxLength={255}
            />
          </Form.Item>

          <Form.Item
            name="description"
            label={<span className="flex items-center"><FileTextOutlined className="mr-1" /> Description</span>}
            tooltip="Additional details about the expense"
          >
            <TextArea
              placeholder="Additional details about this expense..."
              rows={3}
              maxLength={500}
            />
          </Form.Item>
        </div>

        {/* Financial Details */}
        <div className="mb-6">
          <h3 className="text-gray-800 text-lg font-medium mb-4 border-b border-gray-200 pb-2">
            Financial Details
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              name="amount"
              label={<span className="flex items-center"><DollarOutlined className="mr-1" /> Amount (₵) <span className="text-red-500 ml-1">*</span></span>}
              rules={[
                { required: true, message: "Please enter amount" },
                { type: "number", min: 0.01, message: "Amount must be greater than 0" }
              ]}
              tooltip="The total amount spent"
            >
              <InputNumber
                min={0}
                step={0.01}
                precision={2}
                style={{ width: '100%' }}
                placeholder="0.00"
                prefix="₵"
              />
            </Form.Item>

            <Form.Item
              name="categoryId"
              label={<span className="flex items-center"><TagOutlined className="mr-1" /> Category</span>}
              tooltip="Select expense category for better organization"
            >
              <Select
                placeholder="Select category"
                loading={categoriesLoading}
                allowClear
                showSearch
                optionFilterProp="children"
              >
                {categories.map(category => (
                  <Option key={category.id} value={category.id}>
                    <div className="flex items-center">
                      <div
                        className="w-3 h-3 rounded-full mr-2"
                        style={{ backgroundColor: category.color }}
                      />
                      {category.name}
                    </div>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              name="expenseDate"
              label={<span className="flex items-center"><CalendarOutlined className="mr-1" /> Expense Date <span className="text-red-500 ml-1">*</span></span>}
              rules={[{ required: true, message: "Please select expense date" }]}
              tooltip="When this expense occurred"
            >
              <DatePicker
                style={{ width: '100%' }}
                format="YYYY-MM-DD"
                placeholder="Select date"
              />
            </Form.Item>

            <Form.Item
              name="paymentMethod"
              label={<span className="flex items-center"><CreditCardOutlined className="mr-1" /> Payment Method <span className="text-red-500 ml-1">*</span></span>}
              rules={[{ required: true, message: "Please select payment method" }]}
              tooltip="How this expense was paid"
            >
              <Select placeholder="Select payment method">
                {paymentMethods.map(method => (
                  <Option key={method.value} value={method.value}>
                    {method.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </div>
        </div>

        {/* Additional Information */}
        <div className="mb-6">
          <h3 className="text-gray-800 text-lg font-medium mb-4 border-b border-gray-200 pb-2">
            Additional Information
          </h3>

          <Form.Item
            name="vendor"
            label={<span className="flex items-center"><UserOutlined className="mr-1" /> Vendor/Supplier</span>}
            tooltip="Who was paid for this expense"
          >
            <Input
              placeholder="e.g., ABC Company, John Doe, Utility Company"
              maxLength={255}
            />
          </Form.Item>

          <Form.Item
            name="tags"
            label={<span className="flex items-center"><TagOutlined className="mr-1" /> Tags</span>}
            tooltip="Comma-separated tags for better categorization"
          >
            <Input
              placeholder="e.g., urgent, monthly, equipment"
              maxLength={200}
            />
          </Form.Item>

          {/* Recurring Expense */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <Form.Item
              name="isRecurring"
              valuePropName="checked"
              label={<span className="flex items-center">Recurring Expense</span>}
              tooltip="Check if this is a recurring expense"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.isRecurring !== currentValues.isRecurring
              }
            >
              {({ getFieldValue }) =>
                getFieldValue('isRecurring') ? (
                  <Form.Item
                    name="recurringFrequency"
                    label="Frequency"
                    rules={[{ required: true, message: "Please select frequency" }]}
                  >
                    <Select placeholder="Select frequency">
                      {recurringFrequencies.map(freq => (
                        <Option key={freq.value} value={freq.value}>
                          {freq.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                ) : null
              }
            </Form.Item>
          </div>
        </div>
      </Form>
    </SlidingPanel>
  );
};

export default ExpenseFormPanel;
