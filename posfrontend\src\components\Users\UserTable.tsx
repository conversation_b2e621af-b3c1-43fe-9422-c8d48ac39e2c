"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Space, Tag, Checkbox, notification } from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import {
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  StopOutlined,
  DeleteFilled,
} from "@ant-design/icons";
import { ResponsiveTableGrid, TableHeader, TableCell, TableRow } from "@/components/ui/ResponsiveTable";
import { User, PaymentStatus, UserRole } from "@/types/user";
import dayjs from "dayjs";
import { formatPhoneNumberForDisplay } from "@/utils/formatPhoneNumber";

interface UserTableProps {
  users: User[];
  isMobile: boolean;
  onView: (userId: number) => void;
  onEdit: (user: User) => void;
  onDelete: (userId: number) => void;
  onBulkDelete?: (userIds: number[]) => void;
  canManageUser: (role: UserRole) => boolean;
}

const UserTable: React.FC<UserTableProps> = ({
  users,
  isMobile,
  onView,
  onEdit,
  onDelete,
  onBulkDelete,
  canManageUser,
}) => {
  // State for selected users
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Handle select all checkbox change
  const handleSelectAllChange = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked;
    setSelectAll(checked);

    if (checked) {
      // Select all users that the user can manage
      const selectableUserIds = users
        .filter(user => canManageUser(user.role))
        .map(user => user.id);
      setSelectedUsers(selectableUserIds);
    } else {
      // Deselect all users
      setSelectedUsers([]);
    }
  };

  // Handle individual checkbox change
  const handleCheckboxChange = (userId: number, checked: boolean) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, userId]);
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId));
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedUsers.length > 0 && onBulkDelete) {
      onBulkDelete(selectedUsers);
      setSelectedUsers([]);
      setSelectAll(false);
    } else {
      notification.warning({
        message: 'No users selected',
        description: 'Please select at least one user to delete.',
      });
    }
  };

  // Helper functions for rendering tags
  const getPaymentStatusTag = (status: PaymentStatus) => {
    switch (status) {
      case "paid":
        return <Tag className="status-tag status-paid" icon={<CheckCircleOutlined />} color="success">Paid</Tag>;
      case "pending":
        return <Tag className="status-tag status-pending" icon={<ClockCircleOutlined />} color="warning">Pending</Tag>;
      case "overdue":
        return <Tag className="status-tag status-overdue" icon={<WarningOutlined />} color="error">Overdue</Tag>;
      case "inactive":
        return <Tag className="status-tag status-inactive" icon={<StopOutlined />} color="default">Inactive</Tag>;
      default:
        return <Tag className="status-tag" color="default">{status}</Tag>;
    }
  };

  const getRoleTag = (role: UserRole) => {
    switch (role) {
      case "superadmin":
        return <Tag color="purple">Super Admin</Tag>;
      case "admin":
        return <Tag color="blue">Admin</Tag>;
      case "cashier":
        return <Tag color="green">Cashier</Tag>;
      default:
        return <Tag color="default">{role}</Tag>;
    }
  };

  return (
    <div className="overflow-hidden bg-white">
      {/* Bulk Delete Button - Show only when users are selected */}
      {selectedUsers.length > 0 && (
        <div className="p-2 bg-gray-100 border-b flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            {selectedUsers.length} {selectedUsers.length === 1 ? 'user' : 'users'} selected
          </span>
          <Button
            type="primary"
            danger
            icon={<DeleteFilled />}
            onClick={handleBulkDelete}
            className="ml-2"
          >
            Delete Selected
          </Button>
        </div>
      )}

      <ResponsiveTableGrid
        columns={isMobile ? "50px 200px 100px 100px 150px" : "50px 200px 200px 140px 130px 100px 100px 150px"}
        minWidth={isMobile ? "700px" : "1200px"}
      >
        {/* Table Headers */}
        <TableHeader className="text-center">
          <Checkbox
            checked={selectAll}
            onChange={handleSelectAllChange}
            disabled={users.filter(user => canManageUser(user.role)).length === 0}
          />
        </TableHeader>
        <TableHeader sticky={isMobile ? undefined : "left"}>
          Name
        </TableHeader>
        {!isMobile && (
          <>
            <TableHeader>
              Email
            </TableHeader>
            <TableHeader>
              Phone
            </TableHeader>
            <TableHeader>
              Created At
            </TableHeader>
          </>
        )}
        <TableHeader>
          Role
        </TableHeader>
        <TableHeader>
          Status
        </TableHeader>
        <TableHeader sticky={isMobile ? undefined : "right"} className="text-right">
          Actions
        </TableHeader>
        {/* Table Rows */}
        {users.map((user) => (
          <TableRow
            key={user.id}
            selected={selectedUsers.includes(user.id)}
          >
            {/* Checkbox Column */}
            <TableCell className="text-center">
              {canManageUser(user.role) && (
                <Checkbox
                  checked={selectedUsers.includes(user.id)}
                  onChange={(e) => handleCheckboxChange(user.id, e.target.checked)}
                />
              )}
            </TableCell>

            {/* Name Column - Always visible */}
            <TableCell sticky={isMobile ? undefined : "left"}>
              <div className="max-w-[180px] overflow-hidden text-ellipsis font-medium">
                {user.name}
              </div>
            </TableCell>

            {/* Show these columns only on desktop */}
            {!isMobile && (
              <>
                <TableCell>
                  <div className="max-w-[180px] overflow-hidden text-ellipsis text-blue-600">
                    {user.email}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="max-w-[120px] overflow-hidden text-ellipsis font-mono text-sm">
                    {formatPhoneNumberForDisplay(user.phone)}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="max-w-[110px] overflow-hidden text-ellipsis">
                    {dayjs(user.createdAt).format("MMM D, YYYY")}
                  </div>
                </TableCell>
              </>
            )}

            {/* Role Column */}
            <TableCell>
              {getRoleTag(user.role)}
            </TableCell>

            {/* Payment Status Column */}
            <TableCell>
              {getPaymentStatusTag(user.paymentStatus)}
            </TableCell>

            {/* Actions Column - Always visible */}
            <TableCell sticky={isMobile ? undefined : "right"} className="text-right">
              <div className="flex justify-end space-x-1">
                <Tooltip title="View">
                  <Button
                    icon={<EyeOutlined />}
                    onClick={() => onView(user.id)}
                    type="text"
                    className="view-button text-green-500 hover:text-green-400"
                    size={isMobile ? "small" : "middle"}
                  />
                </Tooltip>

                {canManageUser(user.role) && (
                  <>
                    <Tooltip title="Edit">
                      <Button
                        icon={<EditOutlined />}
                        onClick={() => onEdit(user)}
                        type="text"
                        className="edit-button text-blue-500 hover:text-blue-400"
                        size={isMobile ? "small" : "middle"}
                      />
                    </Tooltip>
                    <Tooltip title="Delete">
                      <Button
                        icon={<DeleteOutlined />}
                        onClick={() => onDelete(user.id)}
                        type="text"
                        className="delete-button text-red-500 hover:text-red-400"
                        size={isMobile ? "small" : "middle"}
                      />
                    </Tooltip>
                  </>
                )}
              </div>
            </TableCell>
          </TableRow>
        ))}
      </ResponsiveTableGrid>
    </div>
  );
};

export default UserTable;
