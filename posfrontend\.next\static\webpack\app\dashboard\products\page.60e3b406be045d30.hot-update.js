"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/products/page",{

/***/ "(app-pages-browser)/./src/components/Products/ProductTable.tsx":
/*!**************************************************!*\
  !*** ./src/components/Products/ProductTable.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/notification/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/checkbox/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,Tooltip,notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteFilled.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarcodeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CalendarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarcodeOutlined,CalendarOutlined,DeleteFilled,DeleteOutlined,DollarOutlined,EditOutlined,EyeOutlined,PlusCircleOutlined,ShoppingOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ResponsiveTable */ \"(app-pages-browser)/./src/components/ui/ResponsiveTable.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ProductTable = (param)=>{\n    let { products, loading, onView, onEdit, onDelete, onBulkDelete, onAdjustStock, isMobile = false } = param;\n    _s();\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__.useSelector)({\n        \"ProductTable.useSelector[user]\": (state)=>state.auth.user\n    }[\"ProductTable.useSelector[user]\"]);\n    const userRole = user === null || user === void 0 ? void 0 : user.role;\n    // State for selected products\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectAll, setSelectAll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle select all checkbox change\n    const handleSelectAllChange = (e)=>{\n        const checked = e.target.checked;\n        setSelectAll(checked);\n        if (checked) {\n            // Select all products that the user can delete\n            const selectableProductIds = products.filter((product)=>canEditDelete(product)).map((product)=>product.id);\n            setSelectedProducts(selectableProductIds);\n        } else {\n            // Deselect all products\n            setSelectedProducts([]);\n        }\n    };\n    // Handle individual checkbox change\n    const handleCheckboxChange = (productId, checked)=>{\n        if (checked) {\n            setSelectedProducts((prev)=>[\n                    ...prev,\n                    productId\n                ]);\n        } else {\n            setSelectedProducts((prev)=>prev.filter((id)=>id !== productId));\n        }\n    };\n    // Handle bulk delete\n    const handleBulkDelete = ()=>{\n        if (selectedProducts.length > 0 && onBulkDelete) {\n            onBulkDelete(selectedProducts);\n            setSelectedProducts([]);\n            setSelectAll(false);\n        } else {\n            _barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].warning({\n                message: 'No products selected',\n                description: 'Please select at least one product to delete.'\n            });\n        }\n    };\n    // Format date for display\n    const formatDate = (dateString)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_3___default()(dateString).format(\"MMM D, YYYY\");\n    };\n    // Format currency for display\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-GH', {\n            style: 'currency',\n            currency: 'GHS'\n        }).format(parseFloat(amount));\n    };\n    // Check if user can edit/delete (superadmin can edit all, others only their own)\n    const canEditDelete = (product)=>{\n        if (userRole === \"superadmin\") return true;\n        if (userRole === \"admin\" && (user === null || user === void 0 ? void 0 : user.id) === product.createdBy) return true;\n        return false;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-hidden bg-white\",\n        children: [\n            selectedProducts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 bg-gray-100 border-b flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: [\n                            selectedProducts.length,\n                            \" \",\n                            selectedProducts.length === 1 ? 'product' : 'products',\n                            \" selected\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        type: \"primary\",\n                        danger: true,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: handleBulkDelete,\n                        className: \"ml-2\",\n                        children: \"Delete Selected\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.ResponsiveTableGrid, {\n                columns: isMobile ? \"50px 200px 100px 80px 120px 150px\" : \"50px 200px 100px 80px 100px 120px 150px\",\n                minWidth: isMobile ? \"800px\" : \"1000px\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            checked: selectAll,\n                            onChange: handleSelectAllChange,\n                            disabled: products.filter((product)=>canEditDelete(product)).length === 0\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        sticky: isMobile ? undefined : \"left\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Name\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Price\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Stock\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, undefined),\n                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"SKU\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Created At\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        sticky: isMobile ? undefined : \"right\",\n                        className: \"text-right\",\n                        children: \"Actions\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined),\n                    products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableRow, {\n                            selected: selectedProducts.includes(product.id),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    className: \"text-center\",\n                                    children: canEditDelete(product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        checked: selectedProducts.includes(product.id),\n                                        onChange: (e)=>handleCheckboxChange(product.id, e.target.checked)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    sticky: \"left\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-[180px] overflow-hidden text-ellipsis font-medium\",\n                                        children: product.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-green-600\",\n                                        children: formatCurrency(product.price)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full \".concat(product.stockQuantity <= 0 ? 'bg-red-500 text-white' : product.stockQuantity <= (product.minStockLevel || 5) ? 'bg-yellow-500 text-white' : 'bg-green-500 text-white'),\n                                        children: product.stockQuantity\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, undefined),\n                                !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-xs\",\n                                        children: product.sku || 'N/A'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: formatDate(product.createdAt)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ResponsiveTable__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    sticky: \"right\",\n                                    className: \"text-right\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                title: \"View\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    onClick: ()=>onView(product.id),\n                                                    type: \"text\",\n                                                    className: \"view-button text-green-500 hover:text-green-400\",\n                                                    size: isMobile ? \"small\" : \"middle\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            userRole === \"admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                title: \"Adjust Stock\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    onClick: ()=>onAdjustStock(product),\n                                                    type: \"text\",\n                                                    className: \"adjust-stock-button text-purple-500 hover:text-purple-400\",\n                                                    size: isMobile ? \"small\" : \"middle\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            canEditDelete(product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        title: \"Edit\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            onClick: ()=>onEdit(product),\n                                                            type: \"text\",\n                                                            className: \"edit-button text-blue-500 hover:text-blue-400\",\n                                                            size: isMobile ? \"small\" : \"middle\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        title: \"Delete\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_Tooltip_notification_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarcodeOutlined_CalendarOutlined_DeleteFilled_DeleteOutlined_DollarOutlined_EditOutlined_EyeOutlined_PlusCircleOutlined_ShoppingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            onClick: ()=>onDelete(product.id),\n                                                            type: \"text\",\n                                                            className: \"delete-button text-red-500 hover:text-red-400\",\n                                                            size: isMobile ? \"small\" : \"middle\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, product.id, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Products\\\\ProductTable.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductTable, \"QVOA1Pi+5rHhJmGQeX8BO2tvPjo=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_4__.useSelector\n    ];\n});\n_c = ProductTable;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductTable);\nvar _c;\n$RefreshReg$(_c, \"ProductTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Products/ProductTable.tsx\n"));

/***/ })

});