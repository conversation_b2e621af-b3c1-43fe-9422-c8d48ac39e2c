"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/sales/page",{

/***/ "(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx":
/*!*************************************************!*\
  !*** ./src/components/Sales/SalesFormPanel.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/image/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ScanOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ScanOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PrinterOutlined.js\");\n/* harmony import */ var _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/reduxRTK/services/salesApi */ \"(app-pages-browser)/./src/reduxRTK/services/salesApi.ts\");\n/* harmony import */ var _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/reduxRTK/services/productApi */ \"(app-pages-browser)/./src/reduxRTK/services/productApi.ts\");\n/* harmony import */ var _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/reduxRTK/services/userStoreApi */ \"(app-pages-browser)/./src/reduxRTK/services/userStoreApi.ts\");\n/* harmony import */ var _components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/SlidingPanel */ \"(app-pages-browser)/./src/components/ui/SlidingPanel.tsx\");\n/* harmony import */ var _components_BarcodeScanner_BarcodeScanner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/BarcodeScanner/BarcodeScanner */ \"(app-pages-browser)/./src/components/BarcodeScanner/BarcodeScanner.tsx\");\n/* harmony import */ var _hooks_useBarcodeScanner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useBarcodeScanner */ \"(app-pages-browser)/./src/hooks/useBarcodeScanner.ts\");\n/* harmony import */ var _utils_showMessage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/showMessage */ \"(app-pages-browser)/./src/utils/showMessage.ts\");\n/* harmony import */ var _utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/cloudinaryUtils */ \"(app-pages-browser)/./src/utils/cloudinaryUtils.ts\");\n/* harmony import */ var _sales_panels_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./sales-panels.css */ \"(app-pages-browser)/./src/components/Sales/sales-panels.css\");\n/* harmony import */ var _modern_sales_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./modern-sales.css */ \"(app-pages-browser)/./src/components/Sales/modern-sales.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst SalesFormPanel = (param)=>{\n    let { isOpen, onClose, onSuccess } = param;\n    var _productsData_data_products, _productsData_data;\n    _s();\n    const [form] = _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].useForm();\n    const [productForm] = _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].useForm();\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalAmount, setTotalAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingReceipt, setIsGeneratingReceipt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptPreviewVisible, setReceiptPreviewVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptUrl, setReceiptUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPrinted, setHasPrinted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [barcodeInput, setBarcodeInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Barcode scanner functionality\n    const { isOpen: isScannerOpen, openScanner, closeScanner, handleBarcodeScanned, isLoading: isScannerLoading } = (0,_hooks_useBarcodeScanner__WEBPACK_IMPORTED_MODULE_7__.useBarcodeScanner)({\n        onProductFound: {\n            \"SalesFormPanel.useBarcodeScanner\": (product)=>{\n                setSelectedProduct(product);\n                setQuantity(1);\n                if (productForm) {\n                    productForm.setFieldsValue({\n                        productId: product.id,\n                        quantity: 1\n                    });\n                }\n                // Auto-add to cart after scanning\n                setTimeout({\n                    \"SalesFormPanel.useBarcodeScanner\": ()=>{\n                        handleAddItem();\n                    }\n                }[\"SalesFormPanel.useBarcodeScanner\"], 100);\n            }\n        }[\"SalesFormPanel.useBarcodeScanner\"],\n        onProductNotFound: {\n            \"SalesFormPanel.useBarcodeScanner\": (barcode)=>{\n                console.log('Product not found for barcode:', barcode);\n            }\n        }[\"SalesFormPanel.useBarcodeScanner\"]\n    });\n    // Debug state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            console.log(\"Items state changed:\", items);\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items\n    ]);\n    const { data: productsData, isLoading: isLoadingProducts, refetch: refetchProducts } = (0,_reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery)({\n        page: 1,\n        limit: 1000,\n        search: searchTerm\n    }, {\n        // Force refetch when component mounts and when panel opens\n        refetchOnMountOrArgChange: true,\n        refetchOnFocus: false,\n        refetchOnReconnect: true\n    });\n    // Debug products data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (productsData) {\n                var _productsData_data, _productsData_data_products, _productsData_data1;\n                console.log(\"🛒 Products loaded:\", {\n                    total: ((_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.total) || 0,\n                    productsCount: ((_productsData_data1 = productsData.data) === null || _productsData_data1 === void 0 ? void 0 : (_productsData_data_products = _productsData_data1.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.length) || 0,\n                    isLoading: isLoadingProducts\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        productsData,\n        isLoadingProducts\n    ]);\n    // Get current user ID from auth state\n    const getCurrentUserId = ()=>{\n        if (true) {\n            var _state_auth_user, _state_auth;\n            // @ts-ignore - Redux state is exposed for debugging\n            const state = window.__REDUX_STATE;\n            return (state === null || state === void 0 ? void 0 : (_state_auth = state.auth) === null || _state_auth === void 0 ? void 0 : (_state_auth_user = _state_auth.user) === null || _state_auth_user === void 0 ? void 0 : _state_auth_user.id) || 0;\n        }\n        return 0;\n    };\n    // Fetch user stores\n    const { data: userStoresData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery)(getCurrentUserId());\n    // Fetch default store\n    const { data: defaultStoreData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery)(getCurrentUserId());\n    // Set default store when data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (defaultStoreData === null || defaultStoreData === void 0 ? void 0 : defaultStoreData.data) {\n                setSelectedStore(defaultStoreData.data);\n                form.setFieldsValue({\n                    storeId: defaultStoreData.data.id\n                });\n            } else if ((userStoresData === null || userStoresData === void 0 ? void 0 : userStoresData.data) && userStoresData.data.length > 0) {\n                setSelectedStore(userStoresData.data[0]);\n                form.setFieldsValue({\n                    storeId: userStoresData.data[0].id\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        defaultStoreData,\n        userStoresData,\n        form\n    ]);\n    // Create sale mutation\n    const [createSale, { isLoading: isSubmitting }] = (0,_reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation)();\n    // Calculate total amount whenever items change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (items && items.length > 0) {\n                const total = items.reduce({\n                    \"SalesFormPanel.useEffect.total\": (sum, item)=>sum + item.price * item.quantity\n                }[\"SalesFormPanel.useEffect.total\"], 0);\n                setTotalAmount(total);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: total\n                    });\n                }\n                // Debug log to check items state\n                console.log(\"Current items in useEffect:\", items);\n            } else {\n                setTotalAmount(0);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: 0\n                    });\n                }\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items,\n        form\n    ]);\n    // Handle panel open/close\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (isOpen) {\n                // When panel opens, ensure we have fresh product data\n                console.log(\"🛒 Sales panel opened - fetching fresh product data\");\n                refetchProducts();\n            } else {\n                // Reset forms when panel is closed\n                if (form) {\n                    form.resetFields();\n                }\n                if (productForm) {\n                    productForm.resetFields();\n                }\n                setItems([]);\n                setSelectedProduct(null);\n                setQuantity(1);\n                setTotalAmount(0);\n                setReceiptUrl(null);\n                setReceiptPreviewVisible(false);\n                setHasPrinted(false);\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        isOpen,\n        form,\n        refetchProducts\n    ]);\n    // Handle adding an item to the sale\n    const handleAddItem = ()=>{\n        if (!selectedProduct) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"Please select a product\");\n            return;\n        }\n        if (quantity <= 0) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"Quantity must be greater than 0\");\n            return;\n        }\n        if (selectedProduct.stockQuantity < quantity) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"Only \".concat(selectedProduct.stockQuantity, \" units available in stock\"));\n            return;\n        }\n        console.log(\"Adding item with product:\", selectedProduct);\n        // Check if product already exists in items\n        const existingItemIndex = items.findIndex((item)=>item.productId === selectedProduct.id);\n        if (existingItemIndex >= 0) {\n            // Update existing item\n            const updatedItems = [\n                ...items\n            ];\n            const newQuantity = updatedItems[existingItemIndex].quantity + quantity;\n            if (newQuantity > selectedProduct.stockQuantity) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"Cannot add more than \".concat(selectedProduct.stockQuantity, \" units of this product\"));\n                return;\n            }\n            updatedItems[existingItemIndex].quantity = newQuantity;\n            // Make sure the product name is set\n            if (!updatedItems[existingItemIndex].productName) {\n                updatedItems[existingItemIndex].productName = selectedProduct.name;\n            }\n            // Update the state with the new array\n            console.log(\"Updating existing item. New items array:\", updatedItems);\n            setItems([\n                ...updatedItems\n            ]); // Create a new array reference to force re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"success\", \"Updated quantity of \".concat(selectedProduct.name));\n        } else {\n            // Add new item\n            const newItem = {\n                productId: selectedProduct.id,\n                productName: selectedProduct.name,\n                quantity,\n                price: typeof selectedProduct.price === \"string\" ? parseFloat(selectedProduct.price) : selectedProduct.price\n            };\n            // Create a new array with the new item\n            const newItems = [\n                ...items,\n                newItem\n            ];\n            // Update the state with the new array\n            console.log(\"Adding new item. New items array:\", newItems);\n            setItems(newItems); // This should trigger a re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"success\", \"Added \".concat(quantity, \" \").concat(selectedProduct.name, \" to sale\"));\n        }\n        // Reset selection\n        setSelectedProduct(null);\n        setQuantity(1);\n        if (productForm) {\n            productForm.setFieldsValue({\n                productId: undefined,\n                quantity: 1\n            });\n        }\n    };\n    // Handle removing an item from the sale\n    const handleRemoveItem = (index)=>{\n        const updatedItems = [\n            ...items\n        ];\n        updatedItems.splice(index, 1);\n        setItems(updatedItems);\n    };\n    // Handle printing receipt - directly trigger print dialog\n    const handlePrintReceipt = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n            if (!receiptUrl || hasPrinted) {\n                console.log(\"Skipping print: \", !receiptUrl ? \"No receipt URL\" : \"Already printed\");\n                return;\n            }\n            console.log(\"Printing receipt:\", receiptUrl);\n            // Mark as printed immediately to prevent multiple print dialogs\n            setHasPrinted(true);\n            // Create a hidden iframe to load the image\n            const iframe = document.createElement(\"iframe\");\n            iframe.style.display = \"none\";\n            document.body.appendChild(iframe);\n            // Set up the iframe content with the image and print CSS\n            iframe.onload = ({\n                \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                    if (iframe.contentWindow) {\n                        // Write the HTML content to the iframe\n                        iframe.contentWindow.document.write('\\n          <!DOCTYPE html>\\n          <html>\\n            <head>\\n              <title>Print Receipt</title>\\n              <style>\\n                body {\\n                  margin: 0;\\n                  padding: 0;\\n                  display: flex;\\n                  justify-content: center;\\n                  align-items: center;\\n                  height: 100vh;\\n                }\\n                img {\\n                  max-width: 100%;\\n                  max-height: 100vh;\\n                }\\n                @media print {\\n                  body {\\n                    margin: 0;\\n                    padding: 0;\\n                  }\\n                  img {\\n                    width: 100%;\\n                    height: auto;\\n                  }\\n                }\\n              </style>\\n            </head>\\n            <body>\\n              <img src=\"'.concat(receiptUrl, '\" alt=\"Receipt\" />\\n            </body>\\n          </html>\\n        '));\n                        // Close the document\n                        iframe.contentWindow.document.close();\n                        // Use a single print trigger with a delay to ensure the image is loaded\n                        setTimeout({\n                            \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                if (iframe.contentWindow) {\n                                    try {\n                                        // Print the iframe content\n                                        iframe.contentWindow.focus();\n                                        iframe.contentWindow.print();\n                                    } catch (e) {\n                                        console.error(\"Error printing receipt:\", e);\n                                    }\n                                    // Remove the iframe after printing\n                                    setTimeout({\n                                        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                            document.body.removeChild(iframe);\n                                        }\n                                    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 1000);\n                                }\n                            }\n                        }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 500);\n                    }\n                }\n            })[\"SalesFormPanel.useCallback[handlePrintReceipt]\"];\n            // Set the iframe source to trigger the onload event\n            iframe.src = \"about:blank\";\n        }\n    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], [\n        receiptUrl,\n        hasPrinted\n    ]);\n    // Effect to automatically print receipt when modal is shown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (receiptPreviewVisible && receiptUrl && !hasPrinted) {\n                // Add a small delay to ensure the receipt image is loaded\n                const timer = setTimeout({\n                    \"SalesFormPanel.useEffect.timer\": ()=>{\n                        handlePrintReceipt(); // This now handles the hasPrinted state internally\n                    }\n                }[\"SalesFormPanel.useEffect.timer\"], 800);\n                return ({\n                    \"SalesFormPanel.useEffect\": ()=>clearTimeout(timer)\n                })[\"SalesFormPanel.useEffect\"];\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        receiptPreviewVisible,\n        receiptUrl,\n        hasPrinted,\n        handlePrintReceipt\n    ]);\n    // Handle form submission\n    const handleSubmit = async ()=>{\n        try {\n            var _userStoresData_data;\n            if (items.length === 0) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"Please add at least one item to the sale\");\n                return;\n            }\n            // Validate form fields\n            const values = await form.validateFields();\n            // Check if store is selected\n            if (!selectedStore) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", \"No store information available. Please set up your store in your profile settings.\");\n                return;\n            }\n            // Set loading state for receipt generation\n            setIsGeneratingReceipt(true);\n            // Get store information for receipt\n            const storeInfo = selectedStore || (userStoresData === null || userStoresData === void 0 ? void 0 : (_userStoresData_data = userStoresData.data) === null || _userStoresData_data === void 0 ? void 0 : _userStoresData_data.find((store)=>store.id === values.storeId)) || {\n                name: \"POS System\"\n            };\n            // Generate receipt HTML\n            const receiptHTML = (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_9__.generateReceiptHTML)({\n                id: Date.now(),\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                transactionDate: new Date().toISOString(),\n                items: items.map((item)=>({\n                        productName: item.productName,\n                        quantity: item.quantity,\n                        price: item.price\n                    }))\n            }, storeInfo);\n            // Generate receipt image and get URL\n            let receiptUrl = \"https://receipt.example.com/placeholder\";\n            try {\n                receiptUrl = await (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_9__.generateReceiptImage)(receiptHTML);\n            } catch (error) {\n                console.error(\"Failed to generate receipt image:\", error);\n            // Continue with placeholder URL if image generation fails\n            }\n            const saleData = {\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                items: items.map((item)=>({\n                        productId: item.productId,\n                        quantity: item.quantity,\n                        price: item.price\n                    })),\n                receiptUrl,\n                storeId: selectedStore === null || selectedStore === void 0 ? void 0 : selectedStore.id\n            };\n            const response = await createSale(saleData).unwrap();\n            if (response.success) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"success\", \"Sale created successfully\");\n                // Store the receipt URL for preview\n                setReceiptUrl(receiptUrl);\n                // Show receipt preview modal and offer print option\n                setReceiptPreviewVisible(true);\n                // Refresh product data to get updated stock quantities\n                refetchProducts();\n                // Trigger the success callback to refresh the list WITHOUT closing the panel\n                setTimeout(()=>{\n                    if (onSuccess) {\n                        // Call refetch directly instead of closing the panel\n                        refetchProducts();\n                    }\n                }, 300);\n            // Keep the panel open until the user explicitly closes it\n            // This ensures the receipt modal stays visible\n            } else {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", response.message || \"Failed to create sale\");\n            }\n        } catch (error) {\n            var _error_data;\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_8__.showMessage)(\"error\", ((_error_data = error.data) === null || _error_data === void 0 ? void 0 : _error_data.message) || \"An error occurred while creating the sale\");\n        } finally{\n            setIsGeneratingReceipt(false);\n        }\n    };\n    // Debug log to check items state when rendering\n    console.log(\"Rendering with items:\", items);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        title: \"Point of Sale\",\n        isOpen: isOpen,\n        onClose: onClose,\n        width: \"95%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sales-form min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between border-b border-slate-200 bg-white/80 backdrop-blur-sm px-6 py-4 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"text-xl text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"m-0 text-xl font-bold text-slate-800\",\n                                                children: \"New Transaction\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-slate-500 m-0\",\n                                                children: \"Point of Sale System\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-slate-500 m-0\",\n                                        children: \"Total Amount\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent\",\n                                        children: [\n                                            \"GHS \",\n                                            totalAmount.toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 528,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-6 lg:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 rounded-2xl border border-slate-200 bg-white/70 backdrop-blur-sm p-6 shadow-xl shadow-slate-200/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg mr-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"text-sm text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 556,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-bold text-slate-800 m-0\",\n                                                                            children: \"Product Selection\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-slate-500 m-0\",\n                                                                            children: \"Choose products for this transaction\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 562,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-sm text-slate-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-1 text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \" Required fields\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    form: productForm,\n                                                    layout: \"vertical\",\n                                                    initialValues: {\n                                                        quantity: 1\n                                                    },\n                                                    className: \"product-form\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 gap-6 md:grid-cols-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"md:col-span-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Item, {\n                                                                        name: \"productId\",\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-slate-700 font-medium\",\n                                                                            children: [\n                                                                                \"Product \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-500\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 584,\n                                                                                    columnNumber: 37\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 583,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        className: \"mb-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            showSearch: true,\n                                                                            placeholder: isLoadingProducts ? \"Loading products...\" : \"🔍 Search and select a product...\",\n                                                                            optionFilterProp: \"children\",\n                                                                            loading: isLoadingProducts,\n                                                                            disabled: isLoadingProducts,\n                                                                            onChange: (value)=>{\n                                                                                var _productsData_data;\n                                                                                const product = productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products.find((p)=>p.id === value);\n                                                                                console.log(\"Selected product:\", product);\n                                                                                if (product) {\n                                                                                    // Make a deep copy to avoid reference issues\n                                                                                    setSelectedProduct({\n                                                                                        ...product,\n                                                                                        // Ensure price is properly formatted\n                                                                                        price: typeof product.price === \"string\" ? product.price : String(product.price)\n                                                                                    });\n                                                                                } else {\n                                                                                    setSelectedProduct(null);\n                                                                                }\n                                                                            },\n                                                                            onSearch: setSearchTerm,\n                                                                            filterOption: false,\n                                                                            className: \"modern-select\",\n                                                                            size: \"large\",\n                                                                            suffixIcon: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                spin: true,\n                                                                                className: \"text-blue-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 624,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-center bg-gradient-to-r mr-3 mt-4\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"text-slate-400 \"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 627,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 626,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            notFoundContent: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-center py-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        size: \"small\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 634,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"ml-2 text-slate-500\",\n                                                                                        children: \"Loading products...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 635,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 633,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center py-4 text-slate-500\",\n                                                                                children: \"No products found\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 638,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            children: productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : (_productsData_data_products = _productsData_data.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"].Option, {\n                                                                                    value: product.id,\n                                                                                    disabled: product.stockQuantity <= 0,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"font-medium text-slate-800\",\n                                                                                                        children: product.name\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 652,\n                                                                                                        columnNumber: 35\n                                                                                                    }, undefined),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-sm text-slate-500\",\n                                                                                                        children: [\n                                                                                                            \"GHS \",\n                                                                                                            Number(product.price).toFixed(2)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 653,\n                                                                                                        columnNumber: 35\n                                                                                                    }, undefined)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                lineNumber: 651,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-sm px-2 py-1 rounded-full \".concat(product.stockQuantity <= 0 ? 'bg-red-100 text-red-600' : product.stockQuantity <= 5 ? 'bg-yellow-100 text-yellow-600' : 'bg-green-100 text-green-600'),\n                                                                                                children: product.stockQuantity <= 0 ? \"Out of Stock\" : \"Stock: \".concat(product.stockQuantity)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                lineNumber: 657,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 650,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, product.id, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 645,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 589,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 580,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 579,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Item, {\n                                                                        name: \"quantity\",\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-slate-700 font-medium\",\n                                                                            children: [\n                                                                                \"Quantity \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-500\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 680,\n                                                                                    columnNumber: 38\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 679,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        className: \"mb-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            min: 1,\n                                                                            value: quantity,\n                                                                            onChange: (value)=>setQuantity(value || 1),\n                                                                            style: {\n                                                                                width: \"100%\"\n                                                                            },\n                                                                            className: \"modern-input\",\n                                                                            size: \"large\",\n                                                                            placeholder: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 685,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 676,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 675,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4 space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    type: \"primary\",\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 701,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    onClick: handleAddItem,\n                                                                    className: \"h-14 w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 border-0 rounded-xl font-semibold text-base shadow-lg shadow-blue-200 transition-all duration-200 hover:shadow-xl hover:shadow-blue-300 hover:-translate-y-0.5\",\n                                                                    disabled: !selectedProduct,\n                                                                    size: \"large\",\n                                                                    children: \"Add to Cart\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 711,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    onClick: openScanner,\n                                                                    loading: isScannerLoading,\n                                                                    className: \"h-12 w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 border-0 rounded-xl font-semibold text-base shadow-lg shadow-purple-200 transition-all duration-200 hover:shadow-xl hover:shadow-purple-300 hover:-translate-y-0.5 text-white\",\n                                                                    size: \"large\",\n                                                                    children: isScannerLoading ? 'Searching...' : 'Scan Barcode'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 710,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 698,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 rounded-2xl border border-slate-200 bg-white/70 backdrop-blur-sm p-6 shadow-xl shadow-slate-200/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"text-sm text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 726,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-bold text-slate-800 m-0\",\n                                                                    children: \"Cart Items\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-500 m-0\",\n                                                                    children: \"Items added to this transaction\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 731,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 729,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-[400px] overflow-x-auto overflow-y-auto\",\n                                                    children: items.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-2xl border border-slate-200 bg-gradient-to-br from-slate-50 to-blue-50 p-12 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center w-16 h-16 bg-gradient-to-r from-slate-400 to-slate-500 rounded-full mx-auto mb-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"text-2xl text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-slate-700 mb-2\",\n                                                                children: \"Cart is Empty\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-slate-500\",\n                                                                children: \"Add products to start building your transaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 741,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-hidden rounded-2xl border border-slate-200 bg-white shadow-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-slate-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-12 gap-4 text-sm font-semibold text-slate-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-5\",\n                                                                            children: \"Product\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 747,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 text-center\",\n                                                                            children: \"Qty\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 748,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 text-right\",\n                                                                            children: \"Price\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 749,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 text-right\",\n                                                                            children: \"Subtotal\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 750,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-1 text-center\",\n                                                                            children: \"Action\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 751,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 746,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 745,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"divide-y divide-slate-100\",\n                                                                children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"px-6 py-4 hover:bg-slate-50 transition-colors duration-150\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-12 gap-4 items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-5\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-semibold text-slate-800\",\n                                                                                        children: item.productName || \"Unknown Product\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 762,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 761,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-2 text-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"inline-flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full text-sm font-medium\",\n                                                                                        children: item.quantity\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 767,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 766,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-2 text-right text-slate-600 font-medium\",\n                                                                                    children: [\n                                                                                        \"GHS \",\n                                                                                        Number(item.price).toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 771,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-2 text-right font-bold text-slate-800\",\n                                                                                    children: [\n                                                                                        \"GHS \",\n                                                                                        (Number(item.price) * item.quantity).toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 774,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"col-span-1 text-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 779,\n                                                                                            columnNumber: 41\n                                                                                        }, void 0),\n                                                                                        onClick: ()=>handleRemoveItem(index),\n                                                                                        type: \"text\",\n                                                                                        danger: true,\n                                                                                        className: \"text-red-500 hover:bg-red-50 hover:text-red-600 rounded-lg\",\n                                                                                        size: \"small\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 778,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 777,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 760,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, \"\".concat(item.productId, \"-\").concat(index), false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 756,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-emerald-50 to-green-50 px-6 py-4 border-t border-slate-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-12 gap-4 items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-9 text-right\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg font-bold text-slate-800\",\n                                                                                children: \"Total:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 794,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 793,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-3 text-right\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    totalAmount.toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 797,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 796,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 792,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 791,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sticky top-4 rounded-2xl border border-slate-200 bg-white/70 backdrop-blur-sm p-6 shadow-xl shadow-slate-200/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg mr-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"text-sm text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 814,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-bold text-slate-800 m-0\",\n                                                                children: \"Checkout\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 817,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-slate-500 m-0\",\n                                                                children: \"Complete your transaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 818,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 816,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                form: form,\n                                                layout: \"vertical\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 rounded-xl border border-slate-200 bg-gradient-to-br from-slate-50 to-blue-50 p-5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-slate-600\",\n                                                                        children: \"Items:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 825,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full text-sm font-bold\",\n                                                                        children: items.length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 826,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 824,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-slate-600\",\n                                                                        children: \"Total Quantity:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 831,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-800 rounded-full text-sm font-bold\",\n                                                                        children: items.reduce((sum, item)=>sum + item.quantity, 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 832,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 830,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-slate-600\",\n                                                                        children: \"Subtotal:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 837,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-semibold text-slate-800\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 838,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 836,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"my-4 border-t border-slate-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-bold text-slate-800\",\n                                                                        children: \"Total:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 844,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 845,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 843,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 823,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    selectedStore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"mb-3 block text-slate-700 font-medium\",\n                                                                children: \"Store\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 854,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center rounded-xl border border-slate-200 bg-gradient-to-r from-blue-50 to-indigo-50 p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg mr-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"text-sm text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 857,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 856,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-semibold text-slate-800\",\n                                                                        children: selectedStore.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 859,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"hidden\",\n                                                                        name: \"storeId\",\n                                                                        value: selectedStore.id\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 862,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 855,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 853,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 rounded-xl border border-orange-200 bg-gradient-to-r from-orange-50 to-yellow-50 p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-lg mr-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"text-sm text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 873,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 872,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-orange-800 m-0\",\n                                                                            children: \"Store Setup Required\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 876,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-orange-600 m-0\",\n                                                                            children: \"Please set up your store in profile settings.\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 877,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 875,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 871,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 870,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Item, {\n                                                        name: \"paymentMethod\",\n                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-700 font-medium\",\n                                                            children: [\n                                                                \"Payment Method \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 889,\n                                                                    columnNumber: 40\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 888,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        rules: [\n                                                            {\n                                                                required: true,\n                                                                message: \"Please select a payment method\"\n                                                            }\n                                                        ],\n                                                        initialValue: \"cash\",\n                                                        className: \"mb-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"modern-select\",\n                                                            size: \"large\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"].Option, {\n                                                                    value: \"cash\",\n                                                                    children: \"\\uD83D\\uDCB5 Cash\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 902,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"].Option, {\n                                                                    value: \"card\",\n                                                                    children: \"\\uD83D\\uDCB3 Card\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 905,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"].Option, {\n                                                                    value: \"mobile_money\",\n                                                                    children: \"\\uD83D\\uDCF1 Mobile Money\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 908,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 901,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 885,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-8 space-y-4\",\n                                                        children: [\n                                                            receiptPreviewVisible ? // Show \"Done\" button when receipt is visible\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 919,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                onClick: ()=>{\n                                                                    // Close the modal and reset the receipt state\n                                                                    setReceiptPreviewVisible(false);\n                                                                    setReceiptUrl(null);\n                                                                    setHasPrinted(false);\n                                                                    // Reset the forms to start a new sale\n                                                                    form.resetFields();\n                                                                    productForm.resetFields();\n                                                                    setItems([]);\n                                                                    setSelectedProduct(null);\n                                                                    setQuantity(1);\n                                                                    setTotalAmount(0);\n                                                                },\n                                                                className: \"h-14 w-full bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 border-0 rounded-xl font-semibold text-base shadow-lg shadow-emerald-200 transition-all duration-200 hover:shadow-xl hover:shadow-emerald-300 hover:-translate-y-0.5\",\n                                                                size: \"large\",\n                                                                children: \"Start New Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 917,\n                                                                columnNumber: 23\n                                                            }, undefined) : // Show \"Complete Sale\" button when creating a sale\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 943,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                onClick: handleSubmit,\n                                                                loading: isSubmitting || isGeneratingReceipt,\n                                                                disabled: items.length === 0,\n                                                                className: \"h-14 w-full bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 border-0 rounded-xl font-semibold text-base shadow-lg shadow-emerald-200 transition-all duration-200 hover:shadow-xl hover:shadow-emerald-300 hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:transform-none disabled:hover:shadow-lg\",\n                                                                size: \"large\",\n                                                                children: isGeneratingReceipt ? \"Generating Receipt\" : \"Complete Sale\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 941,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                onClick: onClose,\n                                                                className: \"h-12 w-full border-slate-300 bg-slate-100 text-slate-700 hover:bg-slate-200 rounded-xl font-medium transition-all duration-200 hover:shadow-md\",\n                                                                size: \"large\",\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 956,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 822,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 810,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 549,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 548,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 526,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-gray-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            className: \"mr-2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 975,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Receipt Preview\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 976,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 974,\n                    columnNumber: 11\n                }, void 0),\n                open: receiptPreviewVisible,\n                onCancel: ()=>{\n                    // Close the modal and reset the receipt state\n                    setReceiptPreviewVisible(false);\n                    setReceiptUrl(null);\n                    setHasPrinted(false);\n                    // Reset the forms to start a new sale\n                    form.resetFields();\n                    productForm.resetFields();\n                    setItems([]);\n                    setSelectedProduct(null);\n                    setQuantity(1);\n                    setTotalAmount(0);\n                },\n                width: 500,\n                centered: true,\n                className: \"receipt-preview-modal\",\n                footer: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        onClick: ()=>{\n                            // Close the modal and reset the receipt state\n                            setReceiptPreviewVisible(false);\n                            setReceiptUrl(null);\n                            setHasPrinted(false);\n                            // Reset the forms to start a new sale\n                            form.resetFields();\n                            productForm.resetFields();\n                            setItems([]);\n                            setSelectedProduct(null);\n                            setQuantity(1);\n                            setTotalAmount(0);\n                        },\n                        className: \"border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200\",\n                        children: \"Close & New Sale\"\n                    }, \"close\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 998,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        type: \"primary\",\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ScanOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1021,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: ()=>{\n                            // If already printed once, reset the flag to allow printing again\n                            if (hasPrinted) {\n                                setHasPrinted(false);\n                            }\n                            handlePrintReceipt();\n                        },\n                        className: \"bg-blue-600 hover:bg-blue-700\",\n                        children: hasPrinted ? \"Print Again\" : \"Print Receipt\"\n                    }, \"print\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1018,\n                        columnNumber: 11\n                    }, void 0)\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: receiptUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"receipt-image-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                            src: receiptUrl,\n                            alt: \"Receipt\",\n                            className: \"receipt-image\",\n                            style: {\n                                maxWidth: \"100%\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1038,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1037,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-64 items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            size: \"large\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1047,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1046,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 1035,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 972,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BarcodeScanner_BarcodeScanner__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isScannerOpen,\n                onClose: closeScanner,\n                onScan: handleBarcodeScanned,\n                title: \"Scan Product Barcode\"\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 1054,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n        lineNumber: 520,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SalesFormPanel, \"cSW9e09S5a5AlxqCiHM5E8H+s9k=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].useForm,\n        _hooks_useBarcodeScanner__WEBPACK_IMPORTED_MODULE_7__.useBarcodeScanner,\n        _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery,\n        _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation\n    ];\n});\n_c = SalesFormPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SalesFormPanel);\nvar _c;\n$RefreshReg$(_c, \"SalesFormPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx\n"));

/***/ })

});