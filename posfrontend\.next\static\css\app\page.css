/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/components/Auth/auth.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/* Custom styles for auth components */

/* Override Ant Design form validation error styles */
.ant-form-item-explain-error {
  color: #ff4d4f !important;
  margin-top: 4px;
}

/* Style for input placeholders */
.ant-input::-moz-placeholder, .ant-input-password input::-moz-placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}
.ant-input::placeholder,
.ant-input-password input::placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* Style for input text */
.ant-input,
.ant-input-password input {
  color: rgba(0, 0, 0, 0.85) !important;
}

/* Style for input icons */
.ant-input-password-icon {
  color: rgba(0, 0, 0, 0.45) !important;
}

/* Style for form labels */
.ant-form-item-label > label {
  color: rgba(0, 0, 0, 0.85) !important;
}

