{"version": 3, "sources": ["../../../../src/server/dev/turbopack/manifest-loader.ts"], "sourcesContent": ["import type {\n  EdgeFunctionDefinition,\n  MiddlewareManifest,\n} from '../../../build/webpack/plugins/middleware-plugin'\nimport type { StatsAsset, StatsChunk, StatsChunkGroup, StatsModule, StatsCompilation as WebpackStats } from 'webpack'\nimport type { BuildManifest } from '../../get-page-files'\nimport type { AppBuildManifest } from '../../../build/webpack/plugins/app-build-manifest-plugin'\nimport type { PagesManifest } from '../../../build/webpack/plugins/pages-manifest-plugin'\nimport { pathToRegexp } from 'next/dist/compiled/path-to-regexp'\nimport type { ActionManifest } from '../../../build/webpack/plugins/flight-client-entry-plugin'\nimport type { NextFontManifest } from '../../../build/webpack/plugins/next-font-manifest-plugin'\nimport type { LoadableManifest } from '../../load-components'\nimport {\n  APP_BUILD_MANIFEST,\n  APP_PATHS_MANIFEST,\n  BUILD_MANIFEST,\n  INTERCEPTION_ROUTE_REWRITE_MANIFEST,\n  MIDDLEWARE_BUILD_MANIFEST,\n  MIDDLEWARE_MANIFEST,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  NEXT_FONT_MANIFEST,\n  PAGES_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n  WEBPACK_STATS,\n} from '../../../shared/lib/constants'\nimport { join, posix } from 'path'\nimport { readFile } from 'fs/promises'\nimport type { SetupOpts } from '../../lib/router-utils/setup-dev-bundler'\nimport { deleteCache } from '../require-cache'\nimport { writeFileAtomic } from '../../../lib/fs/write-atomic'\nimport { isInterceptionRouteRewrite } from '../../../lib/generate-interception-routes-rewrites'\nimport {\n  type ClientBuildManifest,\n  normalizeRewritesForBuildManifest,\n  srcEmptySsgManifest,\n  processRoute,\n} from '../../../build/webpack/plugins/build-manifest-plugin'\nimport type { Entrypoints } from './types'\nimport getAssetPathFromRoute from '../../../shared/lib/router/utils/get-asset-path-from-route'\nimport { getEntryKey, type EntryKey } from './entry-key'\nimport type { CustomRoutes } from '../../../lib/load-custom-routes'\nimport { getSortedRoutes } from '../../../shared/lib/router/utils'\nimport { existsSync } from 'fs'\nimport { addMetadataIdToRoute, addRouteSuffix, removeRouteSuffix } from '../turbopack-utils'\nimport { tryToParsePath } from '../../../lib/try-to-parse-path'\n\ninterface InstrumentationDefinition {\n  files: string[]\n  name: 'instrumentation'\n}\n\ntype TurbopackMiddlewareManifest = MiddlewareManifest & {\n  instrumentation?: InstrumentationDefinition\n}\n\nconst getManifestPath = (page: string, distDir: string, name: string, type: string) => {\n  let manifestPath = posix.join(\n    distDir,\n    `server`,\n    type,\n    type === 'middleware' || type === 'instrumentation'\n      ? ''\n      : type === 'app'\n        ? page\n        : getAssetPathFromRoute(page),\n    name\n  )\n  return manifestPath\n}\n\nasync function readPartialManifest<T>(\n  distDir: string,\n  name:\n    | typeof MIDDLEWARE_MANIFEST\n    | typeof BUILD_MANIFEST\n    | typeof APP_BUILD_MANIFEST\n    | typeof PAGES_MANIFEST\n    | typeof WEBPACK_STATS\n    | typeof APP_PATHS_MANIFEST\n    | `${typeof SERVER_REFERENCE_MANIFEST}.json`\n    | `${typeof NEXT_FONT_MANIFEST}.json`\n    | typeof REACT_LOADABLE_MANIFEST,\n  pageName: string,\n  type: 'pages' | 'app' | 'middleware' | 'instrumentation' = 'pages'\n): Promise<T> {\n  const page = pageName\n  const isSitemapRoute = /[\\\\/]sitemap(.xml)?\\/route$/.test(page)\n  let manifestPath = getManifestPath(page, distDir, name, type)\n\n  // Check the ambiguity of /sitemap and /sitemap.xml\n  if (isSitemapRoute && !existsSync(manifestPath)) {\n    manifestPath = getManifestPath(pageName.replace(/\\/sitemap\\/route$/, '/sitemap.xml/route'), distDir, name, type)\n  }\n  // existsSync is faster than using the async version\n  if(!existsSync(manifestPath) && page.endsWith('/route')) {\n    // TODO: Improve implementation of metadata routes, currently it requires this extra check for the variants of the files that can be written.\n    let metadataPage = addRouteSuffix(addMetadataIdToRoute(removeRouteSuffix(page)))\n    manifestPath = getManifestPath(metadataPage, distDir, name, type)\n  }\n  return JSON.parse(await readFile(posix.join(manifestPath), 'utf-8')) as T\n}\n\nexport class TurbopackManifestLoader {\n  private actionManifests: Map<EntryKey, ActionManifest> = new Map()\n  private appBuildManifests: Map<EntryKey, AppBuildManifest> = new Map()\n  private appPathsManifests: Map<EntryKey, PagesManifest> = new Map()\n  private buildManifests: Map<EntryKey, BuildManifest> = new Map()\n  private fontManifests: Map<EntryKey, NextFontManifest> = new Map()\n  private loadableManifests: Map<EntryKey, LoadableManifest> = new Map()\n  private middlewareManifests: Map<EntryKey, TurbopackMiddlewareManifest> =\n    new Map()\n  private pagesManifests: Map<string, PagesManifest> = new Map()\n  private webpackStats: Map<EntryKey, WebpackStats> = new Map()\n  private encryptionKey: string\n\n  private readonly distDir: string\n  private readonly buildId: string\n\n  constructor({\n    distDir,\n    buildId,\n    encryptionKey,\n  }: {\n    buildId: string\n    distDir: string\n    encryptionKey: string\n  }) {\n    this.distDir = distDir\n    this.buildId = buildId\n    this.encryptionKey = encryptionKey\n  }\n\n  delete(key: EntryKey) {\n    this.actionManifests.delete(key)\n    this.appBuildManifests.delete(key)\n    this.appPathsManifests.delete(key)\n    this.buildManifests.delete(key)\n    this.fontManifests.delete(key)\n    this.loadableManifests.delete(key)\n    this.middlewareManifests.delete(key)\n    this.pagesManifests.delete(key)\n    this.webpackStats.delete(key)\n  }\n\n  async loadActionManifest(pageName: string): Promise<void> {\n    this.actionManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        `${SERVER_REFERENCE_MANIFEST}.json`,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private async mergeActionManifests(manifests: Iterable<ActionManifest>) {\n    type ActionEntries = ActionManifest['edge' | 'node']\n    const manifest: ActionManifest = {\n      node: {},\n      edge: {},\n      encryptionKey: this.encryptionKey,\n    }\n\n    function mergeActionIds(\n      actionEntries: ActionEntries,\n      other: ActionEntries\n    ): void {\n      for (const key in other) {\n        const action = (actionEntries[key] ??= {\n          workers: {},\n          layer: {},\n        })\n        Object.assign(action.workers, other[key].workers)\n        Object.assign(action.layer, other[key].layer)\n      }\n    }\n\n    for (const m of manifests) {\n      mergeActionIds(manifest.node, m.node)\n      mergeActionIds(manifest.edge, m.edge)\n    }\n\n    return manifest\n  }\n\n  private async writeActionManifest(): Promise<void> {\n    const actionManifest = await this.mergeActionManifests(\n      this.actionManifests.values()\n    )\n    const actionManifestJsonPath = join(\n      this.distDir,\n      'server',\n      `${SERVER_REFERENCE_MANIFEST}.json`\n    )\n    const actionManifestJsPath = join(\n      this.distDir,\n      'server',\n      `${SERVER_REFERENCE_MANIFEST}.js`\n    )\n    const json = JSON.stringify(actionManifest, null, 2)\n    deleteCache(actionManifestJsonPath)\n    deleteCache(actionManifestJsPath)\n    await writeFileAtomic(actionManifestJsonPath, json)\n    await writeFileAtomic(\n      actionManifestJsPath,\n      `self.__RSC_SERVER_MANIFEST=${JSON.stringify(json)}`\n    )\n  }\n\n  async loadAppBuildManifest(pageName: string): Promise<void> {\n    this.appBuildManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        APP_BUILD_MANIFEST,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private mergeAppBuildManifests(manifests: Iterable<AppBuildManifest>) {\n    const manifest: AppBuildManifest = {\n      pages: {},\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.pages, m.pages)\n    }\n    return manifest\n  }\n\n  private async writeAppBuildManifest(): Promise<void> {\n    const appBuildManifest = this.mergeAppBuildManifests(\n      this.appBuildManifests.values()\n    )\n    const appBuildManifestPath = join(this.distDir, APP_BUILD_MANIFEST)\n    deleteCache(appBuildManifestPath)\n    await writeFileAtomic(\n      appBuildManifestPath,\n      JSON.stringify(appBuildManifest, null, 2)\n    )\n  }\n\n  async loadAppPathsManifest(pageName: string): Promise<void> {\n    this.appPathsManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        APP_PATHS_MANIFEST,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private async writeAppPathsManifest(): Promise<void> {\n    const appPathsManifest = this.mergePagesManifests(\n      this.appPathsManifests.values()\n    )\n    const appPathsManifestPath = join(\n      this.distDir,\n      'server',\n      APP_PATHS_MANIFEST\n    )\n    deleteCache(appPathsManifestPath)\n    await writeFileAtomic(\n      appPathsManifestPath,\n      JSON.stringify(appPathsManifest, null, 2)\n    )\n  }\n\n  private async writeWebpackStats(): Promise<void> {\n    const webpackStats = this.mergeWebpackStats(\n      this.webpackStats.values()\n    )\n    const path = join(\n      this.distDir,\n      'server',\n      WEBPACK_STATS\n    )\n    deleteCache(path)\n    await writeFileAtomic(\n      path,\n      JSON.stringify(webpackStats, null, 2)\n    )\n  }\n\n  async loadBuildManifest(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.buildManifests.set(\n      getEntryKey(type, 'server', pageName),\n      await readPartialManifest(this.distDir, BUILD_MANIFEST, pageName, type)\n    )\n  }\n\n  async loadWebpackStats(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.webpackStats.set(\n      getEntryKey(type, 'client', pageName),\n      await readPartialManifest(this.distDir, WEBPACK_STATS, pageName, type)\n    )\n  }\n\n  private mergeWebpackStats(statsFiles: Iterable<WebpackStats>): WebpackStats {\n    const entrypoints: Record<string, StatsChunkGroup> = {};\n    const assets: Map<string, StatsAsset> = new Map()\n    const chunks: Map<string, StatsChunk> = new Map()\n    const modules: Map<string | number, StatsModule> = new Map()\n\n    for (const statsFile of statsFiles) {\n      if (statsFile.entrypoints) {\n        for (const [k, v] of Object.entries(\n          statsFile.entrypoints\n        )) {\n          if (!entrypoints[k]) {\n            entrypoints[k] = v\n          }\n        }\n      }\n\n      if (statsFile.assets) {\n        for (const asset of statsFile.assets) {\n          if (!assets.has(asset.name)) {\n            assets.set(asset.name, asset)\n          }\n        }\n      }\n\n      if (statsFile.chunks) {\n        for (const chunk of statsFile.chunks) {\n          if (!chunks.has(chunk.name)) {\n            chunks.set(chunk.name, chunk)\n          }\n        }\n      }\n\n      if (statsFile.modules) {\n        for (const module of statsFile.modules) {\n          const id = module.id;\n          if (id != null) {\n            // Merge the chunk list for the module. This can vary across endpoints.\n            const existing = modules.get(id);\n            if (existing == null) {\n              modules.set(id, module)\n            } else if (module.chunks != null && existing.chunks != null) {\n              for (const chunk of module.chunks) {\n                if (!existing.chunks.includes(chunk)) {\n                  existing.chunks.push(chunk)\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return {\n      entrypoints,\n      assets: [...assets.values()],\n      chunks: [...chunks.values()],\n      modules: [...modules.values()],\n    }\n  }\n\n  private mergeBuildManifests(manifests: Iterable<BuildManifest>) {\n    const manifest: Partial<BuildManifest> & Pick<BuildManifest, 'pages'> = {\n      pages: {\n        '/_app': [],\n      },\n      // Something in next.js depends on these to exist even for app dir rendering\n      devFiles: [],\n      ampDevFiles: [],\n      polyfillFiles: [],\n      lowPriorityFiles: [\n        `static/${this.buildId}/_ssgManifest.js`,\n        `static/${this.buildId}/_buildManifest.js`,\n      ],\n      rootMainFiles: [],\n      ampFirstPages: [],\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.pages, m.pages)\n      if (m.rootMainFiles.length) manifest.rootMainFiles = m.rootMainFiles\n      // polyfillFiles should always be the same, so we can overwrite instead of actually merging\n      if (m.polyfillFiles.length) manifest.polyfillFiles = m.polyfillFiles\n    }\n    return manifest\n  }\n\n  private async writeBuildManifest(\n    entrypoints: Entrypoints,\n    devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined,\n    productionRewrites: CustomRoutes['rewrites'] | undefined\n  ): Promise<void> {\n    const rewrites = productionRewrites ?? {\n      ...devRewrites,\n      beforeFiles: (devRewrites?.beforeFiles ?? []).map(processRoute),\n      afterFiles: (devRewrites?.afterFiles ?? []).map(processRoute),\n      fallback: (devRewrites?.fallback ?? []).map(processRoute),\n    }\n    const buildManifest = this.mergeBuildManifests(this.buildManifests.values())\n    const buildManifestPath = join(this.distDir, BUILD_MANIFEST)\n    const middlewareBuildManifestPath = join(\n      this.distDir,\n      'server',\n      `${MIDDLEWARE_BUILD_MANIFEST}.js`\n    )\n    const interceptionRewriteManifestPath = join(\n      this.distDir,\n      'server',\n      `${INTERCEPTION_ROUTE_REWRITE_MANIFEST}.js`\n    )\n    deleteCache(buildManifestPath)\n    deleteCache(middlewareBuildManifestPath)\n    deleteCache(interceptionRewriteManifestPath)\n    await writeFileAtomic(\n      buildManifestPath,\n      JSON.stringify(buildManifest, null, 2)\n    )\n    await writeFileAtomic(\n      middlewareBuildManifestPath,\n      `self.__BUILD_MANIFEST=${JSON.stringify(buildManifest)};`\n    )\n\n    const interceptionRewrites = JSON.stringify(\n      rewrites.beforeFiles.filter(isInterceptionRouteRewrite)\n    )\n\n    await writeFileAtomic(\n      interceptionRewriteManifestPath,\n      `self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST=${JSON.stringify(\n        interceptionRewrites\n      )};`\n    )\n\n    const pagesKeys = [...entrypoints.page.keys()]\n    if (entrypoints.global.app) {\n      pagesKeys.push('/_app')\n    }\n    if (entrypoints.global.error) {\n      pagesKeys.push('/_error')\n    }\n\n    const sortedPageKeys = getSortedRoutes(pagesKeys)\n    const content: ClientBuildManifest = {\n      __rewrites: normalizeRewritesForBuildManifest(rewrites) as any,\n      ...Object.fromEntries(\n        sortedPageKeys.map((pathname) => [\n          pathname,\n          [`static/chunks/pages${pathname === '/' ? '/index' : pathname}.js`],\n        ])\n      ),\n      sortedPages: sortedPageKeys,\n    }\n    const buildManifestJs = `self.__BUILD_MANIFEST = ${JSON.stringify(\n      content\n    )};self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()`\n    await writeFileAtomic(\n      join(this.distDir, 'static', this.buildId, '_buildManifest.js'),\n      buildManifestJs\n    )\n    await writeFileAtomic(\n      join(this.distDir, 'static', this.buildId, '_ssgManifest.js'),\n      srcEmptySsgManifest\n    )\n  }\n\n  private async writeClientMiddlewareManifest(): Promise<void> {\n    const middlewareManifest = this.mergeMiddlewareManifests(\n      this.middlewareManifests.values()\n    )\n\n    const matchers = middlewareManifest?.middleware['/']?.matchers || []\n\n    const clientMiddlewareManifestPath = join(\n      this.distDir,\n      'static',\n      this.buildId,\n      `${TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST}`\n    )\n    deleteCache(clientMiddlewareManifestPath)\n    await writeFileAtomic(\n      clientMiddlewareManifestPath,\n      JSON.stringify(matchers, null, 2)\n    )\n  }\n\n  private async writeFallbackBuildManifest(): Promise<void> {\n    const fallbackBuildManifest = this.mergeBuildManifests(\n      [\n        this.buildManifests.get(getEntryKey('pages', 'server', '_app')),\n        this.buildManifests.get(getEntryKey('pages', 'server', '_error')),\n      ].filter(Boolean) as BuildManifest[]\n    )\n    const fallbackBuildManifestPath = join(\n      this.distDir,\n      `fallback-${BUILD_MANIFEST}`\n    )\n    deleteCache(fallbackBuildManifestPath)\n    await writeFileAtomic(\n      fallbackBuildManifestPath,\n      JSON.stringify(fallbackBuildManifest, null, 2)\n    )\n  }\n\n  async loadFontManifest(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.fontManifests.set(\n      getEntryKey(type, 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        `${NEXT_FONT_MANIFEST}.json`,\n        pageName,\n        type\n      )\n    )\n  }\n\n  private mergeFontManifests(manifests: Iterable<NextFontManifest>) {\n    const manifest: NextFontManifest = {\n      app: {},\n      appUsingSizeAdjust: false,\n      pages: {},\n      pagesUsingSizeAdjust: false,\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.app, m.app)\n      Object.assign(manifest.pages, m.pages)\n\n      manifest.appUsingSizeAdjust =\n        manifest.appUsingSizeAdjust || m.appUsingSizeAdjust\n      manifest.pagesUsingSizeAdjust =\n        manifest.pagesUsingSizeAdjust || m.pagesUsingSizeAdjust\n    }\n    return manifest\n  }\n\n  private async writeNextFontManifest(): Promise<void> {\n    const fontManifest = this.mergeFontManifests(this.fontManifests.values())\n    const json = JSON.stringify(fontManifest, null, 2)\n\n    const fontManifestJsonPath = join(\n      this.distDir,\n      'server',\n      `${NEXT_FONT_MANIFEST}.json`\n    )\n    const fontManifestJsPath = join(\n      this.distDir,\n      'server',\n      `${NEXT_FONT_MANIFEST}.js`\n    )\n    deleteCache(fontManifestJsonPath)\n    deleteCache(fontManifestJsPath)\n    await writeFileAtomic(fontManifestJsonPath, json)\n    await writeFileAtomic(\n      fontManifestJsPath,\n      `self.__NEXT_FONT_MANIFEST=${JSON.stringify(json)}`\n    )\n  }\n\n  async loadLoadableManifest(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.loadableManifests.set(\n      getEntryKey(type, 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        REACT_LOADABLE_MANIFEST,\n        pageName,\n        type\n      )\n    )\n  }\n\n  private mergeLoadableManifests(manifests: Iterable<LoadableManifest>) {\n    const manifest: LoadableManifest = {}\n    for (const m of manifests) {\n      Object.assign(manifest, m)\n    }\n    return manifest\n  }\n\n  private async writeLoadableManifest(): Promise<void> {\n    const loadableManifest = this.mergeLoadableManifests(\n      this.loadableManifests.values()\n    )\n    const loadableManifestPath = join(this.distDir, REACT_LOADABLE_MANIFEST)\n    const middlewareloadableManifestPath = join(\n      this.distDir,\n      'server',\n      `${MIDDLEWARE_REACT_LOADABLE_MANIFEST}.js`\n    )\n\n    const json = JSON.stringify(loadableManifest, null, 2)\n\n    deleteCache(loadableManifestPath)\n    deleteCache(middlewareloadableManifestPath)\n    await writeFileAtomic(loadableManifestPath, json)\n    await writeFileAtomic(\n      middlewareloadableManifestPath,\n      `self.__REACT_LOADABLE_MANIFEST=${JSON.stringify(json)}`\n    )\n  }\n\n  async loadMiddlewareManifest(\n    pageName: string,\n    type: 'pages' | 'app' | 'middleware' | 'instrumentation'\n  ): Promise<void> {\n    this.middlewareManifests.set(\n      getEntryKey(\n        type === 'middleware' || type === 'instrumentation' ? 'root' : type,\n        'server',\n        pageName\n      ),\n      await readPartialManifest(\n        this.distDir,\n        MIDDLEWARE_MANIFEST,\n        pageName,\n        type\n      )\n    )\n  }\n\n  getMiddlewareManifest(key: EntryKey) {\n    return this.middlewareManifests.get(key)\n  }\n\n  deleteMiddlewareManifest(key: EntryKey) {\n    return this.middlewareManifests.delete(key)\n  }\n\n  private mergeMiddlewareManifests(\n    manifests: Iterable<TurbopackMiddlewareManifest>\n  ): MiddlewareManifest {\n    const manifest: MiddlewareManifest = {\n      version: 3,\n      middleware: {},\n      sortedMiddleware: [],\n      functions: {},\n    }\n    let instrumentation: InstrumentationDefinition | undefined = undefined\n    for (const m of manifests) {\n      Object.assign(manifest.functions, m.functions)\n      Object.assign(manifest.middleware, m.middleware)\n      if (m.instrumentation) {\n        instrumentation = m.instrumentation\n      }\n    }\n    const updateFunctionDefinition = (\n      fun: EdgeFunctionDefinition\n    ): EdgeFunctionDefinition => {\n      return {\n        ...fun,\n        files: [...(instrumentation?.files ?? []), ...fun.files],\n      }\n    }\n    for (const key of Object.keys(manifest.middleware)) {\n      const value = manifest.middleware[key]\n      manifest.middleware[key] = updateFunctionDefinition(value)\n    }\n    for (const key of Object.keys(manifest.functions)) {\n      const value = manifest.functions[key]\n      manifest.functions[key] = updateFunctionDefinition(value)\n    }\n    for (const fun of Object.values(manifest.functions).concat(\n      Object.values(manifest.middleware)\n    )) {\n      for (const matcher of fun.matchers) {\n        if (!matcher.regexp) {\n          matcher.regexp = pathToRegexp(matcher.originalSource, [], {\n            delimiter: '/',\n            sensitive: false,\n            strict: true,\n          }).source.replaceAll('\\\\/', '/')\n        }\n      }\n    }\n    manifest.sortedMiddleware = Object.keys(manifest.middleware)\n\n    return manifest\n  }\n\n  private async writeMiddlewareManifest(): Promise<void> {\n    const middlewareManifest = this.mergeMiddlewareManifests(\n      this.middlewareManifests.values()\n    )\n\n    // Normalize regexes as it uses path-to-regexp\n    for (const key in middlewareManifest.middleware) {\n      middlewareManifest.middleware[key].matchers.forEach((matcher) => {\n        if (!matcher.regexp.startsWith('^')) {\n          const parsedPage = tryToParsePath(matcher.regexp)\n          if (parsedPage.error || !parsedPage.regexStr) {\n            throw new Error(`Invalid source: ${matcher.regexp}`)\n          }\n          matcher.regexp = parsedPage.regexStr\n        }\n      })\n    }\n\n    const middlewareManifestPath = join(\n      this.distDir,\n      'server',\n      MIDDLEWARE_MANIFEST\n    )\n    deleteCache(middlewareManifestPath)\n    await writeFileAtomic(\n      middlewareManifestPath,\n      JSON.stringify(middlewareManifest, null, 2)\n    )\n  }\n\n  async loadPagesManifest(pageName: string): Promise<void> {\n    this.pagesManifests.set(\n      getEntryKey('pages', 'server', pageName),\n      await readPartialManifest(this.distDir, PAGES_MANIFEST, pageName)\n    )\n  }\n\n  private mergePagesManifests(manifests: Iterable<PagesManifest>) {\n    const manifest: PagesManifest = {}\n    for (const m of manifests) {\n      Object.assign(manifest, m)\n    }\n    return manifest\n  }\n\n  private async writePagesManifest(): Promise<void> {\n    const pagesManifest = this.mergePagesManifests(this.pagesManifests.values())\n    const pagesManifestPath = join(this.distDir, 'server', PAGES_MANIFEST)\n    deleteCache(pagesManifestPath)\n    await writeFileAtomic(\n      pagesManifestPath,\n      JSON.stringify(pagesManifest, null, 2)\n    )\n  }\n\n  async writeManifests({\n    devRewrites,\n    productionRewrites,\n    entrypoints,\n  }: {\n    devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n    productionRewrites: CustomRoutes['rewrites'] | undefined\n    entrypoints: Entrypoints\n  }) {\n    await this.writeActionManifest()\n    await this.writeAppBuildManifest()\n    await this.writeAppPathsManifest()\n    await this.writeBuildManifest(entrypoints, devRewrites, productionRewrites)\n    await this.writeFallbackBuildManifest()\n    await this.writeLoadableManifest()\n    await this.writeMiddlewareManifest()\n    await this.writeClientMiddlewareManifest()\n    await this.writeNextFontManifest()\n    await this.writePagesManifest()\n\n    if (process.env.TURBOPACK_STATS != null) {\n      await this.writeWebpackStats()\n    }\n  }\n}\n"], "names": ["pathToRegexp", "APP_BUILD_MANIFEST", "APP_PATHS_MANIFEST", "BUILD_MANIFEST", "INTERCEPTION_ROUTE_REWRITE_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "NEXT_FONT_MANIFEST", "PAGES_MANIFEST", "REACT_LOADABLE_MANIFEST", "SERVER_REFERENCE_MANIFEST", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "WEBPACK_STATS", "join", "posix", "readFile", "deleteCache", "writeFileAtomic", "isInterceptionRouteRewrite", "normalizeRewritesForBuildManifest", "srcEmptySsgManifest", "processRoute", "getAssetPathFromRoute", "getEntry<PERSON>ey", "getSortedRoutes", "existsSync", "addMetadataIdToRoute", "addRouteSuffix", "removeRouteSuffix", "tryToParsePath", "getManifestPath", "page", "distDir", "name", "type", "manifestPath", "readPartialManifest", "pageName", "isSitemapRoute", "test", "replace", "endsWith", "metadataPage", "JSON", "parse", "TurbopackManifestLoader", "constructor", "buildId", "<PERSON><PERSON><PERSON>", "actionManifests", "Map", "appBuildManifests", "appPathsManifests", "buildManifests", "fontManifests", "loadableManifests", "middlewareManifests", "pagesManifests", "webpackStats", "delete", "key", "loadActionManifest", "set", "mergeActionManifests", "manifests", "manifest", "node", "edge", "mergeActionIds", "actionEntries", "other", "action", "workers", "layer", "Object", "assign", "m", "writeActionManifest", "actionManifest", "values", "actionManifestJsonPath", "actionManifestJsPath", "json", "stringify", "loadAppBuildManifest", "mergeAppBuildManifests", "pages", "writeAppBuildManifest", "appBuildManifest", "appBuildManifestPath", "loadAppPathsManifest", "writeAppPathsManifest", "appPathsManifest", "mergePagesManifests", "appPathsManifestPath", "writeWebpackStats", "mergeWebpackStats", "path", "loadBuildManifest", "loadWebpackStats", "statsFiles", "entrypoints", "assets", "chunks", "modules", "statsFile", "k", "v", "entries", "asset", "has", "chunk", "module", "id", "existing", "get", "includes", "push", "mergeBuildManifests", "devFiles", "ampDevFiles", "polyfillFiles", "lowPriorityFiles", "rootMainFiles", "ampFirstPages", "length", "writeBuildManifest", "devRewrites", "productionRewrites", "rewrites", "beforeFiles", "map", "afterFiles", "fallback", "buildManifest", "buildManifestPath", "middlewareBuildManifestPath", "interceptionRewriteManifestPath", "interceptionRewrites", "filter", "pagesKeys", "keys", "global", "app", "error", "sortedPageKeys", "content", "__rewrites", "fromEntries", "pathname", "sortedPages", "buildManifestJs", "writeClientMiddlewareManifest", "middlewareManifest", "mergeMiddlewareManifests", "matchers", "middleware", "clientMiddlewareManifestPath", "writeFallbackBuildManifest", "fallbackBuildManifest", "Boolean", "fallbackBuildManifestPath", "loadFontManifest", "mergeFontManifests", "appUsingSizeAdjust", "pagesUsingSizeAdjust", "writeNextFontManifest", "fontManifest", "fontManifestJsonPath", "fontManifestJsPath", "loadLoadableManifest", "mergeLoadableManifests", "writeLoadableManifest", "loadableManifest", "loadableManifestPath", "middlewareloadableManifestPath", "loadMiddlewareManifest", "getMiddlewareManifest", "deleteMiddlewareManifest", "version", "sortedMiddleware", "functions", "instrumentation", "undefined", "updateFunctionDefinition", "fun", "files", "value", "concat", "matcher", "regexp", "originalSource", "delimiter", "sensitive", "strict", "source", "replaceAll", "writeMiddlewareManifest", "for<PERSON>ach", "startsWith", "parsedPage", "regexStr", "Error", "middlewareManifestPath", "loadPagesManifest", "writePagesManifest", "pagesManifest", "pagesManifestPath", "writeManifests", "process", "env", "TURBOPACK_STATS"], "mappings": "AAQA,SAASA,YAAY,QAAQ,oCAAmC;AAIhE,SACEC,kBAAkB,EAClBC,kBAAkB,EAClBC,cAAc,EACdC,mCAAmC,EACnCC,yBAAyB,EACzBC,mBAAmB,EACnBC,kCAAkC,EAClCC,kBAAkB,EAClBC,cAAc,EACdC,uBAAuB,EACvBC,yBAAyB,EACzBC,oCAAoC,EACpCC,aAAa,QACR,gCAA+B;AACtC,SAASC,IAAI,EAAEC,KAAK,QAAQ,OAAM;AAClC,SAASC,QAAQ,QAAQ,cAAa;AAEtC,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,eAAe,QAAQ,+BAA8B;AAC9D,SAASC,0BAA0B,QAAQ,qDAAoD;AAC/F,SAEEC,iCAAiC,EACjCC,mBAAmB,EACnBC,YAAY,QACP,uDAAsD;AAE7D,OAAOC,2BAA2B,6DAA4D;AAC9F,SAASC,WAAW,QAAuB,cAAa;AAExD,SAASC,eAAe,QAAQ,mCAAkC;AAClE,SAASC,UAAU,QAAQ,KAAI;AAC/B,SAASC,oBAAoB,EAAEC,cAAc,EAAEC,iBAAiB,QAAQ,qBAAoB;AAC5F,SAASC,cAAc,QAAQ,iCAAgC;AAW/D,MAAMC,kBAAkB,CAACC,MAAcC,SAAiBC,MAAcC;IACpE,IAAIC,eAAerB,MAAMD,IAAI,CAC3BmB,SACA,CAAC,MAAM,CAAC,EACRE,MACAA,SAAS,gBAAgBA,SAAS,oBAC9B,KACAA,SAAS,QACPH,OACAT,sBAAsBS,OAC5BE;IAEF,OAAOE;AACT;AAEA,eAAeC,oBACbJ,OAAe,EACfC,IASkC,EAClCI,QAAgB,EAChBH,OAA2D,OAAO;IAElE,MAAMH,OAAOM;IACb,MAAMC,iBAAiB,8BAA8BC,IAAI,CAACR;IAC1D,IAAII,eAAeL,gBAAgBC,MAAMC,SAASC,MAAMC;IAExD,mDAAmD;IACnD,IAAII,kBAAkB,CAACb,WAAWU,eAAe;QAC/CA,eAAeL,gBAAgBO,SAASG,OAAO,CAAC,qBAAqB,uBAAuBR,SAASC,MAAMC;IAC7G;IACA,oDAAoD;IACpD,IAAG,CAACT,WAAWU,iBAAiBJ,KAAKU,QAAQ,CAAC,WAAW;QACvD,6IAA6I;QAC7I,IAAIC,eAAef,eAAeD,qBAAqBE,kBAAkBG;QACzEI,eAAeL,gBAAgBY,cAAcV,SAASC,MAAMC;IAC9D;IACA,OAAOS,KAAKC,KAAK,CAAC,MAAM7B,SAASD,MAAMD,IAAI,CAACsB,eAAe;AAC7D;AAEA,OAAO,MAAMU;IAgBXC,YAAY,EACVd,OAAO,EACPe,OAAO,EACPC,aAAa,EAKd,CAAE;aAvBKC,kBAAiD,IAAIC;aACrDC,oBAAqD,IAAID;aACzDE,oBAAkD,IAAIF;aACtDG,iBAA+C,IAAIH;aACnDI,gBAAiD,IAAIJ;aACrDK,oBAAqD,IAAIL;aACzDM,sBACN,IAAIN;aACEO,iBAA6C,IAAIP;aACjDQ,eAA4C,IAAIR;QAetD,IAAI,CAAClB,OAAO,GAAGA;QACf,IAAI,CAACe,OAAO,GAAGA;QACf,IAAI,CAACC,aAAa,GAAGA;IACvB;IAEAW,OAAOC,GAAa,EAAE;QACpB,IAAI,CAACX,eAAe,CAACU,MAAM,CAACC;QAC5B,IAAI,CAACT,iBAAiB,CAACQ,MAAM,CAACC;QAC9B,IAAI,CAACR,iBAAiB,CAACO,MAAM,CAACC;QAC9B,IAAI,CAACP,cAAc,CAACM,MAAM,CAACC;QAC3B,IAAI,CAACN,aAAa,CAACK,MAAM,CAACC;QAC1B,IAAI,CAACL,iBAAiB,CAACI,MAAM,CAACC;QAC9B,IAAI,CAACJ,mBAAmB,CAACG,MAAM,CAACC;QAChC,IAAI,CAACH,cAAc,CAACE,MAAM,CAACC;QAC3B,IAAI,CAACF,YAAY,CAACC,MAAM,CAACC;IAC3B;IAEA,MAAMC,mBAAmBxB,QAAgB,EAAiB;QACxD,IAAI,CAACY,eAAe,CAACa,GAAG,CACtBvC,YAAY,OAAO,UAAUc,WAC7B,MAAMD,oBACJ,IAAI,CAACJ,OAAO,EACZ,GAAGtB,0BAA0B,KAAK,CAAC,EACnC2B,UACA;IAGN;IAEA,MAAc0B,qBAAqBC,SAAmC,EAAE;QAEtE,MAAMC,WAA2B;YAC/BC,MAAM,CAAC;YACPC,MAAM,CAAC;YACPnB,eAAe,IAAI,CAACA,aAAa;QACnC;QAEA,SAASoB,eACPC,aAA4B,EAC5BC,KAAoB;YAEpB,IAAK,MAAMV,OAAOU,MAAO;gBACvB,MAAMC,SAAUF,aAAa,CAACT,IAAI,KAAK;oBACrCY,SAAS,CAAC;oBACVC,OAAO,CAAC;gBACV;gBACAC,OAAOC,MAAM,CAACJ,OAAOC,OAAO,EAAEF,KAAK,CAACV,IAAI,CAACY,OAAO;gBAChDE,OAAOC,MAAM,CAACJ,OAAOE,KAAK,EAAEH,KAAK,CAACV,IAAI,CAACa,KAAK;YAC9C;QACF;QAEA,KAAK,MAAMG,KAAKZ,UAAW;YACzBI,eAAeH,SAASC,IAAI,EAAEU,EAAEV,IAAI;YACpCE,eAAeH,SAASE,IAAI,EAAES,EAAET,IAAI;QACtC;QAEA,OAAOF;IACT;IAEA,MAAcY,sBAAqC;QACjD,MAAMC,iBAAiB,MAAM,IAAI,CAACf,oBAAoB,CACpD,IAAI,CAACd,eAAe,CAAC8B,MAAM;QAE7B,MAAMC,yBAAyBnE,KAC7B,IAAI,CAACmB,OAAO,EACZ,UACA,GAAGtB,0BAA0B,KAAK,CAAC;QAErC,MAAMuE,uBAAuBpE,KAC3B,IAAI,CAACmB,OAAO,EACZ,UACA,GAAGtB,0BAA0B,GAAG,CAAC;QAEnC,MAAMwE,OAAOvC,KAAKwC,SAAS,CAACL,gBAAgB,MAAM;QAClD9D,YAAYgE;QACZhE,YAAYiE;QACZ,MAAMhE,gBAAgB+D,wBAAwBE;QAC9C,MAAMjE,gBACJgE,sBACA,CAAC,2BAA2B,EAAEtC,KAAKwC,SAAS,CAACD,OAAO;IAExD;IAEA,MAAME,qBAAqB/C,QAAgB,EAAiB;QAC1D,IAAI,CAACc,iBAAiB,CAACW,GAAG,CACxBvC,YAAY,OAAO,UAAUc,WAC7B,MAAMD,oBACJ,IAAI,CAACJ,OAAO,EACZhC,oBACAqC,UACA;IAGN;IAEQgD,uBAAuBrB,SAAqC,EAAE;QACpE,MAAMC,WAA6B;YACjCqB,OAAO,CAAC;QACV;QACA,KAAK,MAAMV,KAAKZ,UAAW;YACzBU,OAAOC,MAAM,CAACV,SAASqB,KAAK,EAAEV,EAAEU,KAAK;QACvC;QACA,OAAOrB;IACT;IAEA,MAAcsB,wBAAuC;QACnD,MAAMC,mBAAmB,IAAI,CAACH,sBAAsB,CAClD,IAAI,CAAClC,iBAAiB,CAAC4B,MAAM;QAE/B,MAAMU,uBAAuB5E,KAAK,IAAI,CAACmB,OAAO,EAAEhC;QAChDgB,YAAYyE;QACZ,MAAMxE,gBACJwE,sBACA9C,KAAKwC,SAAS,CAACK,kBAAkB,MAAM;IAE3C;IAEA,MAAME,qBAAqBrD,QAAgB,EAAiB;QAC1D,IAAI,CAACe,iBAAiB,CAACU,GAAG,CACxBvC,YAAY,OAAO,UAAUc,WAC7B,MAAMD,oBACJ,IAAI,CAACJ,OAAO,EACZ/B,oBACAoC,UACA;IAGN;IAEA,MAAcsD,wBAAuC;QACnD,MAAMC,mBAAmB,IAAI,CAACC,mBAAmB,CAC/C,IAAI,CAACzC,iBAAiB,CAAC2B,MAAM;QAE/B,MAAMe,uBAAuBjF,KAC3B,IAAI,CAACmB,OAAO,EACZ,UACA/B;QAEFe,YAAY8E;QACZ,MAAM7E,gBACJ6E,sBACAnD,KAAKwC,SAAS,CAACS,kBAAkB,MAAM;IAE3C;IAEA,MAAcG,oBAAmC;QAC/C,MAAMrC,eAAe,IAAI,CAACsC,iBAAiB,CACzC,IAAI,CAACtC,YAAY,CAACqB,MAAM;QAE1B,MAAMkB,OAAOpF,KACX,IAAI,CAACmB,OAAO,EACZ,UACApB;QAEFI,YAAYiF;QACZ,MAAMhF,gBACJgF,MACAtD,KAAKwC,SAAS,CAACzB,cAAc,MAAM;IAEvC;IAEA,MAAMwC,kBACJ7D,QAAgB,EAChBH,OAAwB,OAAO,EAChB;QACf,IAAI,CAACmB,cAAc,CAACS,GAAG,CACrBvC,YAAYW,MAAM,UAAUG,WAC5B,MAAMD,oBAAoB,IAAI,CAACJ,OAAO,EAAE9B,gBAAgBmC,UAAUH;IAEtE;IAEA,MAAMiE,iBACJ9D,QAAgB,EAChBH,OAAwB,OAAO,EAChB;QACf,IAAI,CAACwB,YAAY,CAACI,GAAG,CACnBvC,YAAYW,MAAM,UAAUG,WAC5B,MAAMD,oBAAoB,IAAI,CAACJ,OAAO,EAAEpB,eAAeyB,UAAUH;IAErE;IAEQ8D,kBAAkBI,UAAkC,EAAgB;QAC1E,MAAMC,cAA+C,CAAC;QACtD,MAAMC,SAAkC,IAAIpD;QAC5C,MAAMqD,SAAkC,IAAIrD;QAC5C,MAAMsD,UAA6C,IAAItD;QAEvD,KAAK,MAAMuD,aAAaL,WAAY;YAClC,IAAIK,UAAUJ,WAAW,EAAE;gBACzB,KAAK,MAAM,CAACK,GAAGC,EAAE,IAAIjC,OAAOkC,OAAO,CACjCH,UAAUJ,WAAW,EACpB;oBACD,IAAI,CAACA,WAAW,CAACK,EAAE,EAAE;wBACnBL,WAAW,CAACK,EAAE,GAAGC;oBACnB;gBACF;YACF;YAEA,IAAIF,UAAUH,MAAM,EAAE;gBACpB,KAAK,MAAMO,SAASJ,UAAUH,MAAM,CAAE;oBACpC,IAAI,CAACA,OAAOQ,GAAG,CAACD,MAAM5E,IAAI,GAAG;wBAC3BqE,OAAOxC,GAAG,CAAC+C,MAAM5E,IAAI,EAAE4E;oBACzB;gBACF;YACF;YAEA,IAAIJ,UAAUF,MAAM,EAAE;gBACpB,KAAK,MAAMQ,SAASN,UAAUF,MAAM,CAAE;oBACpC,IAAI,CAACA,OAAOO,GAAG,CAACC,MAAM9E,IAAI,GAAG;wBAC3BsE,OAAOzC,GAAG,CAACiD,MAAM9E,IAAI,EAAE8E;oBACzB;gBACF;YACF;YAEA,IAAIN,UAAUD,OAAO,EAAE;gBACrB,KAAK,MAAMQ,UAAUP,UAAUD,OAAO,CAAE;oBACtC,MAAMS,KAAKD,OAAOC,EAAE;oBACpB,IAAIA,MAAM,MAAM;wBACd,uEAAuE;wBACvE,MAAMC,WAAWV,QAAQW,GAAG,CAACF;wBAC7B,IAAIC,YAAY,MAAM;4BACpBV,QAAQ1C,GAAG,CAACmD,IAAID;wBAClB,OAAO,IAAIA,OAAOT,MAAM,IAAI,QAAQW,SAASX,MAAM,IAAI,MAAM;4BAC3D,KAAK,MAAMQ,SAASC,OAAOT,MAAM,CAAE;gCACjC,IAAI,CAACW,SAASX,MAAM,CAACa,QAAQ,CAACL,QAAQ;oCACpCG,SAASX,MAAM,CAACc,IAAI,CAACN;gCACvB;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO;YACLV;YACAC,QAAQ;mBAAIA,OAAOvB,MAAM;aAAG;YAC5BwB,QAAQ;mBAAIA,OAAOxB,MAAM;aAAG;YAC5ByB,SAAS;mBAAIA,QAAQzB,MAAM;aAAG;QAChC;IACF;IAEQuC,oBAAoBtD,SAAkC,EAAE;QAC9D,MAAMC,WAAkE;YACtEqB,OAAO;gBACL,SAAS,EAAE;YACb;YACA,4EAA4E;YAC5EiC,UAAU,EAAE;YACZC,aAAa,EAAE;YACfC,eAAe,EAAE;YACjBC,kBAAkB;gBAChB,CAAC,OAAO,EAAE,IAAI,CAAC3E,OAAO,CAAC,gBAAgB,CAAC;gBACxC,CAAC,OAAO,EAAE,IAAI,CAACA,OAAO,CAAC,kBAAkB,CAAC;aAC3C;YACD4E,eAAe,EAAE;YACjBC,eAAe,EAAE;QACnB;QACA,KAAK,MAAMhD,KAAKZ,UAAW;YACzBU,OAAOC,MAAM,CAACV,SAASqB,KAAK,EAAEV,EAAEU,KAAK;YACrC,IAAIV,EAAE+C,aAAa,CAACE,MAAM,EAAE5D,SAAS0D,aAAa,GAAG/C,EAAE+C,aAAa;YACpE,2FAA2F;YAC3F,IAAI/C,EAAE6C,aAAa,CAACI,MAAM,EAAE5D,SAASwD,aAAa,GAAG7C,EAAE6C,aAAa;QACtE;QACA,OAAOxD;IACT;IAEA,MAAc6D,mBACZzB,WAAwB,EACxB0B,WAA2D,EAC3DC,kBAAwD,EACzC;QACf,MAAMC,WAAWD,sBAAsB;YACrC,GAAGD,WAAW;YACdG,aAAa,AAACH,CAAAA,CAAAA,+BAAAA,YAAaG,WAAW,KAAI,EAAE,AAAD,EAAGC,GAAG,CAAC9G;YAClD+G,YAAY,AAACL,CAAAA,CAAAA,+BAAAA,YAAaK,UAAU,KAAI,EAAE,AAAD,EAAGD,GAAG,CAAC9G;YAChDgH,UAAU,AAACN,CAAAA,CAAAA,+BAAAA,YAAaM,QAAQ,KAAI,EAAE,AAAD,EAAGF,GAAG,CAAC9G;QAC9C;QACA,MAAMiH,gBAAgB,IAAI,CAAChB,mBAAmB,CAAC,IAAI,CAACjE,cAAc,CAAC0B,MAAM;QACzE,MAAMwD,oBAAoB1H,KAAK,IAAI,CAACmB,OAAO,EAAE9B;QAC7C,MAAMsI,8BAA8B3H,KAClC,IAAI,CAACmB,OAAO,EACZ,UACA,GAAG5B,0BAA0B,GAAG,CAAC;QAEnC,MAAMqI,kCAAkC5H,KACtC,IAAI,CAACmB,OAAO,EACZ,UACA,GAAG7B,oCAAoC,GAAG,CAAC;QAE7Ca,YAAYuH;QACZvH,YAAYwH;QACZxH,YAAYyH;QACZ,MAAMxH,gBACJsH,mBACA5F,KAAKwC,SAAS,CAACmD,eAAe,MAAM;QAEtC,MAAMrH,gBACJuH,6BACA,CAAC,sBAAsB,EAAE7F,KAAKwC,SAAS,CAACmD,eAAe,CAAC,CAAC;QAG3D,MAAMI,uBAAuB/F,KAAKwC,SAAS,CACzC8C,SAASC,WAAW,CAACS,MAAM,CAACzH;QAG9B,MAAMD,gBACJwH,iCACA,CAAC,2CAA2C,EAAE9F,KAAKwC,SAAS,CAC1DuD,sBACA,CAAC,CAAC;QAGN,MAAME,YAAY;eAAIvC,YAAYtE,IAAI,CAAC8G,IAAI;SAAG;QAC9C,IAAIxC,YAAYyC,MAAM,CAACC,GAAG,EAAE;YAC1BH,UAAUvB,IAAI,CAAC;QACjB;QACA,IAAIhB,YAAYyC,MAAM,CAACE,KAAK,EAAE;YAC5BJ,UAAUvB,IAAI,CAAC;QACjB;QAEA,MAAM4B,iBAAiBzH,gBAAgBoH;QACvC,MAAMM,UAA+B;YACnCC,YAAYhI,kCAAkC8G;YAC9C,GAAGvD,OAAO0E,WAAW,CACnBH,eAAed,GAAG,CAAC,CAACkB,WAAa;oBAC/BA;oBACA;wBAAC,CAAC,mBAAmB,EAAEA,aAAa,MAAM,WAAWA,SAAS,GAAG,CAAC;qBAAC;iBACpE,EACF;YACDC,aAAaL;QACf;QACA,MAAMM,kBAAkB,CAAC,wBAAwB,EAAE5G,KAAKwC,SAAS,CAC/D+D,SACA,uDAAuD,CAAC;QAC1D,MAAMjI,gBACJJ,KAAK,IAAI,CAACmB,OAAO,EAAE,UAAU,IAAI,CAACe,OAAO,EAAE,sBAC3CwG;QAEF,MAAMtI,gBACJJ,KAAK,IAAI,CAACmB,OAAO,EAAE,UAAU,IAAI,CAACe,OAAO,EAAE,oBAC3C3B;IAEJ;IAEA,MAAcoI,gCAA+C;YAK1CC;QAJjB,MAAMA,qBAAqB,IAAI,CAACC,wBAAwB,CACtD,IAAI,CAAClG,mBAAmB,CAACuB,MAAM;QAGjC,MAAM4E,WAAWF,CAAAA,uCAAAA,kCAAAA,mBAAoBG,UAAU,CAAC,IAAI,qBAAnCH,gCAAqCE,QAAQ,KAAI,EAAE;QAEpE,MAAME,+BAA+BhJ,KACnC,IAAI,CAACmB,OAAO,EACZ,UACA,IAAI,CAACe,OAAO,EACZ,GAAGpC,sCAAsC;QAE3CK,YAAY6I;QACZ,MAAM5I,gBACJ4I,8BACAlH,KAAKwC,SAAS,CAACwE,UAAU,MAAM;IAEnC;IAEA,MAAcG,6BAA4C;QACxD,MAAMC,wBAAwB,IAAI,CAACzC,mBAAmB,CACpD;YACE,IAAI,CAACjE,cAAc,CAAC8D,GAAG,CAAC5F,YAAY,SAAS,UAAU;YACvD,IAAI,CAAC8B,cAAc,CAAC8D,GAAG,CAAC5F,YAAY,SAAS,UAAU;SACxD,CAACoH,MAAM,CAACqB;QAEX,MAAMC,4BAA4BpJ,KAChC,IAAI,CAACmB,OAAO,EACZ,CAAC,SAAS,EAAE9B,gBAAgB;QAE9Bc,YAAYiJ;QACZ,MAAMhJ,gBACJgJ,2BACAtH,KAAKwC,SAAS,CAAC4E,uBAAuB,MAAM;IAEhD;IAEA,MAAMG,iBACJ7H,QAAgB,EAChBH,OAAwB,OAAO,EAChB;QACf,IAAI,CAACoB,aAAa,CAACQ,GAAG,CACpBvC,YAAYW,MAAM,UAAUG,WAC5B,MAAMD,oBACJ,IAAI,CAACJ,OAAO,EACZ,GAAGzB,mBAAmB,KAAK,CAAC,EAC5B8B,UACAH;IAGN;IAEQiI,mBAAmBnG,SAAqC,EAAE;QAChE,MAAMC,WAA6B;YACjC8E,KAAK,CAAC;YACNqB,oBAAoB;YACpB9E,OAAO,CAAC;YACR+E,sBAAsB;QACxB;QACA,KAAK,MAAMzF,KAAKZ,UAAW;YACzBU,OAAOC,MAAM,CAACV,SAAS8E,GAAG,EAAEnE,EAAEmE,GAAG;YACjCrE,OAAOC,MAAM,CAACV,SAASqB,KAAK,EAAEV,EAAEU,KAAK;YAErCrB,SAASmG,kBAAkB,GACzBnG,SAASmG,kBAAkB,IAAIxF,EAAEwF,kBAAkB;YACrDnG,SAASoG,oBAAoB,GAC3BpG,SAASoG,oBAAoB,IAAIzF,EAAEyF,oBAAoB;QAC3D;QACA,OAAOpG;IACT;IAEA,MAAcqG,wBAAuC;QACnD,MAAMC,eAAe,IAAI,CAACJ,kBAAkB,CAAC,IAAI,CAAC7G,aAAa,CAACyB,MAAM;QACtE,MAAMG,OAAOvC,KAAKwC,SAAS,CAACoF,cAAc,MAAM;QAEhD,MAAMC,uBAAuB3J,KAC3B,IAAI,CAACmB,OAAO,EACZ,UACA,GAAGzB,mBAAmB,KAAK,CAAC;QAE9B,MAAMkK,qBAAqB5J,KACzB,IAAI,CAACmB,OAAO,EACZ,UACA,GAAGzB,mBAAmB,GAAG,CAAC;QAE5BS,YAAYwJ;QACZxJ,YAAYyJ;QACZ,MAAMxJ,gBAAgBuJ,sBAAsBtF;QAC5C,MAAMjE,gBACJwJ,oBACA,CAAC,0BAA0B,EAAE9H,KAAKwC,SAAS,CAACD,OAAO;IAEvD;IAEA,MAAMwF,qBACJrI,QAAgB,EAChBH,OAAwB,OAAO,EAChB;QACf,IAAI,CAACqB,iBAAiB,CAACO,GAAG,CACxBvC,YAAYW,MAAM,UAAUG,WAC5B,MAAMD,oBACJ,IAAI,CAACJ,OAAO,EACZvB,yBACA4B,UACAH;IAGN;IAEQyI,uBAAuB3G,SAAqC,EAAE;QACpE,MAAMC,WAA6B,CAAC;QACpC,KAAK,MAAMW,KAAKZ,UAAW;YACzBU,OAAOC,MAAM,CAACV,UAAUW;QAC1B;QACA,OAAOX;IACT;IAEA,MAAc2G,wBAAuC;QACnD,MAAMC,mBAAmB,IAAI,CAACF,sBAAsB,CAClD,IAAI,CAACpH,iBAAiB,CAACwB,MAAM;QAE/B,MAAM+F,uBAAuBjK,KAAK,IAAI,CAACmB,OAAO,EAAEvB;QAChD,MAAMsK,iCAAiClK,KACrC,IAAI,CAACmB,OAAO,EACZ,UACA,GAAG1B,mCAAmC,GAAG,CAAC;QAG5C,MAAM4E,OAAOvC,KAAKwC,SAAS,CAAC0F,kBAAkB,MAAM;QAEpD7J,YAAY8J;QACZ9J,YAAY+J;QACZ,MAAM9J,gBAAgB6J,sBAAsB5F;QAC5C,MAAMjE,gBACJ8J,gCACA,CAAC,+BAA+B,EAAEpI,KAAKwC,SAAS,CAACD,OAAO;IAE5D;IAEA,MAAM8F,uBACJ3I,QAAgB,EAChBH,IAAwD,EACzC;QACf,IAAI,CAACsB,mBAAmB,CAACM,GAAG,CAC1BvC,YACEW,SAAS,gBAAgBA,SAAS,oBAAoB,SAASA,MAC/D,UACAG,WAEF,MAAMD,oBACJ,IAAI,CAACJ,OAAO,EACZ3B,qBACAgC,UACAH;IAGN;IAEA+I,sBAAsBrH,GAAa,EAAE;QACnC,OAAO,IAAI,CAACJ,mBAAmB,CAAC2D,GAAG,CAACvD;IACtC;IAEAsH,yBAAyBtH,GAAa,EAAE;QACtC,OAAO,IAAI,CAACJ,mBAAmB,CAACG,MAAM,CAACC;IACzC;IAEQ8F,yBACN1F,SAAgD,EAC5B;QACpB,MAAMC,WAA+B;YACnCkH,SAAS;YACTvB,YAAY,CAAC;YACbwB,kBAAkB,EAAE;YACpBC,WAAW,CAAC;QACd;QACA,IAAIC,kBAAyDC;QAC7D,KAAK,MAAM3G,KAAKZ,UAAW;YACzBU,OAAOC,MAAM,CAACV,SAASoH,SAAS,EAAEzG,EAAEyG,SAAS;YAC7C3G,OAAOC,MAAM,CAACV,SAAS2F,UAAU,EAAEhF,EAAEgF,UAAU;YAC/C,IAAIhF,EAAE0G,eAAe,EAAE;gBACrBA,kBAAkB1G,EAAE0G,eAAe;YACrC;QACF;QACA,MAAME,2BAA2B,CAC/BC;YAEA,OAAO;gBACL,GAAGA,GAAG;gBACNC,OAAO;uBAAKJ,CAAAA,mCAAAA,gBAAiBI,KAAK,KAAI,EAAE;uBAAMD,IAAIC,KAAK;iBAAC;YAC1D;QACF;QACA,KAAK,MAAM9H,OAAOc,OAAOmE,IAAI,CAAC5E,SAAS2F,UAAU,EAAG;YAClD,MAAM+B,QAAQ1H,SAAS2F,UAAU,CAAChG,IAAI;YACtCK,SAAS2F,UAAU,CAAChG,IAAI,GAAG4H,yBAAyBG;QACtD;QACA,KAAK,MAAM/H,OAAOc,OAAOmE,IAAI,CAAC5E,SAASoH,SAAS,EAAG;YACjD,MAAMM,QAAQ1H,SAASoH,SAAS,CAACzH,IAAI;YACrCK,SAASoH,SAAS,CAACzH,IAAI,GAAG4H,yBAAyBG;QACrD;QACA,KAAK,MAAMF,OAAO/G,OAAOK,MAAM,CAACd,SAASoH,SAAS,EAAEO,MAAM,CACxDlH,OAAOK,MAAM,CAACd,SAAS2F,UAAU,GAChC;YACD,KAAK,MAAMiC,WAAWJ,IAAI9B,QAAQ,CAAE;gBAClC,IAAI,CAACkC,QAAQC,MAAM,EAAE;oBACnBD,QAAQC,MAAM,GAAG/L,aAAa8L,QAAQE,cAAc,EAAE,EAAE,EAAE;wBACxDC,WAAW;wBACXC,WAAW;wBACXC,QAAQ;oBACV,GAAGC,MAAM,CAACC,UAAU,CAAC,OAAO;gBAC9B;YACF;QACF;QACAnI,SAASmH,gBAAgB,GAAG1G,OAAOmE,IAAI,CAAC5E,SAAS2F,UAAU;QAE3D,OAAO3F;IACT;IAEA,MAAcoI,0BAAyC;QACrD,MAAM5C,qBAAqB,IAAI,CAACC,wBAAwB,CACtD,IAAI,CAAClG,mBAAmB,CAACuB,MAAM;QAGjC,8CAA8C;QAC9C,IAAK,MAAMnB,OAAO6F,mBAAmBG,UAAU,CAAE;YAC/CH,mBAAmBG,UAAU,CAAChG,IAAI,CAAC+F,QAAQ,CAAC2C,OAAO,CAAC,CAACT;gBACnD,IAAI,CAACA,QAAQC,MAAM,CAACS,UAAU,CAAC,MAAM;oBACnC,MAAMC,aAAa3K,eAAegK,QAAQC,MAAM;oBAChD,IAAIU,WAAWxD,KAAK,IAAI,CAACwD,WAAWC,QAAQ,EAAE;wBAC5C,MAAM,IAAIC,MAAM,CAAC,gBAAgB,EAAEb,QAAQC,MAAM,EAAE;oBACrD;oBACAD,QAAQC,MAAM,GAAGU,WAAWC,QAAQ;gBACtC;YACF;QACF;QAEA,MAAME,yBAAyB9L,KAC7B,IAAI,CAACmB,OAAO,EACZ,UACA3B;QAEFW,YAAY2L;QACZ,MAAM1L,gBACJ0L,wBACAhK,KAAKwC,SAAS,CAACsE,oBAAoB,MAAM;IAE7C;IAEA,MAAMmD,kBAAkBvK,QAAgB,EAAiB;QACvD,IAAI,CAACoB,cAAc,CAACK,GAAG,CACrBvC,YAAY,SAAS,UAAUc,WAC/B,MAAMD,oBAAoB,IAAI,CAACJ,OAAO,EAAExB,gBAAgB6B;IAE5D;IAEQwD,oBAAoB7B,SAAkC,EAAE;QAC9D,MAAMC,WAA0B,CAAC;QACjC,KAAK,MAAMW,KAAKZ,UAAW;YACzBU,OAAOC,MAAM,CAACV,UAAUW;QAC1B;QACA,OAAOX;IACT;IAEA,MAAc4I,qBAAoC;QAChD,MAAMC,gBAAgB,IAAI,CAACjH,mBAAmB,CAAC,IAAI,CAACpC,cAAc,CAACsB,MAAM;QACzE,MAAMgI,oBAAoBlM,KAAK,IAAI,CAACmB,OAAO,EAAE,UAAUxB;QACvDQ,YAAY+L;QACZ,MAAM9L,gBACJ8L,mBACApK,KAAKwC,SAAS,CAAC2H,eAAe,MAAM;IAExC;IAEA,MAAME,eAAe,EACnBjF,WAAW,EACXC,kBAAkB,EAClB3B,WAAW,EAKZ,EAAE;QACD,MAAM,IAAI,CAACxB,mBAAmB;QAC9B,MAAM,IAAI,CAACU,qBAAqB;QAChC,MAAM,IAAI,CAACI,qBAAqB;QAChC,MAAM,IAAI,CAACmC,kBAAkB,CAACzB,aAAa0B,aAAaC;QACxD,MAAM,IAAI,CAAC8B,0BAA0B;QACrC,MAAM,IAAI,CAACc,qBAAqB;QAChC,MAAM,IAAI,CAACyB,uBAAuB;QAClC,MAAM,IAAI,CAAC7C,6BAA6B;QACxC,MAAM,IAAI,CAACc,qBAAqB;QAChC,MAAM,IAAI,CAACuC,kBAAkB;QAE7B,IAAII,QAAQC,GAAG,CAACC,eAAe,IAAI,MAAM;YACvC,MAAM,IAAI,CAACpH,iBAAiB;QAC9B;IACF;AACF"}