"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/sales/page",{

/***/ "(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/DatabaseOutlined.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@ant-design/icons-svg/es/asn/DatabaseOutlined.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// This icon file is generated automatically.\nvar DatabaseOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-600 72h560v208H232V136zm560 480H232V408h560v208zm0 272H232V680h560v208zM304 240a40 40 0 1080 0 40 40 0 10-80 0zm0 272a40 40 0 1080 0 40 40 0 10-80 0zm0 272a40 40 0 1080 0 40 40 0 10-80 0z\" } }] }, \"name\": \"database\", \"theme\": \"outlined\" };\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DatabaseOutlined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9pY29ucy1zdmcvZXMvYXNuL0RhdGFiYXNlT3V0bGluZWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EseUJBQXlCLFVBQVUseUJBQXlCLGtEQUFrRCxpQkFBaUIsMEJBQTBCLG9UQUFvVCxHQUFHO0FBQ2hkLGlFQUFlLGdCQUFnQixFQUFDIiwic291cmNlcyI6WyJFOlxcUFJPSkVDVFNcXHBvc1xccG9zZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQGFudC1kZXNpZ25cXGljb25zLXN2Z1xcZXNcXGFzblxcRGF0YWJhc2VPdXRsaW5lZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUaGlzIGljb24gZmlsZSBpcyBnZW5lcmF0ZWQgYXV0b21hdGljYWxseS5cbnZhciBEYXRhYmFzZU91dGxpbmVkID0geyBcImljb25cIjogeyBcInRhZ1wiOiBcInN2Z1wiLCBcImF0dHJzXCI6IHsgXCJ2aWV3Qm94XCI6IFwiNjQgNjQgODk2IDg5NlwiLCBcImZvY3VzYWJsZVwiOiBcImZhbHNlXCIgfSwgXCJjaGlsZHJlblwiOiBbeyBcInRhZ1wiOiBcInBhdGhcIiwgXCJhdHRyc1wiOiB7IFwiZFwiOiBcIk04MzIgNjRIMTkyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnY4MzJjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjQwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNjAwIDcyaDU2MHYyMDhIMjMyVjEzNnptNTYwIDQ4MEgyMzJWNDA4aDU2MHYyMDh6bTAgMjcySDIzMlY2ODBoNTYwdjIwOHpNMzA0IDI0MGE0MCA0MCAwIDEwODAgMCA0MCA0MCAwIDEwLTgwIDB6bTAgMjcyYTQwIDQwIDAgMTA4MCAwIDQwIDQwIDAgMTAtODAgMHptMCAyNzJhNDAgNDAgMCAxMDgwIDAgNDAgNDAgMCAxMC04MCAwelwiIH0gfV0gfSwgXCJuYW1lXCI6IFwiZGF0YWJhc2VcIiwgXCJ0aGVtZVwiOiBcIm91dGxpbmVkXCIgfTtcbmV4cG9ydCBkZWZhdWx0IERhdGFiYXNlT3V0bGluZWQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/DatabaseOutlined.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/ReloadOutlined.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@ant-design/icons-svg/es/asn/ReloadOutlined.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// This icon file is generated automatically.\nvar ReloadOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z\" } }] }, \"name\": \"reload\", \"theme\": \"outlined\" };\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReloadOutlined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9pY29ucy1zdmcvZXMvYXNuL1JlbG9hZE91dGxpbmVkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLHVCQUF1QixVQUFVLHlCQUF5QixrREFBa0QsaUJBQWlCLDBCQUEwQiwyckJBQTJyQixHQUFHO0FBQ3IxQixpRUFBZSxjQUFjLEVBQUMiLCJzb3VyY2VzIjpbIkU6XFxQUk9KRUNUU1xccG9zXFxwb3Nmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAYW50LWRlc2lnblxcaWNvbnMtc3ZnXFxlc1xcYXNuXFxSZWxvYWRPdXRsaW5lZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUaGlzIGljb24gZmlsZSBpcyBnZW5lcmF0ZWQgYXV0b21hdGljYWxseS5cbnZhciBSZWxvYWRPdXRsaW5lZCA9IHsgXCJpY29uXCI6IHsgXCJ0YWdcIjogXCJzdmdcIiwgXCJhdHRyc1wiOiB7IFwidmlld0JveFwiOiBcIjY0IDY0IDg5NiA4OTZcIiwgXCJmb2N1c2FibGVcIjogXCJmYWxzZVwiIH0sIFwiY2hpbGRyZW5cIjogW3sgXCJ0YWdcIjogXCJwYXRoXCIsIFwiYXR0cnNcIjogeyBcImRcIjogXCJNOTA5LjEgMjA5LjNsLTU2LjQgNDQuMUM3NzUuOCAxNTUuMSA2NTYuMiA5MiA1MjEuOSA5MiAyOTAgOTIgMTAyLjMgMjc5LjUgMTAyIDUxMS41IDEwMS43IDc0My43IDI4OS44IDkzMiA1MjEuOSA5MzJjMTgxLjMgMCAzMzUuOC0xMTUgMzk0LjYtMjc2LjEgMS41LTQuMi0uNy04LjktNC45LTEwLjNsLTU2LjctMTkuNWE4IDggMCAwMC0xMC4xIDQuOGMtMS44IDUtMy44IDEwLTUuOSAxNC45LTE3LjMgNDEtNDIuMSA3Ny44LTczLjcgMTA5LjRBMzQ0Ljc3IDM0NC43NyAwIDAxNjU1LjkgODI5Yy00Mi4zIDE3LjktODcuNCAyNy0xMzMuOCAyNy00Ni41IDAtOTEuNS05LjEtMTMzLjgtMjdBMzQxLjUgMzQxLjUgMCAwMTI3OSA3NTUuMmEzNDIuMTYgMzQyLjE2IDAgMDEtNzMuNy0xMDkuNGMtMTcuOS00Mi40LTI3LTg3LjQtMjctMTMzLjlzOS4xLTkxLjUgMjctMTMzLjljMTcuMy00MSA0Mi4xLTc3LjggNzMuNy0xMDkuNCAzMS42LTMxLjYgNjguNC01Ni40IDEwOS4zLTczLjggNDIuMy0xNy45IDg3LjQtMjcgMTMzLjgtMjcgNDYuNSAwIDkxLjUgOS4xIDEzMy44IDI3YTM0MS41IDM0MS41IDAgMDExMDkuMyA3My44YzkuOSA5LjkgMTkuMiAyMC40IDI3LjggMzEuNGwtNjAuMiA0N2E4IDggMCAwMDMgMTQuMWwxNzUuNiA0M2M1IDEuMiA5LjktMi42IDkuOS03LjdsLjgtMTgwLjljLS4xLTYuNi03LjgtMTAuMy0xMy02LjJ6XCIgfSB9XSB9LCBcIm5hbWVcIjogXCJyZWxvYWRcIiwgXCJ0aGVtZVwiOiBcIm91dGxpbmVkXCIgfTtcbmV4cG9ydCBkZWZhdWx0IFJlbG9hZE91dGxpbmVkO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/ReloadOutlined.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DatabaseOutlined.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@ant-design/icons/es/icons/DatabaseOutlined.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ant_design_icons_svg_es_asn_DatabaseOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ant-design/icons-svg/es/asn/DatabaseOutlined */ \"(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/DatabaseOutlined.js\");\n/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/AntdIcon */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/components/AntdIcon.js\");\n\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\n\n\nvar DatabaseOutlined = function DatabaseOutlined(props, ref) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n        ref: ref,\n        icon: _ant_design_icons_svg_es_asn_DatabaseOutlined__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    }));\n};\n_c = DatabaseOutlined;\n/**![database](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA2NEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWOTZjMC0xNy43LTE0LjMtMzItMzItMzJ6bS02MDAgNzJoNTYwdjIwOEgyMzJWMTM2em01NjAgNDgwSDIzMlY0MDhoNTYwdjIwOHptMCAyNzJIMjMyVjY4MGg1NjB2MjA4ek0zMDQgMjQwYTQwIDQwIDAgMTA4MCAwIDQwIDQwIDAgMTAtODAgMHptMCAyNzJhNDAgNDAgMCAxMDgwIDAgNDAgNDAgMCAxMC04MCAwem0wIDI3MmE0MCA0MCAwIDEwODAgMCA0MCA0MCAwIDEwLTgwIDB6IiAvPjwvc3ZnPg==) */ var RefIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(DatabaseOutlined);\n_c1 = RefIcon;\nif (true) {\n    RefIcon.displayName = 'DatabaseOutlined';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RefIcon);\nvar _c, _c1;\n$RefreshReg$(_c, \"DatabaseOutlined\");\n$RefreshReg$(_c1, \"RefIcon\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DatabaseOutlined.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ant_design_icons_svg_es_asn_ReloadOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ant-design/icons-svg/es/asn/ReloadOutlined */ \"(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/ReloadOutlined.js\");\n/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/AntdIcon */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/components/AntdIcon.js\");\n\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\n\n\nvar ReloadOutlined = function ReloadOutlined(props, ref) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n        ref: ref,\n        icon: _ant_design_icons_svg_es_asn_ReloadOutlined__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    }));\n};\n_c = ReloadOutlined;\n/**![reload](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkwOS4xIDIwOS4zbC01Ni40IDQ0LjFDNzc1LjggMTU1LjEgNjU2LjIgOTIgNTIxLjkgOTIgMjkwIDkyIDEwMi4zIDI3OS41IDEwMiA1MTEuNSAxMDEuNyA3NDMuNyAyODkuOCA5MzIgNTIxLjkgOTMyYzE4MS4zIDAgMzM1LjgtMTE1IDM5NC42LTI3Ni4xIDEuNS00LjItLjctOC45LTQuOS0xMC4zbC01Ni43LTE5LjVhOCA4IDAgMDAtMTAuMSA0LjhjLTEuOCA1LTMuOCAxMC01LjkgMTQuOS0xNy4zIDQxLTQyLjEgNzcuOC03My43IDEwOS40QTM0NC43NyAzNDQuNzcgMCAwMTY1NS45IDgyOWMtNDIuMyAxNy45LTg3LjQgMjctMTMzLjggMjctNDYuNSAwLTkxLjUtOS4xLTEzMy44LTI3QTM0MS41IDM0MS41IDAgMDEyNzkgNzU1LjJhMzQyLjE2IDM0Mi4xNiAwIDAxLTczLjctMTA5LjRjLTE3LjktNDIuNC0yNy04Ny40LTI3LTEzMy45czkuMS05MS41IDI3LTEzMy45YzE3LjMtNDEgNDIuMS03Ny44IDczLjctMTA5LjQgMzEuNi0zMS42IDY4LjQtNTYuNCAxMDkuMy03My44IDQyLjMtMTcuOSA4Ny40LTI3IDEzMy44LTI3IDQ2LjUgMCA5MS41IDkuMSAxMzMuOCAyN2EzNDEuNSAzNDEuNSAwIDAxMTA5LjMgNzMuOGM5LjkgOS45IDE5LjIgMjAuNCAyNy44IDMxLjRsLTYwLjIgNDdhOCA4IDAgMDAzIDE0LjFsMTc1LjYgNDNjNSAxLjIgOS45LTIuNiA5LjktNy43bC44LTE4MC45Yy0uMS02LjYtNy44LTEwLjMtMTMtNi4yeiIgLz48L3N2Zz4=) */ var RefIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(ReloadOutlined);\n_c1 = RefIcon;\nif (true) {\n    RefIcon.displayName = 'ReloadOutlined';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RefIcon);\nvar _c, _c1;\n$RefreshReg$(_c, \"ReloadOutlined\");\n$RefreshReg$(_c1, \"RefIcon\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx":
/*!*************************************************!*\
  !*** ./src/components/Sales/SalesFormPanel.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/image/index.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DatabaseOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=DatabaseOutlined,DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,ReloadOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PrinterOutlined.js\");\n/* harmony import */ var _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/reduxRTK/services/salesApi */ \"(app-pages-browser)/./src/reduxRTK/services/salesApi.ts\");\n/* harmony import */ var _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/reduxRTK/services/productApi */ \"(app-pages-browser)/./src/reduxRTK/services/productApi.ts\");\n/* harmony import */ var _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/reduxRTK/services/userStoreApi */ \"(app-pages-browser)/./src/reduxRTK/services/userStoreApi.ts\");\n/* harmony import */ var _components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/SlidingPanel */ \"(app-pages-browser)/./src/components/ui/SlidingPanel.tsx\");\n/* harmony import */ var _utils_showMessage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/showMessage */ \"(app-pages-browser)/./src/utils/showMessage.ts\");\n/* harmony import */ var _utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/cloudinaryUtils */ \"(app-pages-browser)/./src/utils/cloudinaryUtils.ts\");\n/* harmony import */ var _sales_panels_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./sales-panels.css */ \"(app-pages-browser)/./src/components/Sales/sales-panels.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst SalesFormPanel = (param)=>{\n    let { isOpen, onClose, onSuccess } = param;\n    var _productsData_data, _productsData_data_products, _productsData_data1;\n    _s();\n    const [form] = _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm();\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalAmount, setTotalAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingReceipt, setIsGeneratingReceipt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptPreviewVisible, setReceiptPreviewVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptUrl, setReceiptUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPrinted, setHasPrinted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            console.log(\"Items state changed:\", items);\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items\n    ]);\n    const { data: productsData, isLoading: isLoadingProducts, refetch: refetchProducts, error: productsError, isFetching: isFetchingProducts } = (0,_reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery)({\n        page: 1,\n        limit: 1000,\n        search: searchTerm\n    }, {\n        // Always fetch fresh data from database\n        refetchOnMountOrArgChange: true,\n        refetchOnFocus: true,\n        refetchOnReconnect: true,\n        // Skip caching entirely for sales form to ensure fresh stock data\n        skip: false\n    });\n    // Enhanced products data monitoring and error handling\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (productsData) {\n                var _productsData_data, _productsData_data_products, _productsData_data1;\n                console.log(\"🛒 Fresh products loaded from database:\", {\n                    total: ((_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.total) || 0,\n                    productsCount: ((_productsData_data1 = productsData.data) === null || _productsData_data1 === void 0 ? void 0 : (_productsData_data_products = _productsData_data1.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.length) || 0,\n                    isLoading: isLoadingProducts,\n                    isFetching: isFetchingProducts,\n                    timestamp: new Date().toISOString()\n                });\n            }\n            if (productsError) {\n                console.error(\"❌ Error loading products:\", productsError);\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Failed to load products. Please try again.\");\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        productsData,\n        isLoadingProducts,\n        isFetchingProducts,\n        productsError\n    ]);\n    // Get current user ID from auth state\n    const getCurrentUserId = ()=>{\n        if (true) {\n            var _state_auth_user, _state_auth;\n            // @ts-ignore - Redux state is exposed for debugging\n            const state = window.__REDUX_STATE;\n            return (state === null || state === void 0 ? void 0 : (_state_auth = state.auth) === null || _state_auth === void 0 ? void 0 : (_state_auth_user = _state_auth.user) === null || _state_auth_user === void 0 ? void 0 : _state_auth_user.id) || 0;\n        }\n        return 0;\n    };\n    // Fetch user stores\n    const { data: userStoresData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery)(getCurrentUserId());\n    // Fetch default store\n    const { data: defaultStoreData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery)(getCurrentUserId());\n    // Set default store when data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (defaultStoreData === null || defaultStoreData === void 0 ? void 0 : defaultStoreData.data) {\n                setSelectedStore(defaultStoreData.data);\n                form.setFieldsValue({\n                    storeId: defaultStoreData.data.id\n                });\n            } else if ((userStoresData === null || userStoresData === void 0 ? void 0 : userStoresData.data) && userStoresData.data.length > 0) {\n                setSelectedStore(userStoresData.data[0]);\n                form.setFieldsValue({\n                    storeId: userStoresData.data[0].id\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        defaultStoreData,\n        userStoresData,\n        form\n    ]);\n    // Create sale mutation\n    const [createSale, { isLoading: isSubmitting }] = (0,_reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation)();\n    // Calculate total amount whenever items change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (items && items.length > 0) {\n                const total = items.reduce({\n                    \"SalesFormPanel.useEffect.total\": (sum, item)=>sum + item.price * item.quantity\n                }[\"SalesFormPanel.useEffect.total\"], 0);\n                setTotalAmount(total);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: total\n                    });\n                }\n                // Debug log to check items state\n                console.log(\"Current items in useEffect:\", items);\n            } else {\n                setTotalAmount(0);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: 0\n                    });\n                }\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items,\n        form\n    ]);\n    // Enhanced panel open/close handling with forced data refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (isOpen) {\n                // When panel opens, ALWAYS fetch fresh product data from database\n                console.log(\"🛒 Sales panel opened - forcing fresh product data fetch from database\");\n                // Force refetch to ensure we get the latest stock quantities\n                refetchProducts().then({\n                    \"SalesFormPanel.useEffect\": (result)=>{\n                        if (result.data) {\n                            var _result_data_data_products, _result_data_data;\n                            console.log(\"✅ Fresh product data successfully loaded:\", {\n                                productsCount: ((_result_data_data = result.data.data) === null || _result_data_data === void 0 ? void 0 : (_result_data_data_products = _result_data_data.products) === null || _result_data_data_products === void 0 ? void 0 : _result_data_data_products.length) || 0,\n                                timestamp: new Date().toISOString()\n                            });\n                        }\n                    }\n                }[\"SalesFormPanel.useEffect\"]).catch({\n                    \"SalesFormPanel.useEffect\": (error)=>{\n                        console.error(\"❌ Failed to fetch fresh product data:\", error);\n                        (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Failed to load current product data. Stock quantities may not be accurate.\");\n                    }\n                }[\"SalesFormPanel.useEffect\"]);\n            } else {\n                // Reset form when panel is closed\n                console.log(\"🛒 Sales panel closed - resetting form state\");\n                if (form) {\n                    form.resetFields();\n                }\n                setItems([]);\n                setSelectedProduct(null);\n                setQuantity(1);\n                setTotalAmount(0);\n                setReceiptUrl(null);\n                setReceiptPreviewVisible(false);\n                setHasPrinted(false);\n                setSearchTerm(\"\"); // Reset search term to ensure fresh data on next open\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        isOpen,\n        form,\n        refetchProducts\n    ]);\n    // Handle adding an item to the sale\n    const handleAddItem = ()=>{\n        if (!selectedProduct) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Please select a product\");\n            return;\n        }\n        if (quantity <= 0) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Quantity must be greater than 0\");\n            return;\n        }\n        if (selectedProduct.stockQuantity < quantity) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Only \".concat(selectedProduct.stockQuantity, \" units available in stock\"));\n            return;\n        }\n        console.log(\"Adding item with product:\", selectedProduct);\n        // Check if product already exists in items\n        const existingItemIndex = items.findIndex((item)=>item.productId === selectedProduct.id);\n        if (existingItemIndex >= 0) {\n            // Update existing item\n            const updatedItems = [\n                ...items\n            ];\n            const newQuantity = updatedItems[existingItemIndex].quantity + quantity;\n            if (newQuantity > selectedProduct.stockQuantity) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Cannot add more than \".concat(selectedProduct.stockQuantity, \" units of this product\"));\n                return;\n            }\n            updatedItems[existingItemIndex].quantity = newQuantity;\n            // Make sure the product name is set\n            if (!updatedItems[existingItemIndex].productName) {\n                updatedItems[existingItemIndex].productName = selectedProduct.name;\n            }\n            // Update the state with the new array\n            console.log(\"Updating existing item. New items array:\", updatedItems);\n            setItems([\n                ...updatedItems\n            ]); // Create a new array reference to force re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Updated quantity of \".concat(selectedProduct.name));\n        } else {\n            // Add new item\n            const newItem = {\n                productId: selectedProduct.id,\n                productName: selectedProduct.name,\n                quantity,\n                price: typeof selectedProduct.price === \"string\" ? parseFloat(selectedProduct.price) : selectedProduct.price\n            };\n            // Create a new array with the new item\n            const newItems = [\n                ...items,\n                newItem\n            ];\n            // Update the state with the new array\n            console.log(\"Adding new item. New items array:\", newItems);\n            setItems(newItems); // This should trigger a re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Added \".concat(quantity, \" \").concat(selectedProduct.name, \" to sale\"));\n        }\n        // Reset selection\n        setSelectedProduct(null);\n        setQuantity(1);\n        if (form) {\n            form.setFieldsValue({\n                productId: undefined,\n                quantity: 1\n            });\n        }\n    };\n    // Handle removing an item from the sale\n    const handleRemoveItem = (index)=>{\n        const updatedItems = [\n            ...items\n        ];\n        updatedItems.splice(index, 1);\n        setItems(updatedItems);\n    };\n    // Handle printing receipt - directly trigger print dialog\n    const handlePrintReceipt = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n            if (!receiptUrl || hasPrinted) {\n                console.log(\"Skipping print: \", !receiptUrl ? \"No receipt URL\" : \"Already printed\");\n                return;\n            }\n            console.log(\"Printing receipt:\", receiptUrl);\n            // Mark as printed immediately to prevent multiple print dialogs\n            setHasPrinted(true);\n            // Create a hidden iframe to load the image\n            const iframe = document.createElement(\"iframe\");\n            iframe.style.display = \"none\";\n            document.body.appendChild(iframe);\n            // Set up the iframe content with the image and print CSS\n            iframe.onload = ({\n                \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                    if (iframe.contentWindow) {\n                        // Write the HTML content to the iframe\n                        iframe.contentWindow.document.write('\\n          <!DOCTYPE html>\\n          <html>\\n            <head>\\n              <title>Print Receipt</title>\\n              <style>\\n                body {\\n                  margin: 0;\\n                  padding: 0;\\n                  display: flex;\\n                  justify-content: center;\\n                  align-items: center;\\n                  height: 100vh;\\n                }\\n                img {\\n                  max-width: 100%;\\n                  max-height: 100vh;\\n                }\\n                @media print {\\n                  body {\\n                    margin: 0;\\n                    padding: 0;\\n                  }\\n                  img {\\n                    width: 100%;\\n                    height: auto;\\n                  }\\n                }\\n              </style>\\n            </head>\\n            <body>\\n              <img src=\"'.concat(receiptUrl, '\" alt=\"Receipt\" />\\n            </body>\\n          </html>\\n        '));\n                        // Close the document\n                        iframe.contentWindow.document.close();\n                        // Use a single print trigger with a delay to ensure the image is loaded\n                        setTimeout({\n                            \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                if (iframe.contentWindow) {\n                                    try {\n                                        // Print the iframe content\n                                        iframe.contentWindow.focus();\n                                        iframe.contentWindow.print();\n                                    } catch (e) {\n                                        console.error(\"Error printing receipt:\", e);\n                                    }\n                                    // Remove the iframe after printing\n                                    setTimeout({\n                                        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                            document.body.removeChild(iframe);\n                                        }\n                                    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 1000);\n                                }\n                            }\n                        }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 500);\n                    }\n                }\n            })[\"SalesFormPanel.useCallback[handlePrintReceipt]\"];\n            // Set the iframe source to trigger the onload event\n            iframe.src = \"about:blank\";\n        }\n    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], [\n        receiptUrl,\n        hasPrinted\n    ]);\n    // Effect to automatically print receipt when modal is shown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (receiptPreviewVisible && receiptUrl && !hasPrinted) {\n                // Add a small delay to ensure the receipt image is loaded\n                const timer = setTimeout({\n                    \"SalesFormPanel.useEffect.timer\": ()=>{\n                        handlePrintReceipt(); // This now handles the hasPrinted state internally\n                    }\n                }[\"SalesFormPanel.useEffect.timer\"], 800);\n                return ({\n                    \"SalesFormPanel.useEffect\": ()=>clearTimeout(timer)\n                })[\"SalesFormPanel.useEffect\"];\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        receiptPreviewVisible,\n        receiptUrl,\n        hasPrinted,\n        handlePrintReceipt\n    ]);\n    // Handle form submission\n    const handleSubmit = async ()=>{\n        try {\n            var _userStoresData_data;\n            if (items.length === 0) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Please add at least one item to the sale\");\n                return;\n            }\n            // Validate form fields\n            const values = await form.validateFields();\n            // Check if store is selected\n            if (!selectedStore) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"No store information available. Please set up your store in your profile settings.\");\n                return;\n            }\n            // Set loading state for receipt generation\n            setIsGeneratingReceipt(true);\n            // Get store information for receipt\n            const storeInfo = selectedStore || (userStoresData === null || userStoresData === void 0 ? void 0 : (_userStoresData_data = userStoresData.data) === null || _userStoresData_data === void 0 ? void 0 : _userStoresData_data.find((store)=>store.id === values.storeId)) || {\n                name: \"POS System\"\n            };\n            // Generate receipt HTML\n            const receiptHTML = (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__.generateReceiptHTML)({\n                id: Date.now(),\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                transactionDate: new Date().toISOString(),\n                items: items.map((item)=>({\n                        productName: item.productName,\n                        quantity: item.quantity,\n                        price: item.price\n                    }))\n            }, storeInfo);\n            // Generate receipt image and get URL\n            let receiptUrl = \"https://receipt.example.com/placeholder\";\n            try {\n                receiptUrl = await (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__.generateReceiptImage)(receiptHTML);\n            } catch (error) {\n                console.error(\"Failed to generate receipt image:\", error);\n            // Continue with placeholder URL if image generation fails\n            }\n            const saleData = {\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                items: items.map((item)=>({\n                        productId: item.productId,\n                        quantity: item.quantity,\n                        price: item.price\n                    })),\n                receiptUrl,\n                storeId: selectedStore === null || selectedStore === void 0 ? void 0 : selectedStore.id\n            };\n            const response = await createSale(saleData).unwrap();\n            if (response.success) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Sale created successfully\");\n                // Store the receipt URL for preview\n                setReceiptUrl(receiptUrl);\n                // Show receipt preview modal and offer print option\n                setReceiptPreviewVisible(true);\n                // Refresh product data to get updated stock quantities\n                refetchProducts();\n                // Trigger the success callback to refresh the list WITHOUT closing the panel\n                setTimeout(()=>{\n                    if (onSuccess) {\n                        // Call refetch directly instead of closing the panel\n                        refetchProducts();\n                    }\n                }, 300);\n            // Keep the panel open until the user explicitly closes it\n            // This ensures the receipt modal stays visible\n            } else {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", response.message || \"Failed to create sale\");\n            }\n        } catch (error) {\n            var _error_data;\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", ((_error_data = error.data) === null || _error_data === void 0 ? void 0 : _error_data.message) || \"An error occurred while creating the sale\");\n        } finally{\n            setIsGeneratingReceipt(false);\n        }\n    };\n    // Debug log to check items state when rendering\n    console.log(\"Rendering with items:\", items);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        title: \"Point of Sale System\",\n        isOpen: isOpen,\n        onClose: onClose,\n        width: \"98%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sales-form min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-20 border-b border-gray-200 bg-white px-6 py-4 shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"text-xl text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-gray-800\",\n                                                    children: \"Point of Sale\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: (selectedStore === null || selectedStore === void 0 ? void 0 : selectedStore.name) || 'NEXAPO POS System'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Transaction Total\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-green-600\",\n                                                    children: [\n                                                        \"GHS \",\n                                                        totalAmount.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Items\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl font-semibold text-gray-700\",\n                                                    children: items.length\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-6 xl:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"xl:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-blue-500\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"text-sm text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 562,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 561,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-bold text-gray-800\",\n                                                                            children: \"Product Selection\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 564,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        (isLoadingProducts || isFetchingProducts) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 rounded-full bg-blue-100 px-3 py-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    className: \"text-blue-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 569,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-blue-600\",\n                                                                                    children: \"Loading fresh data...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 570,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 568,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 560,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 576,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            onClick: ()=>{\n                                                                                console.log(\"🔄 Manual refresh triggered\");\n                                                                                refetchProducts();\n                                                                                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"info\", \"Refreshing product data...\");\n                                                                            },\n                                                                            loading: isLoadingProducts || isFetchingProducts,\n                                                                            size: \"small\",\n                                                                            className: \"border-blue-300 text-blue-600 hover:bg-blue-50\",\n                                                                            title: \"Refresh product data from database\",\n                                                                            children: \"Refresh\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 575,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"rounded-full bg-white px-3 py-1 text-xs text-gray-600 shadow-sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"mr-1 text-red-500\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 590,\n                                                                                    columnNumber: 25\n                                                                                }, undefined),\n                                                                                \" Required fields\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 589,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 574,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        (productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 flex items-center space-x-2 text-xs text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 596,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        productsData.data.products.length,\n                                                                        \" products loaded from database\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-600\",\n                                                                    children: [\n                                                                        \"• Last updated: \",\n                                                                        new Date().toLocaleTimeString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 600,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        form: form,\n                                                        layout: \"vertical\",\n                                                        initialValues: {\n                                                            paymentMethod: \"cash\",\n                                                            quantity: 1\n                                                        },\n                                                        className: \"product-form\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 gap-6 lg:grid-cols-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"lg:col-span-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                                            name: \"productId\",\n                                                                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"flex items-center text-sm font-semibold text-gray-700\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"mr-2 text-blue-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 623,\n                                                                                        columnNumber: 31\n                                                                                    }, void 0),\n                                                                                    \"Select Product \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"ml-1 text-red-500\",\n                                                                                        children: \"*\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 624,\n                                                                                        columnNumber: 46\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 622,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            className: \"mb-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                showSearch: true,\n                                                                                placeholder: isLoadingProducts ? \"Loading products...\" : \"🔍 Search and select a product...\",\n                                                                                optionFilterProp: \"children\",\n                                                                                loading: isLoadingProducts,\n                                                                                disabled: isLoadingProducts,\n                                                                                onChange: (value)=>{\n                                                                                    var _productsData_data;\n                                                                                    const product = productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products.find((p)=>p.id === value);\n                                                                                    console.log(\"Selected product:\", product);\n                                                                                    if (product) {\n                                                                                        // Make a deep copy to avoid reference issues\n                                                                                        setSelectedProduct({\n                                                                                            ...product,\n                                                                                            // Ensure price is properly formatted\n                                                                                            price: typeof product.price === \"string\" ? product.price : String(product.price)\n                                                                                        });\n                                                                                    } else {\n                                                                                        setSelectedProduct(null);\n                                                                                    }\n                                                                                },\n                                                                                onSearch: setSearchTerm,\n                                                                                filterOption: false,\n                                                                                className: \"rounded-lg border-gray-300 text-gray-800 shadow-sm\",\n                                                                                size: \"large\",\n                                                                                suffixIcon: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    spin: true,\n                                                                                    className: \"text-blue-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 664,\n                                                                                    columnNumber: 33\n                                                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    className: \"text-gray-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 666,\n                                                                                    columnNumber: 33\n                                                                                }, void 0),\n                                                                                notFoundContent: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-center py-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                            size: \"small\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 672,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"ml-2 text-gray-500\",\n                                                                                            children: \"Loading products...\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 673,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 671,\n                                                                                    columnNumber: 33\n                                                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"py-4 text-center text-gray-500\",\n                                                                                    children: \"No products found\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 676,\n                                                                                    columnNumber: 33\n                                                                                }, void 0),\n                                                                                children: productsData === null || productsData === void 0 ? void 0 : (_productsData_data1 = productsData.data) === null || _productsData_data1 === void 0 ? void 0 : (_productsData_data_products = _productsData_data1.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Option, {\n                                                                                        value: product.id,\n                                                                                        disabled: product.stockQuantity <= 0,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center justify-between py-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex-1\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"font-medium text-gray-800\",\n                                                                                                            children: product.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 690,\n                                                                                                            columnNumber: 37\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"text-sm text-gray-500\",\n                                                                                                            children: [\n                                                                                                                \"GHS \",\n                                                                                                                Number(product.price).toFixed(2)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 693,\n                                                                                                            columnNumber: 37\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 689,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-right\",\n                                                                                                    children: product.stockQuantity <= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: \"rounded-full bg-red-100 px-2 py-1 text-xs font-medium text-red-600\",\n                                                                                                        children: \"Out of Stock\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 699,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: \"rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-600\",\n                                                                                                        children: [\n                                                                                                            \"Stock: \",\n                                                                                                            product.stockQuantity\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 703,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 697,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 688,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, product.id, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 683,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 629,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 619,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                                            name: \"quantity\",\n                                                                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"flex items-center text-sm font-semibold text-gray-700\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mr-2\",\n                                                                                        children: \"\\uD83D\\uDCE6\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 720,\n                                                                                        columnNumber: 31\n                                                                                    }, void 0),\n                                                                                    \"Quantity \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"ml-1 text-red-500\",\n                                                                                        children: \"*\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 721,\n                                                                                        columnNumber: 40\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 719,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            className: \"mb-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                min: 1,\n                                                                                max: (selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.stockQuantity) || 999,\n                                                                                value: quantity,\n                                                                                onChange: (value)=>setQuantity(value || 1),\n                                                                                style: {\n                                                                                    width: \"100%\"\n                                                                                },\n                                                                                className: \"rounded-lg border-gray-300 text-gray-800 shadow-sm\",\n                                                                                size: \"large\",\n                                                                                placeholder: \"Enter quantity\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 726,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 716,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 715,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            selectedProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 rounded-lg border border-blue-200 bg-blue-50 p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-semibold text-gray-800\",\n                                                                                    children: selectedProduct.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 745,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"Price: GHS \",\n                                                                                        Number(selectedProduct.price).toFixed(2),\n                                                                                        \" | Available: \",\n                                                                                        selectedProduct.stockQuantity,\n                                                                                        \" units\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 748,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 744,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-right\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"Subtotal\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 754,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-lg font-bold text-green-600\",\n                                                                                    children: [\n                                                                                        \"GHS \",\n                                                                                        (Number(selectedProduct.price) * quantity).toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 755,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 753,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 743,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 765,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                onClick: handleAddItem,\n                                                                className: \"mt-6 h-14 w-full rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 text-lg font-semibold shadow-lg hover:from-blue-600 hover:to-blue-700 hover:shadow-xl\",\n                                                                disabled: !selectedProduct,\n                                                                size: \"large\",\n                                                                children: \"\\uD83D\\uDED2 Add to Cart\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 763,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-b border-gray-100 bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-green-500\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"text-sm text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 783,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 782,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-bold text-gray-800\",\n                                                                        children: \"Shopping Cart\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 785,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"rounded-full bg-white px-3 py-1 text-sm font-medium text-gray-600 shadow-sm\",\n                                                                        children: [\n                                                                            items.length,\n                                                                            \" \",\n                                                                            items.length === 1 ? 'item' : 'items'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 790,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"rounded-full bg-green-100 px-3 py-1 text-sm font-bold text-green-700\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 793,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-[400px] overflow-x-auto overflow-y-auto\",\n                                                    children: items.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center justify-center py-16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-gray-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"text-3xl text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 803,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"mb-2 text-lg font-semibold text-gray-600\",\n                                                                children: \"Your cart is empty\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 805,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Add products to start a new transaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 808,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"divide-y divide-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-12 gap-4 bg-gray-50 px-6 py-3 text-sm font-semibold text-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-5\",\n                                                                        children: \"Product\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 816,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-2 text-center\",\n                                                                        children: \"Qty\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 817,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-2 text-right\",\n                                                                        children: \"Price\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 818,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-2 text-right\",\n                                                                        children: \"Subtotal\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 819,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-1 text-center\",\n                                                                        children: \"Action\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 820,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 815,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-12 gap-4 px-6 py-4 transition-colors hover:bg-gray-50\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-5 flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"text-blue-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 830,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 829,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-semibold text-gray-800\",\n                                                                                            children: item.productName || \"Unknown Product\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 833,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-gray-500\",\n                                                                                            children: [\n                                                                                                \"Item #\",\n                                                                                                index + 1\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 836,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 832,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 828,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"rounded-full bg-blue-100 px-3 py-1 text-sm font-semibold text-blue-700\",\n                                                                                children: item.quantity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 842,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 841,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 flex items-center justify-end\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-800\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    Number(item.price).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 847,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 846,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 flex items-center justify-end\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-bold text-green-600\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    (Number(item.price) * item.quantity).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 852,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 851,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-1 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 858,\n                                                                                    columnNumber: 37\n                                                                                }, void 0),\n                                                                                onClick: ()=>handleRemoveItem(index),\n                                                                                type: \"text\",\n                                                                                danger: true,\n                                                                                className: \"rounded-full text-red-500 hover:bg-red-50 hover:text-red-600\",\n                                                                                size: \"small\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 857,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 856,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, \"\".concat(item.productId, \"-\").concat(index), true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 824,\n                                                                    columnNumber: 25\n                                                                }, undefined)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border-t border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-semibold text-gray-800\",\n                                                                            children: \"Cart Total\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 871,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-2xl font-bold text-green-600\",\n                                                                            children: [\n                                                                                \"GHS \",\n                                                                                totalAmount.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 874,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 870,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 869,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 799,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sticky top-24 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-b border-gray-100 bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-purple-500\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"text-sm text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 891,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 890,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-bold text-gray-800\",\n                                                            children: \"Checkout\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 889,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 888,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    form: form,\n                                                    layout: \"vertical\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-6 overflow-hidden rounded-lg border border-gray-200 bg-gradient-to-br from-gray-50 to-gray-100 shadow-inner\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border-b border-gray-200 bg-white px-4 py-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-gray-800\",\n                                                                        children: \"Order Summary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 904,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 903,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-4 space-y-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"flex items-center text-gray-600\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"mr-2\",\n                                                                                            children: \"\\uD83D\\uDCE6\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 909,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        \" Items\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 908,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"rounded-full bg-blue-100 px-2 py-1 text-sm font-semibold text-blue-700\",\n                                                                                    children: items.length\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 911,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 907,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"flex items-center text-gray-600\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"mr-2\",\n                                                                                            children: \"\\uD83D\\uDD22\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 917,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        \" Total Quantity\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 916,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium text-gray-800\",\n                                                                                    children: items.reduce((sum, item)=>sum + item.quantity, 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 919,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 915,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between border-t border-gray-300 pt-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"flex items-center text-lg font-semibold text-gray-800\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"mr-2\",\n                                                                                            children: \"\\uD83D\\uDCB0\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 925,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        \" Total Amount\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 924,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-2xl font-bold text-green-600\",\n                                                                                    children: [\n                                                                                        \"GHS \",\n                                                                                        totalAmount.toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 927,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 923,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 906,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 902,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        selectedStore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"mb-3 block text-sm font-semibold text-gray-700\",\n                                                                    children: \"Store Information\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 937,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"rounded-lg border border-green-200 bg-green-50 p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-green-500\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"text-white\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 943,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 942,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-semibold text-gray-800\",\n                                                                                            children: selectedStore.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 946,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-gray-600\",\n                                                                                            children: \"Active Store\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 949,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 945,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 941,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"hidden\",\n                                                                            name: \"storeId\",\n                                                                            value: selectedStore.id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 954,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 940,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 936,\n                                                            columnNumber: 23\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-6 rounded-lg border border-orange-200 bg-orange-50 p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-orange-500\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 965,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 964,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-semibold text-orange-800\",\n                                                                                children: \"No Store Selected\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 968,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-orange-600\",\n                                                                                children: \"Please set up your store in profile settings\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 971,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 967,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 963,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 962,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                            name: \"paymentMethod\",\n                                                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"flex items-center text-sm font-semibold text-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-2\",\n                                                                        children: \"\\uD83D\\uDCB3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 983,\n                                                                        columnNumber: 27\n                                                                    }, void 0),\n                                                                    \"Payment Method \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-1 text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 984,\n                                                                        columnNumber: 42\n                                                                    }, void 0)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 982,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            rules: [\n                                                                {\n                                                                    required: true,\n                                                                    message: \"Please select a payment method\"\n                                                                }\n                                                            ],\n                                                            initialValue: \"cash\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"rounded-lg border-gray-300 text-gray-800 shadow-sm\",\n                                                                size: \"large\",\n                                                                placeholder: \"Select payment method\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Option, {\n                                                                        value: \"cash\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between py-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"mr-3 text-lg\",\n                                                                                            children: \"\\uD83D\\uDCB5\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 1003,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"font-medium\",\n                                                                                                    children: \"Cash Payment\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 1005,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-gray-500\",\n                                                                                                    children: \"Physical cash transaction\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 1006,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 1004,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 1002,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"rounded-full bg-green-100 px-2 py-1 text-xs text-green-600\",\n                                                                                    children: \"Instant\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 1009,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 1001,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1000,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Option, {\n                                                                        value: \"card\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between py-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"mr-3 text-lg\",\n                                                                                            children: \"\\uD83D\\uDCB3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 1017,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"font-medium\",\n                                                                                                    children: \"Card Payment\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 1019,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-gray-500\",\n                                                                                                    children: \"Credit/Debit card\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 1020,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 1018,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 1016,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-600\",\n                                                                                    children: \"Secure\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 1023,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 1015,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1014,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Option, {\n                                                                        value: \"mobile_money\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between py-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"mr-3 text-lg\",\n                                                                                            children: \"\\uD83D\\uDCF1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 1031,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"font-medium\",\n                                                                                                    children: \"Mobile Money\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 1033,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-gray-500\",\n                                                                                                    children: \"MTN, Vodafone, AirtelTigo\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 1034,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 1032,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 1030,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"rounded-full bg-purple-100 px-2 py-1 text-xs text-purple-600\",\n                                                                                    children: \"Popular\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 1037,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 1029,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1028,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 995,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 979,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-8 space-y-4\",\n                                                            children: [\n                                                                receiptPreviewVisible ? // Enhanced \"New Sale\" button when receipt is visible\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    type: \"primary\",\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1050,\n                                                                        columnNumber: 33\n                                                                    }, void 0),\n                                                                    onClick: ()=>{\n                                                                        // Close the modal and reset the receipt state\n                                                                        setReceiptPreviewVisible(false);\n                                                                        setReceiptUrl(null);\n                                                                        setHasPrinted(false);\n                                                                        // Reset the form to start a new sale\n                                                                        form.resetFields();\n                                                                        setItems([]);\n                                                                        setSelectedProduct(null);\n                                                                        setQuantity(1);\n                                                                        setTotalAmount(0);\n                                                                    },\n                                                                    className: \"h-16 w-full rounded-lg bg-gradient-to-r from-green-500 to-green-600 text-lg font-bold shadow-lg hover:from-green-600 hover:to-green-700 hover:shadow-xl\",\n                                                                    size: \"large\",\n                                                                    children: \"\\uD83D\\uDED2 Start New Sale\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1048,\n                                                                    columnNumber: 25\n                                                                }, undefined) : // Enhanced \"Complete Sale\" button\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    type: \"primary\",\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1073,\n                                                                        columnNumber: 33\n                                                                    }, void 0),\n                                                                    onClick: handleSubmit,\n                                                                    loading: isSubmitting || isGeneratingReceipt,\n                                                                    disabled: items.length === 0,\n                                                                    className: \"h-16 w-full rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 text-lg font-bold shadow-lg hover:from-blue-600 hover:to-blue-700 hover:shadow-xl disabled:from-gray-400 disabled:to-gray-500\",\n                                                                    size: \"large\",\n                                                                    children: isGeneratingReceipt ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1082,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            \"Generating Receipt...\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1081,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            \"\\uD83D\\uDCB3 Complete Sale - GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1086,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1071,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    onClick: onClose,\n                                                                    className: \"h-12 w-full rounded-lg border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400\",\n                                                                    size: \"large\",\n                                                                    children: \"❌ Cancel Transaction\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1093,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 1045,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 900,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 898,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 887,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 886,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 553,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 519,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-gray-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            className: \"mr-2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1113,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Receipt Preview\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1114,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 1112,\n                    columnNumber: 11\n                }, void 0),\n                open: receiptPreviewVisible,\n                onCancel: ()=>{\n                    // Close the modal and reset the receipt state\n                    setReceiptPreviewVisible(false);\n                    setReceiptUrl(null);\n                    setHasPrinted(false);\n                    // Reset the form to start a new sale\n                    form.resetFields();\n                    setItems([]);\n                    setSelectedProduct(null);\n                    setQuantity(1);\n                    setTotalAmount(0);\n                },\n                width: 500,\n                centered: true,\n                className: \"receipt-preview-modal\",\n                footer: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        onClick: ()=>{\n                            // Close the modal and reset the receipt state\n                            setReceiptPreviewVisible(false);\n                            setReceiptUrl(null);\n                            setHasPrinted(false);\n                            // Reset the form to start a new sale\n                            form.resetFields();\n                            setItems([]);\n                            setSelectedProduct(null);\n                            setQuantity(1);\n                            setTotalAmount(0);\n                        },\n                        className: \"border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200\",\n                        children: \"Close & New Sale\"\n                    }, \"close\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1135,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        type: \"primary\",\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DatabaseOutlined_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_ReloadOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1157,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: ()=>{\n                            // If already printed once, reset the flag to allow printing again\n                            if (hasPrinted) {\n                                setHasPrinted(false);\n                            }\n                            handlePrintReceipt();\n                        },\n                        className: \"bg-blue-600 hover:bg-blue-700\",\n                        children: hasPrinted ? \"Print Again\" : \"Print Receipt\"\n                    }, \"print\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1154,\n                        columnNumber: 11\n                    }, void 0)\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: receiptUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"receipt-image-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            src: receiptUrl,\n                            alt: \"Receipt\",\n                            className: \"receipt-image\",\n                            style: {\n                                maxWidth: \"100%\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1174,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1173,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-64 items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            size: \"large\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1183,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1182,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 1171,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 1110,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n        lineNumber: 513,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SalesFormPanel, \"Q/g9tGgox4eIYTijyg3KJWlCIZ8=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm,\n        _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery,\n        _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation\n    ];\n});\n_c = SalesFormPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SalesFormPanel);\nvar _c;\n$RefreshReg$(_c, \"SalesFormPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx\n"));

/***/ })

});