"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-notification";
exports.ids = ["vendor-chunks/rc-notification"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-notification/es/Notice.js":
/*!***************************************************!*\
  !*** ./node_modules/rc-notification/es/Notice.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/KeyCode */ \"(ssr)/./node_modules/rc-util/es/KeyCode.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var rc_util_es_pickAttrs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/pickAttrs */ \"(ssr)/./node_modules/rc-util/es/pickAttrs.js\");\n\n\n\n\n\n\n\n\nvar Notify = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    className = props.className,\n    _props$duration = props.duration,\n    duration = _props$duration === void 0 ? 4.5 : _props$duration,\n    showProgress = props.showProgress,\n    _props$pauseOnHover = props.pauseOnHover,\n    pauseOnHover = _props$pauseOnHover === void 0 ? true : _props$pauseOnHover,\n    eventKey = props.eventKey,\n    content = props.content,\n    closable = props.closable,\n    _props$closeIcon = props.closeIcon,\n    closeIcon = _props$closeIcon === void 0 ? 'x' : _props$closeIcon,\n    divProps = props.props,\n    onClick = props.onClick,\n    onNoticeClose = props.onNoticeClose,\n    times = props.times,\n    forcedHovering = props.hovering;\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_6__.useState(false),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_React$useState, 2),\n    hovering = _React$useState2[0],\n    setHovering = _React$useState2[1];\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_6__.useState(0),\n    _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_React$useState3, 2),\n    percent = _React$useState4[0],\n    setPercent = _React$useState4[1];\n  var _React$useState5 = react__WEBPACK_IMPORTED_MODULE_6__.useState(0),\n    _React$useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_React$useState5, 2),\n    spentTime = _React$useState6[0],\n    setSpentTime = _React$useState6[1];\n  var mergedHovering = forcedHovering || hovering;\n  var mergedShowProgress = duration > 0 && showProgress;\n\n  // ======================== Close =========================\n  var onInternalClose = function onInternalClose() {\n    onNoticeClose(eventKey);\n  };\n  var onCloseKeyDown = function onCloseKeyDown(e) {\n    if (e.key === 'Enter' || e.code === 'Enter' || e.keyCode === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_5__[\"default\"].ENTER) {\n      onInternalClose();\n    }\n  };\n\n  // ======================== Effect ========================\n  react__WEBPACK_IMPORTED_MODULE_6__.useEffect(function () {\n    if (!mergedHovering && duration > 0) {\n      var start = Date.now() - spentTime;\n      var timeout = setTimeout(function () {\n        onInternalClose();\n      }, duration * 1000 - spentTime);\n      return function () {\n        if (pauseOnHover) {\n          clearTimeout(timeout);\n        }\n        setSpentTime(Date.now() - start);\n      };\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [duration, mergedHovering, times]);\n  react__WEBPACK_IMPORTED_MODULE_6__.useEffect(function () {\n    if (!mergedHovering && mergedShowProgress && (pauseOnHover || spentTime === 0)) {\n      var start = performance.now();\n      var animationFrame;\n      var calculate = function calculate() {\n        cancelAnimationFrame(animationFrame);\n        animationFrame = requestAnimationFrame(function (timestamp) {\n          var runtime = timestamp + spentTime - start;\n          var progress = Math.min(runtime / (duration * 1000), 1);\n          setPercent(progress * 100);\n          if (progress < 1) {\n            calculate();\n          }\n        });\n      };\n      calculate();\n      return function () {\n        if (pauseOnHover) {\n          cancelAnimationFrame(animationFrame);\n        }\n      };\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [duration, spentTime, mergedHovering, mergedShowProgress, times]);\n\n  // ======================== Closable ========================\n  var closableObj = react__WEBPACK_IMPORTED_MODULE_6__.useMemo(function () {\n    if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(closable) === 'object' && closable !== null) {\n      return closable;\n    }\n    if (closable) {\n      return {\n        closeIcon: closeIcon\n      };\n    }\n    return {};\n  }, [closable, closeIcon]);\n  var ariaProps = (0,rc_util_es_pickAttrs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(closableObj, true);\n\n  // ======================== Progress ========================\n  var validPercent = 100 - (!percent || percent < 0 ? 0 : percent > 100 ? 100 : percent);\n\n  // ======================== Render ========================\n  var noticePrefixCls = \"\".concat(prefixCls, \"-notice\");\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, divProps, {\n    ref: ref,\n    className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(noticePrefixCls, className, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(noticePrefixCls, \"-closable\"), closable)),\n    style: style,\n    onMouseEnter: function onMouseEnter(e) {\n      var _divProps$onMouseEnte;\n      setHovering(true);\n      divProps === null || divProps === void 0 || (_divProps$onMouseEnte = divProps.onMouseEnter) === null || _divProps$onMouseEnte === void 0 || _divProps$onMouseEnte.call(divProps, e);\n    },\n    onMouseLeave: function onMouseLeave(e) {\n      var _divProps$onMouseLeav;\n      setHovering(false);\n      divProps === null || divProps === void 0 || (_divProps$onMouseLeav = divProps.onMouseLeave) === null || _divProps$onMouseLeav === void 0 || _divProps$onMouseLeav.call(divProps, e);\n    },\n    onClick: onClick\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"div\", {\n    className: \"\".concat(noticePrefixCls, \"-content\")\n  }, content), closable && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"a\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    tabIndex: 0,\n    className: \"\".concat(noticePrefixCls, \"-close\"),\n    onKeyDown: onCloseKeyDown,\n    \"aria-label\": \"Close\"\n  }, ariaProps, {\n    onClick: function onClick(e) {\n      e.preventDefault();\n      e.stopPropagation();\n      onInternalClose();\n    }\n  }), closableObj.closeIcon), mergedShowProgress && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"progress\", {\n    className: \"\".concat(noticePrefixCls, \"-progress\"),\n    max: \"100\",\n    value: validPercent\n  }, validPercent + '%'));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Notify);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/Notice.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/NoticeList.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-notification/es/NoticeList.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var rc_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-motion */ \"(ssr)/./node_modules/rc-motion/es/index.js\");\n/* harmony import */ var _Notice__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Notice */ \"(ssr)/./node_modules/rc-notification/es/Notice.js\");\n/* harmony import */ var _NotificationProvider__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./NotificationProvider */ \"(ssr)/./node_modules/rc-notification/es/NotificationProvider.js\");\n/* harmony import */ var _hooks_useStack__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./hooks/useStack */ \"(ssr)/./node_modules/rc-notification/es/hooks/useStack.js\");\n\n\n\n\n\n\nvar _excluded = [\"className\", \"style\", \"classNames\", \"styles\"];\n\n\n\n\n\n\nvar NoticeList = function NoticeList(props) {\n  var configList = props.configList,\n    placement = props.placement,\n    prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    motion = props.motion,\n    onAllNoticeRemoved = props.onAllNoticeRemoved,\n    onNoticeClose = props.onNoticeClose,\n    stackConfig = props.stack;\n  var _useContext = (0,react__WEBPACK_IMPORTED_MODULE_6__.useContext)(_NotificationProvider__WEBPACK_IMPORTED_MODULE_10__.NotificationContext),\n    ctxCls = _useContext.classNames;\n  var dictRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)({});\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_useState, 2),\n    latestNotice = _useState2[0],\n    setLatestNotice = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)([]),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_useState3, 2),\n    hoverKeys = _useState4[0],\n    setHoverKeys = _useState4[1];\n  var keys = configList.map(function (config) {\n    return {\n      config: config,\n      key: String(config.key)\n    };\n  });\n  var _useStack = (0,_hooks_useStack__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(stackConfig),\n    _useStack2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_useStack, 2),\n    stack = _useStack2[0],\n    _useStack2$ = _useStack2[1],\n    offset = _useStack2$.offset,\n    threshold = _useStack2$.threshold,\n    gap = _useStack2$.gap;\n  var expanded = stack && (hoverKeys.length > 0 || keys.length <= threshold);\n  var placementMotion = typeof motion === 'function' ? motion(placement) : motion;\n\n  // Clean hover key\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    if (stack && hoverKeys.length > 1) {\n      setHoverKeys(function (prev) {\n        return prev.filter(function (key) {\n          return keys.some(function (_ref) {\n            var dataKey = _ref.key;\n            return key === dataKey;\n          });\n        });\n      });\n    }\n  }, [hoverKeys, keys, stack]);\n\n  // Force update latest notice\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    var _keys;\n    if (stack && dictRef.current[(_keys = keys[keys.length - 1]) === null || _keys === void 0 ? void 0 : _keys.key]) {\n      var _keys2;\n      setLatestNotice(dictRef.current[(_keys2 = keys[keys.length - 1]) === null || _keys2 === void 0 ? void 0 : _keys2.key]);\n    }\n  }, [keys, stack]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6___default().createElement(rc_motion__WEBPACK_IMPORTED_MODULE_8__.CSSMotionList, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    key: placement,\n    className: classnames__WEBPACK_IMPORTED_MODULE_7___default()(prefixCls, \"\".concat(prefixCls, \"-\").concat(placement), ctxCls === null || ctxCls === void 0 ? void 0 : ctxCls.list, className, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_4__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({}, \"\".concat(prefixCls, \"-stack\"), !!stack), \"\".concat(prefixCls, \"-stack-expanded\"), expanded)),\n    style: style,\n    keys: keys,\n    motionAppear: true\n  }, placementMotion, {\n    onAllRemoved: function onAllRemoved() {\n      onAllNoticeRemoved(placement);\n    }\n  }), function (_ref2, nodeRef) {\n    var config = _ref2.config,\n      motionClassName = _ref2.className,\n      motionStyle = _ref2.style,\n      motionIndex = _ref2.index;\n    var _ref3 = config,\n      key = _ref3.key,\n      times = _ref3.times;\n    var strKey = String(key);\n    var _ref4 = config,\n      configClassName = _ref4.className,\n      configStyle = _ref4.style,\n      configClassNames = _ref4.classNames,\n      configStyles = _ref4.styles,\n      restConfig = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_ref4, _excluded);\n    var dataIndex = keys.findIndex(function (item) {\n      return item.key === strKey;\n    });\n\n    // If dataIndex is -1, that means this notice has been removed in data, but still in dom\n    // Should minus (motionIndex - 1) to get the correct index because keys.length is not the same as dom length\n    var stackStyle = {};\n    if (stack) {\n      var index = keys.length - 1 - (dataIndex > -1 ? dataIndex : motionIndex - 1);\n      var transformX = placement === 'top' || placement === 'bottom' ? '-50%' : '0';\n      if (index > 0) {\n        var _dictRef$current$strK, _dictRef$current$strK2, _dictRef$current$strK3;\n        stackStyle.height = expanded ? (_dictRef$current$strK = dictRef.current[strKey]) === null || _dictRef$current$strK === void 0 ? void 0 : _dictRef$current$strK.offsetHeight : latestNotice === null || latestNotice === void 0 ? void 0 : latestNotice.offsetHeight;\n\n        // Transform\n        var verticalOffset = 0;\n        for (var i = 0; i < index; i++) {\n          var _dictRef$current$keys;\n          verticalOffset += ((_dictRef$current$keys = dictRef.current[keys[keys.length - 1 - i].key]) === null || _dictRef$current$keys === void 0 ? void 0 : _dictRef$current$keys.offsetHeight) + gap;\n        }\n        var transformY = (expanded ? verticalOffset : index * offset) * (placement.startsWith('top') ? 1 : -1);\n        var scaleX = !expanded && latestNotice !== null && latestNotice !== void 0 && latestNotice.offsetWidth && (_dictRef$current$strK2 = dictRef.current[strKey]) !== null && _dictRef$current$strK2 !== void 0 && _dictRef$current$strK2.offsetWidth ? ((latestNotice === null || latestNotice === void 0 ? void 0 : latestNotice.offsetWidth) - offset * 2 * (index < 3 ? index : 3)) / ((_dictRef$current$strK3 = dictRef.current[strKey]) === null || _dictRef$current$strK3 === void 0 ? void 0 : _dictRef$current$strK3.offsetWidth) : 1;\n        stackStyle.transform = \"translate3d(\".concat(transformX, \", \").concat(transformY, \"px, 0) scaleX(\").concat(scaleX, \")\");\n      } else {\n        stackStyle.transform = \"translate3d(\".concat(transformX, \", 0, 0)\");\n      }\n    }\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6___default().createElement(\"div\", {\n      ref: nodeRef,\n      className: classnames__WEBPACK_IMPORTED_MODULE_7___default()(\"\".concat(prefixCls, \"-notice-wrapper\"), motionClassName, configClassNames === null || configClassNames === void 0 ? void 0 : configClassNames.wrapper),\n      style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, motionStyle), stackStyle), configStyles === null || configStyles === void 0 ? void 0 : configStyles.wrapper),\n      onMouseEnter: function onMouseEnter() {\n        return setHoverKeys(function (prev) {\n          return prev.includes(strKey) ? prev : [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(prev), [strKey]);\n        });\n      },\n      onMouseLeave: function onMouseLeave() {\n        return setHoverKeys(function (prev) {\n          return prev.filter(function (k) {\n            return k !== strKey;\n          });\n        });\n      }\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6___default().createElement(_Notice__WEBPACK_IMPORTED_MODULE_9__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restConfig, {\n      ref: function ref(node) {\n        if (dataIndex > -1) {\n          dictRef.current[strKey] = node;\n        } else {\n          delete dictRef.current[strKey];\n        }\n      },\n      prefixCls: prefixCls,\n      classNames: configClassNames,\n      styles: configStyles,\n      className: classnames__WEBPACK_IMPORTED_MODULE_7___default()(configClassName, ctxCls === null || ctxCls === void 0 ? void 0 : ctxCls.notice),\n      style: configStyle,\n      times: times,\n      key: key,\n      eventKey: key,\n      onNoticeClose: onNoticeClose,\n      hovering: stack && hoverKeys.length > 0\n    })));\n  });\n};\nif (true) {\n  NoticeList.displayName = 'NoticeList';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NoticeList);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/NoticeList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/NotificationProvider.js":
/*!*****************************************************************!*\
  !*** ./node_modules/rc-notification/es/NotificationProvider.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationContext: () => (/* binding */ NotificationContext),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar NotificationContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext({});\nvar NotificationProvider = function NotificationProvider(_ref) {\n  var children = _ref.children,\n    classNames = _ref.classNames;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(NotificationContext.Provider, {\n    value: {\n      classNames: classNames\n    }\n  }, children);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NotificationProvider);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbm90aWZpY2F0aW9uL2VzL05vdGlmaWNhdGlvblByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEI7QUFDbkIsdUNBQXVDLDBEQUFtQixHQUFHO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwwREFBbUI7QUFDekM7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsaUVBQWUsb0JBQW9CIiwic291cmNlcyI6WyJFOlxcUFJPSkVDVFNcXHBvc1xccG9zZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmMtbm90aWZpY2F0aW9uXFxlc1xcTm90aWZpY2F0aW9uUHJvdmlkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmV4cG9ydCB2YXIgTm90aWZpY2F0aW9uQ29udGV4dCA9IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVDb250ZXh0KHt9KTtcbnZhciBOb3RpZmljYXRpb25Qcm92aWRlciA9IGZ1bmN0aW9uIE5vdGlmaWNhdGlvblByb3ZpZGVyKF9yZWYpIHtcbiAgdmFyIGNoaWxkcmVuID0gX3JlZi5jaGlsZHJlbixcbiAgICBjbGFzc05hbWVzID0gX3JlZi5jbGFzc05hbWVzO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoTm90aWZpY2F0aW9uQ29udGV4dC5Qcm92aWRlciwge1xuICAgIHZhbHVlOiB7XG4gICAgICBjbGFzc05hbWVzOiBjbGFzc05hbWVzXG4gICAgfVxuICB9LCBjaGlsZHJlbik7XG59O1xuZXhwb3J0IGRlZmF1bHQgTm90aWZpY2F0aW9uUHJvdmlkZXI7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/NotificationProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/Notifications.js":
/*!**********************************************************!*\
  !*** ./node_modules/rc-notification/es/Notifications.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _NoticeList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NoticeList */ \"(ssr)/./node_modules/rc-notification/es/NoticeList.js\");\n\n\n\n\n\n\n// ant-notification ant-notification-topRight\nvar Notifications = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.forwardRef(function (props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-notification' : _props$prefixCls,\n    container = props.container,\n    motion = props.motion,\n    maxCount = props.maxCount,\n    className = props.className,\n    style = props.style,\n    onAllRemoved = props.onAllRemoved,\n    stack = props.stack,\n    renderNotifications = props.renderNotifications;\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_3__.useState([]),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useState, 2),\n    configList = _React$useState2[0],\n    setConfigList = _React$useState2[1];\n\n  // ======================== Close =========================\n  var onNoticeClose = function onNoticeClose(key) {\n    var _config$onClose;\n    // Trigger close event\n    var config = configList.find(function (item) {\n      return item.key === key;\n    });\n    config === null || config === void 0 || (_config$onClose = config.onClose) === null || _config$onClose === void 0 || _config$onClose.call(config);\n    setConfigList(function (list) {\n      return list.filter(function (item) {\n        return item.key !== key;\n      });\n    });\n  };\n\n  // ========================= Refs =========================\n  react__WEBPACK_IMPORTED_MODULE_3__.useImperativeHandle(ref, function () {\n    return {\n      open: function open(config) {\n        setConfigList(function (list) {\n          var clone = (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(list);\n\n          // Replace if exist\n          var index = clone.findIndex(function (item) {\n            return item.key === config.key;\n          });\n          var innerConfig = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, config);\n          if (index >= 0) {\n            var _list$index;\n            innerConfig.times = (((_list$index = list[index]) === null || _list$index === void 0 ? void 0 : _list$index.times) || 0) + 1;\n            clone[index] = innerConfig;\n          } else {\n            innerConfig.times = 0;\n            clone.push(innerConfig);\n          }\n          if (maxCount > 0 && clone.length > maxCount) {\n            clone = clone.slice(-maxCount);\n          }\n          return clone;\n        });\n      },\n      close: function close(key) {\n        onNoticeClose(key);\n      },\n      destroy: function destroy() {\n        setConfigList([]);\n      }\n    };\n  });\n\n  // ====================== Placements ======================\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_3__.useState({}),\n    _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useState3, 2),\n    placements = _React$useState4[0],\n    setPlacements = _React$useState4[1];\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {\n    var nextPlacements = {};\n    configList.forEach(function (config) {\n      var _config$placement = config.placement,\n        placement = _config$placement === void 0 ? 'topRight' : _config$placement;\n      if (placement) {\n        nextPlacements[placement] = nextPlacements[placement] || [];\n        nextPlacements[placement].push(config);\n      }\n    });\n\n    // Fill exist placements to avoid empty list causing remove without motion\n    Object.keys(placements).forEach(function (placement) {\n      nextPlacements[placement] = nextPlacements[placement] || [];\n    });\n    setPlacements(nextPlacements);\n  }, [configList]);\n\n  // Clean up container if all notices fade out\n  var onAllNoticeRemoved = function onAllNoticeRemoved(placement) {\n    setPlacements(function (originPlacements) {\n      var clone = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, originPlacements);\n      var list = clone[placement] || [];\n      if (!list.length) {\n        delete clone[placement];\n      }\n      return clone;\n    });\n  };\n\n  // Effect tell that placements is empty now\n  var emptyRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef(false);\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {\n    if (Object.keys(placements).length > 0) {\n      emptyRef.current = true;\n    } else if (emptyRef.current) {\n      // Trigger only when from exist to empty\n      onAllRemoved === null || onAllRemoved === void 0 || onAllRemoved();\n      emptyRef.current = false;\n    }\n  }, [placements]);\n  // ======================== Render ========================\n  if (!container) {\n    return null;\n  }\n  var placementList = Object.keys(placements);\n  return /*#__PURE__*/(0,react_dom__WEBPACK_IMPORTED_MODULE_4__.createPortal)( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(react__WEBPACK_IMPORTED_MODULE_3__.Fragment, null, placementList.map(function (placement) {\n    var placementConfigList = placements[placement];\n    var list = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_NoticeList__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n      key: placement,\n      configList: placementConfigList,\n      placement: placement,\n      prefixCls: prefixCls,\n      className: className === null || className === void 0 ? void 0 : className(placement),\n      style: style === null || style === void 0 ? void 0 : style(placement),\n      motion: motion,\n      onNoticeClose: onNoticeClose,\n      onAllNoticeRemoved: onAllNoticeRemoved,\n      stack: stack\n    });\n    return renderNotifications ? renderNotifications(list, {\n      prefixCls: prefixCls,\n      key: placement\n    }) : list;\n  })), container);\n});\nif (true) {\n  Notifications.displayName = 'Notifications';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Notifications);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/Notifications.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/hooks/useNotification.js":
/*!******************************************************************!*\
  !*** ./node_modules/rc-notification/es/hooks/useNotification.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useNotification)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Notifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../Notifications */ \"(ssr)/./node_modules/rc-notification/es/Notifications.js\");\n\n\n\nvar _excluded = [\"getContainer\", \"motion\", \"prefixCls\", \"maxCount\", \"className\", \"style\", \"onAllRemoved\", \"stack\", \"renderNotifications\"];\n\n\nvar defaultGetContainer = function defaultGetContainer() {\n  return document.body;\n};\nvar uniqueKey = 0;\nfunction mergeConfig() {\n  var clone = {};\n  for (var _len = arguments.length, objList = new Array(_len), _key = 0; _key < _len; _key++) {\n    objList[_key] = arguments[_key];\n  }\n  objList.forEach(function (obj) {\n    if (obj) {\n      Object.keys(obj).forEach(function (key) {\n        var val = obj[key];\n        if (val !== undefined) {\n          clone[key] = val;\n        }\n      });\n    }\n  });\n  return clone;\n}\nfunction useNotification() {\n  var rootConfig = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var _rootConfig$getContai = rootConfig.getContainer,\n    getContainer = _rootConfig$getContai === void 0 ? defaultGetContainer : _rootConfig$getContai,\n    motion = rootConfig.motion,\n    prefixCls = rootConfig.prefixCls,\n    maxCount = rootConfig.maxCount,\n    className = rootConfig.className,\n    style = rootConfig.style,\n    onAllRemoved = rootConfig.onAllRemoved,\n    stack = rootConfig.stack,\n    renderNotifications = rootConfig.renderNotifications,\n    shareConfig = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(rootConfig, _excluded);\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_3__.useState(),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState, 2),\n    container = _React$useState2[0],\n    setContainer = _React$useState2[1];\n  var notificationsRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef();\n  var contextHolder = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_Notifications__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n    container: container,\n    ref: notificationsRef,\n    prefixCls: prefixCls,\n    motion: motion,\n    maxCount: maxCount,\n    className: className,\n    style: style,\n    onAllRemoved: onAllRemoved,\n    stack: stack,\n    renderNotifications: renderNotifications\n  });\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_3__.useState([]),\n    _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState3, 2),\n    taskQueue = _React$useState4[0],\n    setTaskQueue = _React$useState4[1];\n\n  // ========================= Refs =========================\n  var api = react__WEBPACK_IMPORTED_MODULE_3__.useMemo(function () {\n    return {\n      open: function open(config) {\n        var mergedConfig = mergeConfig(shareConfig, config);\n        if (mergedConfig.key === null || mergedConfig.key === undefined) {\n          mergedConfig.key = \"rc-notification-\".concat(uniqueKey);\n          uniqueKey += 1;\n        }\n        setTaskQueue(function (queue) {\n          return [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(queue), [{\n            type: 'open',\n            config: mergedConfig\n          }]);\n        });\n      },\n      close: function close(key) {\n        setTaskQueue(function (queue) {\n          return [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(queue), [{\n            type: 'close',\n            key: key\n          }]);\n        });\n      },\n      destroy: function destroy() {\n        setTaskQueue(function (queue) {\n          return [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(queue), [{\n            type: 'destroy'\n          }]);\n        });\n      }\n    };\n  }, []);\n\n  // ======================= Container ======================\n  // React 18 should all in effect that we will check container in each render\n  // Which means getContainer should be stable.\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {\n    setContainer(getContainer());\n  });\n\n  // ======================== Effect ========================\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {\n    // Flush task when node ready\n    if (notificationsRef.current && taskQueue.length) {\n      taskQueue.forEach(function (task) {\n        switch (task.type) {\n          case 'open':\n            notificationsRef.current.open(task.config);\n            break;\n          case 'close':\n            notificationsRef.current.close(task.key);\n            break;\n          case 'destroy':\n            notificationsRef.current.destroy();\n            break;\n        }\n      });\n\n      // https://github.com/ant-design/ant-design/issues/52590\n      // React `startTransition` will run once `useEffect` but many times `setState`,\n      // So `setTaskQueue` with filtered array will cause infinite loop.\n      // We cache the first match queue instead.\n      var oriTaskQueue;\n      var tgtTaskQueue;\n\n      // React 17 will mix order of effect & setState in async\n      // - open: setState[0]\n      // - effect[0]\n      // - open: setState[1]\n      // - effect setState([]) * here will clean up [0, 1] in React 17\n      setTaskQueue(function (oriQueue) {\n        if (oriTaskQueue !== oriQueue || !tgtTaskQueue) {\n          oriTaskQueue = oriQueue;\n          tgtTaskQueue = oriQueue.filter(function (task) {\n            return !taskQueue.includes(task);\n          });\n        }\n        return tgtTaskQueue;\n      });\n    }\n  }, [taskQueue]);\n\n  // ======================== Return ========================\n  return [api, contextHolder];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/hooks/useNotification.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/hooks/useStack.js":
/*!***********************************************************!*\
  !*** ./node_modules/rc-notification/es/hooks/useStack.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n\nvar DEFAULT_OFFSET = 8;\nvar DEFAULT_THRESHOLD = 3;\nvar DEFAULT_GAP = 16;\nvar useStack = function useStack(config) {\n  var result = {\n    offset: DEFAULT_OFFSET,\n    threshold: DEFAULT_THRESHOLD,\n    gap: DEFAULT_GAP\n  };\n  if (config && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(config) === 'object') {\n    var _config$offset, _config$threshold, _config$gap;\n    result.offset = (_config$offset = config.offset) !== null && _config$offset !== void 0 ? _config$offset : DEFAULT_OFFSET;\n    result.threshold = (_config$threshold = config.threshold) !== null && _config$threshold !== void 0 ? _config$threshold : DEFAULT_THRESHOLD;\n    result.gap = (_config$gap = config.gap) !== null && _config$gap !== void 0 ? _config$gap : DEFAULT_GAP;\n  }\n  return [!!config, result];\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useStack);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbm90aWZpY2F0aW9uL2VzL2hvb2tzL3VzZVN0YWNrLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXdEO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiw2RUFBTztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFlLFFBQVEiLCJzb3VyY2VzIjpbIkU6XFxQUk9KRUNUU1xccG9zXFxwb3Nmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyYy1ub3RpZmljYXRpb25cXGVzXFxob29rc1xcdXNlU3RhY2suanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF90eXBlb2YgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3R5cGVvZlwiO1xudmFyIERFRkFVTFRfT0ZGU0VUID0gODtcbnZhciBERUZBVUxUX1RIUkVTSE9MRCA9IDM7XG52YXIgREVGQVVMVF9HQVAgPSAxNjtcbnZhciB1c2VTdGFjayA9IGZ1bmN0aW9uIHVzZVN0YWNrKGNvbmZpZykge1xuICB2YXIgcmVzdWx0ID0ge1xuICAgIG9mZnNldDogREVGQVVMVF9PRkZTRVQsXG4gICAgdGhyZXNob2xkOiBERUZBVUxUX1RIUkVTSE9MRCxcbiAgICBnYXA6IERFRkFVTFRfR0FQXG4gIH07XG4gIGlmIChjb25maWcgJiYgX3R5cGVvZihjb25maWcpID09PSAnb2JqZWN0Jykge1xuICAgIHZhciBfY29uZmlnJG9mZnNldCwgX2NvbmZpZyR0aHJlc2hvbGQsIF9jb25maWckZ2FwO1xuICAgIHJlc3VsdC5vZmZzZXQgPSAoX2NvbmZpZyRvZmZzZXQgPSBjb25maWcub2Zmc2V0KSAhPT0gbnVsbCAmJiBfY29uZmlnJG9mZnNldCAhPT0gdm9pZCAwID8gX2NvbmZpZyRvZmZzZXQgOiBERUZBVUxUX09GRlNFVDtcbiAgICByZXN1bHQudGhyZXNob2xkID0gKF9jb25maWckdGhyZXNob2xkID0gY29uZmlnLnRocmVzaG9sZCkgIT09IG51bGwgJiYgX2NvbmZpZyR0aHJlc2hvbGQgIT09IHZvaWQgMCA/IF9jb25maWckdGhyZXNob2xkIDogREVGQVVMVF9USFJFU0hPTEQ7XG4gICAgcmVzdWx0LmdhcCA9IChfY29uZmlnJGdhcCA9IGNvbmZpZy5nYXApICE9PSBudWxsICYmIF9jb25maWckZ2FwICE9PSB2b2lkIDAgPyBfY29uZmlnJGdhcCA6IERFRkFVTFRfR0FQO1xuICB9XG4gIHJldHVybiBbISFjb25maWcsIHJlc3VsdF07XG59O1xuZXhwb3J0IGRlZmF1bHQgdXNlU3RhY2s7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/hooks/useStack.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/index.js":
/*!**************************************************!*\
  !*** ./node_modules/rc-notification/es/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Notice: () => (/* reexport safe */ _Notice__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   NotificationProvider: () => (/* reexport safe */ _NotificationProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   useNotification: () => (/* reexport safe */ _hooks_useNotification__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _hooks_useNotification__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hooks/useNotification */ \"(ssr)/./node_modules/rc-notification/es/hooks/useNotification.js\");\n/* harmony import */ var _Notice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Notice */ \"(ssr)/./node_modules/rc-notification/es/Notice.js\");\n/* harmony import */ var _NotificationProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NotificationProvider */ \"(ssr)/./node_modules/rc-notification/es/NotificationProvider.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbm90aWZpY2F0aW9uL2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFzRDtBQUN4QjtBQUM0QiIsInNvdXJjZXMiOlsiRTpcXFBST0pFQ1RTXFxwb3NcXHBvc2Zyb250ZW5kXFxub2RlX21vZHVsZXNcXHJjLW5vdGlmaWNhdGlvblxcZXNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB1c2VOb3RpZmljYXRpb24gZnJvbSBcIi4vaG9va3MvdXNlTm90aWZpY2F0aW9uXCI7XG5pbXBvcnQgTm90aWNlIGZyb20gXCIuL05vdGljZVwiO1xuaW1wb3J0IE5vdGlmaWNhdGlvblByb3ZpZGVyIGZyb20gXCIuL05vdGlmaWNhdGlvblByb3ZpZGVyXCI7XG5leHBvcnQgeyB1c2VOb3RpZmljYXRpb24sIE5vdGljZSwgTm90aWZpY2F0aW9uUHJvdmlkZXIgfTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/index.js\n");

/***/ })

};
;