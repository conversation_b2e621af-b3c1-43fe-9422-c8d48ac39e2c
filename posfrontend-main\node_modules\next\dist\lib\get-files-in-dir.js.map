{"version": 3, "sources": ["../../src/lib/get-files-in-dir.ts"], "sourcesContent": ["import { join } from 'path'\nimport fs from 'fs/promises'\nimport type { Dirent, StatsBase } from 'fs'\n\nexport async function getFilesInDir(path: string): Promise<Set<string>> {\n  const dir = await fs.opendir(path)\n  const results = new Set<string>()\n\n  for await (const file of dir) {\n    let resolvedFile: Dirent | StatsBase<number> = file\n\n    if (file.isSymbolicLink()) {\n      resolvedFile = await fs.stat(join(path, file.name))\n    }\n\n    if (resolvedFile.isFile()) {\n      results.add(file.name)\n    }\n  }\n\n  return results\n}\n"], "names": ["getFilesInDir", "path", "dir", "fs", "opendir", "results", "Set", "file", "resolvedFile", "isSymbolicLink", "stat", "join", "name", "isFile", "add"], "mappings": ";;;;+BAIsBA;;;eAAAA;;;sBAJD;iEACN;;;;;;AAGR,eAAeA,cAAcC,IAAY;IAC9C,MAAMC,MAAM,MAAMC,iBAAE,CAACC,OAAO,CAACH;IAC7B,MAAMI,UAAU,IAAIC;IAEpB,WAAW,MAAMC,QAAQL,IAAK;QAC5B,IAAIM,eAA2CD;QAE/C,IAAIA,KAAKE,cAAc,IAAI;YACzBD,eAAe,MAAML,iBAAE,CAACO,IAAI,CAACC,IAAAA,UAAI,EAACV,MAAMM,KAAKK,IAAI;QACnD;QAEA,IAAIJ,aAAaK,MAAM,IAAI;YACzBR,QAAQS,GAAG,CAACP,KAAKK,IAAI;QACvB;IACF;IAEA,OAAOP;AACT"}