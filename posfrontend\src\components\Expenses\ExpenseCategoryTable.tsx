"use client";

import React from "react";
import { <PERSON><PERSON>, Tooltip, Checkbox, Tag } from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import {
  EditOutlined,
  DeleteOutlined,
  TagOutlined,
  DeleteFilled,
  CrownOutlined,
} from "@ant-design/icons";
import { ExpenseCategory } from "@/reduxRTK/services/expenseCategoryApi";
import { useSelector } from "react-redux";
import { RootState } from "@/reduxRTK/store/store";
import { UserRole } from "@/types/user";

interface ExpenseCategoryTableProps {
  categories: ExpenseCategory[];
  loading?: boolean;
  onEdit?: (category: ExpenseCategory) => void;
  onDelete?: (categoryId: number) => void;
  onBulkDelete?: (categoryIds: number[]) => void;
  selectedCategories?: number[];
  onSelectionChange?: (selectedIds: number[]) => void;
  isMobile?: boolean;
}

const ExpenseCategoryTable: React.FC<ExpenseCategoryTableProps> = ({
  categories,
  loading = false,
  onEdit,
  onDelete,
  onBulkDelete,
  selectedCategories = [],
  onSelectionChange,
  isMobile = false,
}) => {
  const user = useSelector((state: RootState) => state.auth.user);
  const userRole = user?.role as UserRole;

  // Check permissions
  const canEdit = userRole === "admin" || userRole === "superadmin";
  const canDelete = userRole === "admin" || userRole === "superadmin";

  // Handle individual checkbox change
  const handleCheckboxChange = (categoryId: number, checked: boolean) => {
    if (!onSelectionChange) return;

    const newSelection = checked
      ? [...selectedCategories, categoryId]
      : selectedCategories.filter(id => id !== categoryId);
    
    onSelectionChange(newSelection);
  };

  // Handle select all checkbox
  const handleSelectAll = (e: CheckboxChangeEvent) => {
    if (!onSelectionChange) return;

    if (e.target.checked) {
      // Only select non-default categories for bulk operations
      const selectableIds = categories
        .filter(category => !category.isDefault)
        .map(category => category.id);
      onSelectionChange(selectableIds);
    } else {
      onSelectionChange([]);
    }
  };

  // Check if all selectable categories are selected
  const selectableCategories = categories.filter(category => !category.isDefault);
  const isAllSelected = selectableCategories.length > 0 && 
    selectedCategories.length === selectableCategories.length;
  const isIndeterminate = selectedCategories.length > 0 && 
    selectedCategories.length < selectableCategories.length;

  if (isMobile) {
    return (
      <div className="space-y-3">
        {/* Mobile Bulk Actions */}
        {selectedCategories.length > 0 && canDelete && onBulkDelete && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
            <div className="flex items-center justify-between">
              <span className="text-red-700 text-sm">
                {selectedCategories.length} category(s) selected
              </span>
              <Button
                type="primary"
                danger
                size="small"
                icon={<DeleteFilled />}
                onClick={() => onBulkDelete(selectedCategories)}
              >
                Delete Selected
              </Button>
            </div>
          </div>
        )}

        {/* Mobile Category Cards */}
        {categories.map((category) => (
          <div
            key={category.id}
            className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm"
          >
            {/* Header with checkbox and actions */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                {onSelectionChange && !category.isDefault && (
                  <Checkbox
                    checked={selectedCategories.includes(category.id)}
                    onChange={(e) => handleCheckboxChange(category.id, e.target.checked)}
                    className="mr-3"
                  />
                )}
                <div className="flex items-center">
                  <div
                    className="w-4 h-4 rounded-full mr-2"
                    style={{ backgroundColor: category.color }}
                  />
                  <div>
                    <h4 className="font-medium text-gray-900 text-sm flex items-center">
                      {category.name}
                      {category.isDefault && (
                        <CrownOutlined className="ml-2 text-yellow-500" title="System Default" />
                      )}
                    </h4>
                  </div>
                </div>
              </div>
              
              <div className="flex space-x-1">
                {canEdit && onEdit && !category.isDefault && (
                  <Tooltip title="Edit">
                    <Button
                      type="text"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => onEdit(category)}
                    />
                  </Tooltip>
                )}
                {canDelete && onDelete && !category.isDefault && (
                  <Tooltip title="Delete">
                    <Button
                      type="text"
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => onDelete(category.id)}
                    />
                  </Tooltip>
                )}
              </div>
            </div>

            {/* Category Details */}
            <div className="space-y-2">
              {category.description && (
                <p className="text-sm text-gray-600">{category.description}</p>
              )}
              
              <div className="flex items-center justify-between">
                <Tag color={category.color} className="text-xs">
                  {category.color}
                </Tag>
                {category.isDefault && (
                  <Tag color="gold" className="text-xs">
                    System Default
                  </Tag>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Desktop Table View
  return (
    <div className="overflow-x-auto">
      {/* Bulk Actions */}
      {selectedCategories.length > 0 && canDelete && onBulkDelete && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
          <div className="flex items-center justify-between">
            <span className="text-red-700">
              {selectedCategories.length} category(s) selected
            </span>
            <Button
              type="primary"
              danger
              icon={<DeleteFilled />}
              onClick={() => onBulkDelete(selectedCategories)}
            >
              Delete Selected
            </Button>
          </div>
        </div>
      )}

      <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
        <thead className="bg-gray-50">
          <tr>
            {onSelectionChange && (
              <th className="px-4 py-3 text-left">
                <Checkbox
                  indeterminate={isIndeterminate}
                  checked={isAllSelected}
                  onChange={handleSelectAll}
                />
              </th>
            )}
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Category
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Description
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Color
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Type
            </th>
            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {categories.map((category) => (
            <tr key={category.id} className="hover:bg-gray-50">
              {onSelectionChange && (
                <td className="px-4 py-4 whitespace-nowrap">
                  {!category.isDefault && (
                    <Checkbox
                      checked={selectedCategories.includes(category.id)}
                      onChange={(e) => handleCheckboxChange(category.id, e.target.checked)}
                    />
                  )}
                </td>
              )}
              <td className="px-4 py-4">
                <div className="flex items-center">
                  <div
                    className="w-4 h-4 rounded-full mr-3"
                    style={{ backgroundColor: category.color }}
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900 flex items-center">
                      {category.name}
                      {category.isDefault && (
                        <CrownOutlined className="ml-2 text-yellow-500" title="System Default" />
                      )}
                    </div>
                  </div>
                </div>
              </td>
              <td className="px-4 py-4">
                <div className="text-sm text-gray-900 max-w-xs truncate">
                  {category.description || '-'}
                </div>
              </td>
              <td className="px-4 py-4 whitespace-nowrap">
                <Tag color={category.color} className="text-xs">
                  {category.color}
                </Tag>
              </td>
              <td className="px-4 py-4 whitespace-nowrap">
                {category.isDefault ? (
                  <Tag color="gold" icon={<CrownOutlined />}>
                    System Default
                  </Tag>
                ) : (
                  <Tag color="blue">
                    Custom
                  </Tag>
                )}
              </td>
              <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex justify-end space-x-2">
                  {canEdit && onEdit && !category.isDefault && (
                    <Tooltip title="Edit">
                      <Button
                        type="text"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => onEdit(category)}
                      />
                    </Tooltip>
                  )}
                  {canDelete && onDelete && !category.isDefault && (
                    <Tooltip title="Delete">
                      <Button
                        type="text"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => onDelete(category.id)}
                      />
                    </Tooltip>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ExpenseCategoryTable;
