"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/sales/page",{

/***/ "(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx":
/*!*************************************************!*\
  !*** ./src/components/Sales/SalesFormPanel.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Form,Image,InputNumber,Modal,Select,Spin!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/image/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ShopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,LoadingOutlined,PlusOutlined,PrinterOutlined,SearchOutlined,ShopOutlined,ShoppingCartOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PrinterOutlined.js\");\n/* harmony import */ var _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/reduxRTK/services/salesApi */ \"(app-pages-browser)/./src/reduxRTK/services/salesApi.ts\");\n/* harmony import */ var _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/reduxRTK/services/productApi */ \"(app-pages-browser)/./src/reduxRTK/services/productApi.ts\");\n/* harmony import */ var _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/reduxRTK/services/userStoreApi */ \"(app-pages-browser)/./src/reduxRTK/services/userStoreApi.ts\");\n/* harmony import */ var _components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/SlidingPanel */ \"(app-pages-browser)/./src/components/ui/SlidingPanel.tsx\");\n/* harmony import */ var _utils_showMessage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/showMessage */ \"(app-pages-browser)/./src/utils/showMessage.ts\");\n/* harmony import */ var _utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/cloudinaryUtils */ \"(app-pages-browser)/./src/utils/cloudinaryUtils.ts\");\n/* harmony import */ var _sales_panels_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./sales-panels.css */ \"(app-pages-browser)/./src/components/Sales/sales-panels.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst SalesFormPanel = (param)=>{\n    let { isOpen, onClose, onSuccess } = param;\n    var _productsData_data_products, _productsData_data;\n    _s();\n    const [form] = _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm();\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalAmount, setTotalAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStore, setSelectedStore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGeneratingReceipt, setIsGeneratingReceipt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptPreviewVisible, setReceiptPreviewVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiptUrl, setReceiptUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPrinted, setHasPrinted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            console.log(\"Items state changed:\", items);\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items\n    ]);\n    const { data: productsData, isLoading: isLoadingProducts, refetch: refetchProducts, error: productsError, isFetching: isFetchingProducts } = (0,_reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery)({\n        page: 1,\n        limit: 1000,\n        search: searchTerm\n    }, {\n        // Always fetch fresh data from database\n        refetchOnMountOrArgChange: true,\n        refetchOnFocus: true,\n        refetchOnReconnect: true,\n        // Skip caching entirely for sales form to ensure fresh stock data\n        skip: false\n    });\n    // Enhanced products data monitoring and error handling\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (productsData) {\n                var _productsData_data, _productsData_data_products, _productsData_data1;\n                console.log(\"🛒 Fresh products loaded from database:\", {\n                    total: ((_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.total) || 0,\n                    productsCount: ((_productsData_data1 = productsData.data) === null || _productsData_data1 === void 0 ? void 0 : (_productsData_data_products = _productsData_data1.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.length) || 0,\n                    isLoading: isLoadingProducts,\n                    isFetching: isFetchingProducts,\n                    timestamp: new Date().toISOString()\n                });\n            }\n            if (productsError) {\n                console.error(\"❌ Error loading products:\", productsError);\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Failed to load products. Please try again.\");\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        productsData,\n        isLoadingProducts,\n        isFetchingProducts,\n        productsError\n    ]);\n    // Get current user ID from auth state\n    const getCurrentUserId = ()=>{\n        if (true) {\n            var _state_auth_user, _state_auth;\n            // @ts-ignore - Redux state is exposed for debugging\n            const state = window.__REDUX_STATE;\n            return (state === null || state === void 0 ? void 0 : (_state_auth = state.auth) === null || _state_auth === void 0 ? void 0 : (_state_auth_user = _state_auth.user) === null || _state_auth_user === void 0 ? void 0 : _state_auth_user.id) || 0;\n        }\n        return 0;\n    };\n    // Fetch user stores\n    const { data: userStoresData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery)(getCurrentUserId());\n    // Fetch default store\n    const { data: defaultStoreData } = (0,_reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery)(getCurrentUserId());\n    // Set default store when data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (defaultStoreData === null || defaultStoreData === void 0 ? void 0 : defaultStoreData.data) {\n                setSelectedStore(defaultStoreData.data);\n                form.setFieldsValue({\n                    storeId: defaultStoreData.data.id\n                });\n            } else if ((userStoresData === null || userStoresData === void 0 ? void 0 : userStoresData.data) && userStoresData.data.length > 0) {\n                setSelectedStore(userStoresData.data[0]);\n                form.setFieldsValue({\n                    storeId: userStoresData.data[0].id\n                });\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        defaultStoreData,\n        userStoresData,\n        form\n    ]);\n    // Create sale mutation\n    const [createSale, { isLoading: isSubmitting }] = (0,_reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation)();\n    // Calculate total amount whenever items change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (items && items.length > 0) {\n                const total = items.reduce({\n                    \"SalesFormPanel.useEffect.total\": (sum, item)=>sum + item.price * item.quantity\n                }[\"SalesFormPanel.useEffect.total\"], 0);\n                setTotalAmount(total);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: total\n                    });\n                }\n                // Debug log to check items state\n                console.log(\"Current items in useEffect:\", items);\n            } else {\n                setTotalAmount(0);\n                if (form) {\n                    form.setFieldsValue({\n                        totalAmount: 0\n                    });\n                }\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        items,\n        form\n    ]);\n    // Enhanced panel open/close handling with forced data refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (isOpen) {\n                // When panel opens, ALWAYS fetch fresh product data from database\n                console.log(\"🛒 Sales panel opened - forcing fresh product data fetch from database\");\n                // Force refetch to ensure we get the latest stock quantities\n                refetchProducts().then({\n                    \"SalesFormPanel.useEffect\": (result)=>{\n                        if (result.data) {\n                            var _result_data_data_products, _result_data_data;\n                            console.log(\"✅ Fresh product data successfully loaded:\", {\n                                productsCount: ((_result_data_data = result.data.data) === null || _result_data_data === void 0 ? void 0 : (_result_data_data_products = _result_data_data.products) === null || _result_data_data_products === void 0 ? void 0 : _result_data_data_products.length) || 0,\n                                timestamp: new Date().toISOString()\n                            });\n                        }\n                    }\n                }[\"SalesFormPanel.useEffect\"]).catch({\n                    \"SalesFormPanel.useEffect\": (error)=>{\n                        console.error(\"❌ Failed to fetch fresh product data:\", error);\n                        (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Failed to load current product data. Stock quantities may not be accurate.\");\n                    }\n                }[\"SalesFormPanel.useEffect\"]);\n            } else {\n                // Reset form when panel is closed\n                console.log(\"🛒 Sales panel closed - resetting form state\");\n                if (form) {\n                    form.resetFields();\n                }\n                setItems([]);\n                setSelectedProduct(null);\n                setQuantity(1);\n                setTotalAmount(0);\n                setReceiptUrl(null);\n                setReceiptPreviewVisible(false);\n                setHasPrinted(false);\n                setSearchTerm(\"\"); // Reset search term to ensure fresh data on next open\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        isOpen,\n        form,\n        refetchProducts\n    ]);\n    // Handle adding an item to the sale\n    const handleAddItem = ()=>{\n        if (!selectedProduct) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Please select a product\");\n            return;\n        }\n        if (quantity <= 0) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Quantity must be greater than 0\");\n            return;\n        }\n        if (selectedProduct.stockQuantity < quantity) {\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Only \".concat(selectedProduct.stockQuantity, \" units available in stock\"));\n            return;\n        }\n        console.log(\"Adding item with product:\", selectedProduct);\n        // Check if product already exists in items\n        const existingItemIndex = items.findIndex((item)=>item.productId === selectedProduct.id);\n        if (existingItemIndex >= 0) {\n            // Update existing item\n            const updatedItems = [\n                ...items\n            ];\n            const newQuantity = updatedItems[existingItemIndex].quantity + quantity;\n            if (newQuantity > selectedProduct.stockQuantity) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Cannot add more than \".concat(selectedProduct.stockQuantity, \" units of this product\"));\n                return;\n            }\n            updatedItems[existingItemIndex].quantity = newQuantity;\n            // Make sure the product name is set\n            if (!updatedItems[existingItemIndex].productName) {\n                updatedItems[existingItemIndex].productName = selectedProduct.name;\n            }\n            // Update the state with the new array\n            console.log(\"Updating existing item. New items array:\", updatedItems);\n            setItems([\n                ...updatedItems\n            ]); // Create a new array reference to force re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Updated quantity of \".concat(selectedProduct.name));\n        } else {\n            // Add new item\n            const newItem = {\n                productId: selectedProduct.id,\n                productName: selectedProduct.name,\n                quantity,\n                price: typeof selectedProduct.price === \"string\" ? parseFloat(selectedProduct.price) : selectedProduct.price\n            };\n            // Create a new array with the new item\n            const newItems = [\n                ...items,\n                newItem\n            ];\n            // Update the state with the new array\n            console.log(\"Adding new item. New items array:\", newItems);\n            setItems(newItems); // This should trigger a re-render\n            // Show success message\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Added \".concat(quantity, \" \").concat(selectedProduct.name, \" to sale\"));\n        }\n        // Reset selection\n        setSelectedProduct(null);\n        setQuantity(1);\n        if (form) {\n            form.setFieldsValue({\n                productId: undefined,\n                quantity: 1\n            });\n        }\n    };\n    // Handle removing an item from the sale\n    const handleRemoveItem = (index)=>{\n        const updatedItems = [\n            ...items\n        ];\n        updatedItems.splice(index, 1);\n        setItems(updatedItems);\n    };\n    // Handle printing receipt - directly trigger print dialog\n    const handlePrintReceipt = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n            if (!receiptUrl || hasPrinted) {\n                console.log(\"Skipping print: \", !receiptUrl ? \"No receipt URL\" : \"Already printed\");\n                return;\n            }\n            console.log(\"Printing receipt:\", receiptUrl);\n            // Mark as printed immediately to prevent multiple print dialogs\n            setHasPrinted(true);\n            // Create a hidden iframe to load the image\n            const iframe = document.createElement(\"iframe\");\n            iframe.style.display = \"none\";\n            document.body.appendChild(iframe);\n            // Set up the iframe content with the image and print CSS\n            iframe.onload = ({\n                \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                    if (iframe.contentWindow) {\n                        // Write the HTML content to the iframe\n                        iframe.contentWindow.document.write('\\n          <!DOCTYPE html>\\n          <html>\\n            <head>\\n              <title>Print Receipt</title>\\n              <style>\\n                body {\\n                  margin: 0;\\n                  padding: 0;\\n                  display: flex;\\n                  justify-content: center;\\n                  align-items: center;\\n                  height: 100vh;\\n                }\\n                img {\\n                  max-width: 100%;\\n                  max-height: 100vh;\\n                }\\n                @media print {\\n                  body {\\n                    margin: 0;\\n                    padding: 0;\\n                  }\\n                  img {\\n                    width: 100%;\\n                    height: auto;\\n                  }\\n                }\\n              </style>\\n            </head>\\n            <body>\\n              <img src=\"'.concat(receiptUrl, '\" alt=\"Receipt\" />\\n            </body>\\n          </html>\\n        '));\n                        // Close the document\n                        iframe.contentWindow.document.close();\n                        // Use a single print trigger with a delay to ensure the image is loaded\n                        setTimeout({\n                            \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                if (iframe.contentWindow) {\n                                    try {\n                                        // Print the iframe content\n                                        iframe.contentWindow.focus();\n                                        iframe.contentWindow.print();\n                                    } catch (e) {\n                                        console.error(\"Error printing receipt:\", e);\n                                    }\n                                    // Remove the iframe after printing\n                                    setTimeout({\n                                        \"SalesFormPanel.useCallback[handlePrintReceipt]\": ()=>{\n                                            document.body.removeChild(iframe);\n                                        }\n                                    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 1000);\n                                }\n                            }\n                        }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], 500);\n                    }\n                }\n            })[\"SalesFormPanel.useCallback[handlePrintReceipt]\"];\n            // Set the iframe source to trigger the onload event\n            iframe.src = \"about:blank\";\n        }\n    }[\"SalesFormPanel.useCallback[handlePrintReceipt]\"], [\n        receiptUrl,\n        hasPrinted\n    ]);\n    // Effect to automatically print receipt when modal is shown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SalesFormPanel.useEffect\": ()=>{\n            if (receiptPreviewVisible && receiptUrl && !hasPrinted) {\n                // Add a small delay to ensure the receipt image is loaded\n                const timer = setTimeout({\n                    \"SalesFormPanel.useEffect.timer\": ()=>{\n                        handlePrintReceipt(); // This now handles the hasPrinted state internally\n                    }\n                }[\"SalesFormPanel.useEffect.timer\"], 800);\n                return ({\n                    \"SalesFormPanel.useEffect\": ()=>clearTimeout(timer)\n                })[\"SalesFormPanel.useEffect\"];\n            }\n        }\n    }[\"SalesFormPanel.useEffect\"], [\n        receiptPreviewVisible,\n        receiptUrl,\n        hasPrinted,\n        handlePrintReceipt\n    ]);\n    // Handle form submission\n    const handleSubmit = async ()=>{\n        try {\n            var _userStoresData_data;\n            if (items.length === 0) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"Please add at least one item to the sale\");\n                return;\n            }\n            // Validate form fields\n            const values = await form.validateFields();\n            // Check if store is selected\n            if (!selectedStore) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", \"No store information available. Please set up your store in your profile settings.\");\n                return;\n            }\n            // Set loading state for receipt generation\n            setIsGeneratingReceipt(true);\n            // Get store information for receipt\n            const storeInfo = selectedStore || (userStoresData === null || userStoresData === void 0 ? void 0 : (_userStoresData_data = userStoresData.data) === null || _userStoresData_data === void 0 ? void 0 : _userStoresData_data.find((store)=>store.id === values.storeId)) || {\n                name: \"POS System\"\n            };\n            // Generate receipt HTML\n            const receiptHTML = (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__.generateReceiptHTML)({\n                id: Date.now(),\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                transactionDate: new Date().toISOString(),\n                items: items.map((item)=>({\n                        productName: item.productName,\n                        quantity: item.quantity,\n                        price: item.price\n                    }))\n            }, storeInfo);\n            // Generate receipt image and get URL\n            let receiptUrl = \"https://receipt.example.com/placeholder\";\n            try {\n                receiptUrl = await (0,_utils_cloudinaryUtils__WEBPACK_IMPORTED_MODULE_7__.generateReceiptImage)(receiptHTML);\n            } catch (error) {\n                console.error(\"Failed to generate receipt image:\", error);\n            // Continue with placeholder URL if image generation fails\n            }\n            const saleData = {\n                totalAmount,\n                paymentMethod: values.paymentMethod,\n                items: items.map((item)=>({\n                        productId: item.productId,\n                        quantity: item.quantity,\n                        price: item.price\n                    })),\n                receiptUrl,\n                storeId: selectedStore === null || selectedStore === void 0 ? void 0 : selectedStore.id\n            };\n            const response = await createSale(saleData).unwrap();\n            if (response.success) {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"success\", \"Sale created successfully\");\n                // Store the receipt URL for preview\n                setReceiptUrl(receiptUrl);\n                // Show receipt preview modal and offer print option\n                setReceiptPreviewVisible(true);\n                // Refresh product data to get updated stock quantities\n                refetchProducts();\n                // Trigger the success callback to refresh the list WITHOUT closing the panel\n                setTimeout(()=>{\n                    if (onSuccess) {\n                        // Call refetch directly instead of closing the panel\n                        refetchProducts();\n                    }\n                }, 300);\n            // Keep the panel open until the user explicitly closes it\n            // This ensures the receipt modal stays visible\n            } else {\n                (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", response.message || \"Failed to create sale\");\n            }\n        } catch (error) {\n            var _error_data;\n            (0,_utils_showMessage__WEBPACK_IMPORTED_MODULE_6__.showMessage)(\"error\", ((_error_data = error.data) === null || _error_data === void 0 ? void 0 : _error_data.message) || \"An error occurred while creating the sale\");\n        } finally{\n            setIsGeneratingReceipt(false);\n        }\n    };\n    // Debug log to check items state when rendering\n    console.log(\"Rendering with items:\", items);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SlidingPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        title: \"Point of Sale System\",\n        isOpen: isOpen,\n        onClose: onClose,\n        width: \"98%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sales-form min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-20 border-b border-gray-200 bg-white px-6 py-4 shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"text-xl text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-gray-800\",\n                                                    children: \"Point of Sale\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: (selectedStore === null || selectedStore === void 0 ? void 0 : selectedStore.name) || 'NEXAPO POS System'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Transaction Total\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-green-600\",\n                                                    children: [\n                                                        \"GHS \",\n                                                        totalAmount.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Items\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl font-semibold text-gray-700\",\n                                                    children: items.length\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-6 xl:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"xl:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-blue-500\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"text-sm text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 562,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 561,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-bold text-gray-800\",\n                                                                        children: \"Product Selection\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 564,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-full bg-white px-3 py-1 text-xs text-gray-600 shadow-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-1 text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 569,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \" Required fields\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        form: form,\n                                                        layout: \"vertical\",\n                                                        initialValues: {\n                                                            paymentMethod: \"cash\",\n                                                            quantity: 1\n                                                        },\n                                                        className: \"product-form\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 gap-6 lg:grid-cols-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"lg:col-span-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                                            name: \"productId\",\n                                                                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"flex items-center text-sm font-semibold text-gray-700\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"mr-2 text-blue-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 590,\n                                                                                        columnNumber: 31\n                                                                                    }, void 0),\n                                                                                    \"Select Product \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"ml-1 text-red-500\",\n                                                                                        children: \"*\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 591,\n                                                                                        columnNumber: 46\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 589,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            className: \"mb-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                showSearch: true,\n                                                                                placeholder: isLoadingProducts ? \"Loading products...\" : \"🔍 Search and select a product...\",\n                                                                                optionFilterProp: \"children\",\n                                                                                loading: isLoadingProducts,\n                                                                                disabled: isLoadingProducts,\n                                                                                onChange: (value)=>{\n                                                                                    var _productsData_data;\n                                                                                    const product = productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : _productsData_data.products.find((p)=>p.id === value);\n                                                                                    console.log(\"Selected product:\", product);\n                                                                                    if (product) {\n                                                                                        // Make a deep copy to avoid reference issues\n                                                                                        setSelectedProduct({\n                                                                                            ...product,\n                                                                                            // Ensure price is properly formatted\n                                                                                            price: typeof product.price === \"string\" ? product.price : String(product.price)\n                                                                                        });\n                                                                                    } else {\n                                                                                        setSelectedProduct(null);\n                                                                                    }\n                                                                                },\n                                                                                onSearch: setSearchTerm,\n                                                                                filterOption: false,\n                                                                                className: \"rounded-lg border-gray-300 text-gray-800 shadow-sm\",\n                                                                                size: \"large\",\n                                                                                suffixIcon: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    spin: true,\n                                                                                    className: \"text-blue-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 631,\n                                                                                    columnNumber: 33\n                                                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    className: \"text-gray-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 633,\n                                                                                    columnNumber: 33\n                                                                                }, void 0),\n                                                                                notFoundContent: isLoadingProducts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-center py-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                            size: \"small\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 639,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"ml-2 text-gray-500\",\n                                                                                            children: \"Loading products...\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 640,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 638,\n                                                                                    columnNumber: 33\n                                                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"py-4 text-center text-gray-500\",\n                                                                                    children: \"No products found\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 643,\n                                                                                    columnNumber: 33\n                                                                                }, void 0),\n                                                                                children: productsData === null || productsData === void 0 ? void 0 : (_productsData_data = productsData.data) === null || _productsData_data === void 0 ? void 0 : (_productsData_data_products = _productsData_data.products) === null || _productsData_data_products === void 0 ? void 0 : _productsData_data_products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Option, {\n                                                                                        value: product.id,\n                                                                                        disabled: product.stockQuantity <= 0,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center justify-between py-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex-1\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"font-medium text-gray-800\",\n                                                                                                            children: product.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 657,\n                                                                                                            columnNumber: 37\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"text-sm text-gray-500\",\n                                                                                                            children: [\n                                                                                                                \"GHS \",\n                                                                                                                Number(product.price).toFixed(2)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                            lineNumber: 660,\n                                                                                                            columnNumber: 37\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 656,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-right\",\n                                                                                                    children: product.stockQuantity <= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: \"rounded-full bg-red-100 px-2 py-1 text-xs font-medium text-red-600\",\n                                                                                                        children: \"Out of Stock\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 666,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: \"rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-600\",\n                                                                                                        children: [\n                                                                                                            \"Stock: \",\n                                                                                                            product.stockQuantity\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                        lineNumber: 670,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 664,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 655,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, product.id, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 650,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 596,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 586,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 585,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                                            name: \"quantity\",\n                                                                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"flex items-center text-sm font-semibold text-gray-700\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mr-2\",\n                                                                                        children: \"\\uD83D\\uDCE6\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 687,\n                                                                                        columnNumber: 31\n                                                                                    }, void 0),\n                                                                                    \"Quantity \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"ml-1 text-red-500\",\n                                                                                        children: \"*\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 688,\n                                                                                        columnNumber: 40\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 686,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            className: \"mb-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                min: 1,\n                                                                                max: (selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.stockQuantity) || 999,\n                                                                                value: quantity,\n                                                                                onChange: (value)=>setQuantity(value || 1),\n                                                                                style: {\n                                                                                    width: \"100%\"\n                                                                                },\n                                                                                className: \"rounded-lg border-gray-300 text-gray-800 shadow-sm\",\n                                                                                size: \"large\",\n                                                                                placeholder: \"Enter quantity\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 693,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 683,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 682,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            selectedProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 rounded-lg border border-blue-200 bg-blue-50 p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-semibold text-gray-800\",\n                                                                                    children: selectedProduct.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 712,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"Price: GHS \",\n                                                                                        Number(selectedProduct.price).toFixed(2),\n                                                                                        \" | Available: \",\n                                                                                        selectedProduct.stockQuantity,\n                                                                                        \" units\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 715,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 711,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-right\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"Subtotal\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 721,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-lg font-bold text-green-600\",\n                                                                                    children: [\n                                                                                        \"GHS \",\n                                                                                        (Number(selectedProduct.price) * quantity).toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 722,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 720,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 710,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                type: \"primary\",\n                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 732,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                onClick: handleAddItem,\n                                                                className: \"mt-6 h-14 w-full rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 text-lg font-semibold shadow-lg hover:from-blue-600 hover:to-blue-700 hover:shadow-xl\",\n                                                                disabled: !selectedProduct,\n                                                                size: \"large\",\n                                                                children: \"\\uD83D\\uDED2 Add to Cart\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 730,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-b border-gray-100 bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-green-500\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"text-sm text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 750,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 749,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-bold text-gray-800\",\n                                                                        children: \"Shopping Cart\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 752,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 748,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"rounded-full bg-white px-3 py-1 text-sm font-medium text-gray-600 shadow-sm\",\n                                                                        children: [\n                                                                            items.length,\n                                                                            \" \",\n                                                                            items.length === 1 ? 'item' : 'items'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 757,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"rounded-full bg-green-100 px-3 py-1 text-sm font-bold text-green-700\",\n                                                                        children: [\n                                                                            \"GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 760,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-[400px] overflow-x-auto overflow-y-auto\",\n                                                    children: items.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center justify-center py-16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-gray-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"text-3xl text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 770,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 769,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"mb-2 text-lg font-semibold text-gray-600\",\n                                                                children: \"Your cart is empty\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 772,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Add products to start a new transaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"divide-y divide-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-12 gap-4 bg-gray-50 px-6 py-3 text-sm font-semibold text-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-5\",\n                                                                        children: \"Product\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 783,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-2 text-center\",\n                                                                        children: \"Qty\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 784,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-2 text-right\",\n                                                                        children: \"Price\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 785,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-2 text-right\",\n                                                                        children: \"Subtotal\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 786,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"col-span-1 text-center\",\n                                                                        children: \"Action\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 787,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-12 gap-4 px-6 py-4 transition-colors hover:bg-gray-50\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-5 flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"text-blue-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 797,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 796,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-semibold text-gray-800\",\n                                                                                            children: item.productName || \"Unknown Product\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 800,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-gray-500\",\n                                                                                            children: [\n                                                                                                \"Item #\",\n                                                                                                index + 1\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 803,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 799,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 795,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"rounded-full bg-blue-100 px-3 py-1 text-sm font-semibold text-blue-700\",\n                                                                                children: item.quantity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 809,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 808,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 flex items-center justify-end\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-800\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    Number(item.price).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 814,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 813,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-2 flex items-center justify-end\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-bold text-green-600\",\n                                                                                children: [\n                                                                                    \"GHS \",\n                                                                                    (Number(item.price) * item.quantity).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 819,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 818,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"col-span-1 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 825,\n                                                                                    columnNumber: 37\n                                                                                }, void 0),\n                                                                                onClick: ()=>handleRemoveItem(index),\n                                                                                type: \"text\",\n                                                                                danger: true,\n                                                                                className: \"rounded-full text-red-500 hover:bg-red-50 hover:text-red-600\",\n                                                                                size: \"small\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 824,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 823,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, \"\".concat(item.productId, \"-\").concat(index), true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 25\n                                                                }, undefined)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border-t border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-semibold text-gray-800\",\n                                                                            children: \"Cart Total\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 838,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-2xl font-bold text-green-600\",\n                                                                            children: [\n                                                                                \"GHS \",\n                                                                                totalAmount.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 841,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 837,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 836,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sticky top-24 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-b border-gray-100 bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-purple-500\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"text-sm text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 858,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 857,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-bold text-gray-800\",\n                                                            children: \"Checkout\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 860,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 856,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 855,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    form: form,\n                                                    layout: \"vertical\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-6 overflow-hidden rounded-lg border border-gray-200 bg-gradient-to-br from-gray-50 to-gray-100 shadow-inner\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border-b border-gray-200 bg-white px-4 py-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-gray-800\",\n                                                                        children: \"Order Summary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 871,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 870,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-4 space-y-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"flex items-center text-gray-600\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"mr-2\",\n                                                                                            children: \"\\uD83D\\uDCE6\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 876,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        \" Items\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 875,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"rounded-full bg-blue-100 px-2 py-1 text-sm font-semibold text-blue-700\",\n                                                                                    children: items.length\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 878,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 874,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"flex items-center text-gray-600\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"mr-2\",\n                                                                                            children: \"\\uD83D\\uDD22\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 884,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        \" Total Quantity\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 883,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium text-gray-800\",\n                                                                                    children: items.reduce((sum, item)=>sum + item.quantity, 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 886,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 882,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between border-t border-gray-300 pt-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"flex items-center text-lg font-semibold text-gray-800\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"mr-2\",\n                                                                                            children: \"\\uD83D\\uDCB0\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 892,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        \" Total Amount\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 891,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-2xl font-bold text-green-600\",\n                                                                                    children: [\n                                                                                        \"GHS \",\n                                                                                        totalAmount.toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 894,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 890,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 873,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 869,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        selectedStore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"mb-3 block text-sm font-semibold text-gray-700\",\n                                                                    children: \"Store Information\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"rounded-lg border border-green-200 bg-green-50 p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-green-500\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"text-white\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                        lineNumber: 910,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 909,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-semibold text-gray-800\",\n                                                                                            children: selectedStore.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 913,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-gray-600\",\n                                                                                            children: \"Active Store\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 916,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 912,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 908,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"hidden\",\n                                                                            name: \"storeId\",\n                                                                            value: selectedStore.id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 921,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 907,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 903,\n                                                            columnNumber: 23\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-6 rounded-lg border border-orange-200 bg-orange-50 p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-orange-500\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 932,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 931,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-semibold text-orange-800\",\n                                                                                children: \"No Store Selected\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 935,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-orange-600\",\n                                                                                children: \"Please set up your store in profile settings\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 938,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 934,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 930,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 929,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                            name: \"paymentMethod\",\n                                                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"flex items-center text-sm font-semibold text-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-2\",\n                                                                        children: \"\\uD83D\\uDCB3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 950,\n                                                                        columnNumber: 27\n                                                                    }, void 0),\n                                                                    \"Payment Method \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-1 text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 951,\n                                                                        columnNumber: 42\n                                                                    }, void 0)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 949,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            rules: [\n                                                                {\n                                                                    required: true,\n                                                                    message: \"Please select a payment method\"\n                                                                }\n                                                            ],\n                                                            initialValue: \"cash\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"rounded-lg border-gray-300 text-gray-800 shadow-sm\",\n                                                                size: \"large\",\n                                                                placeholder: \"Select payment method\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Option, {\n                                                                        value: \"cash\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between py-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"mr-3 text-lg\",\n                                                                                            children: \"\\uD83D\\uDCB5\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 970,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"font-medium\",\n                                                                                                    children: \"Cash Payment\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 972,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-gray-500\",\n                                                                                                    children: \"Physical cash transaction\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 973,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 971,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 969,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"rounded-full bg-green-100 px-2 py-1 text-xs text-green-600\",\n                                                                                    children: \"Instant\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 976,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 968,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 967,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Option, {\n                                                                        value: \"card\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between py-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"mr-3 text-lg\",\n                                                                                            children: \"\\uD83D\\uDCB3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 984,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"font-medium\",\n                                                                                                    children: \"Card Payment\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 986,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-gray-500\",\n                                                                                                    children: \"Credit/Debit card\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 987,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 985,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 983,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-600\",\n                                                                                    children: \"Secure\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 990,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 982,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 981,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Option, {\n                                                                        value: \"mobile_money\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between py-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"mr-3 text-lg\",\n                                                                                            children: \"\\uD83D\\uDCF1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 998,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"font-medium\",\n                                                                                                    children: \"Mobile Money\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 1000,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-gray-500\",\n                                                                                                    children: \"MTN, Vodafone, AirtelTigo\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                                    lineNumber: 1001,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                            lineNumber: 999,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 997,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"rounded-full bg-purple-100 px-2 py-1 text-xs text-purple-600\",\n                                                                                    children: \"Popular\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                    lineNumber: 1004,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                            lineNumber: 996,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 995,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                lineNumber: 962,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 946,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-8 space-y-4\",\n                                                            children: [\n                                                                receiptPreviewVisible ? // Enhanced \"New Sale\" button when receipt is visible\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    type: \"primary\",\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1017,\n                                                                        columnNumber: 33\n                                                                    }, void 0),\n                                                                    onClick: ()=>{\n                                                                        // Close the modal and reset the receipt state\n                                                                        setReceiptPreviewVisible(false);\n                                                                        setReceiptUrl(null);\n                                                                        setHasPrinted(false);\n                                                                        // Reset the form to start a new sale\n                                                                        form.resetFields();\n                                                                        setItems([]);\n                                                                        setSelectedProduct(null);\n                                                                        setQuantity(1);\n                                                                        setTotalAmount(0);\n                                                                    },\n                                                                    className: \"h-16 w-full rounded-lg bg-gradient-to-r from-green-500 to-green-600 text-lg font-bold shadow-lg hover:from-green-600 hover:to-green-700 hover:shadow-xl\",\n                                                                    size: \"large\",\n                                                                    children: \"\\uD83D\\uDED2 Start New Sale\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1015,\n                                                                    columnNumber: 25\n                                                                }, undefined) : // Enhanced \"Complete Sale\" button\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    type: \"primary\",\n                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1040,\n                                                                        columnNumber: 33\n                                                                    }, void 0),\n                                                                    onClick: handleSubmit,\n                                                                    loading: isSubmitting || isGeneratingReceipt,\n                                                                    disabled: items.length === 0,\n                                                                    className: \"h-16 w-full rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 text-lg font-bold shadow-lg hover:from-blue-600 hover:to-blue-700 hover:shadow-xl disabled:from-gray-400 disabled:to-gray-500\",\n                                                                    size: \"large\",\n                                                                    children: isGeneratingReceipt ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                                lineNumber: 1049,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            \"Generating Receipt...\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1048,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            \"\\uD83D\\uDCB3 Complete Sale - GHS \",\n                                                                            totalAmount.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                        lineNumber: 1053,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1038,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    onClick: onClose,\n                                                                    className: \"h-12 w-full rounded-lg border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400\",\n                                                                    size: \"large\",\n                                                                    children: \"❌ Cancel Transaction\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                                    lineNumber: 1060,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                            lineNumber: 1012,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                    lineNumber: 867,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                                lineNumber: 865,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                        lineNumber: 854,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                                    lineNumber: 853,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 553,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 519,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-gray-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"mr-2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1080,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Receipt Preview\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1081,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 1079,\n                    columnNumber: 11\n                }, void 0),\n                open: receiptPreviewVisible,\n                onCancel: ()=>{\n                    // Close the modal and reset the receipt state\n                    setReceiptPreviewVisible(false);\n                    setReceiptUrl(null);\n                    setHasPrinted(false);\n                    // Reset the form to start a new sale\n                    form.resetFields();\n                    setItems([]);\n                    setSelectedProduct(null);\n                    setQuantity(1);\n                    setTotalAmount(0);\n                },\n                width: 500,\n                centered: true,\n                className: \"receipt-preview-modal\",\n                footer: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        onClick: ()=>{\n                            // Close the modal and reset the receipt state\n                            setReceiptPreviewVisible(false);\n                            setReceiptUrl(null);\n                            setHasPrinted(false);\n                            // Reset the form to start a new sale\n                            form.resetFields();\n                            setItems([]);\n                            setSelectedProduct(null);\n                            setQuantity(1);\n                            setTotalAmount(0);\n                        },\n                        className: \"border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200\",\n                        children: \"Close & New Sale\"\n                    }, \"close\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1102,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        type: \"primary\",\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_LoadingOutlined_PlusOutlined_PrinterOutlined_SearchOutlined_ShopOutlined_ShoppingCartOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1124,\n                            columnNumber: 19\n                        }, void 0),\n                        onClick: ()=>{\n                            // If already printed once, reset the flag to allow printing again\n                            if (hasPrinted) {\n                                setHasPrinted(false);\n                            }\n                            handlePrintReceipt();\n                        },\n                        className: \"bg-blue-600 hover:bg-blue-700\",\n                        children: hasPrinted ? \"Print Again\" : \"Print Receipt\"\n                    }, \"print\", false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1121,\n                        columnNumber: 11\n                    }, void 0)\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: receiptUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"receipt-image-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            src: receiptUrl,\n                            alt: \"Receipt\",\n                            className: \"receipt-image\",\n                            style: {\n                                maxWidth: \"100%\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1141,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1140,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-64 items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            size: \"large\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                            lineNumber: 1150,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                        lineNumber: 1149,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                    lineNumber: 1138,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n                lineNumber: 1077,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\PROJECTS\\\\pos\\\\posfrontend\\\\src\\\\components\\\\Sales\\\\SalesFormPanel.tsx\",\n        lineNumber: 513,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SalesFormPanel, \"Q/g9tGgox4eIYTijyg3KJWlCIZ8=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Form_Image_InputNumber_Modal_Select_Spin_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm,\n        _reduxRTK_services_productApi__WEBPACK_IMPORTED_MODULE_3__.useGetAllProductsQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserStoresQuery,\n        _reduxRTK_services_userStoreApi__WEBPACK_IMPORTED_MODULE_4__.useGetUserDefaultStoreQuery,\n        _reduxRTK_services_salesApi__WEBPACK_IMPORTED_MODULE_2__.useCreateSaleMutation\n    ];\n});\n_c = SalesFormPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SalesFormPanel);\nvar _c;\n$RefreshReg$(_c, \"SalesFormPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sales/SalesFormPanel.tsx\n"));

/***/ })

});