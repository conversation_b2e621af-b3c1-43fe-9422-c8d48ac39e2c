"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ts-custom-error";
exports.ids = ["vendor-chunks/ts-custom-error"];
exports.modules = {

/***/ "(ssr)/./node_modules/ts-custom-error/dist/custom-error.mjs":
/*!************************************************************!*\
  !*** ./node_modules/ts-custom-error/dist/custom-error.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomError: () => (/* binding */ CustomError),\n/* harmony export */   customErrorFactory: () => (/* binding */ customErrorFactory)\n/* harmony export */ });\nfunction fixProto(target, prototype) {\n  var setPrototypeOf = Object.setPrototypeOf;\n  setPrototypeOf ? setPrototypeOf(target, prototype) : target.__proto__ = prototype;\n}\nfunction fixStack(target, fn) {\n  if (fn === void 0) {\n    fn = target.constructor;\n  }\n\n  var captureStackTrace = Error.captureStackTrace;\n  captureStackTrace && captureStackTrace(target, fn);\n}\n\nvar __extends =  false || function () {\n  var _extendStatics = function extendStatics(d, b) {\n    _extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) {\n        if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n      }\n    };\n\n    return _extendStatics(d, b);\n  };\n\n  return function (d, b) {\n    if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n\n    _extendStatics(d, b);\n\n    function __() {\n      this.constructor = d;\n    }\n\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\n\nvar CustomError = function (_super) {\n  __extends(CustomError, _super);\n\n  function CustomError(message, options) {\n    var _newTarget = this.constructor;\n\n    var _this = _super.call(this, message, options) || this;\n\n    Object.defineProperty(_this, 'name', {\n      value: _newTarget.name,\n      enumerable: false,\n      configurable: true\n    });\n    fixProto(_this, _newTarget.prototype);\n    fixStack(_this);\n    return _this;\n  }\n\n  return CustomError;\n}(Error);\n\nvar __spreadArray =  false || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nfunction customErrorFactory(fn, parent) {\n  if (parent === void 0) {\n    parent = Error;\n  }\n\n  function CustomError() {\n    var args = [];\n\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n\n    if (!(this instanceof CustomError)) return new (CustomError.bind.apply(CustomError, __spreadArray([void 0], args, false)))();\n    parent.apply(this, args);\n    Object.defineProperty(this, 'name', {\n      value: fn.name || parent.name,\n      enumerable: false,\n      configurable: true\n    });\n    fn.apply(this, args);\n    fixStack(this, CustomError);\n  }\n\n  return Object.defineProperties(CustomError, {\n    prototype: {\n      value: Object.create(parent.prototype, {\n        constructor: {\n          value: CustomError,\n          writable: true,\n          configurable: true\n        }\n      })\n    }\n  });\n}\n\n\n//# sourceMappingURL=custom-error.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ts-custom-error/dist/custom-error.mjs\n");

/***/ })

};
;