{"version": 3, "sources": ["../../../src/server/lib/dev-bundler-service.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http'\nimport type { <PERSON>B<PERSON>ler } from './router-utils/setup-dev-bundler'\nimport type { WorkerRequestHandler } from './types'\n\nimport { LRUCache } from './lru-cache'\nimport { createRequestResponseMocks } from './mock-request'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../dev/hot-reloader-types'\n\n/**\n * The DevBundlerService provides an interface to perform tasks with the\n * bundler while in development.\n */\nexport class DevBundlerService {\n  public appIsrManifestInner: InstanceType<typeof LRUCache>\n\n  constructor(\n    private readonly bundler: DevBundler,\n    private readonly handler: WorkerRequestHandler\n  ) {\n    this.appIsrManifestInner = new LRUCache(\n      8_000,\n\n      function length() {\n        return 16\n      }\n    ) as any\n  }\n\n  public ensurePage: typeof this.bundler.hotReloader.ensurePage = async (\n    definition\n  ) => {\n    // TODO: remove after ensure is pulled out of server\n    return await this.bundler.hotReloader.ensurePage(definition)\n  }\n\n  public logErrorWithOriginalStack =\n    this.bundler.logErrorWithOriginalStack.bind(this.bundler)\n\n  public async getFallbackErrorComponents(url?: string) {\n    await this.bundler.hotReloader.buildFallbackError()\n    // Build the error page to ensure the fallback is built too.\n    // TODO: See if this can be moved into hotReloader or removed.\n    await this.bundler.hotReloader.ensurePage({\n      page: '/_error',\n      clientOnly: false,\n      definition: undefined,\n      url,\n    })\n  }\n\n  public async getCompilationError(page: string) {\n    const errors = await this.bundler.hotReloader.getCompilationErrors(page)\n    if (!errors) return\n\n    // Return the very first error we found.\n    return errors[0]\n  }\n\n  public async revalidate({\n    urlPath,\n    revalidateHeaders,\n    opts: revalidateOpts,\n  }: {\n    urlPath: string\n    revalidateHeaders: IncomingMessage['headers']\n    opts: any\n  }) {\n    const mocked = createRequestResponseMocks({\n      url: urlPath,\n      headers: revalidateHeaders,\n    })\n\n    await this.handler(mocked.req, mocked.res)\n    await mocked.res.hasStreamed\n\n    if (\n      mocked.res.getHeader('x-nextjs-cache') !== 'REVALIDATED' &&\n      !(mocked.res.statusCode === 404 && revalidateOpts.unstable_onlyGenerated)\n    ) {\n      throw new Error(`Invalid response ${mocked.res.statusCode}`)\n    }\n\n    return {}\n  }\n\n  public get appIsrManifest() {\n    const serializableManifest: Record<string, boolean> = {}\n\n    for (const key of this.appIsrManifestInner.keys() as string[]) {\n      serializableManifest[key] = this.appIsrManifestInner.get(key) as boolean\n    }\n    return serializableManifest\n  }\n\n  public setAppIsrStatus(key: string, value: boolean | null) {\n    if (value === null) {\n      this.appIsrManifestInner.remove(key)\n    } else {\n      this.appIsrManifestInner.set(key, value)\n    }\n    this.bundler?.hotReloader?.send({\n      action: HMR_ACTIONS_SENT_TO_BROWSER.APP_ISR_MANIFEST,\n      data: this.appIsrManifest,\n    })\n  }\n}\n"], "names": ["L<PERSON><PERSON><PERSON>", "createRequestResponseMocks", "HMR_ACTIONS_SENT_TO_BROWSER", "DevBundlerService", "constructor", "bundler", "handler", "ensurePage", "definition", "hotReloader", "logErrorWithOriginalStack", "bind", "appIsrManifestInner", "length", "getFallbackErrorComponents", "url", "buildFallbackError", "page", "clientOnly", "undefined", "getCompilationError", "errors", "getCompilationErrors", "revalidate", "url<PERSON><PERSON>", "revalidateHeaders", "opts", "revalidateOpts", "mocked", "headers", "req", "res", "hasStreamed", "<PERSON><PERSON><PERSON><PERSON>", "statusCode", "unstable_onlyGenerated", "Error", "appIsrManifest", "serializableManifest", "key", "keys", "get", "setAppIsrStatus", "value", "remove", "set", "send", "action", "APP_ISR_MANIFEST", "data"], "mappings": "AAIA,SAASA,QAAQ,QAAQ,cAAa;AACtC,SAASC,0BAA0B,QAAQ,iBAAgB;AAC3D,SAASC,2BAA2B,QAAQ,4BAA2B;AAEvE;;;CAGC,GACD,OAAO,MAAMC;IAGXC,YACE,AAAiBC,OAAmB,EACpC,AAAiBC,OAA6B,CAC9C;aAFiBD,UAAAA;aACAC,UAAAA;aAWZC,aAAyD,OAC9DC;YAEA,oDAAoD;YACpD,OAAO,MAAM,IAAI,CAACH,OAAO,CAACI,WAAW,CAACF,UAAU,CAACC;QACnD;aAEOE,4BACL,IAAI,CAACL,OAAO,CAACK,yBAAyB,CAACC,IAAI,CAAC,IAAI,CAACN,OAAO;QAjBxD,IAAI,CAACO,mBAAmB,GAAG,IAAIZ,SAC7B,MAEA,SAASa;YACP,OAAO;QACT;IAEJ;IAYA,MAAaC,2BAA2BC,GAAY,EAAE;QACpD,MAAM,IAAI,CAACV,OAAO,CAACI,WAAW,CAACO,kBAAkB;QACjD,4DAA4D;QAC5D,8DAA8D;QAC9D,MAAM,IAAI,CAACX,OAAO,CAACI,WAAW,CAACF,UAAU,CAAC;YACxCU,MAAM;YACNC,YAAY;YACZV,YAAYW;YACZJ;QACF;IACF;IAEA,MAAaK,oBAAoBH,IAAY,EAAE;QAC7C,MAAMI,SAAS,MAAM,IAAI,CAAChB,OAAO,CAACI,WAAW,CAACa,oBAAoB,CAACL;QACnE,IAAI,CAACI,QAAQ;QAEb,wCAAwC;QACxC,OAAOA,MAAM,CAAC,EAAE;IAClB;IAEA,MAAaE,WAAW,EACtBC,OAAO,EACPC,iBAAiB,EACjBC,MAAMC,cAAc,EAKrB,EAAE;QACD,MAAMC,SAAS3B,2BAA2B;YACxCc,KAAKS;YACLK,SAASJ;QACX;QAEA,MAAM,IAAI,CAACnB,OAAO,CAACsB,OAAOE,GAAG,EAAEF,OAAOG,GAAG;QACzC,MAAMH,OAAOG,GAAG,CAACC,WAAW;QAE5B,IACEJ,OAAOG,GAAG,CAACE,SAAS,CAAC,sBAAsB,iBAC3C,CAAEL,CAAAA,OAAOG,GAAG,CAACG,UAAU,KAAK,OAAOP,eAAeQ,sBAAsB,AAAD,GACvE;YACA,MAAM,IAAIC,MAAM,CAAC,iBAAiB,EAAER,OAAOG,GAAG,CAACG,UAAU,EAAE;QAC7D;QAEA,OAAO,CAAC;IACV;IAEA,IAAWG,iBAAiB;QAC1B,MAAMC,uBAAgD,CAAC;QAEvD,KAAK,MAAMC,OAAO,IAAI,CAAC3B,mBAAmB,CAAC4B,IAAI,GAAgB;YAC7DF,oBAAoB,CAACC,IAAI,GAAG,IAAI,CAAC3B,mBAAmB,CAAC6B,GAAG,CAACF;QAC3D;QACA,OAAOD;IACT;IAEOI,gBAAgBH,GAAW,EAAEI,KAAqB,EAAE;YAMzD,2BAAA;QALA,IAAIA,UAAU,MAAM;YAClB,IAAI,CAAC/B,mBAAmB,CAACgC,MAAM,CAACL;QAClC,OAAO;YACL,IAAI,CAAC3B,mBAAmB,CAACiC,GAAG,CAACN,KAAKI;QACpC;SACA,gBAAA,IAAI,CAACtC,OAAO,sBAAZ,4BAAA,cAAcI,WAAW,qBAAzB,0BAA2BqC,IAAI,CAAC;YAC9BC,QAAQ7C,4BAA4B8C,gBAAgB;YACpDC,MAAM,IAAI,CAACZ,cAAc;QAC3B;IACF;AACF"}