"use client";

import { useState, useCallback } from 'react';
import { useGetProductByBarcodeQuery, Product } from '@/reduxRTK/services/productApi';
import { showMessage } from '@/utils/showMessage';

interface UseBarcodeScanner {
  isOpen: boolean;
  openScanner: () => void;
  closeScanner: () => void;
  handleBarcodeScanned: (barcode: string) => void;
  isLoading: boolean;
}

interface UseBarcodeScannersProps {
  onProductFound: (product: Product) => void;
  onProductNotFound?: (barcode: string) => void;
}

export const useBarcodeScanner = ({
  onProductFound,
  onProductNotFound
}: UseBarcodeScannersProps): UseBarcodeScanner => {
  const [isOpen, setIsOpen] = useState(false);
  const [scannedBarcode, setScannedBarcode] = useState<string>('');

  // Query to search for product by barcode
  const {
    data: productData,
    isLoading,
    refetch
  } = useGetProductByBarcodeQuery(
    scannedBarcode,
    {
      skip: !scannedBarcode, // Only run query when we have a barcode
      refetchOnMountOrArgChange: true
    }
  );

  const openScanner = useCallback(() => {
    setIsOpen(true);
  }, []);

  const closeScanner = useCallback(() => {
    setIsOpen(false);
    setScannedBarcode('');
  }, []);

  const handleBarcodeScanned = useCallback(async (barcode: string) => {
    console.log('Barcode scanned:', barcode);

    if (!barcode || barcode.trim() === '') {
      showMessage('error', 'Invalid barcode scanned');
      return;
    }

    setScannedBarcode(barcode.trim());

    // Trigger a refetch with the new barcode
    try {
      const result = await refetch();

      if (result.data?.success && result.data.data) {
        const foundProduct = result.data.data;
        console.log('Product found:', foundProduct);
        showMessage('success', `Product found: ${foundProduct.name}`);
        onProductFound(foundProduct);
      } else {
        console.log('No product found for barcode:', barcode);
        showMessage('warning', `No product found for barcode: ${barcode}`);
        if (onProductNotFound) {
          onProductNotFound(barcode);
        }
      }
    } catch (error) {
      console.error('Error searching for product:', error);
      showMessage('error', 'Error searching for product');
    }
  }, [refetch, onProductFound, onProductNotFound]);

  return {
    isOpen,
    openScanner,
    closeScanner,
    handleBarcodeScanned,
    isLoading
  };
};
